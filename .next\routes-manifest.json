{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}, {"source": "/assessment/:id", "destination": "/avaliacoes/:id", "permanent": true, "regex": "^(?!\\/_next)\\/assessment(?:\\/([^\\/]+?))(?:\\/)?$"}], "headers": [{"source": "/avaliacoes/:path*", "headers": [{"key": "Cache-Control", "value": "public, s-maxage=3600, stale-while-revalidate=86400"}, {"key": "X-Robots-Tag", "value": "index, follow"}], "regex": "^\\/avaliacoes(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}, {"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, s-maxage=86400"}, {"key": "Content-Type", "value": "application/xml"}], "regex": "^\\/sitemap\\.xml(?:\\/)?$"}]}