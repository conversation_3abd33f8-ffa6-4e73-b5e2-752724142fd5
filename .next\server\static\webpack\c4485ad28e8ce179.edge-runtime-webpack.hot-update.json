{"c": [], "r": ["middleware", "edge-runtime-webpack"], "m": ["(middleware)/./middleware.ts", "(middleware)/./node_modules/next/dist/build/webpack/loaders/next-middleware-loader.js?absolutePagePath=C%3A%5CUsers%5Cteu-c%5COneDrive%5CDocumentos%5Cproject%5Cmiddleware.ts&page=%2Fmiddleware&rootDir=C%3A%5CUsers%5Cteu-c%5COneDrive%5CDocumentos%5Cproject&matchers=&preferredRegion=&middlewareConfig=e30%3D!", "(middleware)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js", "(middleware)/./node_modules/next/dist/compiled/@opentelemetry/api/index.js", "(middleware)/./node_modules/next/dist/compiled/cookie/index.js", "(middleware)/./node_modules/next/dist/compiled/p-queue/index.js", "(middleware)/./node_modules/next/dist/compiled/react/cjs/react.react-server.development.js", "(middleware)/./node_modules/next/dist/compiled/react/react.react-server.js", "(middleware)/./node_modules/next/dist/compiled/ua-parser-js/ua-parser.js", "(middleware)/./node_modules/next/dist/esm/api/server.js", "(middleware)/./node_modules/next/dist/esm/client/components/app-router-headers.js", "(middleware)/./node_modules/next/dist/esm/client/components/hooks-server-context.js", "(middleware)/./node_modules/next/dist/esm/client/components/http-access-fallback/http-access-fallback.js", "(middleware)/./node_modules/next/dist/esm/client/components/is-next-router-error.js", "(middleware)/./node_modules/next/dist/esm/client/components/redirect-error.js", "(middleware)/./node_modules/next/dist/esm/client/components/redirect-status-code.js", "(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js", "(middleware)/./node_modules/next/dist/esm/lib/constants.js", "(middleware)/./node_modules/next/dist/esm/lib/metadata/metadata-constants.js", "(middleware)/./node_modules/next/dist/esm/lib/scheduler.js", "(middleware)/./node_modules/next/dist/esm/server/after/after-context.js", "(middleware)/./node_modules/next/dist/esm/server/after/after.js", "(middleware)/./node_modules/next/dist/esm/server/after/builtin-request-context.js", "(middleware)/./node_modules/next/dist/esm/server/after/index.js", "(middleware)/./node_modules/next/dist/esm/server/api-utils/index.js", "(middleware)/./node_modules/next/dist/esm/server/app-render/after-task-async-storage-instance.js", "(middleware)/./node_modules/next/dist/esm/server/app-render/after-task-async-storage.external.js", "(middleware)/./node_modules/next/dist/esm/server/app-render/async-local-storage.js", "(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js", "(middleware)/./node_modules/next/dist/esm/server/async-storage/draft-mode-provider.js", "(middleware)/./node_modules/next/dist/esm/server/async-storage/request-store.js", "(middleware)/./node_modules/next/dist/esm/server/async-storage/work-store.js", "(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js", "(middleware)/./node_modules/next/dist/esm/server/internal-utils.js", "(middleware)/./node_modules/next/dist/esm/server/lib/cache-handlers/default.external.js", "(middleware)/./node_modules/next/dist/esm/server/lib/implicit-tags.js", "(middleware)/./node_modules/next/dist/esm/server/lib/incremental-cache/tags-manifest.external.js", "(middleware)/./node_modules/next/dist/esm/server/lib/lazy-result.js", "(middleware)/./node_modules/next/dist/esm/server/lib/lru-cache.js", "(middleware)/./node_modules/next/dist/esm/server/lib/trace/constants.js", "(middleware)/./node_modules/next/dist/esm/server/lib/trace/tracer.js", "(middleware)/./node_modules/next/dist/esm/server/request/connection.js", "(middleware)/./node_modules/next/dist/esm/server/request/root-params.js", "(middleware)/./node_modules/next/dist/esm/server/request/utils.js", "(middleware)/./node_modules/next/dist/esm/server/revalidation-utils.js", "(middleware)/./node_modules/next/dist/esm/server/use-cache/handlers.js", "(middleware)/./node_modules/next/dist/esm/server/web/adapter.js", "(middleware)/./node_modules/next/dist/esm/server/web/error.js", "(middleware)/./node_modules/next/dist/esm/server/web/exports/index.js", "(middleware)/./node_modules/next/dist/esm/server/web/get-edge-preview-props.js", "(middleware)/./node_modules/next/dist/esm/server/web/globals.js", "(middleware)/./node_modules/next/dist/esm/server/web/next-url.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/headers.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/cookies.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/fetch-event.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/image-response.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/request.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/response.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/url-pattern.js", "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/user-agent.js", "(middleware)/./node_modules/next/dist/esm/server/web/utils.js", "(middleware)/./node_modules/next/dist/esm/server/web/web-on-close.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/get-hostname.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/i18n/detect-domain-locale.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/i18n/normalize-locale-path.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/is-thenable.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/page-path/ensure-leading-slash.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/add-locale.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/add-path-prefix.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/add-path-suffix.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/format-next-pathname-info.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/get-next-pathname-info.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/parse-path.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/path-has-prefix.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/relativize-url.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/remove-path-prefix.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/remove-trailing-slash.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/segment.js", "(middleware)/./node_modules/next/dist/esm/shared/lib/utils/reflect-utils.js", "(middleware)/./node_modules/next/dist/experimental/testmode/context.js", "(middleware)/./node_modules/next/dist/experimental/testmode/fetch.js", "(middleware)/./node_modules/next/dist/experimental/testmode/server-edge.js", "(shared)/./node_modules/next/dist/esm/client/components/app-router-headers.js", "(shared)/./node_modules/next/dist/esm/server/app-render/async-local-storage.js", "(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage-instance.js", "(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js", "(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage-instance.js", "(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js", "buffer", "node:async_hooks", ""]}