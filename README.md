# 🎓 Atividade Pronta - Plataforma de Avaliações Educacionais

Uma plataforma completa para professores criarem, gerenciarem e compartilharem avaliações educacionais com banco de questões inteligente e geração de PDFs otimizados para impressão.

## 🚀 Funcionalidades

### ✅ Implementadas
- **Interface Responsiva** - Design moderno e adaptável para todos os dispositivos
- **Sistema de Autenticação** - Login, registro e recuperação de senha
- **Banco de Questões** - Filtros avançados e busca inteligente
- **Editor de Avaliações** - Drag & drop com preview em tempo real
- **Geração de PDF** - Templates personalizáveis e layout profissional
- **Sistema de Favoritos** - Salve suas questões preferidas
- **Templates Premium** - Layouts exclusivos para assinantes
- **Dashboard Analytics** - Métricas e estatísticas de uso
- **Painel de Administração** - Gerenciamento completo da plataforma
- **Sistema de Assinaturas** - Integração com Stripe para pagamentos
- **Controle de Acesso** - Diferentes níveis de permissão

### 🔄 Em Desenvolvimento
- **Geração por IA** - Criação automática de questões
- **Colaboração** - Compartilhamento entre professores
- **Relatórios Avançados** - Analytics detalhados
- **Importação/Exportação** - Suporte a múltiplos formatos

## 🛠️ Tecnologias

- **Frontend**: React 18, TypeScript, Tailwind CSS, Vite
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Pagamentos**: Stripe
- **PDF**: jsPDF, html2canvas
- **UI/UX**: Framer Motion, Lucide React
- **Estado**: React Query, Context API

## 📦 Instalação

1. **Clone o repositório**
   ```bash
   git clone https://github.com/seu-usuario/atividadepronta.git
   cd atividadepronta
   ```

2. **Instale as dependências**
   ```bash
   npm install
   ```

3. **Configure as variáveis de ambiente**
   ```bash
   cp .env.example .env.local
   ```
   
   Edite `.env.local` com suas credenciais:
   ```env
   VITE_SUPABASE_URL=sua_url_do_supabase
   VITE_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase
   VITE_STRIPE_PUBLISHABLE_KEY=sua_chave_publica_do_stripe
   ```

4. **Execute o projeto**
   ```bash
   npm run dev
   ```

## 🗄️ Setup do Banco de Dados

1. **Crie um projeto no Supabase**
   - Acesse [supabase.com](https://supabase.com)
   - Crie um novo projeto
   - Anote a URL e as chaves API

2. **Execute as migrações**
   ```bash
   # No SQL Editor do Supabase, execute em ordem:
   # supabase/migrations/20250614025550_jade_coast.sql
   # supabase/migrations/20250614025616_yellow_hill.sql
   # supabase/migrations/20250614032245_plain_cell.sql
   # supabase/migrations/20250614033422_jolly_dune.sql
   # supabase/migrations/20250614035422_lively_band.sql
   # supabase/migrations/20250614040431_nameless_lantern.sql
   ```

3. **Configure as Edge Functions**
   ```bash
   # Deploy das funções (se usando Supabase CLI)
   supabase functions deploy create-checkout-session
   supabase functions deploy create-portal-session
   supabase functions deploy stripe-webhook
   supabase functions deploy admin-manage-users
   supabase functions deploy admin-manage-questions
   ```

## 💳 Configuração do Stripe

1. **Crie uma conta no Stripe**
   - Acesse [stripe.com](https://stripe.com)
   - Configure seus produtos e preços
   - Anote as chaves API

2. **Configure os webhooks**
   - URL: `https://seu-projeto.supabase.co/functions/v1/stripe-webhook`
   - Eventos: `customer.subscription.*`, `invoice.payment_*`

## 👨‍💼 Painel de Administração

O sistema inclui um painel de administração completo com as seguintes funcionalidades:

### 🔐 Acesso Administrativo
- Apenas usuários com `is_admin = true` podem acessar
- Interface separada em `/admin`
- Controle total sobre a plataforma

### 📊 Funcionalidades do Admin
- **Visão Geral**: Estatísticas e métricas da plataforma
- **Gerenciamento de Usuários**: Promover/rebaixar admins, excluir usuários
- **Moderação de Questões**: Verificar, aprovar e gerenciar questões
- **Gerenciamento de Avaliações**: Controlar visibilidade e conteúdo
- **Templates**: Gerenciar templates premium e do sistema
- **Assinaturas**: Monitorar pagamentos e status
- **Analytics**: Relatórios detalhados de uso
- **Configurações**: Ajustes globais da plataforma

### 🛡️ Segurança
- Políticas RLS específicas para administradores
- Log de auditoria para todas as ações administrativas
- Verificação de permissões em múltiplas camadas

## 📱 Uso

### Para Professores
1. **Cadastre-se** na plataforma
2. **Explore o banco de questões** com filtros por disciplina, série e dificuldade
3. **Crie avaliações** usando o editor drag & drop
4. **Personalize templates** para suas necessidades
5. **Gere PDFs** otimizados para impressão
6. **Acompanhe estatísticas** de uso e desempenho

### Para Escolas
1. **Assine o plano escolar** para até 50 professores
2. **Gerencie usuários** e permissões
3. **Acesse relatórios** institucionais
4. **Compartilhe recursos** entre professores

### Para Administradores
1. **Acesse o painel admin** em `/admin`
2. **Monitore a plataforma** através de métricas em tempo real
3. **Gerencie usuários** e suas permissões
4. **Modere conteúdo** e questões
5. **Configure** parâmetros globais do sistema

## 🎯 Planos de Assinatura

### 🆓 Gratuito
- 50 questões por mês
- 5 avaliações por mês
- Templates básicos
- Suporte por email

### ⭐ Premium - R$ 29,90/mês
- Questões ilimitadas
- Avaliações ilimitadas
- Templates premium
- Geração por IA
- Analytics avançados
- Suporte prioritário

### 🏫 Escolar - R$ 199,90/mês
- Tudo do Premium
- Até 50 professores
- Gestão centralizada
- Relatórios institucionais
- Treinamento incluído
- Suporte dedicado

## 🔧 Desenvolvimento

### Estrutura do Projeto
```
src/
├── components/
│   ├── admin/          # Painel de administração
│   ├── auth/           # Autenticação
│   ├── billing/        # Sistema de pagamentos
│   ├── common/         # Componentes reutilizáveis
│   ├── dashboard/      # Dashboard principal
│   ├── editor/         # Editor de avaliações
│   ├── questions/      # Banco de questões
│   └── ...
├── contexts/           # Contextos React
├── hooks/              # Hooks customizados
├── services/           # Serviços e APIs
├── types/              # Tipos TypeScript
└── utils/              # Utilitários
```

### Scripts Disponíveis
```bash
npm run dev          # Servidor de desenvolvimento
npm run build        # Build para produção
npm run preview      # Preview do build
npm run lint         # Linting do código
npm run test         # Executar testes
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 📞 Suporte

- **Email**: <EMAIL>
- **Documentação**: [docs.atividadepronta.com.br](https://docs.atividadepronta.com.br)
- **Issues**: [GitHub Issues](https://github.com/seu-usuario/atividadepronta/issues)

---

Desenvolvido com ❤️ para a educação brasileira.
