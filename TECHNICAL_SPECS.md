# 🔧 Atividade Pronta - Especificações Técnicas Detalhadas

## 🏗️ ARQUITETURA DO SISTEMA

### 📊 Diagrama de Arquitetura
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Supabase      │    │   Stripe        │
│   (React/Vite)  │◄──►│   (Backend)     │    │   (Payments)    │
│                 │    │                 │    │                 │
│ • Components    │    │ • Database      │    │ • Subscriptions │
│ • State Mgmt    │    │ • Auth          │    │ • Webhooks      │
│ • PDF Gen       │    │ • Storage       │    │ • Billing       │
│ • UI/UX         │    │ • Edge Funcs    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   External      │
                    │   Services      │
                    │                 │
                    │ • OpenAI/Claude │
                    │ • Email Service │
                    │ • Analytics     │
                    │ • CDN           │
                    └─────────────────┘
```

---

## 🗄️ SCHEMA DE BANCO DETALHADO

### 👤 Tabela: profiles
```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  nome TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  escola TEXT,
  disciplinas TEXT[] DEFAULT '{}',
  plano TEXT DEFAULT 'gratuito' CHECK (plano IN ('gratuito', 'premium', 'escolar')),
  avatar_url TEXT,
  configuracoes JSONB DEFAULT '{}',
  estatisticas JSONB DEFAULT '{}',
  onboarding_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### ❓ Tabela: questions
```sql
CREATE TABLE questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  disciplina TEXT NOT NULL,
  serie TEXT NOT NULL,
  topico TEXT NOT NULL,
  subtopico TEXT,
  dificuldade TEXT NOT NULL CHECK (dificuldade IN ('Fácil', 'Médio', 'Difícil')),
  tipo TEXT NOT NULL CHECK (tipo IN ('multipla_escolha', 'dissertativa', 'verdadeiro_falso')),
  competencia_bncc TEXT,
  enunciado TEXT NOT NULL,
  alternativas JSONB, -- Array de strings para múltipla escolha
  resposta_correta TEXT,
  explicacao TEXT,
  imagem_url TEXT,
  tags TEXT[] DEFAULT '{}',
  autor_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  uso_count INTEGER DEFAULT 0,
  rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  is_public BOOLEAN DEFAULT true,
  is_verified BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}', -- Dados extras como fonte, ano, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_questions_disciplina ON questions(disciplina);
CREATE INDEX idx_questions_serie ON questions(serie);
CREATE INDEX idx_questions_topico ON questions(topico);
CREATE INDEX idx_questions_dificuldade ON questions(dificuldade);
CREATE INDEX idx_questions_tipo ON questions(tipo);
CREATE INDEX idx_questions_autor ON questions(autor_id);
CREATE INDEX idx_questions_public ON questions(is_public);
CREATE INDEX idx_questions_tags ON questions USING GIN(tags);
CREATE INDEX idx_questions_search ON questions USING GIN(to_tsvector('portuguese', enunciado));

-- Trigger para atualizar updated_at
CREATE TRIGGER update_questions_updated_at 
    BEFORE UPDATE ON questions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 📝 Tabela: assessments
```sql
CREATE TABLE assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  titulo TEXT NOT NULL,
  disciplina TEXT NOT NULL,
  serie TEXT NOT NULL,
  questoes_ids UUID[] DEFAULT '{}',
  configuracao JSONB NOT NULL DEFAULT '{}',
  template_id UUID REFERENCES templates(id),
  autor_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT false,
  versoes INTEGER DEFAULT 1,
  estatisticas JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX idx_assessments_autor ON assessments(autor_id);
CREATE INDEX idx_assessments_disciplina ON assessments(disciplina);
CREATE INDEX idx_assessments_serie ON assessments(serie);
CREATE INDEX idx_assessments_public ON assessments(is_public);

-- Trigger
CREATE TRIGGER update_assessments_updated_at 
    BEFORE UPDATE ON assessments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 🎨 Tabela: templates
```sql
CREATE TABLE templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome TEXT NOT NULL,
  categoria TEXT NOT NULL,
  layout_config JSONB NOT NULL,
  preview_image TEXT,
  is_premium BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false, -- Templates do sistema vs. criados por usuários
  autor_id UUID REFERENCES profiles(id),
  rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  downloads INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX idx_templates_categoria ON templates(categoria);
CREATE INDEX idx_templates_premium ON templates(is_premium);
CREATE INDEX idx_templates_system ON templates(is_system);
CREATE INDEX idx_templates_rating ON templates(rating DESC);
```

### ⭐ Tabela: favorites
```sql
CREATE TABLE favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, question_id)
);

-- Índices
CREATE INDEX idx_favorites_user ON favorites(user_id);
CREATE INDEX idx_favorites_question ON favorites(question_id);
```

### 💳 Tabela: subscriptions
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT UNIQUE,
  plano TEXT NOT NULL,
  status TEXT NOT NULL, -- active, canceled, past_due, etc.
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX idx_subscriptions_user ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_customer ON subscriptions(stripe_customer_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
```

### 📊 Tabela: usage_stats
```sql
CREATE TABLE usage_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL, -- 'question_created', 'assessment_generated', 'pdf_downloaded', etc.
  resource_id UUID,
  resource_type TEXT, -- 'question', 'assessment', 'template'
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX idx_usage_stats_user ON usage_stats(user_id);
CREATE INDEX idx_usage_stats_action ON usage_stats(action_type);
CREATE INDEX idx_usage_stats_date ON usage_stats(created_at);
CREATE INDEX idx_usage_stats_resource ON usage_stats(resource_id, resource_type);
```

### 💬 Tabela: question_comments
```sql
CREATE TABLE question_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  comment TEXT NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX idx_question_comments_question ON question_comments(question_id);
CREATE INDEX idx_question_comments_user ON question_comments(user_id);
CREATE INDEX idx_question_comments_approved ON question_comments(is_approved);
```

---

## 🔐 ROW LEVEL SECURITY (RLS)

### 👤 Profiles
```sql
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Usuários podem ver seu próprio perfil
CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT USING (auth.uid() = id);

-- Usuários podem atualizar seu próprio perfil
CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE USING (auth.uid() = id);

-- Usuários podem inserir seu próprio perfil (signup)
CREATE POLICY "Users can insert own profile" ON profiles 
  FOR INSERT WITH CHECK (auth.uid() = id);
```

### ❓ Questions
```sql
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

-- Questões públicas são visíveis para todos
CREATE POLICY "Public questions are viewable by everyone" ON questions 
  FOR SELECT USING (is_public = true);

-- Usuários podem ver suas próprias questões
CREATE POLICY "Users can view own questions" ON questions 
  FOR SELECT USING (auth.uid() = autor_id);

-- Usuários podem criar questões
CREATE POLICY "Users can create questions" ON questions 
  FOR INSERT WITH CHECK (auth.uid() = autor_id);

-- Usuários podem atualizar suas próprias questões
CREATE POLICY "Users can update own questions" ON questions 
  FOR UPDATE USING (auth.uid() = autor_id);

-- Usuários podem deletar suas próprias questões
CREATE POLICY "Users can delete own questions" ON questions 
  FOR DELETE USING (auth.uid() = autor_id);
```

### 📝 Assessments
```sql
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;

-- Usuários podem ver suas próprias avaliações
CREATE POLICY "Users can view own assessments" ON assessments 
  FOR SELECT USING (auth.uid() = autor_id);

-- Avaliações públicas são visíveis para todos
CREATE POLICY "Public assessments are viewable by everyone" ON assessments 
  FOR SELECT USING (is_public = true);

-- Usuários podem criar avaliações
CREATE POLICY "Users can create assessments" ON assessments 
  FOR INSERT WITH CHECK (auth.uid() = autor_id);

-- Usuários podem atualizar suas próprias avaliações
CREATE POLICY "Users can update own assessments" ON assessments 
  FOR UPDATE USING (auth.uid() = autor_id);

-- Usuários podem deletar suas próprias avaliações
CREATE POLICY "Users can delete own assessments" ON assessments 
  FOR DELETE USING (auth.uid() = autor_id);
```

### ⭐ Favorites
```sql
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;

-- Usuários podem gerenciar seus próprios favoritos
CREATE POLICY "Users can manage own favorites" ON favorites 
  FOR ALL USING (auth.uid() = user_id);
```

### 💳 Subscriptions
```sql
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Usuários podem ver sua própria assinatura
CREATE POLICY "Users can view own subscription" ON subscriptions 
  FOR SELECT USING (auth.uid() = user_id);

-- Apenas o sistema pode inserir/atualizar assinaturas (via service role)
CREATE POLICY "Service role can manage subscriptions" ON subscriptions 
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
```

---

## 🔧 EDGE FUNCTIONS

### 📧 Função: send-welcome-email
```typescript
// supabase/functions/send-welcome-email/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, nome } = await req.json()
    
    // Enviar email de boas-vindas
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'EduAssess <<EMAIL>>',
        to: [email],
        subject: 'Bem-vindo ao EduAssess!',
        html: `
          <h1>Olá, ${nome}!</h1>
          <p>Bem-vindo ao EduAssess, sua plataforma para criar avaliações educacionais.</p>
          <p>Comece explorando nosso banco de questões e criando sua primeira avaliação.</p>
        `
      })
    })

    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

### 💳 Função: stripe-webhook
```typescript
// supabase/functions/stripe-webhook/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@13.6.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature!,
      Deno.env.get('STRIPE_WEBHOOK_SECRET')!
    )

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        const subscription = event.data.object as Stripe.Subscription
        
        await supabase
          .from('subscriptions')
          .upsert({
            stripe_customer_id: subscription.customer as string,
            stripe_subscription_id: subscription.id,
            status: subscription.status,
            plano: subscription.items.data[0].price.lookup_key,
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            cancel_at_period_end: subscription.cancel_at_period_end,
          })
        break

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object as Stripe.Subscription
        
        await supabase
          .from('subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', deletedSubscription.id)
        break
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (err) {
    return new Response(`Webhook error: ${err.message}`, {
      status: 400,
    })
  }
})
```

### 🤖 Função: generate-question-ai
```typescript
// supabase/functions/generate-question-ai/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { disciplina, serie, topico, dificuldade, tipo } = await req.json()
    
    const prompt = `
      Crie uma questão de ${disciplina} para ${serie} sobre ${topico}.
      Dificuldade: ${dificuldade}
      Tipo: ${tipo}
      
      ${tipo === 'multipla_escolha' ? 
        'Inclua 4 alternativas (a, b, c, d) sendo apenas uma correta.' : 
        'Questão dissertativa com espaço para resposta livre.'
      }
      
      Inclua também uma explicação detalhada da resposta.
      
      Formato de resposta JSON:
      {
        "enunciado": "...",
        "alternativas": ["...", "...", "...", "..."], // apenas para múltipla escolha
        "resposta_correta": "...",
        "explicacao": "...",
        "competencia_bncc": "..."
      }
    `

    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
      })
    })

    const openaiData = await openaiResponse.json()
    const generatedQuestion = JSON.parse(openaiData.choices[0].message.content)

    return new Response(
      JSON.stringify(generatedQuestion),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

---

## 🎨 COMPONENTES REACT PRINCIPAIS

### 🔐 AuthContext
```typescript
// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '../services/supabaseClient'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: any) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Verificar sessão atual
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password })
    if (error) throw error
  }

  const signUp = async (email: string, password: string, userData: any) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    if (error) throw error
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email)
    if (error) throw error
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signUp,
      signOut,
      resetPassword
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

### 💳 SubscriptionContext
```typescript
// src/contexts/SubscriptionContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { supabase } from '../services/supabaseClient'

interface Subscription {
  id: string
  plano: string
  status: string
  current_period_end: string
  cancel_at_period_end: boolean
}

interface SubscriptionContextType {
  subscription: Subscription | null
  loading: boolean
  isActive: boolean
  isPremium: boolean
  canAccess: (feature: string) => boolean
  refreshSubscription: () => Promise<void>
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined)

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchSubscription = async () => {
    if (!user) {
      setSubscription(null)
      setLoading(false)
      return
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      setSubscription(data)
    } catch (error) {
      console.error('Error fetching subscription:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSubscription()
  }, [user])

  const isActive = subscription?.status === 'active'
  const isPremium = isActive && subscription?.plano !== 'gratuito'

  const canAccess = (feature: string): boolean => {
    if (!subscription) return false

    const features = {
      'unlimited_questions': isPremium,
      'unlimited_assessments': isPremium,
      'premium_templates': isPremium,
      'ai_generation': isPremium,
      'collaboration': isPremium,
      'analytics': isPremium
    }

    return features[feature as keyof typeof features] || false
  }

  return (
    <SubscriptionContext.Provider value={{
      subscription,
      loading,
      isActive,
      isPremium,
      canAccess,
      refreshSubscription: fetchSubscription
    }}>
      {children}
    </SubscriptionContext.Provider>
  )
}

export const useSubscription = () => {
  const context = useContext(SubscriptionContext)
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider')
  }
  return context
}
```

---

## 📊 SISTEMA DE ANALYTICS

### 📈 Eventos Trackados
```typescript
// src/utils/analytics.ts
export enum AnalyticsEvent {
  // Autenticação
  USER_SIGNED_UP = 'user_signed_up',
  USER_SIGNED_IN = 'user_signed_in',
  USER_SIGNED_OUT = 'user_signed_out',
  
  // Questões
  QUESTION_CREATED = 'question_created',
  QUESTION_VIEWED = 'question_viewed',
  QUESTION_FAVORITED = 'question_favorited',
  QUESTION_ADDED_TO_ASSESSMENT = 'question_added_to_assessment',
  
  // Avaliações
  ASSESSMENT_CREATED = 'assessment_created',
  ASSESSMENT_EDITED = 'assessment_edited',
  ASSESSMENT_PREVIEWED = 'assessment_previewed',
  PDF_GENERATED = 'pdf_generated',
  PDF_DOWNLOADED = 'pdf_downloaded',
  
  // Assinaturas
  SUBSCRIPTION_STARTED = 'subscription_started',
  SUBSCRIPTION_CANCELED = 'subscription_canceled',
  SUBSCRIPTION_RENEWED = 'subscription_renewed',
  
  // Busca e Filtros
  SEARCH_PERFORMED = 'search_performed',
  FILTER_APPLIED = 'filter_applied',
  
  // IA
  AI_QUESTION_GENERATED = 'ai_question_generated',
  AI_QUESTION_ACCEPTED = 'ai_question_accepted',
  AI_QUESTION_REJECTED = 'ai_question_rejected'
}

interface AnalyticsProperties {
  [key: string]: string | number | boolean | null
}

export const trackEvent = async (
  event: AnalyticsEvent,
  properties?: AnalyticsProperties
) => {
  try {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', event, properties)
    }

    // Supabase Analytics (custom)
    await supabase.from('usage_stats').insert({
      action_type: event,
      metadata: properties || {}
    })
  } catch (error) {
    console.error('Analytics error:', error)
  }
}
```

---

## 🔒 VALIDAÇÃO E SEGURANÇA

### 🛡️ Schemas de Validação (Zod)
```typescript
// src/utils/validation.ts
import { z } from 'zod'

export const QuestionSchema = z.object({
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  topico: z.string().min(1, 'Tópico é obrigatório'),
  subtopico: z.string().optional(),
  dificuldade: z.enum(['Fácil', 'Médio', 'Difícil']),
  tipo: z.enum(['multipla_escolha', 'dissertativa', 'verdadeiro_falso']),
  competencia_bncc: z.string().optional(),
  enunciado: z.string().min(10, 'Enunciado deve ter pelo menos 10 caracteres'),
  alternativas: z.array(z.string()).optional(),
  resposta_correta: z.string().min(1, 'Resposta correta é obrigatória'),
  explicacao: z.string().min(10, 'Explicação deve ter pelo menos 10 caracteres'),
  tags: z.array(z.string()).default([])
})

export const AssessmentSchema = z.object({
  titulo: z.string().min(1, 'Título é obrigatório'),
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  questoes_ids: z.array(z.string().uuid()).min(1, 'Pelo menos uma questão é necessária'),
  configuracao: z.object({
    cabecalho: z.object({
      nomeEscola: z.string(),
      nomeProva: z.string(),
      serie: z.string(),
      data: z.string(),
      instrucoes: z.string()
    }),
    espacamento: z.enum(['compacto', 'normal', 'expandido']),
    numeracao: z.enum(['automatica', 'manual']),
    quebrasPagina: z.boolean(),
    folhaRespostas: z.boolean()
  })
})

export const ProfileSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  escola: z.string().optional(),
  disciplinas: z.array(z.string()).default([])
})
```

### 🔐 Rate Limiting
```typescript
// src/utils/rateLimiting.ts
interface RateLimitConfig {
  windowMs: number // Janela de tempo em ms
  maxRequests: number // Máximo de requests na janela
}

const rateLimits: Record<string, RateLimitConfig> = {
  'question_creation': { windowMs: 60000, maxRequests: 10 }, // 10 questões por minuto
  'pdf_generation': { windowMs: 60000, maxRequests: 5 }, // 5 PDFs por minuto
  'ai_generation': { windowMs: 60000, maxRequests: 3 }, // 3 gerações IA por minuto
}

export const checkRateLimit = async (
  userId: string,
  action: string
): Promise<boolean> => {
  const config = rateLimits[action]
  if (!config) return true

  const windowStart = new Date(Date.now() - config.windowMs)
  
  const { count } = await supabase
    .from('usage_stats')
    .select('*', { count: 'exact' })
    .eq('user_id', userId)
    .eq('action_type', action)
    .gte('created_at', windowStart.toISOString())

  return (count || 0) < config.maxRequests
}
```

---

Este documento técnico fornece a base completa para implementar todos os recursos avançados da plataforma EduAssess, garantindo escalabilidade, segurança e performance.