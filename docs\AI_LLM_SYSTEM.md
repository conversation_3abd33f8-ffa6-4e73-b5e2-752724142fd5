# EduAssess AI Question Generation System - LLM Integration Guide

## Overview

The EduAssess platform now supports multiple state-of-the-art Large Language Models (LLMs) for generating high-quality educational questions aligned with Brazilian educational standards (BNCC).

## Supported LLM Providers

### 🏆 Premium Models (Best Quality)

| Provider | Model | Cost (Input/Output) | Best For |
|----------|-------|-------------------|----------|
| **Anthropic Claude 3.5 Sonnet** | claude-3-5-sonnet-20241022 | $3.00/$15.00 per 1M tokens | Complex reasoning, pedagogical content |
| **OpenAI GPT-4o** | gpt-4o-2024-11-20 | $2.50/$10.00 per 1M tokens | Portuguese fluency, BNCC alignment |
| **Google Gemini 1.5 Pro** | gemini-1.5-pro | $1.25/$5.00 per 1M tokens | Multilingual, large context |

### 💰 Cost-Effective Models

| Provider | Model | Cost (Input/Output) | Best For |
|----------|-------|-------------------|----------|
| **OpenAI GPT-4o Mini** | gpt-4o-mini | $0.15/$0.60 per 1M tokens | Bulk generation, budget-friendly |
| **Claude 3.5 Haiku** | claude-3-5-haiku-20241022 | $0.80/$4.00 per 1M tokens | Fast generation, good quality |
| **Gemini 2.0 Flash** | gemini-2.0-flash-exp | $0.075/$0.30 per 1M tokens | Ultra-fast, extremely cost-effective |

### 🎯 Specialized Models

| Provider | Model | Cost (Input/Output) | Best For |
|----------|-------|-------------------|----------|
| **Cohere Command R+** | command-r-plus-08-2024 | $2.50/$10.00 per 1M tokens | RAG-enhanced questions, multilingual |

## Default Provider Configuration

### 🚀 2025 Enhanced Fallback Chain (Phase 1 Implemented)
1. **🌟 Primary**: Claude Sonnet 4 (2025) - Best quality for educational content
2. **🚀 Secondary**: Gemini 2.5 Pro (2025) - Enhanced reasoning with competitive pricing
3. **⚡ Tertiary**: Gemini 2.5 Flash (2025) - Ultra-cost-effective for high-volume
4. **📦 Legacy Backup**: Claude 3.5 Sonnet - Reliable fallback
5. **📦 Legacy Backup**: OpenAI GPT-4o - Strong Portuguese support
6. **📦 Legacy Backup**: Gemini 1.5 Pro - Additional cost-effective option

## Admin Interface Features

### Default Provider Selection
- **Visual Provider Cards**: Easy selection of preferred default provider
- **Real-time Metrics**: Performance scores and cost information
- **One-click Default Setting**: Instant provider switching

### Advanced Configuration
- **Priority Override**: Custom fallback chain ordering
- **Model Parameters**: Temperature, max tokens, and provider-specific settings
- **Performance Monitoring**: Success rates, response times, and cost tracking
- **API Key Status**: Real-time validation of provider credentials

### Analytics Dashboard
- **30-day Statistics**: Request counts, success rates, token usage
- **Cost Monitoring**: Per-provider cost tracking and optimization
- **Performance Scores**: Automated scoring based on reliability and speed

## Technical Implementation

### Database Schema
```sql
-- Enhanced ai_provider_settings table
ALTER TABLE ai_provider_settings ADD COLUMN:
- is_default BOOLEAN DEFAULT false
- admin_override_priority INTEGER
- performance_score DECIMAL(3,2) DEFAULT 0.0
- last_used TIMESTAMP WITH TIME ZONE
```

### Edge Function Updates
- **Dynamic Provider Loading**: Database-driven provider configuration
- **Admin Default Respect**: Honors admin-selected default provider
- **Intelligent Fallback**: Automatic failover with comprehensive error handling
- **Performance Tracking**: Real-time metrics collection

### Security Features
- **Admin-Only Access**: All LLM configuration restricted to administrators
- **RLS Policies**: Row-level security for provider settings and logs
- **API Key Protection**: Secure environment variable management
- **Audit Logging**: Comprehensive tracking of all generation attempts

## API Integration Details

### Environment Variables Required
```bash
# Core Providers
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
GOOGLE_API_KEY=your-google-key

# Additional Providers
COHERE_API_KEY=your-cohere-key
```

### Provider Endpoints
- **OpenAI**: `https://api.openai.com/v1/chat/completions`
- **Anthropic**: `https://api.anthropic.com/v1/messages`
- **Google**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent`
- **Cohere**: `https://api.cohere.ai/v1/chat`

## Educational Content Optimization

### BNCC Alignment
- **Competency Mapping**: Automatic BNCC competency assignment
- **Grade-Level Appropriateness**: Age-appropriate language and complexity
- **Subject-Specific Prompts**: Tailored prompts for different disciplines

### Portuguese Language Support
- **Native Fluency**: All models tested for Portuguese educational content
- **Cultural Context**: Brazilian educational context awareness
- **Pedagogical Standards**: Alignment with Brazilian teaching methodologies

### Quality Assurance
- **Multi-Provider Validation**: Cross-validation between different models
- **Confidence Scoring**: AI-generated confidence levels for each question
- **Human Review Integration**: Seamless integration with manual review processes

## Performance Optimization

### Intelligent Caching
- **24-hour Cache**: Reduces API calls for similar requests
- **Cache Hit Tracking**: Performance metrics for cache effectiveness
- **Automatic Cleanup**: Removes old, unused cache entries

### Cost Management
- **Real-time Cost Tracking**: Per-request cost calculation
- **Budget Alerts**: Configurable spending thresholds
- **Provider Cost Comparison**: Automatic selection of most cost-effective options

### Rate Limiting
- **Server-side Limits**: 10 requests per minute per user
- **Provider-specific Limits**: Respects individual API rate limits
- **Graceful Degradation**: Intelligent fallback when limits are reached

## Future Enhancements

### Planned Features
- **Custom Model Fine-tuning**: Brazilian education-specific model training
- **Multi-modal Support**: Image and video question generation
- **Collaborative Filtering**: AI-human collaborative question creation
- **Advanced Analytics**: Predictive analytics for question performance

### Scalability Considerations
- **Load Balancing**: Distribute requests across multiple providers
- **Regional Deployment**: Latency optimization for Brazilian users
- **Enterprise Features**: White-label deployment options

## Troubleshooting

### Common Issues
1. **API Key Errors**: Verify environment variables and provider credentials
2. **Rate Limit Exceeded**: Check request frequency and implement backoff
3. **Quality Issues**: Adjust temperature and prompt engineering parameters
4. **Cost Overruns**: Monitor usage and implement budget controls

### Support Resources
- **Admin Dashboard**: Real-time system status and diagnostics
- **Error Logging**: Comprehensive error tracking and resolution
- **Performance Metrics**: Detailed analytics for optimization
- **Documentation**: Complete API and configuration guides

## Conclusion

The enhanced EduAssess AI system provides world-class question generation capabilities with enterprise-grade reliability, cost control, and administrative oversight. The multi-provider approach ensures high availability while the intelligent fallback system maintains consistent quality across all educational content generation scenarios.
