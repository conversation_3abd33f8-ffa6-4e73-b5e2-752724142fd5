# 🚀 Guia de Configuração - EduAssess

## 📋 Pré-requisitos

- Node.js 18+ instalado
- Conta no Supabase
- Conta no Stripe (opcional, para pagamentos)

## 🗄️ 1. Configuração do Supabase

### 1.1 Criar Projeto
1. Acesse [supabase.com](https://supabase.com)
2. Clique em "New Project"
3. Escolha sua organização
4. Digite o nome do projeto: `eduassess`
5. Crie uma senha forte para o banco
6. Selecione a região mais próxima
7. Clique em "Create new project"

### 1.2 Obter Credenciais
1. No painel do projeto, vá em **Settings > API**
2. Copie a **Project URL**
3. Copie a **anon/public key**

### 1.3 Configurar Variáveis de Ambiente
```bash
# Renomeie .env.example para .env.local
cp .env.example .env.local

# Edite .env.local com suas credenciais:
VITE_SUPABASE_URL=https://seu-projeto-id.supabase.co
VITE_SUPABASE_ANON_KEY=sua_chave_anonima_aqui
```

### 1.4 Executar Migrações
1. No painel do Supabase, vá em **SQL Editor**
2. Clique em "New query"
3. Copie e execute o conteúdo de `supabase/migrations/20250614025550_jade_coast.sql`
4. Aguarde a execução completar
5. Repita para `supabase/migrations/20250614025616_yellow_hill.sql`

### 1.5 Verificar Tabelas
1. Vá em **Table Editor**
2. Verifique se as tabelas foram criadas:
   - profiles
   - questions
   - assessments
   - templates
   - favorites
   - subscriptions
   - usage_stats

## 💳 2. Configuração do Stripe (Opcional)

### 2.1 Criar Conta
1. Acesse [stripe.com](https://stripe.com)
2. Crie uma conta ou faça login
3. Ative o modo de teste

### 2.2 Criar Produtos
1. No dashboard, vá em **Products**
2. Clique em "Add product"
3. Crie os produtos:

**Produto Premium:**
- Nome: "EduAssess Premium"
- Preço: R$ 29,90/mês
- ID do preço: `price_premium_monthly`

**Produto Escolar:**
- Nome: "EduAssess Escolar"
- Preço: R$ 199,90/mês
- ID do preço: `price_escolar_monthly`

### 2.3 Obter Chaves
1. Vá em **Developers > API keys**
2. Copie a **Publishable key**
3. Copie a **Secret key** (para as Edge Functions)

### 2.4 Atualizar Variáveis
```bash
# Adicione no .env.local:
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_sua_chave_publica_aqui
```

## ⚡ 3. Configuração das Edge Functions

### 3.1 Criar Funções no Supabase
1. No painel, vá em **Edge Functions**
2. Clique em "Create a new function"
3. Crie as funções:

**create-checkout-session:**
- Nome: `create-checkout-session`
- Copie o código de `supabase/functions/create-checkout-session/index.ts`

**create-portal-session:**
- Nome: `create-portal-session`
- Copie o código de `supabase/functions/create-portal-session/index.ts`

**stripe-webhook:**
- Nome: `stripe-webhook`
- Copie o código de `supabase/functions/stripe-webhook/index.ts`

### 3.2 Configurar Variáveis das Funções
1. Para cada função, vá em **Settings**
2. Adicione as variáveis:
```
STRIPE_SECRET_KEY=sk_test_sua_chave_secreta_aqui
STRIPE_WEBHOOK_SECRET=whsec_sua_chave_webhook_aqui
```

### 3.3 Configurar Webhooks no Stripe
1. No Stripe, vá em **Developers > Webhooks**
2. Clique em "Add endpoint"
3. URL: `https://seu-projeto.supabase.co/functions/v1/stripe-webhook`
4. Eventos para escutar:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copie o **Signing secret**

## 🚀 4. Executar o Projeto

```bash
# Instalar dependências
npm install

# Executar em modo desenvolvimento
npm run dev

# Acessar em http://localhost:5173
```

## ✅ 5. Verificar Funcionamento

### 5.1 Teste de Autenticação
1. Acesse a aplicação
2. Tente criar uma conta
3. Verifique se o perfil é criado na tabela `profiles`

### 5.2 Teste de Questões
1. Faça login
2. Vá em "Banco de Questões"
3. Verifique se as questões de exemplo aparecem
4. Tente criar uma nova questão

### 5.3 Teste de Avaliações
1. Vá em "Editor"
2. Adicione algumas questões
3. Salve a avaliação
4. Verifique se aparece em "Minhas Avaliações"

### 5.4 Teste de PDF
1. No editor, adicione questões
2. Clique em "PDF"
3. Verifique se o PDF é gerado corretamente

## 🔧 6. Solução de Problemas

### Erro: "Missing Supabase environment variables"
- Verifique se o arquivo `.env.local` existe
- Confirme se as variáveis estão corretas
- Reinicie o servidor de desenvolvimento

### Erro: "Failed to fetch"
- Verifique se o projeto Supabase está ativo
- Confirme se as URLs estão corretas
- Verifique se as políticas RLS estão configuradas

### Erro de autenticação
- Verifique se o email confirmation está desabilitado
- Confirme se as políticas RLS permitem inserção
- Verifique se a função de trigger está ativa

### Erro no Stripe
- Confirme se as chaves estão corretas
- Verifique se os produtos foram criados
- Confirme se os webhooks estão configurados

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs no console do navegador
2. Verifique os logs das Edge Functions no Supabase
3. Consulte a documentação do Supabase
4. Abra uma issue no repositório