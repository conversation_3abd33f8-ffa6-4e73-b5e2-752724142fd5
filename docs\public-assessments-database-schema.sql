-- Extensões para avaliações públicas SEO-otimizadas
-- Adicionar campos SEO à tabela assessments existente

-- 1. Adicionar campos SEO à tabela assessments
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS slug TEXT UNIQUE;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_title TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_description TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_keywords TEXT[];
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS featured_image_url TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS public_category TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS difficulty_level TEXT CHECK (difficulty_level IN ('<PERSON>ácil', '<PERSON>édio', '<PERSON><PERSON><PERSON>cil'));
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS estimated_duration INTEGER; -- em minutos
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS download_count INTEGER DEFAULT 0;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS conversion_count INTEGER DEFAULT 0;

-- 2. Criar tabela para tracking de conversões
CREATE TABLE IF NOT EXISTS public_assessment_conversions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  conversion_type TEXT NOT NULL CHECK (conversion_type IN ('signup', 'upgrade', 'download')),
  source_page TEXT NOT NULL, -- URL da página de origem
  user_agent TEXT,
  ip_address INET,
  utm_source TEXT,
  utm_medium TEXT,
  utm_campaign TEXT,
  utm_content TEXT,
  utm_term TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Criar tabela para categorias públicas
CREATE TABLE IF NOT EXISTS public_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  color TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  seo_title TEXT,
  seo_description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Criar tabela para sitemap cache
CREATE TABLE IF NOT EXISTS sitemap_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL UNIQUE,
  last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  change_frequency TEXT DEFAULT 'weekly',
  priority DECIMAL(2,1) DEFAULT 0.5,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_assessments_slug ON assessments(slug);
CREATE INDEX IF NOT EXISTS idx_assessments_public_category ON assessments(public_category);
CREATE INDEX IF NOT EXISTS idx_assessments_is_public_featured ON assessments(is_public, is_featured);
CREATE INDEX IF NOT EXISTS idx_assessments_view_count ON assessments(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_public_conversions_assessment ON public_assessment_conversions(assessment_id);
CREATE INDEX IF NOT EXISTS idx_public_conversions_created ON public_assessment_conversions(created_at);
CREATE INDEX IF NOT EXISTS idx_public_categories_slug ON public_categories(slug);
CREATE INDEX IF NOT EXISTS idx_sitemap_cache_url ON sitemap_cache(url);

-- Triggers para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_public_categories_updated_at 
    BEFORE UPDATE ON public_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sitemap_cache_updated_at 
    BEFORE UPDATE ON sitemap_cache 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para gerar slug único
CREATE OR REPLACE FUNCTION generate_unique_slug(base_title TEXT)
RETURNS TEXT AS $$
DECLARE
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER := 0;
BEGIN
    -- Converter título para slug
    base_slug := lower(trim(regexp_replace(base_title, '[^a-zA-Z0-9\s\-]', '', 'g')));
    base_slug := regexp_replace(base_slug, '\s+', '-', 'g');
    base_slug := regexp_replace(base_slug, '-+', '-', 'g');
    base_slug := trim(base_slug, '-');
    
    -- Limitar tamanho
    IF length(base_slug) > 50 THEN
        base_slug := substring(base_slug, 1, 50);
        base_slug := trim(base_slug, '-');
    END IF;
    
    final_slug := base_slug;
    
    -- Verificar se slug já existe e adicionar contador se necessário
    WHILE EXISTS (SELECT 1 FROM assessments WHERE slug = final_slug) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Trigger para gerar slug automaticamente
CREATE OR REPLACE FUNCTION auto_generate_slug()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug := generate_unique_slug(NEW.titulo);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_generate_assessment_slug
    BEFORE INSERT OR UPDATE ON assessments
    FOR EACH ROW EXECUTE FUNCTION auto_generate_slug();

-- RLS Policies para novas tabelas
ALTER TABLE public_assessment_conversions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE sitemap_cache ENABLE ROW LEVEL SECURITY;

-- Conversões são visíveis apenas para admins
CREATE POLICY "Admins can view all conversions" ON public_assessment_conversions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- Categorias públicas são visíveis para todos
CREATE POLICY "Public categories are viewable by everyone" ON public_categories
  FOR SELECT USING (is_active = true);

-- Admins podem gerenciar categorias
CREATE POLICY "Admins can manage categories" ON public_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- Sitemap é visível para todos
CREATE POLICY "Sitemap is viewable by everyone" ON sitemap_cache
  FOR SELECT USING (is_active = true);

-- Inserir categorias padrão
INSERT INTO public_categories (name, slug, description, icon, color, sort_order) VALUES
('Matemática', 'matematica', 'Avaliações de matemática para todos os níveis', '📊', '#3B82F6', 1),
('Português', 'portugues', 'Avaliações de língua portuguesa e literatura', '📚', '#10B981', 2),
('Ciências', 'ciencias', 'Avaliações de ciências naturais e biologia', '🔬', '#8B5CF6', 3),
('História', 'historia', 'Avaliações de história e estudos sociais', '📜', '#F59E0B', 4),
('Geografia', 'geografia', 'Avaliações de geografia e meio ambiente', '🌍', '#06B6D4', 5),
('Inglês', 'ingles', 'Avaliações de língua inglesa', '🇺🇸', '#EF4444', 6),
('Educação Física', 'educacao-fisica', 'Avaliações teóricas de educação física', '⚽', '#84CC16', 7),
('Artes', 'artes', 'Avaliações de artes visuais e música', '🎨', '#EC4899', 8)
ON CONFLICT (slug) DO NOTHING;
