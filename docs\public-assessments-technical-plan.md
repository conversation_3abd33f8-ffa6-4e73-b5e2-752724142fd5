# 📋 PLANO TÉCNICO: Sistema de Avaliações Públicas para SEO

## 🎯 OBJETIVO PRINCIPAL
Criar páginas públicas de avaliações indexáveis pelos mecanismos de busca para capturar tráfego orgânico e converter visitantes em usuários pagos.

## 🏗️ ARQUITETURA DA SOLUÇÃO

### **Abordagem Técnica: React SPA + SEO Otimizado**
- **Framework**: React + Vite (mantém arquitetura atual)
- **SEO**: React Helmet Async para meta tags dinâmicas
- **Roteamento**: React Router com rotas públicas
- **Cache**: Sistema inteligente para performance
- **Analytics**: Tracking completo de conversões

### **Estrutura de URLs**
```
/                           → Landing Page (atual)
/avaliacoes                 → Lista pública de avaliações
/avaliacoes/[slug]          → Detalhes da avaliação pública
/avaliacoes/categoria/[cat] → Avaliações por categoria
/app/*                      → Aplicação protegida (atual)
```

## 📊 MODIFICAÇÕES NO BANCO DE DADOS

### **Campos Adicionais na Tabela `assessments`:**
- `slug` (TEXT UNIQUE) - URL amigável
- `seo_title` (TEXT) - Título otimizado para SEO
- `seo_description` (TEXT) - Descrição para meta tags
- `seo_keywords` (TEXT[]) - Palavras-chave
- `featured_image_url` (TEXT) - Imagem destacada
- `is_featured` (BOOLEAN) - Avaliação em destaque
- `public_category` (TEXT) - Categoria pública
- `difficulty_level` (TEXT) - Nível de dificuldade
- `estimated_duration` (INTEGER) - Duração estimada
- `view_count` (INTEGER) - Contador de visualizações
- `download_count` (INTEGER) - Contador de downloads
- `conversion_count` (INTEGER) - Contador de conversões

### **Novas Tabelas:**
1. **`public_assessment_conversions`** - Tracking de conversões
2. **`public_categories`** - Categorias públicas
3. **`sitemap_cache`** - Cache do sitemap XML

## 🎨 COMPONENTES REACT A DESENVOLVER

### **1. Componentes Públicos Base**
```typescript
// src/components/public/
├── PublicAssessmentList.tsx     // Lista de avaliações públicas
├── PublicAssessmentDetail.tsx   // Detalhes da avaliação
├── PublicAssessmentPreview.tsx  // Preview da avaliação
├── PublicCategoryPage.tsx       // Página de categoria
├── SEOHead.tsx                  // Meta tags dinâmicas
├── ConversionModal.tsx          // Modal de conversão
└── PublicBreadcrumbs.tsx        // Navegação breadcrumb
```

### **2. Hooks Personalizados**
```typescript
// src/hooks/
├── usePublicAssessments.ts      // Buscar avaliações públicas
├── useConversionTracking.ts     // Tracking de conversões
├── useSEOMetadata.ts            // Metadados SEO
└── usePublicCategories.ts       // Categorias públicas
```

### **3. Utilitários SEO**
```typescript
// src/lib/seo/
├── seoUtils.ts                  // Funções utilitárias SEO
├── schemaMarkup.ts              // Schema.org markup
├── sitemapGenerator.ts          // Gerador de sitemap
└── metaTagsGenerator.ts         // Gerador de meta tags
```

## 🔄 SISTEMA DE CONVERSÃO DIFERENCIADO

### **Fluxo para Avaliações Gratuitas:**
1. **Preview público** → Visualização completa da avaliação
2. **Botão "Baixar Grátis"** → Modal de cadastro/login
3. **Após cadastro** → Download liberado + tracking
4. **Retorno à página** → Continua navegação

### **Fluxo para Avaliações Premium:**
1. **Preview público** → Visualização completa da avaliação
2. **Botão "Assinar para Baixar"** → Redirect para página de planos
3. **Após assinatura** → Retorno automático + download
4. **Tracking completo** → Origem da conversão registrada

## 📈 SISTEMA DE TRACKING E ANALYTICS

### **Métricas a Rastrear:**
- **Visualizações de página** por avaliação
- **Taxa de conversão** (visitante → usuário)
- **Origem do tráfego** (Google, Bing, direto, etc.)
- **Palavras-chave** que geram tráfego
- **Funil de conversão** completo
- **Performance SEO** por página

### **Dashboard Analytics:**
```typescript
// Métricas em tempo real
interface PublicAnalytics {
  totalViews: number
  totalConversions: number
  conversionRate: number
  topPerformingAssessments: Assessment[]
  trafficSources: TrafficSource[]
  keywordRankings: KeywordRanking[]
}
```

## 🚀 OTIMIZAÇÕES SEO AVANÇADAS

### **Meta Tags Dinâmicas:**
```html
<title>Avaliação de Matemática 3º Ano - Frações | Atividade Pronta</title>
<meta name="description" content="Avaliação completa de matemática sobre frações para 3º ano. Download grátis com gabarito e instruções detalhadas." />
<meta name="keywords" content="avaliação matemática, frações, 3º ano, ensino fundamental" />
<meta property="og:title" content="Avaliação de Matemática 3º Ano - Frações" />
<meta property="og:description" content="Avaliação completa de matemática sobre frações..." />
<meta property="og:image" content="https://atvpronta.com.br/assessments/matematica-3-ano-fracoes.jpg" />
<meta property="og:url" content="https://atvpronta.com.br/avaliacoes/matematica-3-ano-fracoes" />
```

### **Schema Markup Estruturado:**
```json
{
  "@context": "https://schema.org",
  "@type": "EducationalResource",
  "name": "Avaliação de Matemática 3º Ano - Frações",
  "description": "Avaliação completa de matemática sobre frações para 3º ano",
  "educationalLevel": "3º Ano",
  "subject": "Matemática",
  "learningResourceType": "Assessment",
  "author": {
    "@type": "Organization",
    "name": "Atividade Pronta"
  },
  "datePublished": "2024-01-15",
  "inLanguage": "pt-BR"
}
```

### **Sitemap XML Automático:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://atvpronta.com.br/avaliacoes/matematica-3-ano-fracoes</loc>
    <lastmod>2024-01-15</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>
```

## ⚡ OTIMIZAÇÕES DE PERFORMANCE

### **Core Web Vitals:**
- **LCP (Largest Contentful Paint)** < 2.5s
- **FID (First Input Delay)** < 100ms
- **CLS (Cumulative Layout Shift)** < 0.1

### **Estratégias de Cache:**
```typescript
// Cache inteligente por tipo de conteúdo
const CACHE_STRATEGIES = {
  assessmentList: '5 minutes',      // Lista muda frequentemente
  assessmentDetail: '1 hour',       // Detalhes mais estáveis
  categories: '1 day',              // Categorias raramente mudam
  sitemap: '6 hours'                // Sitemap atualizado periodicamente
}
```

### **Lazy Loading:**
- **Imagens** carregadas sob demanda
- **Componentes** com React.lazy()
- **Dados** com infinite scroll

## 🔒 CONSIDERAÇÕES DE SEGURANÇA

### **Validações:**
- **Sanitização** de dados públicos
- **Rate limiting** para APIs públicas
- **Proteção** contra scraping excessivo
- **Validação** de parâmetros de URL

### **Privacidade:**
- **LGPD compliance** para tracking
- **Cookies** apenas essenciais
- **Anonimização** de dados sensíveis

## 📱 RESPONSIVIDADE E ACESSIBILIDADE

### **Design Responsivo:**
- **Mobile-first** approach
- **Breakpoints** otimizados
- **Touch-friendly** interfaces
- **Performance** em dispositivos móveis

### **Acessibilidade:**
- **ARIA labels** adequados
- **Navegação** por teclado
- **Contraste** adequado
- **Screen readers** compatíveis

## 🎯 MÉTRICAS DE SUCESSO

### **KPIs Principais:**
- **Tráfego orgânico** +200% em 6 meses
- **Taxa de conversão** 3-5% (visitante → usuário)
- **Posicionamento** top 10 para palavras-chave alvo
- **Core Web Vitals** todos em verde
- **Tempo de permanência** +50%

### **Metas por Fase:**
- **Fase 1**: Estrutura básica funcionando
- **Fase 2**: SEO otimizado e indexação ativa
- **Fase 3**: Conversões funcionando
- **Fase 4**: Performance otimizada
- **Fase 5**: Analytics completo

## 📅 CRONOGRAMA DE IMPLEMENTAÇÃO

### **Fase 1: Fundação (Semana 1-2)**
- Estrutura de banco de dados
- Componentes públicos base
- Roteamento básico

### **Fase 2: SEO Core (Semana 3-4)**
- Meta tags dinâmicas
- Schema markup
- Sitemap automático

### **Fase 3: Conversão (Semana 5-6)**
- Sistema de conversão diferenciado
- Modais contextuais
- Tracking básico

### **Fase 4: Performance (Semana 7-8)**
- Cache inteligente
- Otimizações Core Web Vitals
- Lazy loading

### **Fase 5: Analytics (Semana 9-10)**
- Dashboard completo
- Métricas avançadas
- Testes A/B

Este plano técnico fornece uma base sólida para implementar um sistema completo de avaliações públicas que irá gerar tráfego orgânico significativo e converter visitantes em usuários pagos da plataforma Atividade Pronta.
