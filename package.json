{"name": "atividade-pronta-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "setup": "node scripts/setup-supabase.js", "validate:stripe": "node scripts/validate-stripe.js"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-slot": "^1.2.3", "@react-pdf/renderer": "^3.4.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^2.1.0", "@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^5.8.4", "class-variance-authority": "^0.7.1", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "framer-motion": "^10.16.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "next": "^15.4.4", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.8.1", "react-router-dom": "^6.18.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "web-vitals": "^5.0.3", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.35", "supabase": "^2.31.8", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^0.34.6"}}