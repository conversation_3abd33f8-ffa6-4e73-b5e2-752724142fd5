// Dynamic robots.txt endpoint for Atividade Pronta
// This file serves the robots.txt dynamically

import { generateRobotsTxt } from '../../src/lib/seo/sitemapGenerator.js'

export default async function handler(req, res) {
  try {
    // Set proper headers for text content
    res.setHeader('Content-Type', 'text/plain; charset=utf-8')
    res.setHeader('Cache-Control', 'public, max-age=86400, s-maxage=86400') // Cache for 24 hours
    
    // Generate robots.txt using the existing function
    const robotsTxt = generateRobotsTxt('https://atvpronta.com.br')
    
    // Return the robots.txt content
    res.status(200).send(robotsTxt)
  } catch (error) {
    console.error('Error generating robots.txt:', error)
    
    // Fallback robots.txt content
    const fallbackRobots = `User-agent: *
Allow: /
Allow: /avaliacoes
Allow: /avaliacoes/*

# Disallow admin and app routes
Disallow: /app/
Disallow: /admin/
Disallow: /api/

# Disallow auth pages
Disallow: /login
Disallow: /register

# Allow sitemap
Sitemap: https://atvpronta.com.br/sitemap.xml

# Crawl delay
Crawl-delay: 1`
    
    res.status(200).send(fallbackRobots)
  }
}
