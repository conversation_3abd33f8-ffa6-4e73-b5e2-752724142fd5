// Dynamic sitemap.xml endpoint for Atividade Pronta
// This file serves the sitemap.xml dynamically using the SitemapGenerator

import { SitemapGenerator } from '../../src/lib/seo/sitemapGenerator.js'

export default async function handler(req, res) {
  try {
    // Set proper headers for XML content
    res.setHeader('Content-Type', 'application/xml; charset=utf-8')
    res.setHeader('Cache-Control', 'public, max-age=3600, s-maxage=3600') // Cache for 1 hour
    
    // Generate sitemap using the existing SitemapGenerator
    const generator = new SitemapGenerator({
      baseUrl: 'https://atvpronta.com.br'
    })
    
    const sitemap = await generator.generateSitemap()
    
    // Return the XML sitemap
    res.status(200).send(sitemap)
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Fallback to static sitemap if dynamic generation fails
    const fallbackSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://atvpronta.com.br/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://atvpronta.com.br/avaliacoes</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`
    
    res.status(200).send(fallbackSitemap)
  }
}
