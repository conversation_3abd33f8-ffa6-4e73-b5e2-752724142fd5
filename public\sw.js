/**
 * Service Worker para cache offline e estratégias de cache avançadas
 * Atividade Pronta - Sistema de Cache Inteligente
 */

const CACHE_NAME = 'atividade-pronta-v1'
const STATIC_CACHE = 'static-v1'
const DYNAMIC_CACHE = 'dynamic-v1'
const IMAGE_CACHE = 'images-v1'
const API_CACHE = 'api-v1'

// Recursos estáticos para cache
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/images/logo.png',
  '/images/logo-dark.png',
  // Adicionar outros assets críticos
]

// Estratégias de cache por tipo de recurso
const CACHE_STRATEGIES = {
  // Cache First - Para assets estáticos
  CACHE_FIRST: 'cache-first',
  // Network First - Para dados dinâmicos
  NETWORK_FIRST: 'network-first',
  // Stale While Revalidate - Para recursos que podem ser atualizados
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  // Network Only - Para recursos que sempre precisam ser atuais
  NETWORK_ONLY: 'network-only',
  // Cache Only - Para recursos offline
  CACHE_ONLY: 'cache-only'
}

// Configuração de TTL por tipo de cache
const CACHE_TTL = {
  [STATIC_CACHE]: 30 * 24 * 60 * 60 * 1000, // 30 dias
  [DYNAMIC_CACHE]: 24 * 60 * 60 * 1000, // 1 dia
  [IMAGE_CACHE]: 7 * 24 * 60 * 60 * 1000, // 7 dias
  [API_CACHE]: 5 * 60 * 1000, // 5 minutos
}

// Instalação do Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Service Worker: Static assets cached')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error)
      })
  )
})

// Ativação do Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // Remover caches antigos
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== IMAGE_CACHE && 
                cacheName !== API_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker: Activated')
        return self.clients.claim()
      })
  )
})

// Interceptação de requisições
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Ignorar requisições não HTTP
  if (!request.url.startsWith('http')) {
    return
  }
  
  // Determinar estratégia de cache baseada no tipo de recurso
  const strategy = getCacheStrategy(request)
  
  event.respondWith(
    handleRequest(request, strategy)
  )
})

/**
 * Determinar estratégia de cache baseada na requisição
 */
function getCacheStrategy(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  // API requests
  if (pathname.startsWith('/api/') || url.hostname.includes('supabase')) {
    return CACHE_STRATEGIES.NETWORK_FIRST
  }
  
  // Imagens
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/i)) {
    return CACHE_STRATEGIES.CACHE_FIRST
  }
  
  // Assets estáticos (CSS, JS, fonts)
  if (pathname.match(/\.(css|js|woff|woff2|ttf|eot)$/i)) {
    return CACHE_STRATEGIES.CACHE_FIRST
  }
  
  // PDFs e documentos
  if (pathname.match(/\.(pdf|doc|docx|xls|xlsx)$/i)) {
    return CACHE_STRATEGIES.STALE_WHILE_REVALIDATE
  }
  
  // Páginas HTML
  if (request.headers.get('accept')?.includes('text/html')) {
    return CACHE_STRATEGIES.NETWORK_FIRST
  }
  
  // Default: Network First
  return CACHE_STRATEGIES.NETWORK_FIRST
}

/**
 * Lidar com requisição baseada na estratégia
 */
async function handleRequest(request, strategy) {
  switch (strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request)
    
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request)
    
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request)
    
    case CACHE_STRATEGIES.NETWORK_ONLY:
      return fetch(request)
    
    case CACHE_STRATEGIES.CACHE_ONLY:
      return caches.match(request)
    
    default:
      return networkFirst(request)
  }
}

/**
 * Estratégia Cache First
 */
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request)
  
  if (cachedResponse && !isExpired(cachedResponse)) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await getAppropriateCache(request)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    // Retornar cache mesmo se expirado em caso de erro de rede
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Retornar página offline se disponível
    if (request.headers.get('accept')?.includes('text/html')) {
      return caches.match('/offline.html')
    }
    
    throw error
  }
}

/**
 * Estratégia Network First
 */
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await getAppropriateCache(request)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    const cachedResponse = await caches.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Retornar página offline para requisições HTML
    if (request.headers.get('accept')?.includes('text/html')) {
      return caches.match('/offline.html')
    }
    
    throw error
  }
}

/**
 * Estratégia Stale While Revalidate
 */
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request)
  
  // Sempre tentar buscar da rede em background
  const networkPromise = fetch(request)
    .then(async (networkResponse) => {
      if (networkResponse.ok) {
        const cache = await getAppropriateCache(request)
        cache.put(request, networkResponse.clone())
      }
      return networkResponse
    })
    .catch(() => {
      // Ignorar erros de rede silenciosamente
    })
  
  // Retornar cache imediatamente se disponível
  if (cachedResponse && !isExpired(cachedResponse)) {
    // Continuar revalidação em background
    networkPromise
    return cachedResponse
  }
  
  // Se não há cache ou está expirado, aguardar rede
  try {
    return await networkPromise
  } catch (error) {
    // Retornar cache expirado se rede falhar
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

/**
 * Obter cache apropriado baseado no tipo de requisição
 */
async function getAppropriateCache(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  if (pathname.startsWith('/api/') || url.hostname.includes('supabase')) {
    return caches.open(API_CACHE)
  }
  
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/i)) {
    return caches.open(IMAGE_CACHE)
  }
  
  if (pathname.match(/\.(css|js|woff|woff2|ttf|eot)$/i)) {
    return caches.open(STATIC_CACHE)
  }
  
  return caches.open(DYNAMIC_CACHE)
}

/**
 * Verificar se resposta em cache está expirada
 */
function isExpired(response) {
  const cachedDate = response.headers.get('sw-cached-date')
  if (!cachedDate) return false
  
  const cacheTime = new Date(cachedDate).getTime()
  const now = Date.now()
  const maxAge = CACHE_TTL[DYNAMIC_CACHE] // Default TTL
  
  return (now - cacheTime) > maxAge
}

/**
 * Limpeza periódica do cache
 */
async function cleanupCache() {
  const cacheNames = await caches.keys()
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName)
    const requests = await cache.keys()
    
    for (const request of requests) {
      const response = await cache.match(request)
      if (response && isExpired(response)) {
        await cache.delete(request)
      }
    }
  }
}

// Executar limpeza a cada hora
setInterval(cleanupCache, 60 * 60 * 1000)

// Mensagens do cliente
self.addEventListener('message', (event) => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'CACHE_URLS':
      cacheUrls(payload.urls)
      break
      
    case 'CLEAR_CACHE':
      clearCache(payload.cacheName)
      break
      
    case 'GET_CACHE_STATS':
      getCacheStats().then(stats => {
        event.ports[0].postMessage(stats)
      })
      break
  }
})

/**
 * Cache URLs específicas
 */
async function cacheUrls(urls) {
  const cache = await caches.open(DYNAMIC_CACHE)
  
  for (const url of urls) {
    try {
      await cache.add(url)
    } catch (error) {
      console.warn('Failed to cache URL:', url, error)
    }
  }
}

/**
 * Limpar cache específico
 */
async function clearCache(cacheName) {
  if (cacheName) {
    await caches.delete(cacheName)
  } else {
    const cacheNames = await caches.keys()
    await Promise.all(cacheNames.map(name => caches.delete(name)))
  }
}

/**
 * Obter estatísticas do cache
 */
async function getCacheStats() {
  const cacheNames = await caches.keys()
  const stats = {}
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName)
    const requests = await cache.keys()
    
    stats[cacheName] = {
      count: requests.length,
      size: 0 // Tamanho seria calculado de forma mais complexa
    }
  }
  
  return stats
}
