# 🧪 Guia de Teste no Navegador - Erro 400 Edge Function

## 🎯 Objetivo
Testar a sincronização manual do plano Premium no navegador com usuário admin real para identificar a causa do erro 400.

## 📋 Pré-requisitos
1. ✅ Edge Function deployada com logs detalhados
2. ✅ Usuário admin existe: `<EMAIL>`
3. ✅ Plano Premium com preço dessincronizado (R$ 49,90 vs R$ 29,90)

## 🔧 Passos para Teste

### **1. Preparar Ambiente**
```bash
# Iniciar aplicação
npm run dev
# Acesse: http://localhost:5173
```

### **2. Login como Admin**
1. **Acesse a aplicação**
2. **Faça login com**: `<EMAIL>`
3. **Confirme que está logado** (verifique se aparece nome/avatar)

### **3. Acessar Painel Admin**
1. **Navegue para**: Painel Admin → Gerenciamento de Planos
2. **Verifique se vê a lista de planos**
3. **Localize o plano Premium** (deve mostrar R$ 49,90)

### **4. Abrir DevTools**
1. **Pressione F12** para abrir DevTools
2. **Vá para aba Console**
3. **Limpe o console** (Ctrl+L)

### **5. Executar Sincronização Manual**
1. **Clique no botão de sincronização** (ícone refresh) do plano Premium
2. **Observe os logs no console**

### **6. Logs Esperados**

#### **✅ Se Funcionando:**
```
🔄 Atualizando plano no Stripe: Premium
🔍 Checking authorization...
👤 User authenticated: <EMAIL>
🔍 Checking admin status...
📋 Profile data: {is_admin: true}
✅ Admin access confirmed for: <EMAIL>
🔄 Updating Stripe product for plan: Premium
💰 Price comparison for Premium:
   Current Stripe price: 2990 cents (R$ 29.9)
   New price from DB: 4990 cents (R$ 49.9)
   Prices are equal: false
🔄 Creating new price because difference is 2000 cents
✅ Plano Premium atualizado no Stripe!
```

#### **❌ Se Erro 400:**
```
🔄 Atualizando plano no Stripe: Premium
❌ Erro na atualização: FunctionsHttpError: Edge Function returned a non-2xx status code
❌ Mensagem de erro processada: Invalid user
```

### **7. Verificar Logs da Edge Function**

#### **No Dashboard do Supabase:**
1. **Acesse**: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz
2. **Vá para**: Edge Functions → sync-plan-with-stripe
3. **Clique na aba "Logs"**
4. **Procure por logs recentes** (timestamp da sua tentativa)

#### **Logs Esperados na Edge Function:**
```
🔍 Checking authorization...
👤 User authenticated: <EMAIL>
🔍 Checking admin status...
📋 Profile data: {is_admin: true}
✅ Admin access confirmed for: <EMAIL>
🔄 Updating Stripe product for plan: Premium
📋 Plan data received: {...}
✅ Stripe product ID found: prod_SbO360GkyRhIBK
🔄 Updating Stripe product...
✅ Stripe product updated successfully
🔄 Checking if price needs to be updated...
📋 Retrieving current price from Stripe: price_1RgBHQE40rGVpnravhLnFEcK
✅ Current price retrieved successfully
💰 Price comparison for Premium:
   Current Stripe price: 2990 cents (R$ 29.9)
   New price from DB: 4990 cents (R$ 49.9)
   Prices are equal: false
🔄 Creating new price because difference is 2000 cents
```

## 🔍 Diagnóstico de Problemas

### **Problema 1: "Invalid user"**
**Causa**: Usuário não está autenticado ou token expirou
**Solução**:
1. Faça logout e login novamente
2. Verifique se está usando o email correto: `<EMAIL>`
3. Confirme que o usuário aparece como logado na interface

### **Problema 2: "Unauthorized: Admin access required"**
**Causa**: Usuário não tem permissão de admin
**Solução**:
1. Confirme que está logado com `<EMAIL>`
2. Verifique se o usuário tem `is_admin: true` na tabela profiles

### **Problema 3: Erro na validação de dados**
**Causa**: Dados do plano estão em formato incorreto
**Solução**:
1. Verifique logs detalhados da Edge Function
2. Confirme se todos os campos obrigatórios estão presentes

### **Problema 4: Erro do Stripe**
**Causa**: Problema na comunicação com API do Stripe
**Solução**:
1. Verifique se variáveis de ambiente estão configuradas na Edge Function
2. Confirme se chave do Stripe é válida

## 📊 Resultados Esperados

### **✅ Sucesso:**
1. **Console do navegador**: Mensagem de sucesso
2. **Interface**: Status muda para "Sincronizado"
3. **Banco**: Novo `stripe_price_id` é salvo
4. **Stripe**: Novo preço de R$ 49,90 é criado

### **❌ Falha:**
1. **Console do navegador**: Erro detalhado
2. **Logs da Edge Function**: Ponto exato da falha
3. **Interface**: Mensagem de erro para o usuário

## 🔧 Próximos Passos

### **Se Teste Falhar:**
1. **Anote o erro exato** do console
2. **Copie os logs** da Edge Function
3. **Verifique se usuário está realmente logado**
4. **Confirme permissões de admin**

### **Se Teste Passar:**
1. **Confirme que novo preço foi criado** no Stripe
2. **Verifique se banco foi atualizado** com novo price_id
3. **Teste com outros planos** para confirmar funcionamento

## 📋 Informações de Debug

### **Usuário Admin:**
- Email: `<EMAIL>`
- ID: `08cc92d0-2d69-43d0-aa4c-562d94035c42`
- is_admin: `true`

### **Plano Premium:**
- ID: `3c80415f-ead8-4361-a119-ac6280191844`
- Preço atual no banco: `R$ 49,90`
- Preço atual no Stripe: `R$ 29,90`
- Diferença esperada: `2000 cents`

### **URLs Importantes:**
- Aplicação: http://localhost:5173
- Dashboard Supabase: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz
- Edge Function Logs: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz/functions/sync-plan-with-stripe/logs
