# Script para instalar Supabase CLI e fazer deploy das Edge Functions
# Execute este script no PowerShell como Administrador

Write-Host "🚀 Instalando Supabase CLI e fazendo deploy das Edge Functions..." -ForegroundColor Green

# Verificar se o Supabase CLI está instalado
$supabaseInstalled = Get-Command supabase -ErrorAction SilentlyContinue

if (-not $supabaseInstalled) {
    Write-Host "📦 Instalando Supabase CLI..." -ForegroundColor Yellow
    
    # Instalar via npm (requer Node.js)
    try {
        npm install -g supabase
        Write-Host "✅ Supabase CLI instalado com sucesso!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Erro ao instalar Supabase CLI via npm. Tentando via Chocolatey..." -ForegroundColor Red
        
        # Verificar se Chocolatey está instalado
        $chocoInstalled = Get-Command choco -ErrorAction SilentlyContinue
        
        if ($chocoInstalled) {
            choco install supabase -y
            Write-Host "✅ Supabase CLI instalado via Chocolatey!" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Chocolatey não encontrado. Instale manualmente:" -ForegroundColor Red
            Write-Host "   1. Instale via npm: npm install -g supabase" -ForegroundColor Yellow
            Write-Host "   2. Ou baixe de: https://github.com/supabase/cli/releases" -ForegroundColor Yellow
            exit 1
        }
    }
}
else {
    Write-Host "✅ Supabase CLI já está instalado" -ForegroundColor Green
}

# Verificar se estamos no diretório correto
if (-not (Test-Path "supabase/functions")) {
    Write-Host "❌ Diretório supabase/functions não encontrado!" -ForegroundColor Red
    Write-Host "   Execute este script na raiz do projeto" -ForegroundColor Yellow
    exit 1
}

# Fazer login no Supabase (se necessário)
Write-Host "🔐 Verificando autenticação..." -ForegroundColor Yellow

try {
    $authStatus = supabase auth status 2>&1
    if ($authStatus -match "not logged in") {
        Write-Host "🔑 Fazendo login no Supabase..." -ForegroundColor Yellow
        Write-Host "   Uma página do navegador será aberta para autenticação" -ForegroundColor Cyan
        supabase auth login
    }
    else {
        Write-Host "✅ Já autenticado no Supabase" -ForegroundColor Green
    }
}
catch {
    Write-Host "⚠️  Erro ao verificar status de autenticação. Tentando fazer login..." -ForegroundColor Yellow
    supabase auth login
}

# Listar Edge Functions existentes
Write-Host "📋 Listando Edge Functions existentes..." -ForegroundColor Yellow
try {
    supabase functions list --project-ref wihmaklmjrylsqurtgwz
}
catch {
    Write-Host "⚠️  Erro ao listar funções. Continuando com deploy..." -ForegroundColor Yellow
}

# Deploy da função sync-plan-with-stripe
Write-Host "🚀 Fazendo deploy da função sync-plan-with-stripe..." -ForegroundColor Green

try {
    supabase functions deploy sync-plan-with-stripe --project-ref wihmaklmjrylsqurtgwz
    Write-Host "✅ Função sync-plan-with-stripe deployada com sucesso!" -ForegroundColor Green
}
catch {
    Write-Host "❌ Erro ao fazer deploy da função:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    Write-Host "🔧 Tentativas de solução:" -ForegroundColor Yellow
    Write-Host "   1. Verifique se você tem permissões no projeto Supabase" -ForegroundColor Cyan
    Write-Host "   2. Confirme se o project-ref está correto: wihmaklmjrylsqurtgwz" -ForegroundColor Cyan
    Write-Host "   3. Tente fazer login novamente: supabase auth login" -ForegroundColor Cyan
    exit 1
}

# Testar a função deployada
Write-Host "🧪 Testando função deployada..." -ForegroundColor Yellow

$testUrl = "https://wihmaklmjrylsqurtgwz.supabase.co/functions/v1/sync-plan-with-stripe"

try {
    $response = Invoke-WebRequest -Uri $testUrl -Method OPTIONS -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Função está respondendo corretamente!" -ForegroundColor Green
    }
}
catch {
    Write-Host "⚠️  Função pode não estar totalmente disponível ainda (normal após deploy)" -ForegroundColor Yellow
    Write-Host "   Aguarde alguns minutos e teste novamente" -ForegroundColor Cyan
}

Write-Host "🎉 Deploy concluído!" -ForegroundColor Green
Write-Host "📝 Próximos passos:" -ForegroundColor Cyan
Write-Host "   1. Aguarde 1-2 minutos para a função ficar totalmente disponível" -ForegroundColor White
Write-Host "   2. Teste a sincronização manual no painel admin" -ForegroundColor White
Write-Host "   3. Verifique os logs no dashboard do Supabase se houver problemas" -ForegroundColor White

# Listar funções após deploy
Write-Host "📋 Funções disponíveis após deploy:" -ForegroundColor Yellow
try {
    supabase functions list --project-ref wihmaklmjrylsqurtgwz
}
catch {
    Write-Host "⚠️  Não foi possível listar funções" -ForegroundColor Yellow
}
