# Manual Deploy da Edge Function sync-plan-with-stripe

Como o deploy via CLI está apresentando problemas, você pode fazer o deploy manual através da interface web do Supabase.

## Passos para Deploy Manual:

### 1. Acesse o Dashboard do Supabase
- Vá para: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz
- Faça login se necessário

### 2. Navegue para Edge Functions
- No menu lateral, clique em "Edge Functions"
- Clique em "Create a new function"

### 3. Configure a Função
- **Nome da função**: `sync-plan-with-stripe`
- **Descrição**: `Sincroniza planos entre admin e Stripe`

### 4. Cole o Código da Função
Copie e cole o código abaixo na interface:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@13.6.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  console.log(`🚀 Sync request received: ${req.method} ${req.url}`)

  try {
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, planData } = await req.json()
    console.log(`📋 Action: ${action}, Plan: ${planData?.name}`)
    
    // Get user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('❌ No authorization header provided')
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      console.error('❌ Invalid user:', userError?.message)
      throw new Error('Invalid user')
    }

    console.log(`👤 User authenticated: ${user.email}`)

    // Verify user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      console.error('❌ Unauthorized access attempt by:', user.email)
      throw new Error('Unauthorized: Admin access required')
    }

    console.log('✅ Admin access verified')

    let result = {}

    switch (action) {
      case 'create':
        result = await createStripeProduct(stripe, supabase, planData)
        break
      case 'update':
        result = await updateStripeProduct(stripe, supabase, planData)
        break
      case 'delete':
        result = await deleteStripeProduct(stripe, supabase, planData)
        break
      default:
        throw new Error('Invalid action')
    }

    return new Response(
      JSON.stringify({ success: true, ...result }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error syncing plan with Stripe:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function createStripeProduct(stripe: Stripe, supabase: any, planData: any) {
  console.log('Creating Stripe product for plan:', planData.name)
  
  // Create product in Stripe
  const product = await stripe.products.create({
    name: planData.name,
    description: planData.description || `Plano ${planData.name}`,
    metadata: {
      supabase_plan_id: planData.id,
    },
  })

  // Create price in Stripe
  const price = await stripe.prices.create({
    product: product.id,
    unit_amount: Math.round(planData.price * 100), // Convert to cents
    currency: planData.currency.toLowerCase(),
    recurring: {
      interval: planData.duration_months === 12 ? 'year' : 'month',
    },
    metadata: {
      supabase_plan_id: planData.id,
    },
  })

  // Update plan in Supabase with Stripe IDs
  const { error: updateError } = await supabase
    .from('plans')
    .update({
      stripe_product_id: product.id,
      stripe_price_id: price.id,
      updated_at: new Date().toISOString(),
    })
    .eq('id', planData.id)

  if (updateError) {
    console.error('Error updating plan with Stripe IDs:', updateError)
    throw updateError
  }

  return {
    stripe_product_id: product.id,
    stripe_price_id: price.id,
    message: 'Plan created and synced with Stripe successfully'
  }
}

async function updateStripeProduct(stripe: Stripe, supabase: any, planData: any) {
  console.log('Updating Stripe product for plan:', planData.name)
  
  if (!planData.stripe_product_id) {
    throw new Error('Plan does not have a Stripe product ID')
  }

  // Update product in Stripe
  await stripe.products.update(planData.stripe_product_id, {
    name: planData.name,
    description: planData.description || `Plano ${planData.name}`,
  })

  // If price changed, create new price and archive old one
  if (planData.stripe_price_id) {
    const currentPrice = await stripe.prices.retrieve(planData.stripe_price_id)
    const newPriceAmount = Math.round(planData.price * 100)
    
    if (currentPrice.unit_amount !== newPriceAmount) {
      // Create new price
      const newPrice = await stripe.prices.create({
        product: planData.stripe_product_id,
        unit_amount: newPriceAmount,
        currency: planData.currency.toLowerCase(),
        recurring: {
          interval: planData.duration_months === 12 ? 'year' : 'month',
        },
        metadata: {
          supabase_plan_id: planData.id,
        },
      })

      // Archive old price
      await stripe.prices.update(planData.stripe_price_id, {
        active: false,
      })

      // Update plan in Supabase with new price ID
      const { error: updateError } = await supabase
        .from('plans')
        .update({
          stripe_price_id: newPrice.id,
          updated_at: new Date().toISOString(),
        })
        .eq('id', planData.id)

      if (updateError) {
        console.error('Error updating plan with new price ID:', updateError)
        throw updateError
      }

      return {
        stripe_price_id: newPrice.id,
        message: 'Plan updated and new price created in Stripe'
      }
    }
  }

  return {
    message: 'Plan updated in Stripe successfully'
  }
}

async function deleteStripeProduct(stripe: Stripe, supabase: any, planData: any) {
  console.log('Archiving Stripe product for plan:', planData.name)
  
  if (!planData.stripe_product_id) {
    throw new Error('Plan does not have a Stripe product ID')
  }

  // Archive product in Stripe (cannot delete products with prices)
  await stripe.products.update(planData.stripe_product_id, {
    active: false,
  })

  // Archive price if exists
  if (planData.stripe_price_id) {
    await stripe.prices.update(planData.stripe_price_id, {
      active: false,
    })
  }

  return {
    message: 'Plan archived in Stripe successfully'
  }
}
```

### 5. Configure Variáveis de Ambiente
Na seção "Environment Variables" da função, adicione:

- `STRIPE_SECRET_KEY`: `sk_test_51RgB4eE40rGVpnraYk2AWc0kSfq7tEYQO7fJMsauoZi1B5zRQmXzbBeMZek7wySfHoEAVOepPqbKzTdfMDhGsNIG00G0RfVu1F`
- `SUPABASE_URL`: `https://wihmaklmjrylsqurtgwz.supabase.co`
- `SUPABASE_SERVICE_ROLE_KEY`: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndpaG1ha2xtanJ5bHNxdXJ0Z3d6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTg2OTAxMCwiZXhwIjoyMDY1NDQ1MDEwfQ.syAwMJpRpYE1qB63yCrlEA45G_0hRAPIK2LloNVLWus`

### 6. Deploy
- Clique em "Deploy function"
- Aguarde o deploy ser concluído

### 7. Teste
Após o deploy, teste a função acessando:
`https://wihmaklmjrylsqurtgwz.supabase.co/functions/v1/sync-plan-with-stripe`

## Verificação de Sucesso

Se tudo estiver funcionando, você deve conseguir:
1. Acessar a URL da função sem erro 404
2. Usar o botão de sincronização manual no painel admin
3. Ver logs da função no dashboard do Supabase

## Troubleshooting

Se ainda houver problemas:
1. Verifique os logs da função no dashboard
2. Confirme se as variáveis de ambiente estão corretas
3. Teste com uma requisição OPTIONS primeiro para verificar CORS
