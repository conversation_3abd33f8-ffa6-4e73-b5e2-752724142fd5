# 🚀 Guia de Teste da Sincronização em Produção

## ✅ Status Atual
- **Edge Function**: Deployada e funcionando
- **CORS**: Configurado corretamente  
- **Autenticação**: Implementada (retorna 401 sem auth)
- **Import**: Atualizado para versão de produção

## 🧪 Testes a Realizar no Navegador

### **Teste 1: Verificar Interface Admin**

1. **Acesse a aplicação**: `http://localhost:5173`
2. **Faça login como admin**
3. **Navegue para**: Painel Admin → Gerenciamento de Planos
4. **Verifique se você vê**:
   - ✅ Lista de planos com status de sincronização
   - ✅ Botões de sincronização manual (ícone refresh)
   - ✅ Indicadores visuais: "Sincronizado" ou "Não sincronizado"

### **Teste 2: Sincronização Manual**

1. **Encontre um plano** com status "Não sincronizado" ou "Sincronizado"
2. **Clique no botão de sincronização** (ícone refresh)
3. **Observe**:
   - ✅ Botão deve mostrar loading (spinner)
   - ✅ Toast de sucesso deve aparecer
   - ✅ Status deve atualizar para "Sincronizado"

**⚠️ Se houver erro**:
- Abra DevTools (F12) → Console
- Procure por erros de rede ou CORS
- Anote a mensagem de erro

### **Teste 3: Criar Novo Plano**

1. **Clique em "Criar Novo Plano"**
2. **Preencha os dados**:
   ```
   Nome: Teste Premium Pro
   Descrição: Plano de teste para sincronização
   Preço: 49.90
   Moeda: BRL
   Duração: Mensal (1 mês)
   Features: Teste 1, Teste 2, Teste 3
   Ativo: ✅
   ```
3. **Clique em "Criar Plano"**
4. **Verifique**:
   - ✅ Plano criado com sucesso
   - ✅ Sincronização automática iniciada
   - ✅ IDs reais do Stripe gerados (não dev_xxx)

### **Teste 4: Verificar IDs Reais do Stripe**

Após sincronização bem-sucedida:

1. **No painel admin**, verifique se os planos mostram:
   - ✅ Status "Sincronizado"
   - ✅ Sem IDs começando com "dev_" ou "prod_dev_"

2. **No dashboard do Stripe**:
   - Acesse: https://dashboard.stripe.com/test/products
   - ✅ Verifique se novos produtos foram criados
   - ✅ Confirme se preços correspondem aos configurados

### **Teste 5: Toggle Mensal/Anual**

1. **Acesse a página de preços** da aplicação
2. **Teste o toggle** Mensal ↔ Anual
3. **Verifique**:
   - ✅ Planos mensais e anuais são exibidos corretamente
   - ✅ Cálculo de desconto está correto
   - ✅ Preços são buscados dinamicamente do banco

## 🔧 Troubleshooting

### **Erro de CORS**
```
Access to fetch at '...' from origin 'http://localhost:5173' has been blocked by CORS policy
```
**Solução**: Verifique se a Edge Function tem os headers CORS corretos

### **Erro 401 - Unauthorized**
```
{"code":401,"message":"Missing authorization header"}
```
**Solução**: Usuário não está logado ou não é admin

### **Erro 500 - Internal Server Error**
**Solução**: 
1. Verifique logs no dashboard do Supabase
2. Confirme variáveis de ambiente na Edge Function
3. Verifique se código foi copiado corretamente

### **IDs ainda com prefixo "dev_"**
**Solução**: 
1. Confirme que import foi alterado para `planSyncService.ts`
2. Recarregue a página
3. Teste sincronização novamente

## 📋 Checklist de Validação

### ✅ Funcionalidade Básica
- [ ] Edge Function responde (não 404)
- [ ] CORS funcionando (sem erros no console)
- [ ] Autenticação funcionando (rejeita não-admin)
- [ ] Interface admin carrega corretamente

### ✅ Sincronização Manual
- [ ] Botão de sincronização visível
- [ ] Loading state funciona
- [ ] Toast de sucesso/erro aparece
- [ ] Status atualiza após sincronização

### ✅ Criação de Planos
- [ ] Formulário de criação funciona
- [ ] Sincronização automática após criação
- [ ] IDs reais do Stripe são gerados
- [ ] Plano aparece no dashboard do Stripe

### ✅ Interface do Usuário
- [ ] Toggle mensal/anual funciona
- [ ] Preços são buscados dinamicamente
- [ ] Cálculo de desconto correto
- [ ] Checkout funciona com novos planos

## 🎯 Resultados Esperados

### **Sincronização Bem-sucedida**
```
✅ Plano Teste Premium Pro sincronizado com Stripe!
```

### **IDs Reais Gerados**
```
Product ID: prod_1234567890abcdef
Price ID: price_1234567890abcdef
```

### **No Dashboard do Stripe**
- Novo produto: "Teste Premium Pro"
- Preço: R$ 49,90/mês
- Status: Ativo

## 🚨 Se Algo Não Funcionar

1. **Abra DevTools** (F12) → Console
2. **Anote erros** exatos
3. **Verifique logs** no Supabase Dashboard
4. **Teste com dados simples** primeiro
5. **Confirme variáveis** de ambiente

## 📞 Próximos Passos

Após validação bem-sucedida:
1. ✅ Sistema está em produção
2. ✅ Sincronização bidirecional funcionando
3. ✅ Pronto para uso real
4. 🧹 Limpar arquivos de desenvolvimento
