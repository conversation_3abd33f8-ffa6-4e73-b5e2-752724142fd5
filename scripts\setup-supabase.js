#!/usr/bin/env node

/**
 * Script para verificar e configurar o Supabase
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 EduAssess - Setup do Supabase\n')

// Verificar se .env.local existe
const envPath = path.join(process.cwd(), '.env.local')
if (!fs.existsSync(envPath)) {
  console.log('❌ Arquivo .env.local não encontrado')
  console.log('📝 Criando .env.local a partir do .env.example...\n')
  
  const examplePath = path.join(process.cwd(), '.env.example')
  if (fs.existsSync(examplePath)) {
    fs.copyFileSync(examplePath, envPath)
    console.log('✅ Arquivo .env.local criado')
    console.log('⚠️  IMPORTANTE: Edite o .env.local com suas credenciais do Supabase\n')
  } else {
    console.log('❌ Arquivo .env.example não encontrado')
    process.exit(1)
  }
}

// Ler variáveis de ambiente
require('dotenv').config({ path: envPath })

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY

console.log('🔍 Verificando configuração...\n')

// Verificar se as variáveis estão definidas
if (!supabaseUrl || supabaseUrl === 'your_supabase_project_url') {
  console.log('❌ VITE_SUPABASE_URL não configurada')
  console.log('📝 Configure no arquivo .env.local\n')
} else {
  console.log('✅ VITE_SUPABASE_URL configurada')
}

if (!supabaseKey || supabaseKey === 'your_supabase_anon_key') {
  console.log('❌ VITE_SUPABASE_ANON_KEY não configurada')
  console.log('📝 Configure no arquivo .env.local\n')
} else {
  console.log('✅ VITE_SUPABASE_ANON_KEY configurada')
}

// Verificar se as migrações existem
const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations')
if (fs.existsSync(migrationsDir)) {
  const migrations = fs.readdirSync(migrationsDir).filter(f => f.endsWith('.sql'))
  console.log(`✅ ${migrations.length} migrações encontradas`)
  migrations.forEach(migration => {
    console.log(`   📄 ${migration}`)
  })
} else {
  console.log('❌ Pasta de migrações não encontrada')
}

console.log('\n📋 Próximos passos:')
console.log('1. Configure as variáveis no .env.local')
console.log('2. Execute as migrações no SQL Editor do Supabase')
console.log('3. Configure as Edge Functions (se usar Stripe)')
console.log('4. Execute: npm run dev')
console.log('\n📖 Consulte docs/SETUP_GUIDE.md para instruções detalhadas')