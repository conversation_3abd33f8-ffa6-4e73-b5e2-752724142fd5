const { createClient } = require('@supabase/supabase-js');
const { config } = require('dotenv');

// Carregar variáveis de ambiente
config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function syncStripePlans() {
  console.log('🔄 Sincronizando planos com Stripe...\n');

  try {
    // 1. Verificar planos atuais no banco
    console.log('1. Verificando planos no banco:');
    const { data: plans, error: plansError } = await supabase
      .from('plans')
      .select('*')
      .order('name');

    if (plansError) {
      throw plansError;
    }

    plans.forEach(plan => {
      console.log(`   ✓ ${plan.name}: R$ ${plan.price} - Stripe ID: ${plan.stripe_price_id || 'N/A'}`);
    });

    // 2. Verificar se todos os planos têm Stripe Price IDs
    console.log('\n2. Verificando integração com Stripe:');
    const missingStripeIds = plans.filter(plan => !plan.stripe_price_id && plan.name !== 'Gratuito');
    
    if (missingStripeIds.length > 0) {
      console.log('   ❌ Planos sem Stripe Price ID:');
      missingStripeIds.forEach(plan => {
        console.log(`      - ${plan.name}`);
      });
    } else {
      console.log('   ✅ Todos os planos pagos têm Stripe Price IDs configurados');
    }

    // 3. Verificar consistência de preços
    console.log('\n3. Verificando consistência de dados:');
    const expectedPlans = [
      { name: 'Gratuito', price: '0.00' },
      { name: 'Premium', price: '29.90', stripe_price_id: 'price_1RgBHQE40rGVpnravhLnFEcK' },
      { name: 'Escolar', price: '199.90', stripe_price_id: 'price_1RgCLfE40rGVpnrafFfvawtj' }
    ];

    let allConsistent = true;
    expectedPlans.forEach(expected => {
      const actual = plans.find(p => p.name === expected.name);
      if (!actual) {
        console.log(`   ❌ Plano ${expected.name} não encontrado no banco`);
        allConsistent = false;
      } else if (actual.price !== expected.price) {
        console.log(`   ❌ ${expected.name}: Preço inconsistente (esperado: ${expected.price}, atual: ${actual.price})`);
        allConsistent = false;
      } else if (expected.stripe_price_id && actual.stripe_price_id !== expected.stripe_price_id) {
        console.log(`   ❌ ${expected.name}: Stripe Price ID inconsistente`);
        allConsistent = false;
      } else {
        console.log(`   ✅ ${expected.name}: Dados consistentes`);
      }
    });

    if (allConsistent) {
      console.log('\n✅ Sincronização completa! Todos os dados estão consistentes.');
    } else {
      console.log('\n⚠️  Encontrados problemas de consistência. Verifique os dados acima.');
    }

    // 4. Verificar webhook
    console.log('\n4. Verificando configuração do webhook:');
    console.log('   - Certifique-se de que o webhook do Stripe está configurado para:');
    console.log('     * customer.subscription.created');
    console.log('     * customer.subscription.updated');
    console.log('     * customer.subscription.deleted');
    console.log('   - URL do webhook deve apontar para a função Supabase');
    console.log('   - Webhook secret deve estar configurado nas variáveis de ambiente');

    console.log('\n🎉 Análise de sincronização concluída!');

  } catch (error) {
    console.error('❌ Erro durante a sincronização:', error.message);
  }
}

// Executar sincronização
syncStripePlans();
