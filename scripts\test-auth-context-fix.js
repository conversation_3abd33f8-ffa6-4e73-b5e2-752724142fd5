#!/usr/bin/env node

/**
 * Test script to verify AuthContext fixes are working correctly
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const AUTHCONTEXT_PATH = path.join(__dirname, '../src/contexts/AuthContext.tsx');

function testAuthContextFix() {
  console.log('🔍 Testing AuthContext Critical Fixes...\n');
  
  if (!fs.existsSync(AUTHCONTEXT_PATH)) {
    console.error('❌ AuthContext.tsx not found');
    process.exit(1);
  }
  
  const content = fs.readFileSync(AUTHCONTEXT_PATH, 'utf8');
  
  const criticalChecks = [
    {
      name: 'Empty useEffect dependencies (prevents infinite loop)',
      pattern: /\}, \[\]\) \/\/ Empty dependency array to run only once on mount/,
      description: 'CRITICAL: Prevents infinite re-initialization loop',
      severity: 'CRITICAL'
    },
    {
      name: 'Proper initialization completion',
      pattern: /setLoading\(false\)[\s\S]*?setInitialized\(true\)[\s\S]*?setInitializing\(false\)/,
      description: 'Ensures loading state is properly completed',
      severity: 'HIGH'
    },
    {
      name: 'Error handling with state cleanup',
      pattern: /catch \(error\) \{[\s\S]*?setLoading\(false\)[\s\S]*?setInitialized\(true\)[\s\S]*?setInitializing\(false\)/,
      description: 'Ensures errors don\'t prevent initialization completion',
      severity: 'HIGH'
    },
    {
      name: 'Initialization guard',
      pattern: /if \(initializing \|\| initialized\) \{[\s\S]*?return[\s\S]*?\}/,
      description: 'Prevents multiple initialization attempts',
      severity: 'MEDIUM'
    },
    {
      name: 'No problematic finally block',
      pattern: /finally \{[\s\S]*?session\?\.user/,
      description: 'Should NOT have finally block with session reference',
      severity: 'CRITICAL',
      shouldNotExist: true
    }
  ];
  
  let criticalIssues = 0;
  let highIssues = 0;
  let passedChecks = 0;
  
  console.log('🧪 Running Critical Checks:\n');
  
  criticalChecks.forEach((check, index) => {
    const found = check.pattern.test(content);
    const passed = check.shouldNotExist ? !found : found;
    
    let status, severity;
    if (passed) {
      status = '✅';
      passedChecks++;
    } else {
      if (check.severity === 'CRITICAL') {
        status = '🚨';
        criticalIssues++;
      } else if (check.severity === 'HIGH') {
        status = '⚠️';
        highIssues++;
      } else {
        status = '❌';
      }
    }
    
    console.log(`${status} ${check.name}`);
    console.log(`   ${check.description}`);
    console.log(`   Severity: ${check.severity}`);
    
    if (!passed) {
      if (check.shouldNotExist) {
        console.log(`   ❌ FOUND problematic pattern (should not exist)`);
      } else {
        console.log(`   ❌ Pattern NOT found`);
      }
    }
    
    console.log('');
  });
  
  // Additional logic checks
  console.log('🔍 Additional Logic Checks:\n');
  
  const logicChecks = [
    {
      name: 'No infinite loop potential',
      test: () => {
        // Check if useEffect has dependencies that could cause loops
        const useEffectMatch = content.match(/useEffect\(\(\) => \{[\s\S]*?\}, \[(.*?)\]\)/);
        if (useEffectMatch) {
          const deps = useEffectMatch[1].trim();
          return deps === '' || deps === '// Empty dependency array to run only once on mount';
        }
        return false;
      },
      description: 'UseEffect should have empty dependencies to prevent loops'
    },
    {
      name: 'Proper state initialization order',
      test: () => {
        // Check that setInitializing(true) comes before other operations
        const initMatch = content.match(/setInitializing\(true\)([\s\S]*?)setInitialized\(true\)/);
        return initMatch && initMatch[1].includes('setLoading(false)');
      },
      description: 'State should be set in correct order: initializing -> loading -> initialized'
    },
    {
      name: 'Auth subscription cleanup',
      test: () => {
        return content.includes('authSubscription.unsubscribe()') && 
               content.includes('mounted = false');
      },
      description: 'Proper cleanup of auth subscription and mounted flag'
    }
  ];
  
  logicChecks.forEach(check => {
    const passed = check.test();
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    console.log(`   ${check.description}`);
    if (passed) passedChecks++;
    console.log('');
  });
  
  // Summary
  console.log('📊 RESULTS SUMMARY:\n');
  console.log(`✅ Passed Checks: ${passedChecks}/${criticalChecks.length + logicChecks.length}`);
  console.log(`🚨 Critical Issues: ${criticalIssues}`);
  console.log(`⚠️  High Priority Issues: ${highIssues}`);
  
  if (criticalIssues === 0 && highIssues === 0) {
    console.log('\n🎉 ALL CRITICAL FIXES VERIFIED!');
    console.log('\n✅ The AuthContext should now:');
    console.log('   - Load properly without getting stuck');
    console.log('   - Work correctly in single and multiple tabs');
    console.log('   - Handle errors gracefully');
    console.log('   - Complete initialization in all scenarios');
    
    console.log('\n📋 MANUAL TESTING STEPS:');
    console.log('1. 🌐 Open the application in a fresh browser tab');
    console.log('2. 🔄 Refresh the page multiple times');
    console.log('3. 🗂️  Open multiple tabs simultaneously');
    console.log('4. 🔐 Test with logged in and logged out states');
    console.log('5. 🕵️  Check browser console for proper initialization logs');
    
    return true;
  } else {
    console.log('\n❌ CRITICAL ISSUES FOUND!');
    console.log('\nThe application may still experience loading problems.');
    console.log('Please review and fix the issues above before testing.');
    
    return false;
  }
}

// Run the test
const success = testAuthContextFix();

console.log('\n' + '='.repeat(60));
console.log('AuthContext Critical Fix Validation');
console.log('='.repeat(60));

if (success) {
  console.log('\n✅ VALIDATION PASSED - AuthContext should work correctly');
  process.exit(0);
} else {
  console.log('\n❌ VALIDATION FAILED - Critical issues need to be resolved');
  process.exit(1);
}
