# 🧪 Teste da Correção do Erro de Preço Padrão

## 🎯 Objetivo
Validar que a correção implementada resolve o erro "This price cannot be archived because it is the default price" durante a sincronização manual.

## 🔧 Correções Implementadas

### **1. Definir Novo Preço como Padrão Primeiro**
```typescript
// Set new price as default for the product before archiving old one
console.log('🔄 Setting new price as default for product...')
await stripe.products.update(planData.stripe_product_id, {
  default_price: newPrice.id,
})
console.log('✅ New price set as default')
```

### **2. Verificação de Segurança**
```typescript
// Double-check if old price is still default (safety check)
const isStillDefault = await isDefaultPrice(stripe, planData.stripe_product_id, planData.stripe_price_id)
if (isStillDefault) {
  console.log('⚠️ Old price is still default - skipping archive to avoid error')
} else {
  // Proceed with archiving
}
```

### **3. Tratamento de Erro Robusto**
```typescript
try {
  await stripe.prices.update(planData.stripe_price_id, {
    active: false,
  })
  console.log('✅ Old price archived successfully')
} catch (archiveError: any) {
  console.error('⚠️ Warning: Could not archive old price:', archiveError.message)
  // Don't throw error - operation can continue
}
```

## 📋 Passos para Teste

### **1. Preparar Ambiente**
```bash
# Iniciar aplicação
npm run dev
# Acesse: http://localhost:5173
```

### **2. Login e Acesso**
1. **Login**: `<EMAIL>`
2. **Navegue**: Painel Admin → Gerenciamento de Planos
3. **Abra DevTools**: F12 → Console

### **3. Executar Teste**
1. **Localize o plano Premium** (R$ 49,90)
2. **Clique no botão de sincronização manual**
3. **Observe os logs detalhados**

## 📊 Logs Esperados

### **✅ Sucesso (Novo Comportamento):**
```
🔄 Atualizando plano no Stripe: Premium
🔍 Checking authorization...
👤 User authenticated: <EMAIL>
✅ Admin access confirmed for: <EMAIL>
🔄 Updating Stripe product for plan: Premium
📋 Plan data received: {...}
✅ Stripe product ID found: prod_SbO360GkyRhIBK
🔄 Updating Stripe product...
✅ Stripe product updated successfully
🔄 Checking if price needs to be updated...
📋 Retrieving current price from Stripe: price_1RgBHQE40rGVpnravhLnFEcK
✅ Current price retrieved successfully
💰 Price comparison for Premium:
   Current Stripe price: 2990 cents (R$ 29.9)
   New price from DB: 4990 cents (R$ 49.9)
   Prices are equal: false
🔄 Creating new price because difference is 2000 cents
🔄 Setting new price as default for product...
✅ New price set as default
🗄️ Archiving old price...
✅ Old price archived successfully
✅ Plano Premium atualizado no Stripe!
```

### **❌ Erro Anterior (Corrigido):**
```
❌ Erro na atualização: StripeInvalidRequestError: This price cannot be archived because it is the default price of its product
```

## 🔍 Validação dos Resultados

### **1. No Console do Navegador**
- ✅ Mensagem de sucesso: "Plano Premium atualizado no Stripe!"
- ✅ Sem erros relacionados a preço padrão
- ✅ Logs detalhados mostrando cada etapa

### **2. No Dashboard do Stripe**
1. **Acesse**: https://dashboard.stripe.com/test/products
2. **Localize produto Premium**: `prod_SbO360GkyRhIBK`
3. **Verifique**:
   - ✅ Novo preço de R$ 49,90 existe e está ativo
   - ✅ Novo preço é o padrão do produto
   - ✅ Preço antigo de R$ 29,90 está inativo (arquivado)

### **3. No Banco de Dados**
```sql
SELECT name, price, stripe_price_id, updated_at 
FROM plans 
WHERE name = 'Premium' AND duration_months = 1;
```
- ✅ `price`: 49.9
- ✅ `stripe_price_id`: Novo ID (não mais `price_1RgBHQE40rGVpnravhLnFEcK`)
- ✅ `updated_at`: Timestamp recente

### **4. Logs da Edge Function**
1. **Dashboard**: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz
2. **Logs**: Edge Functions → sync-plan-with-stripe → Logs
3. **Procurar**: Logs com timestamp da sincronização
4. **Verificar**: Sequência completa sem erros

## 🔧 Cenários de Teste

### **Cenário 1: Mudança de Preço (Principal)**
- **Situação**: Premium R$ 49,90 vs Stripe R$ 29,90
- **Esperado**: Novo preço criado, definido como padrão, antigo arquivado
- **Resultado**: ✅ Deve funcionar sem erros

### **Cenário 2: Sem Mudança de Preço**
- **Situação**: Preços iguais no banco e Stripe
- **Esperado**: Nenhum novo preço criado
- **Resultado**: ✅ Deve pular criação de preço

### **Cenário 3: Primeiro Preço do Produto**
- **Situação**: Produto sem preço padrão
- **Esperado**: Novo preço criado e definido como padrão
- **Resultado**: ✅ Deve funcionar normalmente

## 🚨 Troubleshooting

### **Se ainda houver erro de preço padrão:**
1. **Verifique logs**: Confirme se nova lógica está sendo executada
2. **Verifique Stripe**: Confirme se produto tem preço padrão definido
3. **Teste isolado**: Use Stripe Dashboard para testar manualmente

### **Se novo preço não for criado:**
1. **Verifique comparação**: Logs devem mostrar diferença de preços
2. **Verifique dados**: Confirme se preço no banco está correto
3. **Verifique permissões**: Confirme se chave Stripe tem permissões

### **Se arquivamento falhar:**
1. **Não é crítico**: Operação pode continuar sem arquivar preço antigo
2. **Verifique logs**: Warning deve aparecer mas não interromper processo
3. **Verificação manual**: Arquive preço antigo manualmente se necessário

## 📈 Métricas de Sucesso

### **✅ Critérios de Aprovação:**
1. **Sem erro 400**: Não mais "default price" error
2. **Novo preço criado**: Com valor correto (R$ 49,90)
3. **Preço definido como padrão**: No produto Stripe
4. **Preço antigo arquivado**: Ou warning se não conseguir
5. **Banco atualizado**: Com novo stripe_price_id
6. **Logs detalhados**: Mostrando cada etapa

### **✅ Indicadores de Qualidade:**
1. **Operação atômica**: Se falhar, não deixa estado inconsistente
2. **Logs informativos**: Fácil debug em caso de problemas
3. **Tratamento de erro**: Graceful handling de casos edge
4. **Performance**: Operação completa em < 10 segundos

## 🎯 Próximos Passos

### **Se Teste Passar:**
1. ✅ Correção validada e funcionando
2. ✅ Testar com outros planos (Escolar, etc.)
3. ✅ Documentar solução para referência futura
4. ✅ Monitorar logs para casos edge

### **Se Teste Falhar:**
1. 🔧 Analisar logs específicos do erro
2. 🔧 Verificar se Edge Function foi atualizada
3. 🔧 Testar cenários alternativos
4. 🔧 Implementar correções adicionais se necessário

## 📋 Informações de Referência

### **IDs Importantes:**
- **Produto Premium**: `prod_SbO360GkyRhIBK`
- **Preço Atual**: `price_1RgBHQE40rGVpnravhLnFEcK` (R$ 29,90)
- **Preço Esperado**: R$ 49,90 (4990 cents)
- **Plano ID**: `3c80415f-ead8-4361-a119-ac6280191844`

### **URLs de Monitoramento:**
- **Aplicação**: http://localhost:5173
- **Stripe Dashboard**: https://dashboard.stripe.com/test/products
- **Supabase Logs**: https://supabase.com/dashboard/project/wihmaklmjrylsqurtgwz/functions/sync-plan-with-stripe/logs
