-- Phase 1 Testing Script: Database Updates
-- Run these tests after applying the migration

-- Test 1: Verify new columns exist
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'subscriptions' 
  AND column_name IN ('trial_start', 'trial_end', 'trial_status')
ORDER BY column_name;

-- Expected: 3 rows showing the new columns

-- Test 2: Test get_user_plan function with trialing status
-- First, create a test user with trialing subscription
DO $$
DECLARE
  test_user_id UUID := '00000000-0000-0000-0000-000000000001';
  test_plan TEXT;
BEGIN
  -- Insert test subscription with trialing status
  INSERT INTO subscriptions (
    user_id, 
    plano, 
    status, 
    trial_start, 
    trial_end,
    trial_status
  ) VALUES (
    test_user_id,
    'premium',
    'trialing',
    NOW(),
    NOW() + INTERVAL '7 days',
    'active'
  ) ON CONFLICT DO NOTHING;
  
  -- Test the function
  SELECT get_user_plan(test_user_id) INTO test_plan;
  
  -- Verify result
  IF test_plan = 'premium' THEN
    RAISE NOTICE 'TEST PASSED: get_user_plan correctly returns premium for trialing user';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: get_user_plan returned % instead of premium', test_plan;
  END IF;
  
  -- Clean up
  DELETE FROM subscriptions WHERE user_id = test_user_id;
END $$;

-- Test 3: Test is_user_in_trial function
DO $$
DECLARE
  test_user_id UUID := '00000000-0000-0000-0000-000000000002';
  is_trial BOOLEAN;
BEGIN
  -- Insert test subscription with trialing status
  INSERT INTO subscriptions (
    user_id, 
    plano, 
    status, 
    trial_start, 
    trial_end,
    trial_status
  ) VALUES (
    test_user_id,
    'premium',
    'trialing',
    NOW(),
    NOW() + INTERVAL '7 days',
    'active'
  ) ON CONFLICT DO NOTHING;
  
  -- Test the function
  SELECT is_user_in_trial(test_user_id) INTO is_trial;
  
  -- Verify result
  IF is_trial = TRUE THEN
    RAISE NOTICE 'TEST PASSED: is_user_in_trial correctly identifies trialing user';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: is_user_in_trial returned FALSE for trialing user';
  END IF;
  
  -- Clean up
  DELETE FROM subscriptions WHERE user_id = test_user_id;
END $$;

-- Test 4: Test can_create_assessment with trialing user
DO $$
DECLARE
  test_user_id UUID := '00000000-0000-0000-0000-000000000003';
  can_create BOOLEAN;
BEGIN
  -- Insert test subscription with trialing status
  INSERT INTO subscriptions (
    user_id, 
    plano, 
    status, 
    trial_start, 
    trial_end,
    trial_status
  ) VALUES (
    test_user_id,
    'premium',
    'trialing',
    NOW(),
    NOW() + INTERVAL '7 days',
    'active'
  ) ON CONFLICT DO NOTHING;
  
  -- Test the function
  SELECT can_create_assessment(test_user_id) INTO can_create;
  
  -- Verify result
  IF can_create = TRUE THEN
    RAISE NOTICE 'TEST PASSED: can_create_assessment allows trialing premium user';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: can_create_assessment blocked trialing premium user';
  END IF;
  
  -- Clean up
  DELETE FROM subscriptions WHERE user_id = test_user_id;
END $$;

-- Test 5: Test trigger for automatic trial_end calculation
DO $$
DECLARE
  test_user_id UUID := '00000000-0000-0000-0000-000000000004';
  calculated_end TIMESTAMP WITH TIME ZONE;
  expected_end TIMESTAMP WITH TIME ZONE;
BEGIN
  expected_end := NOW() + INTERVAL '7 days';
  
  -- Insert subscription with only trial_start
  INSERT INTO subscriptions (
    user_id, 
    plano, 
    status, 
    trial_start
  ) VALUES (
    test_user_id,
    'premium',
    'trialing',
    NOW()
  ) ON CONFLICT DO NOTHING;
  
  -- Get the calculated trial_end
  SELECT trial_end INTO calculated_end
  FROM subscriptions 
  WHERE user_id = test_user_id;
  
  -- Verify result (within 1 minute tolerance)
  IF ABS(EXTRACT(EPOCH FROM (calculated_end - expected_end))) < 60 THEN
    RAISE NOTICE 'TEST PASSED: Trigger correctly calculated trial_end date';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: Trigger did not calculate correct trial_end date';
  END IF;
  
  -- Clean up
  DELETE FROM subscriptions WHERE user_id = test_user_id;
END $$;

-- Test 6: Verify indexes were created
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename = 'subscriptions' 
  AND indexname IN ('idx_subscriptions_trial_status', 'idx_subscriptions_trial_end');

-- Expected: 2 rows showing the new indexes

RAISE NOTICE 'All Phase 1 database tests completed successfully!';
