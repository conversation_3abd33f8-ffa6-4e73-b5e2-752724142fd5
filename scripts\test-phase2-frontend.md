# Phase 2 Testing Guide: Frontend Context Updates

## Prerequisites
- Phase 1 database migration must be completed
- Application must be running locally or in staging environment

## Test 1: SubscriptionContext Trial Recognition

### Setup Test Data
```sql
-- Create test user with trialing subscription
INSERT INTO subscriptions (
  user_id, 
  plano, 
  status, 
  trial_start, 
  trial_end,
  trial_status,
  stripe_customer_id,
  stripe_subscription_id
) VALUES (
  'YOUR_USER_ID_HERE',  -- Replace with actual user ID
  'premium',
  'trialing',
  NOW(),
  NOW() + INTERVAL '7 days',
  'active',
  'cus_test123',
  'sub_test123'
) ON CONFLICT (user_id) DO UPDATE SET
  plano = EXCLUDED.plano,
  status = EXCLUDED.status,
  trial_start = EXCLUDED.trial_start,
  trial_end = EXCLUDED.trial_end,
  trial_status = EXCLUDED.trial_status;
```

### Frontend Tests

#### Test 1.1: Context Values
1. **Login** with the test user
2. **Open browser DevTools** → Console
3. **Run this code** in console:
```javascript
// Access the subscription context (assuming React DevTools is available)
// Or add this temporarily to a component for testing
console.log('Subscription Context Values:', {
  isTrialing: window.subscriptionContext?.isTrialing,
  isPremium: window.subscriptionContext?.isPremium,
  daysLeftInTrial: window.subscriptionContext?.daysLeftInTrial,
  trialEndDate: window.subscriptionContext?.trialEndDate
});
```

**Expected Results:**
- ✅ `isTrialing: true`
- ✅ `isPremium: true`
- ✅ `daysLeftInTrial: 7` (or close to 7)
- ✅ `trialEndDate: [Date object 7 days from now]`

#### Test 1.2: Feature Access During Trial
1. **Navigate** to Assessment Editor (`/app/assessments/new`)
2. **Verify** premium features are accessible:
   - ✅ AI generation buttons are enabled
   - ✅ Premium templates are available
   - ✅ No usage limit warnings for premium features

3. **Navigate** to Dashboard
4. **Verify** premium statistics are shown:
   - ✅ Analytics sections are visible
   - ✅ No "upgrade to premium" prompts

#### Test 1.3: Trial Status Display
1. **Navigate** to Billing page (`/app/billing`)
2. **Verify** SubscriptionStatus component shows:
   - ✅ Title: "Teste Premium Ativo"
   - ✅ Description: "Teste gratuito - X dias restantes"
   - ✅ Orange color scheme (trial indicator)
   - ✅ Crown icon displayed

## Test 2: Active Subscription (Non-Trial)

### Setup Test Data
```sql
-- Update test user to active subscription
UPDATE subscriptions 
SET status = 'active', trial_status = 'converted'
WHERE user_id = 'YOUR_USER_ID_HERE';
```

### Frontend Tests

#### Test 2.1: Context Values
**Expected Results:**
- ✅ `isTrialing: false`
- ✅ `isPremium: true`
- ✅ `daysLeftInTrial: null`

#### Test 2.2: Status Display
1. **Refresh** the billing page
2. **Verify** SubscriptionStatus component shows:
   - ✅ Title: "Plano Premium Ativo"
   - ✅ Description: "Acesso a recursos avançados e geração por IA"
   - ✅ Blue color scheme (active subscription)

## Test 3: Free User (No Subscription)

### Setup Test Data
```sql
-- Remove subscription for test user
DELETE FROM subscriptions WHERE user_id = 'YOUR_USER_ID_HERE';
```

### Frontend Tests

#### Test 3.1: Context Values
**Expected Results:**
- ✅ `isTrialing: false`
- ✅ `isPremium: false`
- ✅ `daysLeftInTrial: null`

#### Test 3.2: Feature Restrictions
1. **Navigate** to Assessment Editor
2. **Verify** free plan restrictions:
   - ✅ Usage limit warnings appear
   - ✅ Premium features are disabled/hidden
   - ✅ Upgrade prompts are shown

## Test 4: Escolar Trial

### Setup Test Data
```sql
-- Create escolar trial subscription
INSERT INTO subscriptions (
  user_id, 
  plano, 
  status, 
  trial_start, 
  trial_end,
  trial_status
) VALUES (
  'YOUR_USER_ID_HERE',
  'escolar',
  'trialing',
  NOW(),
  NOW() + INTERVAL '7 days',
  'active'
) ON CONFLICT (user_id) DO UPDATE SET
  plano = EXCLUDED.plano,
  status = EXCLUDED.status,
  trial_start = EXCLUDED.trial_start,
  trial_end = EXCLUDED.trial_end,
  trial_status = EXCLUDED.trial_status;
```

### Frontend Tests

#### Test 4.1: Context Values
**Expected Results:**
- ✅ `isTrialing: true`
- ✅ `isEscolar: true`
- ✅ `isPremium: false`

#### Test 4.2: Status Display
**Expected Results:**
- ✅ Title: "Teste Escolar Ativo"
- ✅ Orange color scheme
- ✅ Days remaining shown

## Rollback Procedure

If Phase 2 causes issues:

1. **Revert SubscriptionContext.tsx**:
```bash
git checkout HEAD~1 -- src/contexts/SubscriptionContext.tsx
```

2. **Revert database types**:
```bash
git checkout HEAD~1 -- src/types/database.ts
```

3. **Revert SubscriptionStatus component**:
```bash
git checkout HEAD~1 -- src/components/billing/SubscriptionStatus.tsx
```

4. **Restart application**

## Success Criteria

Phase 2 is successful when:
- ✅ All context values are correctly calculated
- ✅ Trial users have access to paid features
- ✅ Trial status is properly displayed in UI
- ✅ No TypeScript errors in console
- ✅ No runtime errors in browser console
- ✅ Existing functionality for active/free users unchanged

## Common Issues & Solutions

### Issue: TypeScript errors about missing properties
**Solution**: Ensure database types are updated with trial fields

### Issue: Context values are undefined
**Solution**: Check that database migration was applied and subscription data exists

### Issue: Trial users still treated as free users
**Solution**: Verify the subscription query includes 'trialing' status

### Issue: Days calculation is incorrect
**Solution**: Check timezone handling in trial end date calculation
