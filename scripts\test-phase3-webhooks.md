# Phase 3 Testing Guide: Webhook Enhancement

## Prerequisites
- Phase 1 and Phase 2 must be completed
- Stripe webhook endpoint must be configured
- Access to Stripe Dashboard for testing

## Test Setup

### 1. Configure Webhook Events
In Stripe Dashboard → Webhooks, ensure these events are enabled:
- ✅ `customer.subscription.created`
- ✅ `customer.subscription.updated`
- ✅ `customer.subscription.deleted`
- ✅ `customer.subscription.trial_will_end`
- ✅ `invoice.payment_succeeded`
- ✅ `invoice.payment_failed`
- ✅ `invoice.upcoming`

### 2. Test Environment Setup
```bash
# Install Stripe CLI for local testing
stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook
```

## Test 1: Trial Subscription Creation

### Trigger Event
```bash
# Create a subscription with trial using Stripe CLI
stripe subscriptions create \
  --customer cus_test_customer \
  --items '[{"price": "price_test_premium"}]' \
  --trial_period_days 7
```

### Expected Webhook Processing
1. **Event**: `customer.subscription.created`
2. **Database Updates**:
   ```sql
   -- Verify subscription was created with trial data
   SELECT 
     status, 
     trial_start, 
     trial_end, 
     trial_status,
     plano
   FROM subscriptions 
   WHERE stripe_subscription_id = 'sub_test_xxx';
   ```

**Expected Results**:
- ✅ `status: 'trialing'`
- ✅ `trial_start: [timestamp]`
- ✅ `trial_end: [timestamp 7 days later]`
- ✅ `trial_status: 'active'`
- ✅ `plano: 'premium'` (or 'escolar')

## Test 2: Trial Will End Event

### Trigger Event
```bash
# Simulate trial ending soon (3 days before end)
stripe events create customer.subscription.trial_will_end \
  --data-object subscription_id=sub_test_xxx
```

### Expected Webhook Processing
1. **Event**: `customer.subscription.trial_will_end`
2. **Database Updates**:
   ```sql
   -- Verify trial status was updated
   SELECT trial_status, metadata 
   FROM subscriptions 
   WHERE stripe_subscription_id = 'sub_test_xxx';
   ```

**Expected Results**:
- ✅ `trial_status: 'ending'`
- ✅ `metadata` contains `trial_ending_notification_sent`

## Test 3: Trial to Paid Conversion

### Trigger Event
```bash
# Update subscription to active (trial ended)
stripe subscriptions update sub_test_xxx \
  --trial_end now
```

### Expected Webhook Processing
1. **Event**: `customer.subscription.updated`
2. **Database Updates**:
   ```sql
   -- Verify conversion was recorded
   SELECT status, trial_status 
   FROM subscriptions 
   WHERE stripe_subscription_id = 'sub_test_xxx';
   ```

**Expected Results**:
- ✅ `status: 'active'`
- ✅ `trial_status: 'converted'`

## Test 4: Payment Failure During Trial

### Trigger Event
```bash
# Simulate payment failure
stripe events create invoice.payment_failed \
  --data-object subscription=sub_test_xxx
```

### Expected Webhook Processing
1. **Event**: `invoice.payment_failed`
2. **Database Updates**:
   ```sql
   -- Verify payment failure was recorded
   SELECT status, metadata 
   FROM subscriptions 
   WHERE stripe_subscription_id = 'sub_test_xxx';
   ```

**Expected Results**:
- ✅ `status: 'past_due'`
- ✅ `metadata` contains `last_payment_failure`

## Test 5: Upcoming Invoice (Trial Ending)

### Trigger Event
```bash
# Simulate upcoming invoice for trial conversion
stripe events create invoice.upcoming \
  --data-object subscription=sub_test_xxx
```

### Expected Webhook Processing
1. **Event**: `invoice.upcoming`
2. **Database Updates**:
   ```sql
   -- Verify upcoming conversion was recorded
   SELECT trial_status, metadata 
   FROM subscriptions 
   WHERE stripe_subscription_id = 'sub_test_xxx';
   ```

**Expected Results**:
- ✅ `trial_status: 'ending'`
- ✅ `metadata` contains `trial_conversion_upcoming`

## Test 6: Webhook Error Handling

### Test Invalid Data
```bash
# Send malformed webhook
curl -X POST http://localhost:54321/functions/v1/stripe-webhook \
  -H "Content-Type: application/json" \
  -d '{"invalid": "data"}'
```

**Expected Results**:
- ✅ Webhook returns 400 error
- ✅ No database corruption
- ✅ Error logged properly

## Integration Tests

### Test 1: End-to-End Trial Flow
1. **Create trial subscription** via frontend
2. **Verify webhook processing** creates correct database records
3. **Check frontend** shows trial status correctly
4. **Simulate trial ending** webhook
5. **Verify frontend** updates trial status

### Test 2: Multiple Webhook Events
1. **Send multiple events** in quick succession
2. **Verify** database consistency
3. **Check** no race conditions occur

## Monitoring & Debugging

### Webhook Logs
```bash
# View webhook function logs
supabase functions logs stripe-webhook --follow
```

### Database Monitoring
```sql
-- Monitor subscription changes
SELECT 
  stripe_subscription_id,
  status,
  trial_status,
  updated_at
FROM subscriptions 
ORDER BY updated_at DESC 
LIMIT 10;
```

### Stripe Dashboard
- Check webhook delivery status
- Review event logs
- Verify retry attempts

## Rollback Procedure

If Phase 3 causes issues:

1. **Revert webhook function**:
```bash
git checkout HEAD~1 -- supabase/functions/stripe-webhook/index.ts
```

2. **Redeploy function**:
```bash
supabase functions deploy stripe-webhook
```

3. **Disable new webhook events** in Stripe Dashboard temporarily

## Success Criteria

Phase 3 is successful when:
- ✅ All webhook events are processed correctly
- ✅ Trial data is properly stored and updated
- ✅ No webhook processing errors
- ✅ Database remains consistent
- ✅ Frontend reflects webhook updates immediately
- ✅ Error handling works for malformed requests

## Common Issues & Solutions

### Issue: Webhook signature verification fails
**Solution**: Check webhook secret in environment variables

### Issue: Database updates fail
**Solution**: Verify database schema matches webhook data structure

### Issue: Events processed multiple times
**Solution**: Implement idempotency keys for webhook processing

### Issue: Trial dates are incorrect
**Solution**: Check timezone handling in timestamp conversion

### Issue: Webhook timeouts
**Solution**: Optimize database queries and add proper indexing
