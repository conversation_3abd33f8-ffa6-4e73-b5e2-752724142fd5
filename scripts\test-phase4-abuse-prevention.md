# Phase 4 Testing Guide: Trial Abuse Prevention

## Prerequisites
- Phases 1, 2, and 3 must be completed
- Database migration for trial_history table applied
- Frontend components integrated

## Test Setup

### 1. Apply Migration
```bash
supabase db push
```

### 2. Verify Database Schema
```sql
-- Check if trial_history table exists
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'trial_history'
ORDER BY ordinal_position;

-- Check if functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_name IN ('can_user_start_trial', 'record_trial_start', 'update_trial_status');
```

## Test 1: First Trial Attempt (Should Succeed)

### Setup Test User
```sql
-- Create test user profile
INSERT INTO profiles (id, nome, email, plano) 
VALUES ('test-user-1', 'Test User 1', '<EMAIL>', 'gratuito')
ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email;
```

### Test Validation
```sql
-- Test trial eligibility for first-time user
SELECT can_user_start_trial(
  'test-user-1'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_1',
  'device_fingerprint_1'
);
```

**Expected Result:**
```json
{
  "allowed": true,
  "reason": "eligible",
  "message": "Usuário elegível para teste gratuito."
}
```

### Test Recording Trial
```sql
-- Record trial start
SELECT record_trial_start(
  'test-user-1'::uuid,
  '<EMAIL>',
  'premium',
  NOW(),
  NOW() + INTERVAL '7 days',
  'cus_test_customer_1',
  '*************'::inet,
  'Mozilla/5.0 Test Browser',
  'pm_test_fingerprint_1',
  'device_fingerprint_1'
);
```

**Verify Record:**
```sql
SELECT * FROM trial_history WHERE user_id = 'test-user-1';
```

## Test 2: Duplicate Email Attempt (Should Fail)

### Test Same Email Different User
```sql
-- Create second user with same email
INSERT INTO profiles (id, nome, email, plano) 
VALUES ('test-user-2', 'Test User 2', '<EMAIL>', 'gratuito')
ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email;

-- Test trial eligibility (should fail)
SELECT can_user_start_trial(
  'test-user-2'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_2',
  'device_fingerprint_2'
);
```

**Expected Result:**
```json
{
  "allowed": false,
  "reason": "email_limit_exceeded",
  "message": "Este email já foi usado para um teste gratuito. Cada email pode ter apenas um teste.",
  "cooldown_ends": "2025-04-26T..."
}
```

## Test 3: Same User Multiple Attempts (Should Fail)

### Test Same User ID
```sql
-- Test same user trying again
SELECT can_user_start_trial(
  'test-user-1'::uuid,
  '<EMAIL>',
  'escolar',  -- Different plan
  '*************'::inet,
  'pm_test_fingerprint_3',
  'device_fingerprint_3'
);
```

**Expected Result:**
```json
{
  "allowed": false,
  "reason": "user_limit_exceeded",
  "message": "Você já utilizou seu teste gratuito.",
  "cooldown_ends": "2025-04-26T..."
}
```

## Test 4: IP Address Limits

### Setup Multiple Users Same IP
```sql
-- Create multiple users
INSERT INTO profiles (id, nome, email, plano) VALUES 
('test-user-3', 'Test User 3', '<EMAIL>', 'gratuito'),
('test-user-4', 'Test User 4', '<EMAIL>', 'gratuito'),
('test-user-5', 'Test User 5', '<EMAIL>', 'gratuito'),
('test-user-6', 'Test User 6', '<EMAIL>', 'gratuito')
ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email;

-- Record trials for first 3 users (IP limit is 3)
SELECT record_trial_start(
  ('test-user-' || i)::uuid,
  ('test' || i || '@example.com'),
  'premium',
  NOW(),
  NOW() + INTERVAL '7 days',
  ('cus_test_customer_' || i),
  '*************'::inet,
  'Mozilla/5.0 Test Browser',
  ('pm_test_fingerprint_' || i),
  ('device_fingerprint_' || i)
) FROM generate_series(3, 5) i;
```

### Test IP Limit Exceeded
```sql
-- Test 4th user from same IP (should fail)
SELECT can_user_start_trial(
  'test-user-6'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_6',
  'device_fingerprint_6'
);
```

**Expected Result:**
```json
{
  "allowed": false,
  "reason": "ip_limit_exceeded",
  "message": "Muitos testes foram iniciados deste endereço IP. Tente novamente mais tarde.",
  "cooldown_ends": "2025-02-02T..."
}
```

## Test 5: Payment Method Fingerprint

### Test Same Payment Method
```sql
-- Test same payment method fingerprint
SELECT can_user_start_trial(
  'test-user-6'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_1',  -- Same as test-user-1
  'device_fingerprint_6'
);
```

**Expected Result:**
```json
{
  "allowed": false,
  "reason": "payment_method_limit_exceeded",
  "message": "Este método de pagamento já foi usado para um teste gratuito.",
  "cooldown_ends": "2025-04-26T..."
}
```

## Test 6: Device Fingerprint Limits

### Test Device Limit (2 trials per device)
```sql
-- Create users for device testing
INSERT INTO profiles (id, nome, email, plano) VALUES 
('test-user-7', 'Test User 7', '<EMAIL>', 'gratuito'),
('test-user-8', 'Test User 8', '<EMAIL>', 'gratuito'),
('test-user-9', 'Test User 9', '<EMAIL>', 'gratuito')
ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email;

-- Record 2 trials for same device
SELECT record_trial_start(
  ('test-user-' || i)::uuid,
  ('test' || i || '@example.com'),
  'premium',
  NOW(),
  NOW() + INTERVAL '7 days',
  ('cus_test_customer_' || i),
  ('192.168.1.' || (200 + i))::inet,
  'Mozilla/5.0 Test Browser',
  ('pm_test_fingerprint_' || i),
  'shared_device_fingerprint'  -- Same device
) FROM generate_series(7, 8) i;

-- Test 3rd user on same device (should fail)
SELECT can_user_start_trial(
  'test-user-9'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_9',
  'shared_device_fingerprint'
);
```

**Expected Result:**
```json
{
  "allowed": false,
  "reason": "device_limit_exceeded",
  "message": "Este dispositivo já foi usado para testes gratuitos. Limite: 2 testes por dispositivo.",
  "cooldown_ends": "2025-04-26T..."
}
```

## Test 7: Trial Status Updates

### Test Automatic Status Updates
```sql
-- Create subscription for test user
INSERT INTO subscriptions (
  user_id, 
  stripe_customer_id, 
  stripe_subscription_id,
  plano, 
  status
) VALUES (
  'test-user-1',
  'cus_test_customer_1',
  'sub_test_123',
  'premium',
  'trialing'
);

-- Update to active (should trigger trial_history update)
UPDATE subscriptions 
SET status = 'active' 
WHERE user_id = 'test-user-1';

-- Check trial_history was updated
SELECT trial_status FROM trial_history 
WHERE stripe_customer_id = 'cus_test_customer_1';
```

**Expected Result:** `trial_status = 'converted'`

## Test 8: Frontend Integration

### Test useTrialValidation Hook
1. **Open browser DevTools** → Console
2. **Navigate** to pricing page
3. **Run validation test**:
```javascript
// Test the hook (add this temporarily to a component)
const { validateTrialEligibility } = useTrialValidation();
validateTrialEligibility('premium').then(result => {
  console.log('Trial validation result:', result);
});
```

### Test TrialValidationModal
1. **Click** on a premium plan
2. **Verify** modal appears with validation
3. **Check** appropriate message is shown
4. **Test** different scenarios by modifying database

## Test 9: Admin Monitoring

### Test Abuse Monitoring View
```sql
-- Check admin monitoring view
SELECT * FROM trial_abuse_monitoring;
```

**Expected:** Shows users with multiple trial attempts

### Test Cleanup After Testing
```sql
-- Clean up test data
DELETE FROM trial_history WHERE email LIKE '<EMAIL>';
DELETE FROM subscriptions WHERE user_id LIKE 'test-user-%';
DELETE FROM profiles WHERE email LIKE '<EMAIL>';
```

## Performance Testing

### Test Function Performance
```sql
-- Test with large dataset
EXPLAIN ANALYZE 
SELECT can_user_start_trial(
  'test-user-1'::uuid,
  '<EMAIL>',
  'premium',
  '*************'::inet,
  'pm_test_fingerprint_1',
  'device_fingerprint_1'
);
```

**Expected:** Query should complete in < 50ms

## Success Criteria

Phase 4 is successful when:
- ✅ All validation functions work correctly
- ✅ Abuse prevention rules are enforced
- ✅ Frontend integration works smoothly
- ✅ Database performance is acceptable
- ✅ Admin monitoring provides useful insights
- ✅ No false positives for legitimate users
- ✅ Proper error messages for blocked attempts

## Common Issues & Solutions

### Issue: Functions not found
**Solution:** Ensure migration was applied correctly

### Issue: RLS policies blocking access
**Solution:** Check policy definitions and user permissions

### Issue: Performance issues with large datasets
**Solution:** Verify indexes are created and optimize queries

### Issue: False positives for legitimate users
**Solution:** Adjust limits in the validation function
