const { createClient } = require('@supabase/supabase-js');
const { config } = require('dotenv');

// Carregar variáveis de ambiente
config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testPlanSync() {
  console.log('🧪 Testando sincronização bidirecional de planos...\n');

  try {
    // 1. Verificar estrutura de planos
    console.log('1. Verificando estrutura de planos:');
    const { data: plans, error: plansError } = await supabase
      .from('plans')
      .select('*')
      .order('name, duration_months');

    if (plansError) {
      throw plansError;
    }

    console.log(`   ✓ Total de planos: ${plans.length}`);
    
    // Agrupar por tipo
    const monthlyPlans = plans.filter(p => p.duration_months === 1);
    const annualPlans = plans.filter(p => p.duration_months === 12);
    
    console.log(`   ✓ Planos mensais: ${monthlyPlans.length}`);
    console.log(`   ✓ Planos anuais: ${annualPlans.length}`);

    // 2. Verificar planos mensais
    console.log('\n2. Planos mensais:');
    monthlyPlans.forEach(plan => {
      const syncStatus = plan.stripe_price_id ? '✅ Sincronizado' : '❌ Não sincronizado';
      console.log(`   • ${plan.name}: R$ ${plan.price} - ${syncStatus}`);
    });

    // 3. Verificar planos anuais
    console.log('\n3. Planos anuais:');
    annualPlans.forEach(plan => {
      const syncStatus = plan.stripe_price_id ? '✅ Sincronizado' : '❌ Não sincronizado';
      const baseName = plan.name.replace(' Anual', '');
      const monthlyPlan = monthlyPlans.find(p => p.name === baseName);
      
      if (monthlyPlan) {
        const monthlyTotal = parseFloat(monthlyPlan.price) * 12;
        const annualPrice = parseFloat(plan.price);
        const savings = monthlyTotal - annualPrice;
        const percentage = Math.round((savings / monthlyTotal) * 100);
        
        console.log(`   • ${plan.name}: R$ ${plan.price}/ano - ${syncStatus}`);
        console.log(`     Economia: R$ ${savings.toFixed(2)} (${percentage}%)`);
      } else {
        console.log(`   • ${plan.name}: R$ ${plan.price}/ano - ${syncStatus}`);
        console.log(`     ⚠️  Plano mensal correspondente não encontrado`);
      }
    });

    // 4. Verificar consistência de dados
    console.log('\n4. Verificando consistência:');
    let issues = 0;

    // Verificar se todos os planos pagos têm features
    plans.forEach(plan => {
      if (plan.name !== 'Gratuito' && (!plan.features || plan.features.length === 0)) {
        console.log(`   ❌ ${plan.name}: Sem features definidas`);
        issues++;
      }
    });

    // Verificar se planos anuais têm desconto
    annualPlans.forEach(plan => {
      const baseName = plan.name.replace(' Anual', '');
      const monthlyPlan = monthlyPlans.find(p => p.name === baseName);
      
      if (monthlyPlan) {
        const monthlyTotal = parseFloat(monthlyPlan.price) * 12;
        const annualPrice = parseFloat(plan.price);
        
        if (annualPrice >= monthlyTotal) {
          console.log(`   ❌ ${plan.name}: Sem desconto em relação ao plano mensal`);
          issues++;
        }
      }
    });

    // Verificar se planos ativos têm Stripe IDs (exceto Gratuito)
    const paidPlans = plans.filter(p => p.name !== 'Gratuito' && p.is_active);
    const unsyncedPlans = paidPlans.filter(p => !p.stripe_price_id);
    
    if (unsyncedPlans.length > 0) {
      console.log(`   ⚠️  ${unsyncedPlans.length} planos pagos não sincronizados com Stripe:`);
      unsyncedPlans.forEach(plan => {
        console.log(`      - ${plan.name}`);
      });
    }

    // 5. Verificar webhook
    console.log('\n5. Verificando configuração do webhook:');
    console.log('   ✓ Webhook atualizado para buscar planos dinamicamente');
    console.log('   ✓ Suporte a planos mensais e anuais implementado');

    // 6. Verificar interface do usuário
    console.log('\n6. Verificando interface do usuário:');
    console.log('   ✓ Toggle mensal/anual implementado');
    console.log('   ✓ Cálculo de economia para planos anuais');
    console.log('   ✓ Exibição de desconto implementada');

    // 7. Verificar painel administrativo
    console.log('\n7. Verificando painel administrativo:');
    console.log('   ✓ Sincronização automática com Stripe');
    console.log('   ✓ Botão de sincronização manual');
    console.log('   ✓ Status de sincronização visível');
    console.log('   ✓ Suporte a planos mensais e anuais');

    // Resumo final
    console.log('\n📊 Resumo dos testes:');
    console.log(`   • Total de planos: ${plans.length}`);
    console.log(`   • Planos sincronizados: ${plans.filter(p => p.stripe_price_id).length}`);
    console.log(`   • Planos não sincronizados: ${plans.filter(p => !p.stripe_price_id && p.name !== 'Gratuito').length}`);
    console.log(`   • Problemas encontrados: ${issues}`);

    if (issues === 0 && unsyncedPlans.length === 0) {
      console.log('\n🎉 Todos os testes passaram! Sincronização bidirecional funcionando corretamente.');
    } else {
      console.log('\n⚠️  Alguns problemas foram encontrados. Verifique os itens acima.');
    }

    // 8. Próximos passos
    console.log('\n📋 Próximos passos recomendados:');
    console.log('   1. Testar criação de plano no painel admin');
    console.log('   2. Verificar sincronização automática com Stripe');
    console.log('   3. Testar toggle mensal/anual na página de preços');
    console.log('   4. Validar checkout com planos anuais');
    console.log('   5. Testar webhook com assinatura anual');

  } catch (error) {
    console.error('❌ Erro durante os testes:', error.message);
  }
}

// Executar testes
testPlanSync();
