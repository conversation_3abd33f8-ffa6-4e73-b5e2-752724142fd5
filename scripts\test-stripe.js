const { config } = require('dotenv');
config();

console.log('🔍 Testando configuração do Stripe...\n');

// Verificar variáveis de ambiente
console.log('Variáveis de ambiente:');
console.log('VITE_STRIPE_PUBLISHABLE_KEY:', process.env.VITE_STRIPE_PUBLISHABLE_KEY ? 'Configurada' : 'Não configurada');
console.log('STRIPE_SECRET_KEY:', process.env.STRIPE_SECRET_KEY ? 'Configurada' : 'Não configurada');
console.log('STRIPE_WEBHOOK_SECRET:', process.env.STRIPE_WEBHOOK_SECRET ? 'Configurada' : 'Não configurada');

// Verificar se é modo de teste
const isTestMode = process.env.VITE_STRIPE_PUBLISHABLE_KEY?.startsWith('pk_test_');
console.log('Modo:', isTestMode ? 'Teste' : 'Produção');

// Price IDs no código
console.log('\nPrice IDs no código:');
console.log('Premium:', 'price_1RgBHQE40rGVpnravhLnFEcK');
console.log('Escolar:', 'price_1RgCLfE40rGVpnrafFfvawtj');

console.log('\n✅ Configuração básica verificada');
