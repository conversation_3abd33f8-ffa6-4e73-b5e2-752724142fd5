#!/usr/bin/env node

/**
 * Validation script to check if the multi-tab loading fix is properly implemented
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const AUTHCONTEXT_PATH = path.join(__dirname, '../src/contexts/AuthContext.tsx');

function validateFix() {
  console.log('🔍 Validating Multi-Tab Loading Fix...\n');
  
  if (!fs.existsSync(AUTHCONTEXT_PATH)) {
    console.error('❌ AuthContext.tsx not found at expected path');
    process.exit(1);
  }
  
  const content = fs.readFileSync(AUTHCONTEXT_PATH, 'utf8');
  
  const checks = [
    {
      name: 'Initializing state variable',
      pattern: /const \[initializing, setInitializing\] = useState\(false\)/,
      description: 'Prevents multiple initialization attempts'
    },
    {
      name: 'Initialization guard',
      pattern: /if \(initializing \|\| initialized\) \{[\s\S]*?return[\s\S]*?\}/,
      description: 'Guards against race conditions'
    },
    {
      name: 'Loading state fix in initial setup',
      pattern: /setLoading\(false\)[\s\S]*?setInitialized\(true\)[\s\S]*?setInitializing\(false\)/,
      description: 'Ensures loading state is properly set after initial session setup'
    },
    {
      name: 'Protected auth state listener',
      pattern: /if \(!initialized\) \{[\s\S]*?setLoading\(false\)/,
      description: 'Prevents unnecessary loading state updates'
    },
    {
      name: 'UseEffect dependencies',
      pattern: /\}, \[initialized, initializing\]\)/,
      description: 'Proper dependency tracking for initialization'
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach((check, index) => {
    const passed = check.pattern.test(content);
    const status = passed ? '✅' : '❌';
    
    console.log(`${status} Check ${index + 1}: ${check.name}`);
    console.log(`   ${check.description}`);
    
    if (passed) {
      passedChecks++;
    } else {
      console.log(`   ⚠️  Pattern not found: ${check.pattern}`);
    }
    
    console.log('');
  });
  
  console.log(`\n📊 Results: ${passedChecks}/${checks.length} checks passed\n`);
  
  if (passedChecks === checks.length) {
    console.log('🎉 All checks passed! The multi-tab loading fix is properly implemented.');
    console.log('\n📋 Next steps:');
    console.log('1. Test manually by opening multiple tabs');
    console.log('2. Check browser console for proper initialization logs');
    console.log('3. Verify no infinite loading states occur');
    console.log('4. Test with different user authentication states');
    
    return true;
  } else {
    console.log('❌ Some checks failed. Please review the implementation.');
    console.log('\n🔧 Missing components:');
    
    checks.forEach((check, index) => {
      if (!check.pattern.test(content)) {
        console.log(`- ${check.name}: ${check.description}`);
      }
    });
    
    return false;
  }
}

// Additional checks for related files
function validateRelatedFiles() {
  console.log('\n🔍 Checking related files...\n');
  
  const files = [
    {
      path: path.join(__dirname, '../src/lib/supabase.ts'),
      checks: [
        {
          name: 'Supabase session persistence',
          pattern: /persistSession:\s*true/,
          description: 'Ensures sessions persist across tabs'
        }
      ]
    },
    {
      path: path.join(__dirname, '../docs/multi-tab-test-instructions.md'),
      checks: [
        {
          name: 'Test instructions exist',
          pattern: /Multi-Tab Loading Issue/,
          description: 'Manual testing instructions are available'
        }
      ]
    }
  ];
  
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      const content = fs.readFileSync(file.path, 'utf8');
      const fileName = path.basename(file.path);
      
      file.checks.forEach(check => {
        const passed = check.pattern.test(content);
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${fileName}: ${check.name}`);
      });
    } else {
      console.log(`⚠️  File not found: ${path.basename(file.path)}`);
    }
  });
}

// Run validation
const mainChecksPassed = validateFix();
validateRelatedFiles();

console.log('\n' + '='.repeat(60));
console.log('Multi-Tab Loading Fix Validation Complete');
console.log('='.repeat(60));

if (mainChecksPassed) {
  console.log('\n✅ VALIDATION PASSED - Fix is properly implemented');
  process.exit(0);
} else {
  console.log('\n❌ VALIDATION FAILED - Please review the implementation');
  process.exit(1);
}
