const Stripe = require('stripe');
const { config } = require('dotenv');

// Carregar variáveis de ambiente
config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

async function validateStripeConfiguration() {
  console.log('🔍 Validando configuração do Stripe...\n');

  try {
    // 1. Verificar se as chaves estão configuradas
    console.log('1. Verificando chaves de API:');
    console.log(`   ✓ Publishable Key: ${process.env.VITE_STRIPE_PUBLISHABLE_KEY ? 'Configurada' : '❌ Não configurada'}`);
    console.log(`   ✓ Secret Key: ${process.env.STRIPE_SECRET_KEY ? 'Configurada' : '❌ Não configurada'}`);
    console.log(`   ✓ Webhook Secret: ${process.env.STRIPE_WEBHOOK_SECRET ? 'Configurada' : '❌ Não configurada'}`);
    console.log(`   ✓ Modo: ${process.env.VITE_STRIPE_PUBLISHABLE_KEY?.startsWith('pk_test_') ? 'Teste' : 'Produção'}\n`);

    // 2. Listar todos os produtos
    console.log('2. Produtos no Stripe:');
    const products = await stripe.products.list({ limit: 10 });
    
    if (products.data.length === 0) {
      console.log('   ❌ Nenhum produto encontrado no Stripe\n');
    } else {
      products.data.forEach(product => {
        console.log(`   ✓ ${product.name} (ID: ${product.id})`);
        console.log(`     - Ativo: ${product.active ? 'Sim' : 'Não'}`);
        console.log(`     - Descrição: ${product.description || 'N/A'}`);
      });
      console.log('');
    }

    // 3. Listar todos os preços
    console.log('3. Preços no Stripe:');
    const prices = await stripe.prices.list({ limit: 20 });
    
    if (prices.data.length === 0) {
      console.log('   ❌ Nenhum preço encontrado no Stripe\n');
    } else {
      for (const price of prices.data) {
        const product = await stripe.products.retrieve(price.product);
        console.log(`   ✓ ${product.name}: ${price.unit_amount / 100} ${price.currency.toUpperCase()}/${price.recurring?.interval || 'único'}`);
        console.log(`     - Price ID: ${price.id}`);
        console.log(`     - Ativo: ${price.active ? 'Sim' : 'Não'}`);
        console.log(`     - Tipo: ${price.type}`);
        if (price.recurring) {
          console.log(`     - Recorrência: ${price.recurring.interval_count} ${price.recurring.interval}(s)`);
        }
        console.log('');
      }
    }

    // 4. Verificar Price IDs específicos do código
    console.log('4. Verificando Price IDs do código:');
    const priceIdsInCode = [
      'price_1RgBHQE40rGVpnravhLnFEcK', // Premium
      'price_1RgCLfE40rGVpnrafFfvawtj'  // Escolar
    ];

    for (const priceId of priceIdsInCode) {
      try {
        const price = await stripe.prices.retrieve(priceId);
        const product = await stripe.products.retrieve(price.product);
        console.log(`   ✓ ${priceId}: ${product.name} - ${price.unit_amount / 100} ${price.currency.toUpperCase()}`);
        console.log(`     - Ativo: ${price.active ? 'Sim' : 'Não'}`);
        console.log(`     - Recorrência: ${price.recurring?.interval || 'único'}`);
      } catch (error) {
        console.log(`   ❌ ${priceId}: Não encontrado - ${error.message}`);
      }
    }

    console.log('\n5. Resumo da Análise:');
    console.log('   - Verifique se os Price IDs no código correspondem aos preços ativos no Stripe');
    console.log('   - Certifique-se de que os produtos estão ativos');
    console.log('   - Confirme se os preços estão corretos');
    console.log('   - Verifique se o webhook está configurado corretamente');

  } catch (error) {
    console.error('❌ Erro ao validar configuração do Stripe:', error.message);
  }
}

// Executar validação
validateStripeConfiguration();
