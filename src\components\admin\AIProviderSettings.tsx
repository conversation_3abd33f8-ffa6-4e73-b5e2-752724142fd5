import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Settings, BarChart3, DollarSign, Zap, CheckCircle, XCircle, RefreshCw, Bot } from 'lucide-react'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import toast from 'react-hot-toast'

interface ProviderSetting {
  id: string
  provider_name: string
  is_enabled: boolean
  priority: number
  api_key_configured: boolean
  max_requests_per_minute: number
  cost_per_token: number
  model_name: string
  settings: any
  is_default: boolean
  admin_override_priority: number | null
  last_used: string | null
  performance_score: number
}

interface ProviderStats {
  provider: string
  total_requests: number
  successful_requests: number
  failed_requests: number
  total_questions: number
  total_tokens: number
  total_cost: number
  avg_duration_ms: number
  success_rate: number
}

const AIProviderSettings: React.FC = () => {
  const { user, isAdmin } = useAuth()
  const [providers, setProviders] = useState<ProviderSetting[]>([])
  const [stats, setStats] = useState<ProviderStats[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  useEffect(() => {
    if (isAdmin) {
      loadProviders()
      loadStats()
    }
  }, [isAdmin])

  const loadProviders = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_provider_settings')
        .select('*')
        .order('priority')

      if (error) throw error
      setProviders(data || [])
    } catch (error) {
      console.error('Error loading providers:', error)
      toast.error('Erro ao carregar configurações dos provedores')
    }
  }

  const loadStats = async () => {
    try {
      const { data, error } = await supabase.rpc('get_provider_statistics', { days_back: 30 })
      if (error) throw error
      setStats(data || [])
    } catch (error) {
      console.error('Error loading stats:', error)
      toast.error('Erro ao carregar estatísticas')
    } finally {
      setLoading(false)
    }
  }

  const updateProvider = async (providerId: string, updates: Partial<ProviderSetting>) => {
    setUpdating(providerId)
    try {
      const { error } = await supabase
        .from('ai_provider_settings')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', providerId)

      if (error) throw error

      await loadProviders()
      toast.success('Configuração atualizada com sucesso')
    } catch (error) {
      console.error('Error updating provider:', error)
      toast.error('Erro ao atualizar configuração')
    } finally {
      setUpdating(null)
    }
  }

  const setDefaultProvider = async (providerId: string) => {
    setUpdating(providerId)
    try {
      // First, remove default from all providers
      await supabase
        .from('ai_provider_settings')
        .update({ is_default: false, updated_at: new Date().toISOString() })
        .neq('id', '00000000-0000-0000-0000-000000000000') // Update all

      // Then set the selected provider as default
      const { error } = await supabase
        .from('ai_provider_settings')
        .update({ is_default: true, updated_at: new Date().toISOString() })
        .eq('id', providerId)

      if (error) throw error

      await loadProviders()
      toast.success('Provedor padrão definido com sucesso')
    } catch (error) {
      console.error('Error setting default provider:', error)
      toast.error('Erro ao definir provedor padrão')
    } finally {
      setUpdating(null)
    }
  }

  const getProviderIcon = (providerName: string) => {
    switch (providerName.toLowerCase()) {
      case 'openai': return '🤖'
      case 'openai-gpt4o-mini': return '🤖'
      case 'anthropic': return '🧠'
      case 'claude-3-5-haiku': return '🧠'
      case 'claude-sonnet-4': return '🧠✨'
      case 'google': return '🔍'
      case 'gemini-2-0-flash': return '🔍'
      case 'gemini-2-5-pro': return '🔍🚀'
      case 'gemini-2-5-flash': return '🔍⚡'
      case 'cohere-command-r-plus': return '🎯'
      default: return '⚡'
    }
  }

  const getProviderDisplayName = (providerName: string) => {
    switch (providerName.toLowerCase()) {
      case 'openai': return 'OpenAI GPT-4o'
      case 'openai-gpt4o-mini': return 'OpenAI GPT-4o Mini'
      case 'anthropic': return 'Claude 3.5 Sonnet'
      case 'claude-3-5-haiku': return 'Claude 3.5 Haiku'
      case 'claude-sonnet-4': return 'Claude Sonnet 4 (2025)'
      case 'google': return 'Gemini 1.5 Pro'
      case 'gemini-2-0-flash': return 'Gemini 2.0 Flash'
      case 'gemini-2-5-pro': return 'Gemini 2.5 Pro (2025)'
      case 'gemini-2-5-flash': return 'Gemini 2.5 Flash (2025)'
      case 'cohere-command-r-plus': return 'Command R+'
      default: return providerName.charAt(0).toUpperCase() + providerName.slice(1)
    }
  }

  const formatCost = (cost: number) => {
    return `$${cost.toFixed(6)}`
  }

  const formatSuccessRate = (rate: number) => {
    return `${rate.toFixed(1)}%`
  }

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Acesso restrito a administradores</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-500 dark:text-blue-400" />
        <span className="ml-2 text-gray-600 dark:text-gray-300">Carregando configurações...</span>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Configurações de IA</h1>
          <p className="text-gray-600 dark:text-gray-300">Gerencie provedores de IA e monitore o desempenho</p>
        </div>
        <button
          onClick={() => { loadProviders(); loadStats() }}
          className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          aria-label="Atualizar configurações dos provedores"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Atualizar
        </button>
      </div>

      {/* Default Provider Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Bot className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-600 dark:text-blue-400" />
            Provedor Padrão para Geração
          </h2>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-1">
            Selecione o provedor de IA preferido para geração de questões
          </p>
        </div>

        <div className="p-4 sm:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
            {providers.filter(p => p.is_enabled).map((provider) => (
              <motion.div
                key={provider.id}
                className={`relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
                  provider.is_default
                    ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-700/50'
                }`}
                onClick={() => setDefaultProvider(provider.id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    setDefaultProvider(provider.id)
                  }
                }}
                tabIndex={0}
                role="button"
                aria-label={`Definir ${getProviderDisplayName(provider.provider_name)} como provedor padrão`}
                aria-pressed={provider.is_default}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {provider.is_default && (
                  <div className="absolute -top-2 -right-2 bg-blue-500 dark:bg-blue-400 text-white rounded-full p-1 shadow-lg">
                    <CheckCircle className="w-4 h-4" />
                  </div>
                )}

                <div className="flex items-start space-x-3">
                  <span className="text-xl sm:text-2xl mt-0.5" role="img" aria-label={`Ícone do ${getProviderDisplayName(provider.provider_name)}`}>
                    {getProviderIcon(provider.provider_name)}
                  </span>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm sm:text-base truncate">
                      {getProviderDisplayName(provider.provider_name)}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-0.5 truncate">{provider.model_name}</p>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3 mt-2 space-y-1 sm:space-y-0">
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                        {formatCost(provider.cost_per_token)}/token
                      </span>
                      {provider.performance_score > 0 && (
                        <span className="text-xs text-blue-600 dark:text-blue-400">
                          Score: {provider.performance_score.toFixed(1)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Provider Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Settings className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" />
            Configurações Avançadas dos Provedores
          </h2>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {providers.map((provider) => (
            <motion.div
              key={provider.id}
              className="p-4 sm:p-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <span className="text-xl sm:text-2xl" role="img" aria-label={`Ícone do ${getProviderDisplayName(provider.provider_name)}`}>
                    {getProviderIcon(provider.provider_name)}
                  </span>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm sm:text-base truncate">
                      {getProviderDisplayName(provider.provider_name)}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-0.5 truncate">{provider.model_name}</p>
                    {provider.is_default && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 mt-1">
                        Padrão
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-wrap items-center gap-3 sm:gap-4 lg:space-x-6 lg:gap-0">
                  <div className="text-center min-w-0">
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1">Prioridade</p>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={provider.priority}
                      onChange={(e) => updateProvider(provider.id, { priority: parseInt(e.target.value) })}
                      className="w-14 sm:w-16 text-center border border-gray-300 dark:border-gray-600 rounded px-1 sm:px-2 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={updating === provider.id}
                      aria-label={`Prioridade do ${getProviderDisplayName(provider.provider_name)}`}
                    />
                  </div>

                  <div className="text-center min-w-0">
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1">Custo/Token</p>
                    <p className="font-medium text-green-600 dark:text-green-400 text-xs sm:text-sm truncate">{formatCost(provider.cost_per_token)}</p>
                  </div>

                  <div className="text-center min-w-0">
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1">API Key</p>
                    <div className="flex items-center justify-center">
                      {provider.api_key_configured ? (
                        <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 dark:text-green-400" aria-label="API Key configurada" />
                      ) : (
                        <XCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 dark:text-red-400" aria-label="API Key não configurada" />
                      )}
                    </div>
                  </div>

                  <div className="text-center min-w-0">
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1">Status</p>
                    <button
                      onClick={() => updateProvider(provider.id, { is_enabled: !provider.is_enabled })}
                      disabled={updating === provider.id}
                      className={`px-2 sm:px-3 py-1 rounded-full text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
                        provider.is_enabled
                          ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-900/70 focus:ring-green-500'
                          : 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-900/70 focus:ring-red-500'
                      }`}
                      aria-label={`${provider.is_enabled ? 'Desativar' : 'Ativar'} ${getProviderDisplayName(provider.provider_name)}`}
                    >
                      {provider.is_enabled ? 'Ativo' : 'Inativo'}
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" />
            Estatísticas (Últimos 30 dias)
          </h2>
        </div>
        
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full align-middle">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Provedor
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Requisições
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Taxa de Sucesso
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Questões
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tokens
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Custo Total
                </th>
                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tempo Médio
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {stats.map((stat) => (
                <tr key={stat.provider} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="mr-2 text-sm sm:text-base" role="img" aria-label={`Ícone do ${getProviderDisplayName(stat.provider)}`}>
                        {getProviderIcon(stat.provider)}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white text-sm sm:text-base truncate">
                        {getProviderDisplayName(stat.provider)}
                      </span>
                    </div>
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {stat.total_requests.toLocaleString()}
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      stat.success_rate >= 95 ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200' :
                      stat.success_rate >= 80 ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200' :
                      'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200'
                    }`}>
                      {formatSuccessRate(stat.success_rate)}
                    </span>
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {stat.total_questions.toLocaleString()}
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {stat.total_tokens.toLocaleString()}
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {formatCost(stat.total_cost)}
                  </td>
                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {Math.round(stat.avg_duration_ms)}ms
                  </td>
                </tr>
              ))}
            </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AIProviderSettings
