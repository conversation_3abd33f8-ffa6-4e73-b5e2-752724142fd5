import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Users,
  FileText,
  Layout,
  BarChart3,
  Settings,
  Menu,
  X,
  Home,
  CreditCard,
  Activity,
  Database,
  Bell,
  Sparkles,
  Sun,
  Moon,
  Globe,
  Monitor,
  School,
  Bot,
  MessageSquare,
  Package,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useTheme } from '../../contexts/ThemeContext'

interface AdminSidebarSubItem {
  id: string
  label: string
  path: string
  badge?: number
}

interface AdminSidebarItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  path: string
  badge?: number
  subItems?: AdminSidebarSubItem[]
}

interface AdminSidebarCategory {
  id: string
  label: string
  icon: React.ComponentType<any>
  items: AdminSidebarItem[]
  collapsible?: boolean
  defaultExpanded?: boolean
}

const adminSidebarCategories: AdminSidebarCategory[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    defaultExpanded: true,
    items: [
      { id: 'overview', label: 'Visão Geral', icon: BarChart3, path: '/admin' },
      { id: 'analytics', label: 'Analytics', icon: Activity, path: '/admin/analytics' }
    ]
  },
  {
    id: 'content',
    label: 'Conteúdo',
    icon: FileText,
    defaultExpanded: true,
    items: [
      { id: 'questions', label: 'Questões', icon: FileText, path: '/admin/questions' },
      { id: 'bulk-generator', label: 'Gerador em Massa', icon: Sparkles, path: '/admin/bulk-generator' },
      { id: 'assessments', label: 'Avaliações', icon: Database, path: '/admin/assessments' },
      { id: 'templates', label: 'Modelos Prontos', icon: Layout, path: '/admin/templates' },
      { id: 'feedback', label: 'Feedbacks', icon: MessageSquare, path: '/admin/feedback' }
    ]
  },
  {
    id: 'users',
    label: 'Usuários & Escolas',
    icon: Users,
    defaultExpanded: true,
    items: [
      { id: 'users', label: 'Usuários', icon: Users, path: '/admin/users' },
      { id: 'schools', label: 'Escolas', icon: School, path: '/admin/schools' },
      { id: 'notifications', label: 'Notificações', icon: Bell, path: '/admin/notifications' }
    ]
  },
  {
    id: 'billing',
    label: 'Financeiro',
    icon: CreditCard,
    defaultExpanded: false,
    items: [
      { id: 'plans', label: 'Planos', icon: Package, path: '/admin/plans' },
      { id: 'subscriptions', label: 'Assinaturas', icon: CreditCard, path: '/admin/subscriptions' }
    ]
  },
  {
    id: 'system',
    label: 'Sistema',
    icon: Settings,
    defaultExpanded: false,
    items: [
      { id: 'seo', label: 'SEO & Sitemap', icon: Globe, path: '/admin/seo' },
      { id: 'ai-providers', label: 'Provedores de IA', icon: Bot, path: '/admin/ai-providers' },
      { id: 'settings', label: 'Configurações', icon: Settings, path: '/admin/settings' }
    ]
  }
]

const AdminLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>(
    adminSidebarCategories.reduce((acc, category) => {
      acc[category.id] = category.defaultExpanded ?? true
      return acc
    }, {} as Record<string, boolean>)
  )
  const navigate = useNavigate()
  const location = useLocation()
  const { profile, signOut } = useAuth()
  const { theme, setTheme } = useTheme()

  const isActive = (path: string) => {
    if (path === '/admin') {
      return location.pathname === '/admin'
    }
    return location.pathname.startsWith(path)
  }

  const isItemActive = (item: AdminSidebarItem) => {
    if (item.subItems) {
      return item.subItems.some(subItem => isActive(subItem.path)) || isActive(item.path)
    }
    return isActive(item.path)
  }

  const isCategoryActive = (category: AdminSidebarCategory) => {
    return category.items.some(item => isItemActive(item))
  }

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }))
  }

  const handleNavigation = (path: string) => {
    navigate(path)
    setSidebarOpen(false)
  }

  const handleBackToApp = () => {
    navigate('/app')
  }

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('light')
    } else if (theme === 'system') {
      setTheme('light')
    }
  }

  const getThemeIcon = () => {
    if (theme === 'light') return <Sun className="w-4 h-4" />
    if (theme === 'dark') return <Moon className="w-4 h-4" />
    return <Monitor className="w-4 h-4" />
  }

  const getThemeText = () => {
    if (theme === 'light') return 'Tema Claro'
    if (theme === 'dark') return 'Tema Escuro'
    return 'Tema do Sistema'
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <aside className={`
        fixed lg:relative inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col
        transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-600 rounded-xl flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900 dark:text-white">Admin Panel</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Atividade Pronta</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
          {adminSidebarCategories.map((category) => {
            const CategoryIcon = category.icon
            const isExpanded = expandedCategories[category.id]
            const categoryActive = isCategoryActive(category)

            return (
              <div key={category.id} className="space-y-1">
                {/* Category Header */}
                <button
                  onClick={() => toggleCategory(category.id)}
                  className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 text-left group ${
                    categoryActive
                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <CategoryIcon className={`w-4 h-4 ${
                    categoryActive
                      ? 'text-red-700 dark:text-red-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`} />
                  <span className="flex-1 text-sm font-semibold uppercase tracking-wide">
                    {category.label}
                  </span>
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>

                {/* Category Items */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="space-y-1 ml-2"
                    >
                      {category.items.map((item) => {
                        const ItemIcon = item.icon
                        const itemActive = isItemActive(item)

                        return (
                          <div key={item.id}>
                            {/* Main Item */}
                            <button
                              onClick={() => handleNavigation(item.path)}
                              className={`w-full flex items-center space-x-3 px-4 py-2.5 rounded-lg transition-all duration-200 text-left group ${
                                itemActive
                                  ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800'
                                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                              }`}
                            >
                              <ItemIcon className={`w-4 h-4 ${
                                itemActive
                                  ? 'text-red-700 dark:text-red-400'
                                  : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                              }`} />
                              <span className="flex-1 font-medium text-sm">{item.label}</span>

                              {item.badge && (
                                <span className="bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-xs px-2 py-0.5 rounded-full">
                                  {item.badge}
                                </span>
                              )}
                            </button>

                            {/* Sub Items */}
                            {item.subItems && itemActive && (
                              <div className="ml-6 mt-1 space-y-1">
                                {item.subItems.map((subItem) => {
                                  const subItemActive = isActive(subItem.path)

                                  return (
                                    <button
                                      key={subItem.id}
                                      onClick={() => handleNavigation(subItem.path)}
                                      className={`w-full flex items-center space-x-2 px-3 py-1.5 rounded-md transition-all duration-200 text-left ${
                                        subItemActive
                                          ? 'bg-red-100 dark:bg-red-900/40 text-red-700 dark:text-red-400'
                                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/30'
                                      }`}
                                    >
                                      <div className={`w-1.5 h-1.5 rounded-full ${
                                        subItemActive
                                          ? 'bg-red-600 dark:bg-red-400'
                                          : 'bg-gray-400 dark:bg-gray-500'
                                      }`} />
                                      <span className="text-xs font-medium">{subItem.label}</span>

                                      {subItem.badge && (
                                        <span className="bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-xs px-1.5 py-0.5 rounded-full">
                                          {subItem.badge}
                                        </span>
                                      )}
                                    </button>
                                  )
                                })}
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {profile?.nome?.charAt(0) || 'A'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {profile?.nome || 'Admin'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Administrador</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <button
              onClick={toggleTheme}
              className="w-full flex items-center justify-between text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm"
            >
              <div className="flex items-center space-x-2">
                {getThemeIcon()}
                <span>Alternar tema</span>
              </div>
            </button>
            
            <button
              onClick={handleBackToApp}
              className="w-full flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm"
            >
              <Home className="w-4 h-4" />
              <span>Voltar ao App</span>
            </button>
            
            <button
              onClick={signOut}
              className="w-full flex items-center space-x-2 text-red-600 hover:text-red-700 px-3 py-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-sm"
            >
              <span>Sair</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden min-w-0">
        {/* Header */}
        <header className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center w-full">
          <div className="flex items-center w-full px-4 lg:px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors lg:hidden mr-4"
            >
              <Menu className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
            
            <div className="flex-1">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Painel de Administração
              </h2>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <Outlet />
        </main>
      </div>
    </div>
  )
}

export default AdminLayout