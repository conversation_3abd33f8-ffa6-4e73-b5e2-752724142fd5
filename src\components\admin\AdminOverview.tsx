import React, { useState } from 'react'
import {
  Users,
  FileText,
  Database,
  CreditCard,
  TrendingUp,
  Activity,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Loader,
  Bell,
  BarChart3,
  Download,
  X
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { useQuery } from '@tanstack/react-query'
import { Database as DatabaseType } from '../../types/database'
import { useNotifications } from '../../hooks/useNotifications'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { createNotificationForAllUsers } from '../../services/notificationService'
import toast from 'react-hot-toast'

type UsageStat = DatabaseType['public']['Tables']['usage_stats']['Row']

interface AdminStats {
  totalUsers: number
  totalQuestions: number
  totalAssessments: number
  activeSubscriptions: number
  recentActivityCount: number
  recentActivities: UsageStat[]
  systemHealth: 'healthy' | 'warning' | 'error'
}

const fetchAdminStats = async (): Promise<AdminStats> => {
  try {
    const { count: usersCount, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    const { count: questionsCount, error: questionsError } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true })

    const { count: assessmentsCount, error: assessmentsError } = await supabase
      .from('assessments')
      .select('*', { count: 'exact', head: true })

    const { count: subscriptionsCount, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')

    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const { data: activityData, count: activityCount, error: activityError } = await supabase
      .from('usage_stats')
      .select('*, profiles(nome, email)', { count: 'exact' })
      .gte('created_at', yesterday)
      .order('created_at', { ascending: false })
      .limit(10)

    if (usersError) throw usersError
    if (questionsError) throw questionsError
    if (assessmentsError) throw assessmentsError
    if (subscriptionsError) throw subscriptionsError
    if (activityError) throw activityError

    return {
      totalUsers: usersCount || 0,
      totalQuestions: questionsCount || 0,
      totalAssessments: assessmentsCount || 0,
      activeSubscriptions: subscriptionsCount || 0,
      recentActivityCount: activityCount || 0,
      recentActivities: activityData || [],
      systemHealth: 'healthy'
    }
  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return {
      totalUsers: 0,
      totalQuestions: 0,
      totalAssessments: 0,
      activeSubscriptions: 0,
      recentActivityCount: 0,
      recentActivities: [],
      systemHealth: 'error'
    }
  }
}

const AdminOverview: React.FC = () => {
  const { data: stats, isLoading, error, refetch, isRefetching } = useQuery<AdminStats, Error>({
    queryKey: ['adminStats'],
    queryFn: fetchAdminStats,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 1,
    initialData: {
      totalUsers: 0,
      totalQuestions: 0,
      totalAssessments: 0,
      activeSubscriptions: 0,
      recentActivityCount: 0,
      recentActivities: [],
      systemHealth: 'healthy'
    }
  })

  const navigate = useNavigate()
  const { notifications, isLoading: loadingNotifications } = useNotifications()
  const [showCreateAssessment, setShowCreateAssessment] = useState(false)
  const [showSendNotification, setShowSendNotification] = useState(false)
  const [showExportConfirm, setShowExportConfirm] = useState(false)
  const [notificationFilter, setNotificationFilter] = useState<'all' | 'unread' | 'system'>('all')
  const { profile } = useAuth()
  const isAdmin = !!profile?.is_admin
  const [notificationForm, setNotificationForm] = useState({ title: '', message: '', type: 'info' })
  const [sendingNotification, setSendingNotification] = useState(false)

  const filteredNotifications = notifications
    ? notifications.filter(n => {
        if (notificationFilter === 'unread') return !n.read
        if (notificationFilter === 'system') return n.type === 'system'
        return true
      })
    : []

  const statCards = [
    {
      label: 'Total de Usuários',
      value: stats.totalUsers,
      icon: Users,
      iconBgClass: 'bg-red-100 dark:bg-red-900/20',
      iconTextClass: 'text-red-600 dark:text-red-400'
    },
    {
      label: 'Total de Questões',
      value: stats.totalQuestions,
      icon: Database,
      iconBgClass: 'bg-blue-100 dark:bg-blue-900/20',
      iconTextClass: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Total de Avaliações',
      value: stats.totalAssessments,
      icon: FileText,
      iconBgClass: 'bg-green-100 dark:bg-green-900/20',
      iconTextClass: 'text-green-600 dark:text-green-400'
    },
    {
      label: 'Assinaturas Ativas',
      value: stats.activeSubscriptions,
      icon: CreditCard,
      iconBgClass: 'bg-purple-100 dark:bg-purple-900/20',
      iconTextClass: 'text-purple-600 dark:text-purple-400'
    }
  ]

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando estatísticas...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">
              Erro ao carregar estatísticas: {error.message}
            </p>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Por favor, tente novamente mais tarde.
            </p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Ações rápidas (apenas para admin) */}
      {isAdmin && (
        <div className="flex flex-wrap gap-4 mb-4">
          <button
            onClick={() => setShowCreateAssessment(true)}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors text-sm font-medium"
          >
            <FileText className="w-4 h-4" />
            <span>Criar Avaliação</span>
          </button>
          <button
            onClick={() => setShowSendNotification(true)}
            className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors text-sm font-medium"
          >
            <Bell className="w-4 h-4" />
            <span>Enviar Notificação</span>
          </button>
          <button
            onClick={() => navigate('/admin/analytics')}
            className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors text-sm font-medium"
          >
            <BarChart3 className="w-4 h-4" />
            <span>Ver Analytics</span>
          </button>
          <button
            onClick={() => setShowExportConfirm(true)}
            className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-900 text-white px-4 py-2 rounded-lg shadow-sm transition-colors text-sm font-medium"
          >
            <Download className="w-4 h-4" />
            <span>Exportar Dados</span>
          </button>
        </div>
      )}

      {/* Modais de ações rápidas */}
      {showCreateAssessment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-xl w-full max-w-md relative">
            <button onClick={() => setShowCreateAssessment(false)} className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
              <X className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-bold mb-4">Criar Nova Avaliação</h2>
            <p className="mb-4 text-gray-600 dark:text-gray-300">Você será redirecionado para o editor de avaliações.</p>
            <button
              onClick={() => { setShowCreateAssessment(false); navigate('/admin/assessments?new=1') }}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-medium"
            >Ir para o Editor</button>
          </div>
        </div>
      )}
      {showSendNotification && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-xl w-full max-w-md relative">
            <button onClick={() => setShowSendNotification(false)} className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
              <X className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-bold mb-4">Enviar Notificação</h2>
            <form
              onSubmit={async e => {
                e.preventDefault()
                setSendingNotification(true)
                try {
                  await createNotificationForAllUsers({
                    type: notificationForm.type as any,
                    title: notificationForm.title,
                    message: notificationForm.message
                  })
                  toast.success('Notificação enviada para todos os usuários!')
                  setShowSendNotification(false)
                  setNotificationForm({ title: '', message: '', type: 'info' })
                } catch (err) {
                  toast.error('Erro ao enviar notificação')
                } finally {
                  setSendingNotification(false)
                }
              }}
            >
              <input
                type="text"
                placeholder="Título"
                className="w-full mb-3 px-3 py-2 rounded border border-gray-300 dark:bg-gray-800 dark:text-white"
                required
                value={notificationForm.title}
                onChange={e => setNotificationForm(f => ({ ...f, title: e.target.value }))}
              />
              <textarea
                placeholder="Mensagem"
                className="w-full mb-3 px-3 py-2 rounded border border-gray-300 dark:bg-gray-800 dark:text-white"
                required
                value={notificationForm.message}
                onChange={e => setNotificationForm(f => ({ ...f, message: e.target.value }))}
              />
              <div className="mb-3">
                <label className="block text-xs text-gray-500 mb-1">Tipo:</label>
                <select
                  className="w-full px-2 py-1 rounded border border-gray-300 dark:bg-gray-800 dark:text-white"
                  value={notificationForm.type}
                  onChange={e => setNotificationForm(f => ({ ...f, type: e.target.value }))}
                >
                  <option value="info">Informação</option>
                  <option value="success">Sucesso</option>
                  <option value="warning">Aviso</option>
                  <option value="error">Erro</option>
                </select>
              </div>
              <button
                type="submit"
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg font-medium disabled:opacity-60"
                disabled={sendingNotification}
              >
                {sendingNotification ? 'Enviando...' : 'Enviar'}
              </button>
            </form>
          </div>
        </div>
      )}
      {showExportConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-xl w-full max-w-md relative">
            <button onClick={() => setShowExportConfirm(false)} className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
              <X className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-bold mb-4">Exportar Dados</h2>
            <p className="mb-4 text-gray-600 dark:text-gray-300">Deseja exportar os dados principais da plataforma em CSV?</p>
            <button
              onClick={() => {
                setShowExportConfirm(false)
                const csvContent = [
                  ['Métrica', 'Valor'],
                  ['Total de Usuários', stats.totalUsers],
                  ['Total de Questões', stats.totalQuestions],
                  ['Total de Avaliações', stats.totalAssessments],
                  ['Assinaturas Ativas', stats.activeSubscriptions]
                ].map(row => row.join(',')).join('\n')
                const blob = new Blob([csvContent], { type: 'text/csv' })
                const url = URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.href = url
                link.download = `admin-overview-${new Date().toISOString().split('T')[0]}.csv`
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                URL.revokeObjectURL(url)
              }}
              className="w-full bg-gray-700 hover:bg-gray-900 text-white py-2 rounded-lg font-medium"
            >Exportar</button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Visão Geral</h1>
          <p className="text-gray-600 dark:text-gray-400">Estatísticas e métricas da plataforma</p>
        </div>
        
        <div className="flex items-center space-x-2">
          {stats.systemHealth === 'healthy' && (
            <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm font-medium">Sistema Saudável</span>
            </div>
          )}
          {stats.systemHealth === 'warning' && (
            <div className="flex items-center space-x-2 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle className="w-5 h-5" />
              <span className="text-sm font-medium">Atenção Necessária</span>
            </div>
          )}
          {stats.systemHealth === 'error' && (
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span className="text-sm font-medium">Problemas Detectados</span>
            </div>
          )}
          <button
            onClick={() => refetch()}
            disabled={isRefetching}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Atualizar dados"
          >
            {isRefetching ? <Loader className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            <span>Atualizar</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={card.label}
            className="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-sm border border-gray-200 dark:border-gray-700 flex items-center space-x-4 transition-transform duration-200 hover:scale-105 hover:shadow-lg group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div className={`p-3 rounded-full ${card.iconBgClass} group-hover:scale-110 transition-transform duration-200`}>
              <card.icon className={`w-7 h-7 ${card.iconTextClass} group-hover:animate-bounce`} />
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{card.label}</p>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{card.value.toLocaleString()}</h2>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
        className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 space-y-4"
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-3 mb-4">
          Atividade Recente{' '}
          <span className="text-gray-500 dark:text-gray-400 text-sm">({stats.recentActivityCount} eventos nas últimas 24h)</span>
        </h2>
        {stats.recentActivities.length > 0 ? (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {stats.recentActivities.map((activity, index) => (
              <li key={activity.id} className="py-3 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Activity className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.event_type === 'question_created' && `Nova questão criada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                      {activity.event_type === 'assessment_created' && `Nova avaliação criada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                      {activity.event_type === 'user_login' && `Login de usuário: ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                      {activity.event_type === 'subscription_activated' && `Nova assinatura ativada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                      {!activity.event_type && `Atividade desconhecida de ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(activity.created_at).toLocaleString('pt-BR')}
                    </p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">
            Nenhuma atividade recente nas últimas 24h.
          </div>
        )}
      </motion.div>

      {/* Painel de notificações recentes com filtro */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 space-y-4"
      >
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white border-b-0 pb-0 mb-0">
            Notificações Recentes
          </h2>
          <div className="flex items-center space-x-1">
            <button onClick={() => setNotificationFilter('all')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Todas</button>
            <button onClick={() => setNotificationFilter('unread')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'unread' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Não lidas</button>
            <button onClick={() => setNotificationFilter('system')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'system' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Sistema</button>
          </div>
        </div>
        {loadingNotifications ? (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">Carregando notificações...</div>
        ) : filteredNotifications && filteredNotifications.length > 0 ? (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredNotifications.slice(0, 6).map((notification) => (
              <li
                key={notification.id}
                className={`py-3 flex items-start space-x-3 transition-colors duration-150 rounded-lg px-2 ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500' : ''}`}
              >
                <Bell className={`w-5 h-5 mt-1 ${!notification.read ? 'text-blue-500 animate-pulse' : 'text-gray-400'}`} />
                <div>
                  <p className={`text-sm font-medium ${!notification.read ? 'text-blue-900 dark:text-blue-200' : 'text-gray-900 dark:text-white'}`}>{notification.title}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{notification.message}</p>
                  <p className="text-xs text-gray-400 mt-1">{new Date(notification.created_at).toLocaleString('pt-BR')}</p>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">Nenhuma notificação recente.</div>
        )}
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <button
          onClick={() => navigate('/admin/users')}
          className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-105 transition-transform group"
        >
          <Users className="w-10 h-10 text-blue-600 group-hover:animate-bounce mb-2" />
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Gestão de Usuários</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">Ver, editar e aprovar usuários</span>
        </button>
        <button
          onClick={() => navigate('/admin/assessments?highlighted=1')}
          className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-105 transition-transform group"
        >
          <FileText className="w-10 h-10 text-purple-600 group-hover:animate-bounce mb-2" />
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Avaliações em Destaque</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">Mais usadas/recentes</span>
        </button>
        <button
          onClick={() => navigate('/admin/questions?top=1')}
          className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-105 transition-transform group"
        >
          <TrendingUp className="w-10 h-10 text-green-600 group-hover:animate-bounce mb-2" />
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Questões Mais Respondidas</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">Insights de conteúdo</span>
        </button>
        <button
          onClick={() => navigate('/admin/monitoring')}
          className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-105 transition-transform group"
        >
          <Activity className="w-10 h-10 text-red-600 group-hover:animate-bounce mb-2" />
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Monitoramento do Sistema</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">Status e logs</span>
        </button>
      </div>
    </div>
  )
}

export default AdminOverview