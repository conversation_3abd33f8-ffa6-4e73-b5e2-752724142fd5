import React from 'react'
import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

interface AnalyticsStatsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  color: 'blue' | 'green' | 'purple' | 'orange' | 'yellow' | 'red' | 'indigo' | 'gray'
  delay?: number
  suffix?: string
  prefix?: string
}

const colorClasses = {
  blue: {
    icon: 'bg-blue-500',
    iconText: 'text-white'
  },
  green: {
    icon: 'bg-green-500',
    iconText: 'text-white'
  },
  purple: {
    icon: 'bg-purple-500',
    iconText: 'text-white'
  },
  orange: {
    icon: 'bg-orange-500',
    iconText: 'text-white'
  },
  yellow: {
    icon: 'bg-yellow-500',
    iconText: 'text-white'
  },
  red: {
    icon: 'bg-red-500',
    iconText: 'text-white'
  },
  indigo: {
    icon: 'bg-indigo-500',
    iconText: 'text-white'
  },
  gray: {
    icon: 'bg-gray-500',
    iconText: 'text-white'
  }
}

const AnalyticsStatsCard: React.FC<AnalyticsStatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color,
  delay = 0,
  suffix = '',
  prefix = ''
}) => {
  const colors = colorClasses[color]

  const displayValue = typeof value === 'number' 
    ? value.toLocaleString() 
    : value

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {title}
          </p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {prefix}{displayValue}{suffix}
          </p>
        </div>
        <div className={`w-12 h-12 ${colors.icon} rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 ${colors.iconText}`} />
        </div>
      </div>
    </motion.div>
  )
}

export default AnalyticsStatsCard
