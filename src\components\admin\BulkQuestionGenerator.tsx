import React, { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { Shield } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useQuestionGeneration } from '../../hooks/useQuestionGeneration'
import { useQuestionManagement } from '../../hooks/useQuestionManagement'
import { useAuditLog } from '../../hooks/useAuditLog'
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts'
import GenerationForm from './BulkQuestionGenerator/GenerationForm'
import QuestionsList from './BulkQuestionGenerator/QuestionsList'
import ShortcutsHelp from './BulkQuestionGenerator/ShortcutsHelp'
import PerformanceMetrics from './BulkQuestionGenerator/PerformanceMetrics'
import GenerationInfo from './BulkQuestionGenerator/GenerationInfo'
import ErrorDisplay from './BulkQuestionGenerator/ErrorDisplay'
import type { GenerationParams } from './BulkQuestionGenerator/GenerationForm'
import toast from 'react-hot-toast'

const BulkQuestionGenerator: React.FC = () => {
  const { user, isAdmin, loading } = useAuth()
  const auditLog = useAuditLog()

  // Hooks personalizados para lógica de negócio
  const questionGeneration = useQuestionGeneration()
  const questionManagement = useQuestionManagement()

  // Estado local para parâmetros de geração
  const [params, setParams] = useState<GenerationParams>({
    disciplina: '',
    serie: '',
    topico: '',
    dificuldade: 'Médio',
    tipo: 'multipla_escolha',
    quantidade: 5
  })

  // Estado para controle de erros
  const [lastError, setLastError] = useState<string | null>(null)

  // Atalhos de teclado
  useKeyboardShortcuts({
    shortcuts: [
      {
        key: 'g',
        ctrlKey: true,
        callback: () => {
          if (!questionGeneration.generating && params.disciplina && params.serie && params.topico) {
            handleGenerate()
          }
        },
        description: 'Gerar questões'
      },
      {
        key: 'a',
        ctrlKey: true,
        callback: () => {
          const pendingQuestions = questionManagement.questions.filter(q => q.status === 'pending')
          if (pendingQuestions.length > 0) {
            questionManagement.acceptAllQuestions()
          }
        },
        description: 'Aceitar todas as questões pendentes'
      },
      {
        key: 'r',
        ctrlKey: true,
        callback: () => {
          const pendingQuestions = questionManagement.questions.filter(q => q.status === 'pending')
          if (pendingQuestions.length > 0) {
            questionManagement.rejectAllQuestions()
          }
        },
        description: 'Rejeitar todas as questões pendentes'
      }
    ],
    enabled: !loading && isAdmin
  })

  // Registrar acesso ao painel administrativo
  useEffect(() => {
    if (user && isAdmin) {
      auditLog.logAdminAccess('bulk_question_generator', 'VIEW')
    }
  }, [user, isAdmin, auditLog])

  // Verificação de autenticação e autorização robusta
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-600 dark:bg-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <div className="w-8 h-8 border-4 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Verificando permissões administrativas...</p>
        </div>
      </div>
    )
  }

  // Redirecionar se não for usuário autenticado
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Redirecionar se não for administrador
  if (!isAdmin) {
    toast.error('Acesso negado: Permissões de administrador necessárias')
    return <Navigate to="/app" replace />
  }

  // Handlers para os componentes filhos
  const handleGenerate = async () => {
    try {
      setLastError(null) // Clear previous errors
      const generatedQuestions = await questionGeneration.generateQuestions(params)
      questionManagement.setQuestions(generatedQuestions)
    } catch (error: any) {
      setLastError(error.message || 'Erro desconhecido na geração de questões')
    }
  }

  const handleRetry = () => {
    setLastError(null)
    handleGenerate()
  }

  const handleContactSupport = () => {
    window.open('mailto:<EMAIL>?subject=Erro na Geração de Questões&body=' + encodeURIComponent(`
Erro encontrado: ${lastError}

Parâmetros de geração:
- Disciplina: ${params.disciplina}
- Série: ${params.serie}
- Tópico: ${params.topico}
- Dificuldade: ${params.dificuldade}
- Tipo: ${params.tipo}
- Quantidade: ${params.quantidade}

Por favor, descreva o que estava fazendo quando o erro ocorreu.
    `), '_blank')
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
      <div className="space-y-6">
        {/* Formulário de Geração */}
        <GenerationForm
          params={params}
          setParams={setParams}
          onGenerate={handleGenerate}
          generating={questionGeneration.generating}
          progress={questionGeneration.progress}
          currentStep={questionGeneration.currentStep}
          rateLimit={questionGeneration.rateLimit}
        />

        {/* Display de Erro */}
        <ErrorDisplay
          error={lastError}
          onRetry={handleRetry}
          onContactSupport={handleContactSupport}
          isVisible={!!lastError && !questionGeneration.generating}
        />

        {/* Informações da Geração */}
        <GenerationInfo
          generationInfo={questionGeneration.lastGenerationInfo}
          isVisible={!questionGeneration.generating && questionManagement.questions.length > 0}
        />

        {/* Métricas de Performance */}
        <PerformanceMetrics
          metrics={questionGeneration.performanceMetrics}
          questionsGenerated={questionManagement.questions.length}
          isVisible={!questionGeneration.generating && questionManagement.questions.length > 0}
        />

        {/* Lista de Questões */}
        <QuestionsList
          questions={questionManagement.questions}
          searchTerm={questionManagement.searchTerm}
          setSearchTerm={questionManagement.setSearchTerm}
          filterBy={questionManagement.filterBy}
          setFilterBy={questionManagement.setFilterBy}
          onAcceptAll={questionManagement.acceptAllQuestions}
          onRejectAll={questionManagement.rejectAllQuestions}
          onAcceptQuestion={questionManagement.acceptQuestion}
          onRejectQuestion={questionManagement.rejectQuestion}
          onRegenerateQuestion={questionManagement.regenerateQuestion}
          onExportQuestions={questionManagement.exportQuestions}
        />

        {/* Atalhos de Teclado */}
        <ShortcutsHelp />
      </div>
    </div>
  )
}

export default BulkQuestionGenerator