import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Wifi, Key } from 'lucide-react'

interface ErrorDisplayProps {
  error: string | null
  onRetry?: () => void
  onContactSupport?: () => void
  isVisible: boolean
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  onRetry, 
  onContactSupport, 
  isVisible 
}) => {
  if (!isVisible || !error) return null

  const getErrorType = (errorMessage: string) => {
    if (errorMessage.includes('Rate limit exceeded') || errorMessage.includes('Limite de requisições')) {
      return 'rate_limit'
    } else if (errorMessage.includes('All AI providers failed') || errorMessage.includes('provedores de IA')) {
      return 'providers_failed'
    } else if (errorMessage.includes('API key') || errorMessage.includes('indisponível')) {
      return 'service_unavailable'
    } else if (errorMessage.includes('Invalid quantidade') || errorMessage.includes('inválida')) {
      return 'validation_error'
    } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return 'network_error'
    }
    return 'unknown'
  }

  const getErrorIcon = (type: string) => {
    switch (type) {
      case 'rate_limit':
        return <Clock className="w-6 h-6 text-orange-500" />
      case 'providers_failed':
        return <Wifi className="w-6 h-6 text-red-500" />
      case 'service_unavailable':
        return <Key className="w-6 h-6 text-yellow-500" />
      case 'validation_error':
        return <Settings className="w-6 h-6 text-blue-500" />
      case 'network_error':
        return <Wifi className="w-6 h-6 text-red-500" />
      default:
        return <AlertTriangle className="w-6 h-6 text-red-500" />
    }
  }

  const getErrorTitle = (type: string) => {
    switch (type) {
      case 'rate_limit':
        return 'Limite de Requisições Excedido'
      case 'providers_failed':
        return 'Serviços de IA Indisponíveis'
      case 'service_unavailable':
        return 'Serviço Temporariamente Indisponível'
      case 'validation_error':
        return 'Erro de Validação'
      case 'network_error':
        return 'Erro de Conexão'
      default:
        return 'Erro na Geração'
    }
  }

  const getErrorSuggestions = (type: string) => {
    switch (type) {
      case 'rate_limit':
        return [
          'Aguarde alguns minutos antes de tentar novamente',
          'Reduza a quantidade de questões por requisição',
          'Considere fazer upgrade para um plano premium'
        ]
      case 'providers_failed':
        return [
          'Tente novamente em alguns minutos',
          'Verifique sua conexão com a internet',
          'Entre em contato com o suporte se o problema persistir'
        ]
      case 'service_unavailable':
        return [
          'O serviço está em manutenção temporária',
          'Tente novamente em alguns minutos',
          'Entre em contato com o suporte para mais informações'
        ]
      case 'validation_error':
        return [
          'Verifique se todos os campos obrigatórios estão preenchidos',
          'Certifique-se de que a quantidade está entre 1 e 20',
          'Revise os parâmetros de geração'
        ]
      case 'network_error':
        return [
          'Verifique sua conexão com a internet',
          'Tente recarregar a página',
          'Use uma conexão mais estável se possível'
        ]
      default:
        return [
          'Tente novamente em alguns minutos',
          'Verifique sua conexão com a internet',
          'Entre em contato com o suporte se o problema persistir'
        ]
    }
  }

  const errorType = getErrorType(error)
  const suggestions = getErrorSuggestions(errorType)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 sm:p-6"
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {getErrorIcon(errorType)}
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-red-900 mb-2">
            {getErrorTitle(errorType)}
          </h3>
          
          <p className="text-red-700 mb-4">
            {error}
          </p>

          <div className="mb-4">
            <h4 className="font-medium text-red-900 mb-2">Sugestões:</h4>
            <ul className="list-disc list-inside space-y-1 text-red-700">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="text-sm">
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>

          <div className="flex space-x-3">
            {onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Tentar Novamente
              </button>
            )}
            
            {onContactSupport && errorType !== 'validation_error' && (
              <button
                onClick={onContactSupport}
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
              >
                Contatar Suporte
              </button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default ErrorDisplay
