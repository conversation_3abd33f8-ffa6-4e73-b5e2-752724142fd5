import React from 'react'
import { <PERSON>rk<PERSON>, AlertTriangle, HelpCircle } from 'lucide-react'
import { motion } from 'framer-motion'
import Tooltip from '../../ui/Tooltip'
import { useDisciplinas, useSeriesByDisciplina, useTopicos, useSubtopicos } from '../../../hooks/useEducationOptions'

export interface GenerationParams {
  disciplina: string
  serie: string
  topico: string
  subtopico?: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  quantidade: number
  competencia_bncc?: string
  contexto?: string
}

interface RateLimitInfo {
  canMakeRequest: boolean
  remainingRequests: number
  resetTime: number | null
  isBlocked: boolean
  formatTimeUntilReset: () => string
}

interface GenerationFormProps {
  params: GenerationParams
  setParams: React.Dispatch<React.SetStateAction<GenerationParams>>
  onGenerate: () => void
  generating: boolean
  progress: number
  currentStep: string
  rateLimit: RateLimitInfo
}

const GenerationForm: React.FC<GenerationFormProps> = ({
  params,
  setParams,
  onGenerate,
  generating,
  progress,
  currentStep,
  rateLimit
}) => {
  // Hooks para buscar opções dinamicamente
  const { data: disciplinas, isLoading: isLoadingDisciplinas } = useDisciplinas()
  const { data: series, isLoading: isLoadingSeries } = useSeriesByDisciplina(params.disciplina)
  const { data: topicos, isLoading: isLoadingTopicos } = useTopicos(params.disciplina)
  const { data: subtopicos, isLoading: isLoadingSubtopicos } = useSubtopicos(params.disciplina, params.topico)

  // Função para resetar campos dependentes quando disciplina muda
  const handleDisciplinaChange = (disciplina: string) => {
    setParams(prev => ({
      ...prev,
      disciplina,
      serie: '', // Reset série
      topico: '', // Reset tópico
      subtopico: '' // Reset subtópico
    }))
  }

  // Função para resetar subtópico quando tópico muda
  const handleTopicoChange = (topico: string) => {
    setParams(prev => ({
      ...prev,
      topico,
      subtopico: '' // Reset subtópico
    }))
  }
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6 space-y-4 sm:space-y-6"
    >
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
          <Sparkles className="w-5 h-5 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Gerador de Questões por IA
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Configure os parâmetros para gerar questões automaticamente
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Disciplina */}
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Disciplina *
            </label>
            <Tooltip content="Selecione a disciplina ou matéria para a qual as questões serão geradas">
              <HelpCircle className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
            </Tooltip>
          </div>
          <select
            value={params.disciplina}
            onChange={(e) => handleDisciplinaChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            disabled={isLoadingDisciplinas}
          >
            <option value="">
              {isLoadingDisciplinas ? 'Carregando disciplinas...' : 'Selecione uma disciplina'}
            </option>
            {disciplinas?.map((disciplina) => (
              <option key={disciplina.value} value={disciplina.value}>
                {disciplina.label}
              </option>
            ))}
          </select>
        </div>

        {/* Série */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Série *
          </label>
          <select
            value={params.serie}
            onChange={(e) => setParams(prev => ({ ...prev, serie: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            disabled={!params.disciplina || isLoadingSeries}
          >
            <option value="">
              {!params.disciplina
                ? 'Selecione uma disciplina primeiro'
                : isLoadingSeries
                ? 'Carregando séries...'
                : 'Selecione uma série'
              }
            </option>
            {series?.map((serie) => (
              <option key={serie.value} value={serie.value}>
                {serie.label}
              </option>
            ))}
          </select>
        </div>

        {/* Tópico */}
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Tópico *
            </label>
            <Tooltip content="Selecione o assunto específico sobre o qual as questões serão geradas. Os tópicos são carregados dinamicamente baseados na disciplina selecionada">
              <HelpCircle className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
            </Tooltip>
          </div>
          <select
            value={params.topico}
            onChange={(e) => handleTopicoChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            disabled={!params.disciplina || isLoadingTopicos}
          >
            <option value="">
              {!params.disciplina
                ? 'Selecione uma disciplina primeiro'
                : isLoadingTopicos
                ? 'Carregando tópicos...'
                : 'Selecione um tópico'
              }
            </option>
            {topicos?.map((topico) => (
              <option key={topico.value} value={topico.value}>
                {topico.label}
              </option>
            ))}
          </select>
        </div>

        {/* Subtópico */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Subtópico
          </label>
          <select
            value={params.subtopico || ''}
            onChange={(e) => setParams(prev => ({ ...prev, subtopico: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            disabled={!params.disciplina || !params.topico || isLoadingSubtopicos}
          >
            <option value="">
              {!params.disciplina
                ? 'Selecione uma disciplina primeiro'
                : !params.topico
                ? 'Selecione um tópico primeiro'
                : isLoadingSubtopicos
                ? 'Carregando subtópicos...'
                : 'Selecione um subtópico (opcional)'
              }
            </option>
            {subtopicos?.map((subtopico) => (
              <option key={subtopico.value} value={subtopico.value}>
                {subtopico.label}
              </option>
            ))}
          </select>
        </div>

        {/* Dificuldade */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Dificuldade
          </label>
          <select
            value={params.dificuldade}
            onChange={(e) => setParams(prev => ({ ...prev, dificuldade: e.target.value as 'Fácil' | 'Médio' | 'Difícil' }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="Fácil">Fácil</option>
            <option value="Médio">Médio</option>
            <option value="Difícil">Difícil</option>
          </select>
        </div>

        {/* Tipo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tipo de Questão
          </label>
          <select
            value={params.tipo}
            onChange={(e) => setParams(prev => ({ ...prev, tipo: e.target.value as 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso' }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="multipla_escolha">Múltipla Escolha</option>
            <option value="dissertativa">Dissertativa</option>
            <option value="verdadeiro_falso">Verdadeiro/Falso</option>
          </select>
        </div>

        {/* Quantidade */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Quantidade
          </label>
          <input
            type="number"
            min="1"
            max="50"
            value={params.quantidade}
            onChange={(e) => setParams(prev => ({ ...prev, quantidade: parseInt(e.target.value) || 1 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      {/* Competência BNCC */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Competência BNCC
        </label>
        <input
          type="text"
          value={params.competencia_bncc || ''}
          onChange={(e) => setParams(prev => ({ ...prev, competencia_bncc: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          placeholder="Ex: EF09MA09 - Compreender os processos de fatoração..."
          maxLength={300}
        />
      </div>

      {/* Contexto */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Contexto Adicional
        </label>
        <textarea
          value={params.contexto || ''}
          onChange={(e) => setParams(prev => ({ ...prev, contexto: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
          placeholder="Forneça contexto adicional, exemplos específicos ou requisitos especiais para as questões..."
          maxLength={1000}
        />
      </div>

      {/* Rate Limit Warning */}
      {rateLimit.isBlocked && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Rate limit atingido
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Você pode fazer mais {rateLimit.remainingRequests} requisições em {rateLimit.formatTimeUntilReset()}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      {generating && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              {currentStep || 'Processando...'}
            </span>
            <span className="text-sm text-blue-600 dark:text-blue-400">
              {progress}%
            </span>
          </div>
          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
            <div
              className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Generate Button */}
      <button
        onClick={onGenerate}
        disabled={generating || !params.disciplina || !params.serie || !params.topico || rateLimit.isBlocked}
        className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-400 dark:disabled:from-gray-700 dark:disabled:to-gray-700 text-white px-6 py-3 rounded-lg transition-all duration-200"
      >
        {generating ? (
          <>
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>Gerando questões...</span>
          </>
        ) : (
          <>
            <Sparkles className="w-5 h-5" />
            <span>Gerar {params.quantidade} Questões</span>
          </>
        )}
      </button>
      
      {/* Rate Limit Status */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        Requisições restantes: {rateLimit.remainingRequests}/10 por minuto
        {rateLimit.resetTime && (
          <span className="ml-2">
            • Reset em {rateLimit.formatTimeUntilReset()}
          </span>
        )}
      </div>
    </motion.div>
  )
}

export default GenerationForm
