import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, <PERSON>, DollarSign, Database, Zap } from 'lucide-react'

interface GenerationInfoProps {
  generationInfo: {
    provider?: string
    tokensUsed?: number
    cost?: number
    cached?: boolean
  }
  isVisible: boolean
}

const GenerationInfo: React.FC<GenerationInfoProps> = ({ generationInfo, isVisible }) => {
  if (!isVisible || !generationInfo.provider) return null

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return '🤖'
      case 'anthropic':
        return '🧠'
      case 'claude-sonnet-4':
        return '🧠✨'
      case 'google':
        return '🔍'
      case 'gemini-2-5-pro':
        return '🔍🚀'
      case 'gemini-2-5-flash':
        return '🔍⚡'
      case 'cache':
        return '💾'
      default:
        return '🤖'
    }
  }

  const getProviderName = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return 'OpenAI GPT-4o'
      case 'anthropic':
        return 'Claude 3.5 Sonnet'
      case 'claude-sonnet-4':
        return 'Claude Sonnet 4 (2025)'
      case 'google':
        return 'Gemini 1.5 Pro'
      case 'gemini-2-5-pro':
        return 'Gemini 2.5 Pro (2025)'
      case 'gemini-2-5-flash':
        return 'Gemini 2.5 Flash (2025)'
      case 'cache':
        return 'Cache'
      default:
        return provider
    }
  }

  const formatCost = (cost?: number) => {
    if (!cost || cost === 0) return 'Gratuito'
    return `$${cost.toFixed(6)}`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 sm:p-6"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{getProviderIcon(generationInfo.provider)}</span>
            <div>
              <h3 className="font-semibold text-gray-900">
                {generationInfo.cached ? 'Recuperado do Cache' : 'Gerado com IA'}
              </h3>
              <p className="text-sm text-gray-600">
                {getProviderName(generationInfo.provider)}
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-6">
          {generationInfo.cached ? (
            <div className="flex items-center space-x-2 text-green-600">
              <Database className="w-4 h-4" />
              <span className="text-sm font-medium">Cache Hit</span>
            </div>
          ) : (
            <>
              {generationInfo.tokensUsed && generationInfo.tokensUsed > 0 && (
                <div className="flex items-center space-x-2 text-blue-600">
                  <Zap className="w-4 h-4" />
                  <span className="text-sm">
                    {generationInfo.tokensUsed.toLocaleString()} tokens
                  </span>
                </div>
              )}
              
              <div className="flex items-center space-x-2 text-green-600">
                <DollarSign className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {formatCost(generationInfo.cost)}
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      {generationInfo.cached && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-green-600" />
            <p className="text-sm text-green-700">
              Questões similares foram encontradas no cache, economizando tempo e recursos.
            </p>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default GenerationInfo
