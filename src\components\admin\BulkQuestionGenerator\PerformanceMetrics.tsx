import React from 'react'
import { motion } from 'framer-motion'
import { Clock, Zap, TrendingUp, Activity } from 'lucide-react'

interface PerformanceMetricsProps {
  metrics: {
    startTime?: number
    endTime?: number
    duration?: number
    questionsPerSecond?: number
  }
  questionsGenerated: number
  isVisible: boolean
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  metrics,
  questionsGenerated,
  isVisible
}) => {
  if (!isVisible || !metrics.duration) return null

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  const getEfficiencyScore = (): number => {
    if (!metrics.questionsPerSecond) return 0
    // Score baseado em questões por segundo (máximo 100)
    return Math.min(100, Math.round(metrics.questionsPerSecond * 10))
  }

  const getEfficiencyColor = (score: number): string => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const efficiencyScore = getEfficiencyScore()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 sm:p-6"
    >
      <div className="flex items-center space-x-2 mb-3">
        <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
          Métricas de Performance
        </h4>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Duração */}
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
            <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Duração</p>
            <p className="text-sm font-semibold text-gray-900 dark:text-white">
              {formatDuration(metrics.duration)}
            </p>
          </div>
        </div>

        {/* Velocidade */}
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
            <Zap className="w-4 h-4 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Velocidade</p>
            <p className="text-sm font-semibold text-gray-900 dark:text-white">
              {metrics.questionsPerSecond?.toFixed(1)}/s
            </p>
          </div>
        </div>

        {/* Questões */}
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
            <TrendingUp className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Questões</p>
            <p className="text-sm font-semibold text-gray-900 dark:text-white">
              {questionsGenerated}
            </p>
          </div>
        </div>

        {/* Eficiência */}
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/50 rounded-lg">
            <Activity className="w-4 h-4 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Eficiência</p>
            <p className={`text-sm font-semibold ${getEfficiencyColor(efficiencyScore)}`}>
              {efficiencyScore}%
            </p>
          </div>
        </div>
      </div>

      {/* Barra de eficiência */}
      <div className="mt-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Score de Eficiência
          </span>
          <span className={`text-xs font-medium ${getEfficiencyColor(efficiencyScore)}`}>
            {efficiencyScore}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${efficiencyScore}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-2 rounded-full ${
              efficiencyScore >= 80 
                ? 'bg-green-500' 
                : efficiencyScore >= 60 
                ? 'bg-yellow-500' 
                : 'bg-red-500'
            }`}
          />
        </div>
      </div>

      {/* Dicas de performance */}
      {efficiencyScore < 60 && (
        <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <p className="text-xs text-yellow-800 dark:text-yellow-200">
            💡 <strong>Dica:</strong> Para melhor performance, tente gerar menos questões por vez ou simplifique os parâmetros.
          </p>
        </div>
      )}
    </motion.div>
  )
}

export default PerformanceMetrics
