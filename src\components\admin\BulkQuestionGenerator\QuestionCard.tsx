import React from 'react'
import { Check, X, Refresh<PERSON>w, FileText, AlertTriangle } from 'lucide-react'
import { motion } from 'framer-motion'
import { GeneratedQuestion } from './QuestionsList'

interface QuestionCardProps {
  question: GeneratedQuestion
  index: number
  onAccept: () => void
  onReject: () => void
  onRegenerate: () => void
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  index,
  onAccept,
  onReject,
  onRegenerate
}) => {
  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  // Função para obter texto do status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendente'
      case 'accepted':
        return 'Aceita'
      case 'rejected':
        return 'Rejeitada'
      default:
        return 'Desconhecido'
    }
  }

  // Função para obter cor da dificuldade
  const getDifficultyColor = (dificuldade: string) => {
    switch (dificuldade) {
      case 'Fácil':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'Médio':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'Difícil':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ scale: 1.01 }}
      className={`bg-white dark:bg-gray-800 rounded-xl border p-4 sm:p-6 space-y-3 sm:space-y-4 transition-all duration-200 ${
        question.status === 'accepted'
          ? 'border-green-200 dark:border-green-800 shadow-green-100 dark:shadow-green-900/20'
          : question.status === 'rejected'
          ? 'border-red-200 dark:border-red-800 shadow-red-100 dark:shadow-red-900/20'
          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
      } hover:shadow-lg`}
    >
      {/* Header da questão */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
            <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Questão #{index + 1}
            </h4>
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <span>{question.disciplina}</span>
              <span>•</span>
              <span>{question.serie}</span>
              <span>•</span>
              <span>{question.topico}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Badge de dificuldade */}
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.dificuldade)}`}>
            {question.dificuldade}
          </span>
          
          {/* Badge de status */}
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(question.status)}`}>
            {getStatusText(question.status)}
          </span>
        </div>
      </div>

      {/* Tipo de questão */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">Tipo:</span>{' '}
        {question.tipo === 'multipla_escolha' && 'Múltipla Escolha'}
        {question.tipo === 'dissertativa' && 'Dissertativa'}
        {question.tipo === 'verdadeiro_falso' && 'Verdadeiro/Falso'}
      </div>

      {/* Enunciado */}
      <div className="space-y-2">
        <h5 className="font-medium text-gray-900 dark:text-white">Enunciado:</h5>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {question.enunciado}
        </p>
      </div>

      {/* Alternativas (se múltipla escolha) */}
      {question.tipo === 'multipla_escolha' && question.alternativas && (
        <div className="space-y-2">
          <h5 className="font-medium text-gray-900 dark:text-white">Alternativas:</h5>
          <div className="space-y-1">
            {question.alternativas.map((alternativa, idx) => (
              <div
                key={idx}
                className={`p-2 rounded-lg text-sm ${
                  String.fromCharCode(97 + idx) === question.resposta_correta
                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                    : 'bg-gray-50 dark:bg-gray-700/50'
                }`}
              >
                <span className="font-medium">
                  {String.fromCharCode(97 + idx)})
                </span>{' '}
                {alternativa}
                {String.fromCharCode(97 + idx) === question.resposta_correta && (
                  <span className="ml-2 text-green-600 dark:text-green-400 text-xs font-medium">
                    (Correta)
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Resposta correta (se não for múltipla escolha) */}
      {question.tipo !== 'multipla_escolha' && (
        <div className="space-y-2">
          <h5 className="font-medium text-gray-900 dark:text-white">Resposta Correta:</h5>
          <p className="text-gray-700 dark:text-gray-300 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
            {question.resposta_correta}
          </p>
        </div>
      )}

      {/* Explicação */}
      <div className="space-y-2">
        <h5 className="font-medium text-gray-900 dark:text-white">Explicação:</h5>
        <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
          {question.explicacao}
        </p>
      </div>

      {/* Competência BNCC */}
      {question.competencia_bncc && (
        <div className="space-y-2">
          <h5 className="font-medium text-gray-900 dark:text-white">Competência BNCC:</h5>
          <p className="text-gray-700 dark:text-gray-300 text-sm">
            {question.competencia_bncc}
          </p>
        </div>
      )}

      {/* Confidence score */}
      {question.confidence && (
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-600 dark:text-gray-400">Confiança da IA:</span>
          <div className="flex items-center space-x-1">
            <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full ${
                  question.confidence >= 0.8
                    ? 'bg-green-500'
                    : question.confidence >= 0.6
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
                style={{ width: `${question.confidence * 100}%` }}
              />
            </div>
            <span className="text-xs font-medium">
              {Math.round(question.confidence * 100)}%
            </span>
          </div>
        </div>
      )}

      {/* Ações */}
      {question.status === 'pending' && (
        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <motion.button
              onClick={onRegenerate}
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
              title="Regenerar questão"
            >
              <RefreshCw className="w-4 h-4" />
            </motion.button>
          </div>
          
          <div className="flex items-center space-x-2">
            <motion.button
              onClick={onAccept}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <Check className="w-4 h-4" />
              <span>Aceitar</span>
            </motion.button>

            <motion.button
              onClick={onReject}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <X className="w-4 h-4" />
              <span>Rejeitar</span>
            </motion.button>
          </div>
        </div>
      )}

      {/* Aviso de baixa confiança */}
      {question.confidence && question.confidence < 0.6 && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <span className="font-medium">Atenção:</span> Esta questão tem baixa confiança da IA. 
              Recomenda-se revisão cuidadosa antes da aprovação.
            </p>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default QuestionCard
