import React from 'react'
import { Search, Filter, Check, X, Download } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import QuestionCard from './QuestionCard'

export interface GeneratedQuestion {
  id: string
  disciplina: string
  serie: string
  topico: string
  subtopico?: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  competencia_bncc?: string
  enunciado: string
  alternativas?: string[]
  resposta_correta: string
  explicacao: string
  confidence?: number
  status: 'pending' | 'accepted' | 'rejected'
}

interface QuestionsListProps {
  questions: GeneratedQuestion[]
  searchTerm: string
  setSearchTerm: (term: string) => void
  filterBy: string
  setFilterBy: (filter: string) => void
  onAcceptAll: () => void
  onRejectAll: () => void
  onAcceptQuestion: (question: GeneratedQuestion) => void
  onRejectQuestion: (questionId: string) => void
  onRegenerateQuestion: (questionId: string) => void
  onExportQuestions: () => void
}

const QuestionsList: React.FC<QuestionsListProps> = ({
  questions,
  searchTerm,
  setSearchTerm,
  filterBy,
  setFilterBy,
  onAcceptAll,
  onRejectAll,
  onAcceptQuestion,
  onRejectQuestion,
  onRegenerateQuestion,
  onExportQuestions
}) => {
  // Filtrar questões baseado na busca e filtro
  const filteredQuestions = questions.filter(question => {
    const matchesSearch = question.enunciado.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.disciplina.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.topico.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterBy === 'all' || question.status === filterBy
    
    return matchesSearch && matchesFilter
  })

  // Contar questões por status
  const pendingCount = questions.filter(q => q.status === 'pending').length
  const acceptedCount = questions.filter(q => q.status === 'accepted').length
  const rejectedCount = questions.filter(q => q.status === 'rejected').length

  if (questions.length === 0) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="space-y-4"
      >
        {/* Header com controles */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Questões Geradas ({questions.length})
              </h3>
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-yellow-600 dark:text-yellow-400">
                  Pendentes: {pendingCount}
                </span>
                <span className="text-green-600 dark:text-green-400">
                  Aceitas: {acceptedCount}
                </span>
                <span className="text-red-600 dark:text-red-400">
                  Rejeitadas: {rejectedCount}
                </span>
              </div>
            </div>
            
            <div className="flex flex-wrap items-center gap-2">
              {pendingCount > 0 && (
                <>
                  <button
                    onClick={onAcceptAll}
                    className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                  >
                    <Check className="w-4 h-4" />
                    <span>Aceitar Todas</span>
                  </button>
                  
                  <button
                    onClick={onRejectAll}
                    className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                  >
                    <X className="w-4 h-4" />
                    <span>Rejeitar Todas</span>
                  </button>
                </>
              )}
              
              {acceptedCount > 0 && (
                <button
                  onClick={onExportQuestions}
                  className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Exportar Aceitas</span>
                </button>
              )}
            </div>
          </div>

          {/* Controles de busca e filtro */}
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar questões..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white appearance-none"
              >
                <option value="all">Todas</option>
                <option value="pending">Pendentes</option>
                <option value="accepted">Aceitas</option>
                <option value="rejected">Rejeitadas</option>
              </select>
            </div>
          </div>
        </div>

        {/* Lista de questões */}
        <div className="space-y-4">
          {filteredQuestions.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || filterBy !== 'all' 
                  ? 'Nenhuma questão encontrada com os filtros aplicados.'
                  : 'Nenhuma questão gerada ainda.'
                }
              </p>
            </div>
          ) : (
            filteredQuestions.map((question, index) => (
              <QuestionCard
                key={question.id}
                question={question}
                index={index}
                onAccept={() => onAcceptQuestion(question)}
                onReject={() => onRejectQuestion(question.id)}
                onRegenerate={() => onRegenerateQuestion(question.id)}
              />
            ))
          )}
        </div>

        {/* Resumo no final */}
        {filteredQuestions.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
              Mostrando {filteredQuestions.length} de {questions.length} questões
              {(searchTerm || filterBy !== 'all') && (
                <span className="ml-2">
                  • Filtros aplicados: 
                  {searchTerm && ` busca="${searchTerm}"`}
                  {filterBy !== 'all' && ` status="${filterBy}"`}
                </span>
              )}
            </div>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default QuestionsList
