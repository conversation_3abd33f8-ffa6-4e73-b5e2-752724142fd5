import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Save, Plus, Trash2, Search } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database'
import { useQuestions } from '../../hooks/useQuestions'
import toast from 'react-hot-toast'

type Assessment = Database['public']['Tables']['assessments']['Row']
type AssessmentUpdate = Database['public']['Tables']['assessments']['Update']
type Question = Database['public']['Tables']['questions']['Row']

const assessmentSchema = z.object({
  titulo: z.string().min(1, 'Título é obrigatório'),
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  is_public: z.boolean().default(false)
})

type AssessmentFormData = z.infer<typeof assessmentSchema>

interface EditAssessmentModalProps {
  isOpen: boolean
  onClose: () => void
  assessment: Assessment | null
  onSave: (assessmentId: string, updates: AssessmentUpdate) => Promise<void>
  isSaving: boolean
}

const DISCIPLINAS = ['Matemática', 'Português', 'Ciências', 'História', 'Geografia', 'Física', 'Química', 'Biologia', 'Inglês', 'Espanhol', 'Filosofia', 'Sociologia']
const SERIES = ['6º Ano', '7º Ano', '8º Ano', '9º Ano', '1º Médio', '2º Médio', '3º Médio']

const EditAssessmentModal: React.FC<EditAssessmentModalProps> = ({
  isOpen,
  onClose,
  assessment,
  onSave,
  isSaving
}) => {
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const { questions, isLoading: questionsLoading } = useQuestions({ 
    search: searchTerm,
    status: 'approved' // Apenas questões aprovadas
  })

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<AssessmentFormData>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      titulo: '',
      disciplina: '',
      serie: '',
      is_public: false
    }
  })

  // Reset form and load questions when assessment changes
  useEffect(() => {
    if (assessment) {
      reset({
        titulo: assessment.titulo,
        disciplina: assessment.disciplina,
        serie: assessment.serie,
        is_public: assessment.is_public
      })

      // Load selected questions
      const loadSelectedQuestions = async () => {
        if (!assessment.questoes_ids || assessment.questoes_ids.length === 0) {
          setSelectedQuestions([])
          return
        }

        try {
          const { data, error } = await supabase
            .from('questions')
            .select('*')
            .in('id', assessment.questoes_ids)

          if (error) throw error
          setSelectedQuestions(data || [])
        } catch (error) {
          console.error('Error loading selected questions:', error)
          toast.error('Erro ao carregar questões da avaliação')
        }
      }

      loadSelectedQuestions()
    }
  }, [assessment, reset])

  const onSubmit = async (data: AssessmentFormData) => {
    if (!assessment) return

    try {
      const assessmentData: AssessmentUpdate = {
        ...data,
        questoes_ids: selectedQuestions.map(q => q.id)
      }

      await onSave(assessment.id, assessmentData)
      onClose()
    } catch (error) {
      console.error('Error saving assessment:', error)
      toast.error('Erro ao salvar avaliação')
    }
  }

  const addQuestion = (question: Question) => {
    if (!selectedQuestions.find(q => q.id === question.id)) {
      setSelectedQuestions([...selectedQuestions, question])
    }
  }

  const removeQuestion = (questionId: string) => {
    setSelectedQuestions(selectedQuestions.filter(q => q.id !== questionId))
  }

  const availableQuestions = questions.filter(
    q => !selectedQuestions.find(sq => sq.id === q.id)
  )

  if (!isOpen || !assessment) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Editar Avaliação</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="flex h-[calc(90vh-80px)]">
            {/* Form */}
            <div className="w-1/2 p-6 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Título *
                  </label>
                  <input
                    {...register('titulo')}
                    type="text"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Título da avaliação"
                  />
                  {errors.titulo && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.titulo.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Disciplina *
                    </label>
                    <select
                      {...register('disciplina')}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Selecione...</option>
                      {DISCIPLINAS.map((disciplina) => (
                        <option key={disciplina} value={disciplina}>
                          {disciplina}
                        </option>
                      ))}
                    </select>
                    {errors.disciplina && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.disciplina.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Série *
                    </label>
                    <select
                      {...register('serie')}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Selecione...</option>
                      {SERIES.map((serie) => (
                        <option key={serie} value={serie}>
                          {serie}
                        </option>
                      ))}
                    </select>
                    {errors.serie && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.serie.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      {...register('is_public')}
                      className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                    />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Avaliação Pública
                    </span>
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                    Avaliações públicas são visíveis para todos os usuários.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Questões Selecionadas ({selectedQuestions.length})
                  </h3>
                  
                  {selectedQuestions.length === 0 ? (
                    <div className="text-center py-8 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400">
                        Nenhuma questão selecionada. Adicione questões da lista à direita.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedQuestions.map((question, index) => (
                        <div 
                          key={question.id} 
                          className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                        >
                          <div className="flex-shrink-0 mt-1">
                            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-sm font-medium">
                              {index + 1}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-900 dark:text-white font-medium line-clamp-1">
                              {question.enunciado}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {question.disciplina} • {question.serie} • {question.dificuldade}
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeQuestion(question.id)}
                            className="p-1 text-red-500 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:disabled:bg-blue-800/50 text-white rounded-lg transition-colors"
                  >
                    {isSaving ? (
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save className="w-5 h-5" />
                    )}
                    <span>Salvar Avaliação</span>
                  </button>
                </div>
              </form>
            </div>

            {/* Question Bank */}
            <div className="w-1/2 p-6 overflow-y-auto">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Banco de Questões
                </h3>
                
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Buscar questões..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {questionsLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="w-8 h-8 border-4 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : availableQuestions.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <p className="text-gray-500 dark:text-gray-400">
                    Nenhuma questão disponível para adicionar.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {availableQuestions.map((question) => (
                    <div 
                      key={question.id} 
                      className="flex items-start space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 dark:text-white font-medium line-clamp-2">
                          {question.enunciado}
                        </p>
                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          <span className="text-xs px-2 py-0.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 rounded-full">
                            {question.disciplina}
                          </span>
                          <span className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-full">
                            {question.serie}
                          </span>
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                            question.dificuldade === 'Fácil' ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300' :
                            question.dificuldade === 'Médio' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300' :
                            'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300'
                          }`}>
                            {question.dificuldade}
                          </span>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => addQuestion(question)}
                        className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default EditAssessmentModal