import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Save, Plus, Layout, Crown } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'

type Template = Database['public']['Tables']['templates']['Row']
type TemplateUpdate = Database['public']['Tables']['templates']['Update']

const templateSchema = z.object({
  nome: z.string().min(1, 'Nome é obrigatório'),
  categoria: z.string().min(1, 'Categoria é obrigatória'),
  preview_image: z.string().optional(),
  is_premium: z.boolean().default(false),
  is_system: z.boolean().default(false),
  tags: z.array(z.string()).default([])
})

type TemplateFormData = z.infer<typeof templateSchema>

interface EditTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: Template | null
  onSave: (templateId: string, updates: TemplateUpdate) => Promise<void>
  isSaving: boolean
}

const CATEGORIAS = ['Padrão', 'Premium', 'Design', 'Escolar', 'Minimalista', 'Colorido']

const EditTemplateModal: React.FC<EditTemplateModalProps> = ({
  isOpen,
  onClose,
  template,
  onSave,
  isSaving
}) => {
  const [newTag, setNewTag] = useState('')
  const [layoutConfig, setLayoutConfig] = useState<Record<string, any>>({})

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      nome: '',
      categoria: '',
      preview_image: '',
      is_premium: false,
      is_system: false,
      tags: []
    }
  })

  const watchedTags = watch('tags')

  // Reset form when template changes
  useEffect(() => {
    if (template) {
      reset({
        nome: template.nome,
        categoria: template.categoria,
        preview_image: template.preview_image || '',
        is_premium: template.is_premium,
        is_system: template.is_system,
        tags: template.tags || []
      })
      setLayoutConfig(template.layout_config || {})
    }
  }, [template, reset])

  const onSubmit = async (data: TemplateFormData) => {
    if (!template) return

    try {
      const templateData: TemplateUpdate = {
        ...data,
        preview_image: data.preview_image || null,
        layout_config: layoutConfig
      }

      await onSave(template.id, templateData)
      onClose()
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Erro ao salvar template')
    }
  }

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      setValue('tags', [...watchedTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setValue('tags', watchedTags.filter((_, i) => i !== index))
  }

  const updateLayoutConfig = (key: string, value: any) => {
    setLayoutConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  if (!isOpen || !template) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Editar Template</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nome *
                </label>
                <input
                  {...register('nome')}
                  type="text"
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Nome do template"
                />
                {errors.nome && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.nome.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Categoria *
                </label>
                <select
                  {...register('categoria')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Selecione...</option>
                  {CATEGORIAS.map((categoria) => (
                    <option key={categoria} value={categoria}>
                      {categoria}
                    </option>
                  ))}
                </select>
                {errors.categoria && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.categoria.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                URL da Imagem de Preview
              </label>
              <input
                {...register('preview_image')}
                type="text"
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="https://exemplo.com/imagem.jpg"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('is_premium')}
                    className="rounded text-yellow-600 focus:ring-yellow-500 dark:bg-gray-600 dark:border-gray-500"
                  />
                  <div className="flex items-center space-x-2">
                    <Crown className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Template Premium
                    </span>
                  </div>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                  Templates premium são exclusivos para assinantes.
                </p>
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('is_system')}
                    className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                  />
                  <div className="flex items-center space-x-2">
                    <Layout className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Template do Sistema
                    </span>
                  </div>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                  Templates do sistema não podem ser excluídos pelos usuários.
                </p>
              </div>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {watchedTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full text-sm"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Digite uma tag e pressione Enter"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Layout Configuration */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Configuração de Layout
              </h3>
              
              <div className="space-y-4 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Cabeçalho - Nome da Escola
                  </label>
                  <input
                    type="text"
                    value={(layoutConfig.cabecalho?.nomeEscola || '')}
                    onChange={(e) => updateLayoutConfig('cabecalho', {
                      ...layoutConfig.cabecalho,
                      nomeEscola: e.target.value
                    })}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Nome da Escola"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Cabeçalho - Nome da Prova
                  </label>
                  <input
                    type="text"
                    value={(layoutConfig.cabecalho?.nomeProva || '')}
                    onChange={(e) => updateLayoutConfig('cabecalho', {
                      ...layoutConfig.cabecalho,
                      nomeProva: e.target.value
                    })}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Avaliação"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Cabeçalho - Instruções
                  </label>
                  <textarea
                    value={(layoutConfig.cabecalho?.instrucoes || '')}
                    onChange={(e) => updateLayoutConfig('cabecalho', {
                      ...layoutConfig.cabecalho,
                      instrucoes: e.target.value
                    })}
                    rows={2}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Instruções para os alunos"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Espaçamento
                    </label>
                    <select
                      value={(layoutConfig.espacamento || 'normal')}
                      onChange={(e) => updateLayoutConfig('espacamento', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="compacto">Compacto</option>
                      <option value="normal">Normal</option>
                      <option value="expandido">Expandido</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Numeração
                    </label>
                    <select
                      value={(layoutConfig.numeracao || 'automatica')}
                      onChange={(e) => updateLayoutConfig('numeracao', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="automatica">Automática</option>
                      <option value="manual">Manual</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={layoutConfig.quebrasPagina || false}
                        onChange={(e) => updateLayoutConfig('quebrasPagina', e.target.checked)}
                        className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                      />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Quebras de Página
                      </span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={layoutConfig.folhaRespostas || false}
                        onChange={(e) => updateLayoutConfig('folhaRespostas', e.target.checked)}
                        className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                      />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Folha de Respostas
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:disabled:bg-blue-800/50 text-white rounded-lg transition-colors"
              >
                {isSaving ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="w-5 h-5" />
                )}
                <span>Salvar Template</span>
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default EditTemplateModal