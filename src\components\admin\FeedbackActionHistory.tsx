import React from 'react'
import { motion } from 'framer-motion'
import {
  CheckCircle,
  XCircle,
  Flag,
  MessageCircle,
  User,
  Calendar,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { useFeedbackActions } from '../../hooks/useQuestionFeedback'
import { Database } from '../../types/database'

type FeedbackAction = Database['public']['Tables']['feedback_actions']['Row'] & {
  profiles: { nome: string; email: string } | null
}

interface FeedbackActionHistoryProps {
  feedbackId?: string
  limit?: number
  showFeedbackInfo?: boolean
}

const FeedbackActionHistory: React.FC<FeedbackActionHistoryProps> = ({
  feedbackId,
  limit = 50,
  showFeedbackInfo = false
}) => {
  const { data: actions = [], isLoading, error } = useFeedbackActions(feedbackId)

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
      case 'flagged':
        return <Flag className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
      case 'responded':
        return <MessageCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
    }
  }

  const getActionLabel = (actionType: string) => {
    switch (actionType) {
      case 'approved':
        return 'Aprovado'
      case 'rejected':
        return 'Rejeitado'
      case 'flagged':
        return 'Sinalizado'
      case 'responded':
        return 'Respondido'
      default:
        return actionType
    }
  }

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'approved':
        return 'text-green-700 dark:text-green-300'
      case 'rejected':
        return 'text-red-700 dark:text-red-300'
      case 'flagged':
        return 'text-yellow-700 dark:text-yellow-300'
      case 'responded':
        return 'text-blue-700 dark:text-blue-300'
      default:
        return 'text-gray-700 dark:text-gray-300'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-600 dark:text-red-400">
          Erro ao carregar histórico de ações
        </p>
      </div>
    )
  }

  if (actions.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Nenhuma ação registrada
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          {feedbackId 
            ? 'Não há ações administrativas registradas para este feedback.'
            : 'Não há ações administrativas registradas no sistema.'
          }
        </p>
      </div>
    )
  }

  const displayActions = limit ? actions.slice(0, limit) : actions

  return (
    <div className="space-y-4">
      {!feedbackId && (
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Histórico de Ações Administrativas
          </h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {actions.length} ação(ões) registrada(s)
          </span>
        </div>
      )}

      <div className="space-y-3">
        {displayActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          >
            <div className="flex items-start space-x-3">
              {/* Action Icon */}
              <div className="flex-shrink-0 mt-1">
                {getActionIcon(action.action_type)}
              </div>

              {/* Action Content */}
              <div className="flex-1 min-w-0">
                {/* Header */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className={`font-medium ${getActionColor(action.action_type)}`}>
                      {getActionLabel(action.action_type)}
                    </span>
                    {action.reason && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        • {action.reason}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(action.created_at).toLocaleString('pt-BR')}
                  </span>
                </div>

                {/* Admin Info */}
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <User className="w-3 h-3" />
                  <span>{action.profiles?.nome || 'Administrador'}</span>
                  {action.profiles?.email && (
                    <>
                      <span className="text-gray-400">•</span>
                      <span className="text-xs">{action.profiles.email}</span>
                    </>
                  )}
                </div>

                {/* Status Changes */}
                {(action.previous_status || action.new_status) && (
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded p-3 mb-3">
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Mudanças de Status:
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                      {action.previous_status && (
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Anterior:</span>
                          <div className="mt-1">
                            {Object.entries(action.previous_status as Record<string, any>).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span className="capitalize">{key.replace('_', ' ')}:</span>
                                <span className={value ? 'text-green-600' : 'text-red-600'}>
                                  {value?.toString() || 'false'}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      {action.new_status && (
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Novo:</span>
                          <div className="mt-1">
                            {Object.entries(action.new_status as Record<string, any>).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span className="capitalize">{key.replace('_', ' ')}:</span>
                                <span className={value ? 'text-green-600' : 'text-red-600'}>
                                  {value?.toString() || 'false'}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {action.notes && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-3">
                    <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">
                      Observações:
                    </div>
                    <p className="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap">
                      {action.notes}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Show More Button */}
      {limit && actions.length > limit && (
        <div className="text-center pt-4">
          <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
            Ver mais {actions.length - limit} ação(ões)
          </button>
        </div>
      )}
    </div>
  )
}

export default FeedbackActionHistory
