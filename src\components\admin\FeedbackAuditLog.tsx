import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Calendar,
  Filter,
  Download,
  Search,
  AlertTriangle,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  Flag,
  MessageCircle
} from 'lucide-react'
import { useFeedbackActions, useFeedbackStats } from '../../hooks/useQuestionFeedback'
import { useAuth } from '../../contexts/AuthContext'
import FeedbackActionHistory from './FeedbackActionHistory'
import FeedbackStatsCard from './FeedbackStatsCard'

interface AuditFilters {
  dateRange: string
  actionType: string
  adminId: string
}

const FeedbackAuditLog: React.FC = () => {
  const { isAdmin } = useAuth()
  const [filters, setFilters] = useState<AuditFilters>({
    dateRange: '',
    actionType: '',
    adminId: ''
  })

  const { data: actions = [], isLoading } = useFeedbackActions()
  const { data: stats } = useFeedbackStats()

  // Calculate audit statistics
  const auditStats = React.useMemo(() => {
    const totalActions = actions.length
    const approvedActions = actions.filter(a => a.action_type === 'approved').length
    const rejectedActions = actions.filter(a => a.action_type === 'rejected').length
    const flaggedActions = actions.filter(a => a.action_type === 'flagged').length
    const respondedActions = actions.filter(a => a.action_type === 'responded').length

    // Get unique admins
    const uniqueAdmins = new Set(actions.map(a => a.admin_id)).size

    // Actions in last 24 hours
    const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000)
    const recentActions = actions.filter(a => new Date(a.created_at) > last24h).length

    return {
      totalActions,
      approvedActions,
      rejectedActions,
      flaggedActions,
      respondedActions,
      uniqueAdmins,
      recentActions
    }
  }, [actions])

  const exportAuditLog = () => {
    const csvContent = [
      ['Data', 'Ação', 'Administrador', 'Email', 'Observações'].join(','),
      ...actions.map(action => [
        new Date(action.created_at).toLocaleString('pt-BR'),
        action.action_type,
        action.profiles?.nome || 'N/A',
        action.profiles?.email || 'N/A',
        (action.notes || '').replace(/,/g, ';')
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `feedback-audit-log-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Acesso Negado
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Você precisa ter permissões de administrador para acessar esta página.
        </p>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Log de Auditoria - Feedbacks
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Histórico completo de ações administrativas em feedbacks
          </p>
        </div>
        
        <button
          onClick={exportAuditLog}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Download className="w-4 h-4" />
          <span>Exportar CSV</span>
        </button>
      </div>

      {/* Audit Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <FeedbackStatsCard
          title="Total de Ações"
          value={auditStats.totalActions}
          icon={Shield}
          color="blue"
        />
        <FeedbackStatsCard
          title="Aprovações"
          value={auditStats.approvedActions}
          icon={CheckCircle}
          color="green"
        />
        <FeedbackStatsCard
          title="Rejeições"
          value={auditStats.rejectedActions}
          icon={XCircle}
          color="red"
        />
        <FeedbackStatsCard
          title="Últimas 24h"
          value={auditStats.recentActions}
          icon={Clock}
          color="purple"
        />
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Administradores Ativos
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {auditStats.uniqueAdmins}
              </p>
            </div>
            <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
              <Shield className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Sinalizações
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {auditStats.flaggedActions}
              </p>
            </div>
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <Flag className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Respostas
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {auditStats.respondedActions}
              </p>
            </div>
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <MessageCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Filtros
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Período
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todos os períodos</option>
              <option value="today">Hoje</option>
              <option value="week">Última semana</option>
              <option value="month">Último mês</option>
              <option value="quarter">Último trimestre</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tipo de Ação
            </label>
            <select
              value={filters.actionType}
              onChange={(e) => setFilters(prev => ({ ...prev, actionType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todas as ações</option>
              <option value="approved">Aprovações</option>
              <option value="rejected">Rejeições</option>
              <option value="flagged">Sinalizações</option>
              <option value="responded">Respostas</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Administrador
            </label>
            <select
              value={filters.adminId}
              onChange={(e) => setFilters(prev => ({ ...prev, adminId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todos os administradores</option>
              {Array.from(new Set(actions.map(a => a.profiles?.nome).filter(Boolean))).map(name => (
                <option key={name} value={name}>
                  {name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Action History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <FeedbackActionHistory />
      </div>
    </div>
  )
}

export default FeedbackAuditLog
