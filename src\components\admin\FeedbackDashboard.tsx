import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Calendar,
  Filter,
  Download,
  AlertTriangle,
  Star,
  MessageSquare,
  Users,
  FileText,
  Clock
} from 'lucide-react'
import { useQuestionFeedback, useFeedbackStats } from '../../hooks/useQuestionFeedback'
import { useAuth } from '../../contexts/AuthContext'
import FeedbackStatsCard from './FeedbackStatsCard'

interface DashboardFilters {
  period: 'week' | 'month' | 'quarter' | 'year'
  startDate: string
  endDate: string
}

const FeedbackDashboard: React.FC = () => {
  const { isAdmin } = useAuth()
  const [filters, setFilters] = useState<DashboardFilters>({
    period: 'month',
    startDate: '',
    endDate: ''
  })

  // Calculate date range based on period
  const dateRange = useMemo(() => {
    const now = new Date()
    let startDate: Date
    
    switch (filters.period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        break
      case 'quarter':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
        break
      case 'year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    return {
      start: filters.startDate || startDate.toISOString(),
      end: filters.endDate || now.toISOString()
    }
  }, [filters])

  const { data: stats, isLoading: statsLoading } = useFeedbackStats(
    dateRange.start,
    dateRange.end
  )

  const { feedback, isLoading: feedbackLoading } = useQuestionFeedback({
    limit: 1000 // Get more data for analysis
  })

  // Calculate advanced metrics
  const advancedMetrics = useMemo(() => {
    if (!feedback.length) return null

    // Filter feedback by date range
    const filteredFeedback = feedback.filter(f => {
      const feedbackDate = new Date(f.created_at)
      return feedbackDate >= new Date(dateRange.start) && feedbackDate <= new Date(dateRange.end)
    })

    // Response time analysis
    const responseTimes = filteredFeedback
      .filter(f => f.reviewed_at)
      .map(f => {
        const created = new Date(f.created_at)
        const reviewed = new Date(f.reviewed_at!)
        return (reviewed.getTime() - created.getTime()) / (1000 * 60 * 60) // hours
      })

    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0

    // Rating distribution
    const ratingDistribution = [1, 2, 3, 4, 5].map(rating => ({
      rating,
      count: filteredFeedback.filter(f => f.rating === rating).length,
      percentage: (filteredFeedback.filter(f => f.rating === rating).length / filteredFeedback.length) * 100
    }))

    // Most common suggestions
    const allSuggestions = filteredFeedback
      .flatMap(f => f.suggestions || [])
      .filter(Boolean)

    const suggestionCounts = allSuggestions.reduce((acc, suggestion) => {
      acc[suggestion] = (acc[suggestion] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const topSuggestions = Object.entries(suggestionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([suggestion, count]) => ({ suggestion, count }))

    // Questions with most feedback
    const questionFeedbackCounts = filteredFeedback.reduce((acc, f) => {
      acc[f.question_id] = (acc[f.question_id] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const topQuestions = Object.entries(questionFeedbackCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([questionId, count]) => {
        const feedbackItem = filteredFeedback.find(f => f.question_id === questionId)
        return {
          questionId,
          count,
          question: feedbackItem?.questions?.enunciado?.substring(0, 100) + '...' || 'Questão não encontrada',
          disciplina: feedbackItem?.questions?.disciplina || 'N/A'
        }
      })

    // Approval rate trend (last 7 days)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date.toISOString().split('T')[0]
    }).reverse()

    const approvalTrend = last7Days.map(date => {
      const dayFeedback = filteredFeedback.filter(f => 
        f.created_at.split('T')[0] === date
      )
      const approved = dayFeedback.filter(f => f.is_approved).length
      const total = dayFeedback.length
      return {
        date,
        approvalRate: total > 0 ? (approved / total) * 100 : 0,
        total
      }
    })

    return {
      avgResponseTime,
      ratingDistribution,
      topSuggestions,
      topQuestions,
      approvalTrend,
      totalInPeriod: filteredFeedback.length
    }
  }, [feedback, dateRange])

  const exportDashboardData = () => {
    if (!stats || !advancedMetrics) return

    const data = {
      period: filters.period,
      dateRange,
      stats,
      advancedMetrics
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `feedback-dashboard-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Acesso Negado
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Você precisa ter permissões de administrador para acessar esta página.
        </p>
      </div>
    )
  }

  if (statsLoading || feedbackLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard de Feedbacks
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Análise detalhada e métricas de feedback dos usuários
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={filters.period}
            onChange={(e) => setFilters(prev => ({ ...prev, period: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="week">Última semana</option>
            <option value="month">Último mês</option>
            <option value="quarter">Último trimestre</option>
            <option value="year">Último ano</option>
          </select>
          
          <button
            onClick={exportDashboardData}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
        </div>
      </div>

      {/* Main Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <FeedbackStatsCard
            title="Total de Feedbacks"
            value={stats.total_feedback}
            icon={MessageSquare}
            color="blue"
          />
          <FeedbackStatsCard
            title="Taxa de Aprovação"
            value={stats.total_feedback > 0 ? Math.round((stats.approved_feedback / stats.total_feedback) * 100) : 0}
            icon={TrendingUp}
            color="green"
            suffix="%"
          />
          <FeedbackStatsCard
            title="Avaliação Média"
            value={stats.avg_rating.toFixed(1)}
            icon={Star}
            color="yellow"
            suffix="/5"
          />
          <FeedbackStatsCard
            title="Pendentes"
            value={stats.pending_feedback}
            icon={Clock}
            color="orange"
          />
        </div>
      )}

      {/* Advanced Metrics */}
      {advancedMetrics && (
        <>
          {/* Response Time & Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Response Time */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Tempo Médio de Resposta
              </h3>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {advancedMetrics.avgResponseTime.toFixed(1)}h
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tempo médio para revisar feedbacks
                </p>
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Distribuição de Avaliações
              </h3>
              <div className="space-y-2">
                {advancedMetrics.ratingDistribution.map(({ rating, count, percentage }) => (
                  <div key={rating} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        {[...Array(rating)].map((_, i) => (
                          <Star key={i} className="w-3 h-3 text-yellow-500 fill-current" />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        ({count})
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-yellow-500 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-10">
                        {percentage.toFixed(0)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Top Issues & Questions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Suggestions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Principais Problemas Reportados
              </h3>
              <div className="space-y-3">
                {advancedMetrics.topSuggestions.map(({ suggestion, count }, index) => (
                  <div key={suggestion} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {suggestion}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {count}x
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Questions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Questões Mais Comentadas
              </h3>
              <div className="space-y-3">
                {advancedMetrics.topQuestions.map(({ questionId, count, question, disciplina }, index) => (
                  <div key={questionId} className="border-l-4 border-blue-500 pl-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {disciplina}
                      </span>
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                        {count} feedback(s)
                      </span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-white line-clamp-2">
                      {question}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default FeedbackDashboard
