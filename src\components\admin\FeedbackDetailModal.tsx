import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  X,
  Star,
  User,
  Calendar,
  MessageSquare,
  CheckCircle,
  XCircle,
  Trash2,
  Send,
  AlertTriangle,
  Clock,
  Eye
} from 'lucide-react'
import { Database } from '../../types/database'
import { getFeedbackStatus, getFeedbackTypeLabel } from '../../hooks/useQuestionFeedback'
import FeedbackActionHistory from './FeedbackActionHistory'

type QuestionFeedback = Database['public']['Tables']['question_feedback']['Row'] & {
  profiles: { nome: string; email: string } | null
  questions: { enunciado: string; disciplina: string; serie: string; topico: string } | null
}

interface FeedbackDetailModalProps {
  isOpen: boolean
  onClose: () => void
  feedback: QuestionFeedback
  onApprove: (id: string, response?: string) => void
  onReject: (id: string, response?: string) => void
  onDelete: (id: string) => void
  isLoading: boolean
}

const FeedbackDetailModal: React.FC<FeedbackDetailModalProps> = ({
  isOpen,
  onClose,
  feedback,
  onApprove,
  onReject,
  onDelete,
  isLoading
}) => {
  const [adminResponse, setAdminResponse] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const status = getFeedbackStatus(feedback)

  const handleApprove = () => {
    onApprove(feedback.id, adminResponse.trim() || undefined)
    setAdminResponse('')
  }

  const handleReject = () => {
    onReject(feedback.id, adminResponse.trim() || undefined)
    setAdminResponse('')
  }

  const handleDelete = () => {
    onDelete(feedback.id)
    setShowDeleteConfirm(false)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="inline-block w-full max-w-4xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Detalhes do Feedback
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      status.status === 'pending' 
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                        : status.status === 'approved'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                    }`}>
                      {status.label}
                    </span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                      {getFeedbackTypeLabel(feedback.feedback_type)}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
              {/* User Info */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Informações do Usuário
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {feedback.profiles?.nome || 'Usuário desconhecido'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(feedback.created_at).toLocaleString('pt-BR')}
                    </span>
                  </div>
                  <div className="md:col-span-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Email: {feedback.profiles?.email || 'Não disponível'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Question Info */}
              {feedback.questions && (
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Questão Avaliada
                  </h4>
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {feedback.questions.disciplina} • {feedback.questions.serie} • {feedback.questions.topico}
                    </div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {feedback.questions.enunciado}
                    </p>
                  </div>
                </div>
              )}

              {/* Rating */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Avaliação
                </h4>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-5 h-5 ${
                          star <= feedback.rating
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ({feedback.rating}/5)
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {feedback.rating === 1 && 'Muito ruim'}
                    {feedback.rating === 2 && 'Ruim'}
                    {feedback.rating === 3 && 'Regular'}
                    {feedback.rating === 4 && 'Bom'}
                    {feedback.rating === 5 && 'Excelente'}
                  </span>
                </div>
              </div>

              {/* Helpful */}
              {feedback.helpful !== null && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Questão foi útil?
                  </h4>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    feedback.helpful
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {feedback.helpful ? 'Sim' : 'Não'}
                  </span>
                </div>
              )}

              {/* Comment */}
              {feedback.comment && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Comentário
                  </h4>
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {feedback.comment}
                    </p>
                  </div>
                </div>
              )}

              {/* Suggestions */}
              {feedback.suggestions && feedback.suggestions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Sugestões de Melhoria
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {feedback.suggestions.map((suggestion, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                      >
                        {suggestion}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Existing Admin Response */}
              {feedback.admin_response && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Resposta da Equipe
                  </h4>
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <p className="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap">
                      {feedback.admin_response}
                    </p>
                    {feedback.reviewed_at && (
                      <div className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                        Respondido em {new Date(feedback.reviewed_at).toLocaleString('pt-BR')}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Admin Response Form (only if not reviewed or can edit) */}
              {(!feedback.is_reviewed || feedback.admin_response) && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {feedback.admin_response ? 'Editar Resposta' : 'Resposta da Equipe'}
                  </h4>
                  <textarea
                    value={adminResponse}
                    onChange={(e) => setAdminResponse(e.target.value)}
                    placeholder="Digite uma resposta para o usuário (opcional)..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                </div>
              )}

              {/* Action History */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Histórico de Ações
                </h4>
                <FeedbackActionHistory feedbackId={feedback.id} limit={5} />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="flex items-center space-x-2 px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Excluir</span>
                </button>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Fechar
                </button>

                {!feedback.is_reviewed && (
                  <>
                    <button
                      onClick={handleReject}
                      disabled={isLoading}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                    >
                      <XCircle className="w-4 h-4" />
                      <span>Rejeitar</span>
                    </button>

                    <button
                      onClick={handleApprove}
                      disabled={isLoading}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>Aprovar</span>
                    </button>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 z-60 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity" />
              
              <div className="inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
                <div className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="w-6 h-6 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        Confirmar Exclusão
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Esta ação não pode ser desfeita.
                      </p>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-6">
                    Tem certeza que deseja excluir este feedback? Todos os dados relacionados serão perdidos permanentemente.
                  </p>
                  
                  <div className="flex items-center justify-end space-x-3">
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={handleDelete}
                      disabled={isLoading}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                    >
                      Excluir
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AnimatePresence>
  )
}

export default FeedbackDetailModal
