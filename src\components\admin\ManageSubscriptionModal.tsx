import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Save, ExternalLink, CreditCard, Calendar, User } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'

type Subscription = Database['public']['Tables']['subscriptions']['Row']
type SubscriptionUpdate = Database['public']['Tables']['subscriptions']['Update']

interface SubscriptionWithProfile extends Subscription {
  profiles?: {
    nome: string
    email: string
  }
}

const subscriptionSchema = z.object({
  plano: z.enum(['gratuito', 'premium', 'escolar']),
  status: z.enum(['active', 'canceled', 'past_due', 'incomplete']),
  cancel_at_period_end: z.boolean().default(false)
})

type SubscriptionFormData = z.infer<typeof subscriptionSchema>

interface ManageSubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  subscription: SubscriptionWithProfile | null
  onSave: (subscriptionId: string, updates: SubscriptionUpdate) => Promise<void>
  onCancel: (subscriptionId: string) => Promise<void>
  isSaving: boolean
}

const ManageSubscriptionModal: React.FC<ManageSubscriptionModalProps> = ({
  isOpen,
  onClose,
  subscription,
  onSave,
  onCancel,
  isSaving
}) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionSchema),
    defaultValues: {
      plano: 'gratuito',
      status: 'active',
      cancel_at_period_end: false
    }
  })

  // Reset form when subscription changes
  useEffect(() => {
    if (subscription) {
      reset({
        plano: subscription.plano as 'gratuito' | 'premium' | 'escolar',
        status: subscription.status as 'active' | 'canceled' | 'past_due' | 'incomplete',
        cancel_at_period_end: subscription.cancel_at_period_end
      })
    }
  }, [subscription, reset])

  const onSubmit = async (data: SubscriptionFormData) => {
    if (!subscription) return

    try {
      await onSave(subscription.id, data)
      onClose()
    } catch (error) {
      console.error('Error saving subscription:', error)
      toast.error('Erro ao salvar assinatura')
    }
  }

  const handleCancelSubscription = async () => {
    if (!subscription) return
    
    if (confirm('Tem certeza que deseja cancelar esta assinatura?')) {
      try {
        await onCancel(subscription.id)
        onClose()
      } catch (error) {
        console.error('Error canceling subscription:', error)
        toast.error('Erro ao cancelar assinatura')
      }
    }
  }

  if (!isOpen || !subscription) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Gerenciar Assinatura</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Subscription Info */}
            <div className="mb-6 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Detalhes da Assinatura
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    ID: {subscription.id.slice(0, 8)}...
                  </p>
                </div>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-2">
                  <User className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {subscription.profiles?.nome || 'Nome não disponível'}
                    </p>
                    <p className="text-gray-600 dark:text-gray-400">
                      {subscription.profiles?.email || 'Email não disponível'}
                    </p>
                  </div>
                </div>
                
                {subscription.current_period_start && subscription.current_period_end && (
                  <div className="flex items-start space-x-2">
                    <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        Período Atual
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {new Date(subscription.current_period_start).toLocaleDateString('pt-BR')} - {' '}
                        {new Date(subscription.current_period_end).toLocaleDateString('pt-BR')}
                      </p>
                    </div>
                  </div>
                )}
                
                {subscription.stripe_customer_id && (
                  <div className="mt-2">
                    <a
                      href={`https://dashboard.stripe.com/customers/${subscription.stripe_customer_id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Ver no Stripe</span>
                    </a>
                  </div>
                )}
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Plano
                </label>
                <select
                  {...register('plano')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="gratuito">Gratuito</option>
                  <option value="premium">Premium</option>
                  <option value="escolar">Escolar</option>
                </select>
                {errors.plano && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.plano.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  {...register('status')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="active">Ativa</option>
                  <option value="canceled">Cancelada</option>
                  <option value="past_due">Em Atraso</option>
                  <option value="incomplete">Incompleta</option>
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.status.message}</p>
                )}
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('cancel_at_period_end')}
                    className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Cancelar no fim do período
                  </span>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                  A assinatura permanecerá ativa até o fim do período atual e não será renovada.
                </p>
              </div>

              <div className="flex justify-between space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={handleCancelSubscription}
                  disabled={isSaving || subscription.status === 'canceled'}
                  className="px-4 py-2 border border-red-300 dark:border-red-700 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Cancelar Assinatura
                </button>
                
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Fechar
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:disabled:bg-blue-800/50 text-white rounded-lg transition-colors"
                  >
                    {isSaving ? (
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save className="w-5 h-5" />
                    )}
                    <span>Salvar</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ManageSubscriptionModal