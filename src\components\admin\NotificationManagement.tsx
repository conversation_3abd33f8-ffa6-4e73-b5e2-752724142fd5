import React, { useState } from 'react'
import { 
  Bell, 
  Send, 
  Users, 
  Shield, 
  User,
  Info,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  Loader
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import { useMutation, useQueryClient } from '@tanstack/react-query'

// Tipos para a notificação que será enviada para a Edge Function
interface NotificationPayload {
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  action_url?: string | null;
  action_label?: string | null;
  user_id?: string;
}

const NotificationManagement: React.FC = () => {
  const [notificationType, setNotificationType] = useState<'info' | 'success' | 'warning' | 'error'>('info')
  const [title, setTitle] = useState('')
  const [message, setMessage] = useState('')
  const [actionUrl, setActionUrl] = useState('')
  const [actionLabel, setActionLabel] = useState('')
  const [targetType, setTargetType] = useState<'all' | 'admins' | 'specific'>('all')
  const [specificUserId, setSpecificUserId] = useState('')

  const queryClient = useQueryClient()

  // Mutação para enviar a notificação
  const sendNotificationMutation = useMutation<any, Error, NotificationPayload>({
    mutationFn: async (notification) => {
      const action = targetType === 'all'
        ? 'create_for_all_users'
        : targetType === 'admins'
          ? 'create_for_admins'
          : 'create_for_user'

      const { data, error } = await supabase.functions.invoke('admin-notifications', {
        body: { action, notification }
      })

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Notificação enviada com sucesso!')
      // Resetar formulário após o sucesso
      setTitle('')
      setMessage('')
      setActionUrl('')
      setActionLabel('')
      setSpecificUserId('')
      // Invalide qualquer query de notificações se você tiver uma lista no futuro
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
    },
    onError: (err) => {
      console.error('Error sending notification:', err)
      toast.error(err.message || 'Erro ao enviar notificação')
    }
  })

  const handleSendNotification = async () => {
    if (!title || !message) {
      toast.error('Título e mensagem são obrigatórios')
      return
    }

    if (targetType === 'specific' && !specificUserId) {
      toast.error('ID do usuário é obrigatório para notificação específica')
      return
    }

    const notificationData: NotificationPayload = {
      type: notificationType,
      title,
      message,
      action_url: actionUrl || null,
      action_label: actionLabel || null,
      user_id: targetType === 'specific' ? specificUserId : undefined
    }

    sendNotificationMutation.mutate(notificationData) // Dispara a mutação
  }

  const getTypeIcon = () => {
    switch (notificationType) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
      case 'error': return <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
      default: return <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />
    }
  }

  const getTargetIcon = () => {
    switch (targetType) {
      case 'admins': return <Shield className="w-5 h-5 text-red-600 dark:text-red-400" />
      case 'specific': return <User className="w-5 h-5 text-purple-600 dark:text-purple-400" />
      default: return <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Gerenciamento de Notificações</h1>
        <p className="text-gray-600 dark:text-gray-400">Envie notificações para usuários da plataforma</p>
      </div>

      {/* Notification Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
      >
        <div className="space-y-6">
          {/* Notification Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tipo de Notificação
            </label>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              <button
                onClick={() => setNotificationType('info')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  notificationType === 'info'
                    ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                }`}
              >
                <Info className="w-5 h-5" />
                <span>Informação</span>
              </button>
              
              <button
                onClick={() => setNotificationType('success')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  notificationType === 'success'
                    ? 'border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20'
                }`}
              >
                <CheckCircle className="w-5 h-5" />
                <span>Sucesso</span>
              </button>
              
              <button
                onClick={() => setNotificationType('warning')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  notificationType === 'warning'
                    ? 'border-yellow-500 dark:border-yellow-400 bg-yellow-50 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-yellow-300 dark:hover:border-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20'
                }`}
              >
                <AlertTriangle className="w-5 h-5" />
                <span>Alerta</span>
              </button>
              
              <button
                onClick={() => setNotificationType('error')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  notificationType === 'error'
                    ? 'border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
                }`}
              >
                <XCircle className="w-5 h-5" />
                <span>Erro</span>
              </button>
            </div>
          </div>

          {/* Target Users */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Destinatários
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <button
                onClick={() => setTargetType('all')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  targetType === 'all'
                    ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                }`}
              >
                <Users className="w-5 h-5" />
                <span>Todos os Usuários</span>
              </button>
              
              <button
                onClick={() => setTargetType('admins')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  targetType === 'admins'
                    ? 'border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
                }`}
              >
                <Shield className="w-5 h-5" />
                <span>Apenas Administradores</span>
              </button>
              
              <button
                onClick={() => setTargetType('specific')}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  targetType === 'specific'
                    ? 'border-purple-500 dark:border-purple-400 bg-purple-50 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20'
                }`}
              >
                <User className="w-5 h-5" />
                <span>Usuário Específico</span>
              </button>
            </div>
          </div>

          {targetType === 'specific' && (
            <div>
              <label htmlFor="specificUserId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ID do Usuário Específico
              </label>
              <input
                type="text"
                id="specificUserId"
                value={specificUserId}
                onChange={(e) => setSpecificUserId(e.target.value)}
                placeholder="Ex: 123e4567-e89b-12d3-a456-426614174000"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          )}

          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Título da Notificação
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Ex: Atualização Importante"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Mensagem
            </label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              placeholder="Digite a mensagem da notificação aqui..."
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-y"
            ></textarea>
          </div>

          {/* Action URL and Label */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="actionUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                URL de Ação (Opcional)
              </label>
              <input
                type="url"
                id="actionUrl"
                value={actionUrl}
                onChange={(e) => setActionUrl(e.target.value)}
                placeholder="Ex: https://app.atvpronta.com/settings"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label htmlFor="actionLabel" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Rótulo da Ação (Opcional)
              </label>
              <input
                type="text"
                id="actionLabel"
                value={actionLabel}
                onChange={(e) => setActionLabel(e.target.value)}
                placeholder="Ex: Ir para Configurações"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          {/* Send Button */}
          <button
            onClick={handleSendNotification}
            disabled={sendNotificationMutation.isLoading}
            className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          >
            {sendNotificationMutation.isLoading ? (
              <Loader className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
            <span>{sendNotificationMutation.isLoading ? 'Enviando...' : 'Enviar Notificação'}</span>
          </button>
        </div>
      </motion.div>

      {/* Placeholder for Sent Notifications History */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 text-center text-gray-500 dark:text-gray-400"
      >
        <Clock className="w-12 h-12 mx-auto mb-4" />
        <p>Histórico de Notificações Enviadas</p>
        <p className="text-sm mt-2">
          (Será implementado na Fase 2 com `useQuery` para listar notificações)
        </p>
      </motion.div>
    </div>
  )
}

export default NotificationManagement