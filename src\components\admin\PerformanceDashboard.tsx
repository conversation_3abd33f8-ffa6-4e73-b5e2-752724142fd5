import React, { useState, useEffect } from 'react'
import { useWebVitals } from '../../lib/performance/webVitals'
import { useAssetCache } from '../../hooks/useAssetCache'
import { useIntelligentCache } from '../../hooks/useIntelligentCache'
import { useServiceWorker } from '../../hooks/useServiceWorker'

/**
 * Dashboard completo de performance para administradores
 */
export const PerformanceDashboard: React.FC = () => {
  const { metrics, performanceScore, getFullData } = useWebVitals()
  const { stats: assetStats, clearCache: clearAssetCache } = useAssetCache()
  const { cacheStats, getCacheStats, optimizeCache } = useIntelligentCache()
  const { cacheStats: swCacheStats, getCacheSize, isOnline } = useServiceWorker()
  
  const [totalCacheSize, setTotalCacheSize] = useState<number>(0)
  const [performanceHistory, setPerformanceHistory] = useState<any[]>([])
  const [alerts, setAlerts] = useState<string[]>([])
  
  // Atualizar dados periodicamente
  useEffect(() => {
    const interval = setInterval(async () => {
      // Atualizar tamanho do cache
      const size = await getCacheSize()
      setTotalCacheSize(size)
      
      // Adicionar ao histórico
      const currentData = {
        timestamp: Date.now(),
        score: performanceScore.score,
        metrics: metrics.reduce((acc, metric) => {
          acc[metric.name] = metric.value
          return acc
        }, {} as Record<string, number>)
      }
      
      setPerformanceHistory(prev => {
        const updated = [...prev, currentData]
        // Manter apenas últimas 50 entradas
        return updated.slice(-50)
      })
      
      // Verificar alertas
      const newAlerts: string[] = []
      metrics.forEach(metric => {
        if (metric.rating === 'poor') {
          newAlerts.push(`${metric.name} com performance crítica: ${metric.value}`)
        }
      })
      
      if (totalCacheSize > 100 * 1024 * 1024) { // > 100MB
        newAlerts.push('Cache muito grande: ' + formatBytes(totalCacheSize))
      }
      
      if (!isOnline) {
        newAlerts.push('Aplicação está offline')
      }
      
      setAlerts(newAlerts)
    }, 10000) // A cada 10 segundos
    
    return () => clearInterval(interval)
  }, [metrics, performanceScore, totalCacheSize, isOnline, getCacheSize])
  
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }
  
  const exportPerformanceData = () => {
    const data = {
      timestamp: new Date().toISOString(),
      performanceScore,
      metrics,
      cacheStats,
      assetStats,
      swCacheStats,
      totalCacheSize,
      performanceHistory,
      alerts,
      fullData: getFullData()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard de Performance
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitoramento em tempo real da performance da aplicação
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={optimizeCache}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Otimizar Cache
          </button>
          <button
            onClick={exportPerformanceData}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Exportar Dados
          </button>
        </div>
      </div>
      
      {/* Alertas */}
      {alerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <h3 className="text-red-800 font-medium">Alertas de Performance</h3>
          </div>
          <ul className="text-red-700 text-sm space-y-1">
            {alerts.map((alert, index) => (
              <li key={index}>• {alert}</li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Cards de métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Score geral */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Score Geral</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {performanceScore.score}
              </p>
              <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${getScoreColor(performanceScore.score)}`}>
                Grade {performanceScore.grade}
              </span>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
        
        {/* Cache total */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Cache Total</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatBytes(totalCacheSize)}
              </p>
              <p className="text-sm text-gray-500">
                {Object.keys(swCacheStats).length} caches
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
          </div>
        </div>
        
        {/* Assets em cache */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Assets</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {assetStats.totalAssets}
              </p>
              <p className="text-sm text-gray-500">
                {formatBytes(assetStats.totalSize)}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
        
        {/* Status da conexão */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Conexão</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {isOnline ? 'Online' : 'Offline'}
              </p>
              <p className="text-sm text-gray-500">
                Service Worker ativo
              </p>
            </div>
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
              isOnline ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <svg className={`w-6 h-6 ${isOnline ? 'text-green-600' : 'text-red-600'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      {/* Métricas detalhadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Web Vitals */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Core Web Vitals
          </h3>
          <div className="space-y-4">
            {metrics.map((metric) => (
              <div key={metric.name} className="flex items-center justify-between">
                <div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {metric.name}
                  </span>
                  <p className="text-sm text-gray-500">
                    {metric.name === 'CLS' ? 'Layout Shift' :
                     metric.name === 'FID' ? 'Input Delay' :
                     metric.name === 'FCP' ? 'First Paint' :
                     metric.name === 'LCP' ? 'Largest Paint' :
                     metric.name === 'TTFB' ? 'Time to Byte' :
                     metric.name === 'INP' ? 'Next Paint' : metric.name}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {metric.name === 'CLS' ? metric.value.toFixed(3) : `${Math.round(metric.value)}ms`}
                  </div>
                  <span className={`text-xs px-2 py-1 rounded ${
                    metric.rating === 'good' ? 'bg-green-100 text-green-800' :
                    metric.rating === 'needs-improvement' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {metric.rating.replace('-', ' ')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Cache breakdown */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cache Breakdown
          </h3>
          <div className="space-y-4">
            {Object.entries(swCacheStats).map(([cacheName, stats]) => (
              <div key={cacheName} className="flex items-center justify-between">
                <div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {cacheName.replace('-v1', '')}
                  </span>
                  <p className="text-sm text-gray-500">
                    {stats.count} itens
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatBytes(stats.size || 0)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Histórico de performance */}
      {performanceHistory.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Histórico de Performance
          </h3>
          <div className="h-64 flex items-end space-x-1">
            {performanceHistory.slice(-20).map((entry, index) => (
              <div
                key={index}
                className="flex-1 bg-blue-200 dark:bg-blue-700 rounded-t"
                style={{ height: `${(entry.score / 100) * 100}%` }}
                title={`Score: ${entry.score} - ${new Date(entry.timestamp).toLocaleTimeString()}`}
              />
            ))}
          </div>
          <div className="flex justify-between text-sm text-gray-500 mt-2">
            <span>Últimos 20 registros</span>
            <span>Score: 0-100</span>
          </div>
        </div>
      )}
    </div>
  )
}
