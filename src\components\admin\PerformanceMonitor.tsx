import React, { useState, useEffect } from 'react'
import { 
  Activity, 
  Zap, 
  Clock, 
  Eye, 
  Gauge,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { motion } from 'framer-motion'
import { usePerformanceMonitor } from '../../hooks/usePerformanceMonitor'
import { useIntelligentCache } from '../../hooks/useIntelligentCache'

/**
 * Componente para monitorar performance das páginas públicas
 */
const PerformanceMonitor: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'metrics' | 'cache' | 'resources'>('metrics')
  const [autoRefresh, setAutoRefresh] = useState(false)
  
  const {
    metrics,
    recommendations,
    isSupported,
    getPerformanceScore,
    getPerformanceGrade,
    getSlowResources,
    measureMemoryUsage
  } = usePerformanceMonitor()
  
  const {
    cacheStats,
    getCacheHealth,
    optimizeCache,
    clearAllCache
  } = useIntelligentCache()

  const performanceScore = getPerformanceScore()
  const performanceGrade = getPerformanceGrade()
  const cacheHealth = getCacheHealth()
  const memoryUsage = measureMemoryUsage()
  const slowResources = getSlowResources()

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      window.location.reload()
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh])

  // Metric card component
  const MetricCard: React.FC<{
    title: string
    value: number
    unit: string
    threshold: number
    icon: React.ComponentType<any>
    description: string
  }> = ({ title, value, unit, threshold, icon: Icon, description }) => {
    const isGood = value <= threshold
    const percentage = Math.min((value / threshold) * 100, 100)
    
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${isGood ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
              <Icon className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">{title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">{description}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {value}{unit}
            </div>
            <div className={`text-sm ${isGood ? 'text-green-600' : 'text-red-600'}`}>
              {isGood ? 'Bom' : 'Precisa melhorar'}
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              isGood ? 'bg-green-500' : 'bg-red-500'
            }`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Limite: {threshold}{unit}
        </div>
      </div>
    )
  }

  if (!isSupported) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-md text-center">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Performance API não suportada
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Seu navegador não suporta as APIs necessárias para monitoramento de performance.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Monitor de Performance
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Métricas de performance das páginas públicas
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              autoRefresh 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            <span>Auto Refresh</span>
          </button>
        </div>
      </div>

      {/* Performance Score */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Score Geral de Performance
            </h3>
            <div className="flex items-center space-x-4">
              <div className={`text-4xl font-bold ${
                performanceGrade.color === 'green' ? 'text-green-600' :
                performanceGrade.color === 'blue' ? 'text-blue-600' :
                performanceGrade.color === 'yellow' ? 'text-yellow-600' :
                performanceGrade.color === 'orange' ? 'text-orange-600' :
                'text-red-600'
              }`}>
                {performanceScore}
              </div>
              <div>
                <div className={`text-2xl font-bold ${
                  performanceGrade.color === 'green' ? 'text-green-600' :
                  performanceGrade.color === 'blue' ? 'text-blue-600' :
                  performanceGrade.color === 'yellow' ? 'text-yellow-600' :
                  performanceGrade.color === 'orange' ? 'text-orange-600' :
                  'text-red-600'
                }`}>
                  {performanceGrade.grade}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {performanceGrade.label}
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <Gauge className={`w-16 h-16 ${
              performanceGrade.color === 'green' ? 'text-green-600' :
              performanceGrade.color === 'blue' ? 'text-blue-600' :
              performanceGrade.color === 'yellow' ? 'text-yellow-600' :
              performanceGrade.color === 'orange' ? 'text-orange-600' :
              'text-red-600'
            }`} />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'metrics', label: 'Core Web Vitals', icon: Activity },
            { id: 'cache', label: 'Cache', icon: Zap },
            { id: 'resources', label: 'Recursos', icon: Clock }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'metrics' && (
        <div className="space-y-6">
          {/* Core Web Vitals */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MetricCard
              title="LCP"
              value={metrics.lcp}
              unit="ms"
              threshold={2500}
              icon={Eye}
              description="Largest Contentful Paint"
            />
            <MetricCard
              title="FID"
              value={metrics.fid}
              unit="ms"
              threshold={100}
              icon={Clock}
              description="First Input Delay"
            />
            <MetricCard
              title="CLS"
              value={metrics.cls}
              unit=""
              threshold={0.1}
              icon={Activity}
              description="Cumulative Layout Shift"
            />
            <MetricCard
              title="TTFB"
              value={metrics.ttfb}
              unit="ms"
              threshold={800}
              icon={Zap}
              description="Time to First Byte"
            />
            <MetricCard
              title="FCP"
              value={metrics.fcp}
              unit="ms"
              threshold={1800}
              icon={TrendingUp}
              description="First Contentful Paint"
            />
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
              <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Recomendações de Otimização
              </h4>
              <ul className="space-y-2">
                {recommendations.map((rec, index) => (
                  <li key={index} className="text-yellow-700 dark:text-yellow-300 flex items-start">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Memory Usage */}
          {memoryUsage && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                Uso de Memória
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{memoryUsage.used}MB</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Usado</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{memoryUsage.total}MB</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Total</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{memoryUsage.limit}MB</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Limite</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'cache' && (
        <div className="space-y-6">
          {/* Cache Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md text-center">
              <div className="text-3xl font-bold text-green-600">{cacheStats.hits}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Cache Hits</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md text-center">
              <div className="text-3xl font-bold text-red-600">{cacheStats.misses}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Cache Misses</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md text-center">
              <div className="text-3xl font-bold text-blue-600">{cacheStats.hitRate}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Hit Rate</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md text-center">
              <div className="text-3xl font-bold text-purple-600">{cacheStats.size}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Cache Size</div>
            </div>
          </div>

          {/* Cache Health */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                Saúde do Cache
              </h4>
              <div className="flex items-center space-x-3">
                <button
                  onClick={optimizeCache}
                  className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Otimizar</span>
                </button>
                <button
                  onClick={clearAllCache}
                  className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <AlertTriangle className="w-4 h-4" />
                  <span>Limpar Tudo</span>
                </button>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className={`w-4 h-4 rounded-full ${
                cacheHealth.health === 'excellent' ? 'bg-green-500' :
                cacheHealth.health === 'good' ? 'bg-blue-500' :
                cacheHealth.health === 'fair' ? 'bg-yellow-500' :
                'bg-red-500'
              }`} />
              <span className="font-medium text-gray-900 dark:text-white capitalize">
                {cacheHealth.health === 'excellent' ? 'Excelente' :
                 cacheHealth.health === 'good' ? 'Bom' :
                 cacheHealth.health === 'fair' ? 'Regular' : 'Ruim'}
              </span>
            </div>

            {cacheHealth.recommendations.length > 0 && (
              <ul className="space-y-2">
                {cacheHealth.recommendations.map((rec, index) => (
                  <li key={index} className="text-gray-600 dark:text-gray-300 flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                    {rec}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}

      {activeTab === 'resources' && (
        <div className="space-y-6">
          {/* Slow Resources */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
              Recursos Lentos (> 1s)
            </h4>
            
            {slowResources.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-300">
                  Nenhum recurso lento detectado!
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {slowResources.slice(0, 10).map((resource, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {resource.name.split('/').pop()}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {resource.type} • {Math.round(resource.size / 1024)}KB
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-red-600">
                        {resource.duration}ms
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {resource.startTime}ms
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default PerformanceMonitor
