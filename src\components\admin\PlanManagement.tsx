import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { Database } from '../../types/database';
import toast from 'react-hot-toast';
import { Loader2, PlusCircle, Edit, Trash2, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import {
  syncPlanWithStripe,
  updatePlanInStripe,
  deletePlanFromStripe,
  validatePlanForSync,
  getPlanSyncStatus,
  formatPrice,
  formatDuration
} from '../../services/planSyncService';

// Tipo para os planos baseado no schema do banco de dados
type Plan = Database['public']['Tables']['plans']['Row'];

const PlanManagement: React.FC = () => {
  const { authLoading } = useAuth();
  const queryClient = useQueryClient();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [planToEdit, setPlanToEdit] = useState<Plan | null>(null);
  const [syncingPlans, setSyncingPlans] = useState<Set<string>>(new Set());
  const [newPlan, setNewPlan] = useState({
    name: '',
    description: '',
    price: 0,
    currency: 'BRL',
    duration_months: 1,
    features: '', // String separada por vírgulas para facilitar a entrada
    is_active: true,
    stripe_product_id: null,
    stripe_price_id: null,
  });

  // Fetch plans
  const { data: plans, isLoading, isError, error } = useQuery<Plan[], Error>({
    queryKey: ['adminPlans'],
    queryFn: async () => {
      const { data, error } = await supabase.from('plans').select('*');
      if (error) throw error;
      return data;
    },
    enabled: !authLoading, // Habilita a query apenas quando o AuthContext não estiver carregando
  });

  // Create plan mutation
  const createPlanMutation = useMutation<Plan, Error, typeof newPlan>({
    mutationFn: async (planData) => {
      // Validate plan data
      const validation = validatePlanForSync(planData);
      if (!validation.valid) {
        throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
      }

      const { data, error } = await supabase.from('plans').insert({
        ...planData,
        features: planData.features.split(',').map(f => f.trim()),
      }).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: async (createdPlan) => {
      queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
      toast.success('Plano criado com sucesso!');

      // Sync with Stripe if it's a paid plan
      if (createdPlan.name !== 'Gratuito' && createdPlan.price > 0) {
        setSyncingPlans(prev => new Set(prev).add(createdPlan.id));
        const syncResult = await syncPlanWithStripe(createdPlan);
        setSyncingPlans(prev => {
          const newSet = new Set(prev);
          newSet.delete(createdPlan.id);
          return newSet;
        });

        if (syncResult.success) {
          queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
        }
      }

      setIsCreateModalOpen(false);
      setNewPlan({ // Reset form
        name: '', description: '', price: 0, currency: 'BRL', duration_months: 1, features: '', is_active: true, stripe_product_id: null, stripe_price_id: null,
      });
    },
    onError: (err) => {
      toast.error(`Erro ao criar plano: ${err.message}`);
    },
  });

  // Update plan mutation
  const updatePlanMutation = useMutation<Plan, Error, Plan>({
    mutationFn: async (planData) => {
      // Validate plan data
      const validation = validatePlanForSync(planData);
      if (!validation.valid) {
        throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
      }

      const { id, ...updates } = planData;
      const { data, error } = await supabase.from('plans').update({
        ...updates,
        features: typeof updates.features === 'string' ? updates.features.split(',').map(f => f.trim()) : updates.features,
      }).eq('id', id).select().single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (updatedPlan) => {
      queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
      toast.success('Plano atualizado com sucesso!');

      // Sync with Stripe if it's a paid plan and has Stripe IDs
      if (updatedPlan.name !== 'Gratuito' && updatedPlan.stripe_product_id) {
        setSyncingPlans(prev => new Set(prev).add(updatedPlan.id));
        try {
          const syncResult = await updatePlanInStripe(updatedPlan);
          if (syncResult.success) {
            queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
          }
        } catch (syncError) {
          console.error('Erro na sincronização com Stripe:', syncError);
          // Não mostrar erro para o usuário se o plano foi salvo com sucesso
        } finally {
          setSyncingPlans(prev => {
            const newSet = new Set(prev);
            newSet.delete(updatedPlan.id);
            return newSet;
          });
        }
      }

      setIsEditModalOpen(false);
      setPlanToEdit(null);
    },
    onError: (err) => {
      toast.error(`Erro ao atualizar plano: ${err.message}`);
    },
  });

  // Delete plan mutation
  const deletePlanMutation = useMutation<void, Error, string>({
    mutationFn: async (planId) => {
      const { error } = await supabase.from('plans').delete().eq('id', planId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
      toast.success('Plano excluído com sucesso!');
    },
    onError: (err) => {
      toast.error(`Erro ao excluir plano: ${err.message}`);
    },
  });

  const handleEditClick = (plan: Plan) => {
    setPlanToEdit({
      ...plan,
      features: Array.isArray(plan.features) ? plan.features.join(', ') : '', // Converte array para string para o input
    });
    setIsEditModalOpen(true);
  };

  const handleSavePlan = () => {
    if (planToEdit) {
      updatePlanMutation.mutate(planToEdit);
    } else {
      createPlanMutation.mutate(newPlan);
    }
  };

  const closeModal = (modalType: 'create' | 'edit') => {
    if (modalType === 'create') {
      setIsCreateModalOpen(false);
      setNewPlan({ name: '', description: '', price: 0, currency: 'BRL', duration_months: 1, features: '', is_active: true, stripe_product_id: null, stripe_price_id: null });
    } else {
      setIsEditModalOpen(false);
      setPlanToEdit(null);
    }
  };

  const handleManualSync = async (plan: Plan) => {
    if (syncingPlans.has(plan.id)) return;

    setSyncingPlans(prev => new Set(prev).add(plan.id));

    try {
      let syncResult;
      if (!plan.stripe_product_id) {
        syncResult = await syncPlanWithStripe(plan);
      } else {
        syncResult = await updatePlanInStripe(plan);
      }

      if (syncResult.success) {
        queryClient.invalidateQueries({ queryKey: ['adminPlans'] });
      }
    } finally {
      setSyncingPlans(prev => {
        const newSet = new Set(prev);
        newSet.delete(plan.id);
        return newSet;
      });
    }
  };

  const renderSyncStatus = (plan: Plan) => {
    const status = getPlanSyncStatus(plan);
    const isLoading = syncingPlans.has(plan.id);

    if (isLoading) {
      return (
        <div className="flex items-center space-x-1 text-blue-600">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-xs">Sincronizando...</span>
        </div>
      );
    }

    if (!status.canSync) {
      return (
        <span className="text-xs text-gray-500">N/A</span>
      );
    }

    if (status.isSynced) {
      return (
        <div className="flex items-center space-x-1 text-green-600">
          <CheckCircle className="w-4 h-4" />
          <span className="text-xs">Sincronizado</span>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-1 text-orange-600">
        <AlertCircle className="w-4 h-4" />
        <span className="text-xs">Não sincronizado</span>
      </div>
    );
  };

  if (isLoading || authLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg text-gray-700 dark:text-gray-300">Carregando planos...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-full text-red-500">
        <p>Erro ao carregar planos: {error?.message}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-100">Gerenciamento de Planos</h1>

      <div className="flex justify-end mb-4">
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex items-center transition-colors"
        >
          <PlusCircle className="mr-2 h-4 w-4" /> Adicionar Novo Plano
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Nome</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Preço</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Duração</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Stripe</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Ações</th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {plans?.map((plan) => (
              <tr key={plan.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{plan.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{formatPrice(parseFloat(plan.price), plan.currency)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{formatDuration(plan.duration_months)}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {plan.is_active ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100">Ativo</span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100">Inativo</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {renderSyncStatus(plan)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEditClick(plan)}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-600 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      title="Editar Plano"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    {getPlanSyncStatus(plan).canSync && (
                      <button
                        onClick={() => handleManualSync(plan)}
                        disabled={syncingPlans.has(plan.id)}
                        className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-600 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Sincronizar com Stripe"
                      >
                        {syncingPlans.has(plan.id) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </button>
                    )}

                    <button
                      onClick={() => deletePlanMutation.mutate(plan.id)}
                      disabled={deletePlanMutation.isLoading}
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-600 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Excluir Plano"
                    >
                      {deletePlanMutation.isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            {plans && plans.length === 0 && (
              <tr>
                <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Nenhum plano encontrado.</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Create Plan Modal */}
      <AnimatePresence>
        {isCreateModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => closeModal('create')}
          >
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
              onClick={e => e.stopPropagation()}
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Novo Plano</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">Preencha os detalhes do novo plano.</p>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="name" className="text-right text-gray-700 dark:text-gray-300">
                    Nome
                  </label>
                  <input
                    id="name"
                    type="text"
                    value={newPlan.name}
                    onChange={(e) => setNewPlan({ ...newPlan, name: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="description" className="text-right text-gray-700 dark:text-gray-300">
                    Descrição
                  </label>
                  <textarea
                    id="description"
                    value={newPlan.description || ''}
                    onChange={(e) => setNewPlan({ ...newPlan, description: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="price" className="text-right text-gray-700 dark:text-gray-300">
                    Preço
                  </label>
                  <input
                    id="price"
                    type="number"
                    value={newPlan.price}
                    onChange={(e) => setNewPlan({ ...newPlan, price: parseFloat(e.target.value) || 0 })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="duration_months" className="text-right text-gray-700 dark:text-gray-300">
                    Duração
                  </label>
                  <select
                    id="duration_months"
                    value={newPlan.duration_months}
                    onChange={(e) => setNewPlan({ ...newPlan, duration_months: parseInt(e.target.value) })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={1}>Mensal (1 mês)</option>
                    <option value={12}>Anual (12 meses)</option>
                  </select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="features" className="text-right text-gray-700 dark:text-gray-300">
                    Recursos (separar por vírgula)
                  </label>
                  <textarea
                    id="features"
                    value={newPlan.features}
                    onChange={(e) => setNewPlan({ ...newPlan, features: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="is_active" className="text-right text-gray-700 dark:text-gray-300">
                    Ativo
                  </label>
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={newPlan.is_active}
                    onChange={(e) => setNewPlan({ ...newPlan, is_active: e.target.checked })}
                    className="col-span-3 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => closeModal('create')}
                  className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSavePlan}
                  disabled={createPlanMutation.isLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {createPlanMutation.isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Criar Plano
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Plan Modal */}
      <AnimatePresence>
        {isEditModalOpen && planToEdit && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => closeModal('edit')}
          >
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
              onClick={e => e.stopPropagation()}
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Editar Plano</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">Faça as alterações no plano.</p>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-name" className="text-right text-gray-700 dark:text-gray-300">
                    Nome
                  </label>
                  <input
                    id="edit-name"
                    type="text"
                    value={planToEdit?.name || ''}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, name: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-description" className="text-right text-gray-700 dark:text-gray-300">
                    Descrição
                  </label>
                  <textarea
                    id="edit-description"
                    value={planToEdit?.description || ''}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, description: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-price" className="text-right text-gray-700 dark:text-gray-300">
                    Preço
                  </label>
                  <input
                    id="edit-price"
                    type="number"
                    value={planToEdit?.price || 0}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, price: parseFloat(e.target.value) || 0 })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-duration_months" className="text-right text-gray-700 dark:text-gray-300">
                    Duração
                  </label>
                  <select
                    id="edit-duration_months"
                    value={planToEdit?.duration_months || 1}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, duration_months: parseInt(e.target.value) })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={1}>Mensal (1 mês)</option>
                    <option value={12}>Anual (12 meses)</option>
                  </select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-features" className="text-right text-gray-700 dark:text-gray-300">
                    Recursos (separar por vírgula)
                  </label>
                  <textarea
                    id="edit-features"
                    value={planToEdit?.features || ''}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, features: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-is_active" className="text-right text-gray-700 dark:text-gray-300">
                    Ativo
                  </label>
                  <input
                    type="checkbox"
                    id="edit-is_active"
                    checked={planToEdit?.is_active || false}
                    onChange={(e) => setPlanToEdit({ ...planToEdit!, is_active: e.target.checked })}
                    className="col-span-3 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => closeModal('edit')}
                  className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSavePlan}
                  disabled={updatePlanMutation.isLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updatePlanMutation.isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Salvar Alterações
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PlanManagement; 