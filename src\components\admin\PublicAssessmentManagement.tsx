import React, { useState, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  Globe, 
  Eye, 
  EyeOff, 
  Edit, 
  BarChart3, 
  Download, 
  Search,
  Filter,
  RefreshCw,
  CheckSquare,
  Square,
  Loader2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  MousePointer
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import SEOMetadataEditor from './SEOMetadataEditor'

interface PublicAssessment {
  id: string
  titulo: string
  disciplina: string
  serie: string
  slug: string | null
  is_public: boolean
  is_featured: boolean
  public_category: string | null
  difficulty_level: string | null
  view_count: number
  download_count: number
  conversion_count: number
  created_at: string
  updated_at: string
  profiles: {
    nome: string
    email: string
  }
}

interface ConversionStats {
  total_conversions: number
  signup_conversions: number
  upgrade_conversions: number
  download_conversions: number
}

/**
 * Enhanced admin interface for managing public assessments
 */
const PublicAssessmentManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'public' | 'private'>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [selectedAssessments, setSelectedAssessments] = useState<Set<string>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const [showSeoEditor, setShowSeoEditor] = useState<string | null>(null)
  
  const queryClient = useQueryClient()
  const itemsPerPage = 20

  // Fetch assessments with enhanced data
  const { data: assessments = [], isLoading, refetch } = useQuery({
    queryKey: ['admin-public-assessments', searchTerm, statusFilter, categoryFilter, currentPage],
    queryFn: async () => {
      let query = supabase
        .from('assessments')
        .select(`
          id, titulo, disciplina, serie, slug, is_public, is_featured,
          public_category, difficulty_level, view_count, download_count,
          conversion_count, created_at, updated_at,
          profiles!assessments_autor_id_fkey(nome, email)
        `)
        .order('updated_at', { ascending: false })
        .range((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage - 1)

      if (searchTerm) {
        query = query.or(`titulo.ilike.%${searchTerm}%,disciplina.ilike.%${searchTerm}%`)
      }

      if (statusFilter === 'public') {
        query = query.eq('is_public', true)
      } else if (statusFilter === 'private') {
        query = query.eq('is_public', false)
      }

      if (categoryFilter !== 'all') {
        query = query.eq('public_category', categoryFilter)
      }

      const { data, error } = await query
      if (error) throw error
      return data as PublicAssessment[]
    }
  })

  // Fetch conversion statistics
  const { data: conversionStats } = useQuery({
    queryKey: ['conversion-stats'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('public_assessment_conversions')
        .select('conversion_type')

      if (error) throw error

      const stats: ConversionStats = {
        total_conversions: data.length,
        signup_conversions: data.filter(c => c.conversion_type === 'signup').length,
        upgrade_conversions: data.filter(c => c.conversion_type === 'upgrade').length,
        download_conversions: data.filter(c => c.conversion_type === 'download').length
      }

      return stats
    }
  })

  // Fetch public categories
  const { data: categories = [] } = useQuery({
    queryKey: ['public-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('public_categories')
        .select('name, slug')
        .eq('is_active', true)
        .order('sort_order')

      if (error) throw error
      return data
    }
  })

  // Toggle public status mutation
  const togglePublicMutation = useMutation({
    mutationFn: async ({ assessmentId, isPublic }: { assessmentId: string; isPublic: boolean }) => {
      const { error } = await supabase
        .from('assessments')
        .update({ 
          is_public: !isPublic,
          updated_at: new Date().toISOString()
        })
        .eq('id', assessmentId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-public-assessments'] })
      toast.success('Status público atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error toggling public status:', error)
      toast.error('Erro ao atualizar status público')
    }
  })

  // Bulk operations mutation
  const bulkOperationMutation = useMutation({
    mutationFn: async ({ operation, assessmentIds }: { operation: 'make_public' | 'make_private' | 'feature' | 'unfeature'; assessmentIds: string[] }) => {
      const updates: any = { updated_at: new Date().toISOString() }
      
      switch (operation) {
        case 'make_public':
          updates.is_public = true
          break
        case 'make_private':
          updates.is_public = false
          break
        case 'feature':
          updates.is_featured = true
          break
        case 'unfeature':
          updates.is_featured = false
          break
      }

      const { error } = await supabase
        .from('assessments')
        .update(updates)
        .in('id', assessmentIds)

      if (error) throw error
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['admin-public-assessments'] })
      setSelectedAssessments(new Set())
      
      const operationNames = {
        make_public: 'tornadas públicas',
        make_private: 'tornadas privadas',
        feature: 'destacadas',
        unfeature: 'removidas do destaque'
      }
      
      toast.success(`${variables.assessmentIds.length} avaliações ${operationNames[variables.operation]}!`)
    },
    onError: (error) => {
      console.error('Error in bulk operation:', error)
      toast.error('Erro na operação em lote')
    }
  })

  // Handle assessment selection
  const toggleAssessmentSelection = (assessmentId: string) => {
    const newSelection = new Set(selectedAssessments)
    if (newSelection.has(assessmentId)) {
      newSelection.delete(assessmentId)
    } else {
      newSelection.add(assessmentId)
    }
    setSelectedAssessments(newSelection)
  }

  const selectAllAssessments = () => {
    if (selectedAssessments.size === assessments.length) {
      setSelectedAssessments(new Set())
    } else {
      setSelectedAssessments(new Set(assessments.map(a => a.id)))
    }
  }

  // Calculate statistics
  const stats = useMemo(() => {
    const publicAssessments = assessments.filter(a => a.is_public)
    const totalViews = assessments.reduce((sum, a) => sum + (a.view_count || 0), 0)
    const totalDownloads = assessments.reduce((sum, a) => sum + (a.download_count || 0), 0)
    
    return {
      total: assessments.length,
      public: publicAssessments.length,
      private: assessments.length - publicAssessments.length,
      totalViews,
      totalDownloads,
      avgViewsPerAssessment: assessments.length > 0 ? Math.round(totalViews / assessments.length) : 0
    }
  }, [assessments])

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Avaliações Públicas
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Gerencie avaliações públicas, SEO e conversões
          </p>
        </div>
        
        <button
          onClick={() => refetch()}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total de Avaliações</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Públicas</p>
              <p className="text-2xl font-bold text-green-600">{stats.public}</p>
            </div>
            <Globe className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total de Visualizações</p>
              <p className="text-2xl font-bold text-purple-600">{stats.totalViews.toLocaleString()}</p>
            </div>
            <Eye className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total de Downloads</p>
              <p className="text-2xl font-bold text-orange-600">{stats.totalDownloads.toLocaleString()}</p>
            </div>
            <Download className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Conversion Statistics */}
      {conversionStats && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Estatísticas de Conversão
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{conversionStats.total_conversions}</p>
              <p className="text-sm text-gray-600 dark:text-gray-300">Total</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{conversionStats.signup_conversions}</p>
              <p className="text-sm text-gray-600 dark:text-gray-300">Cadastros</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{conversionStats.upgrade_conversions}</p>
              <p className="text-sm text-gray-600 dark:text-gray-300">Upgrades</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">{conversionStats.download_conversions}</p>
              <p className="text-sm text-gray-600 dark:text-gray-300">Downloads</p>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Buscar
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por título ou disciplina..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todas</option>
              <option value="public">Públicas</option>
              <option value="private">Privadas</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Categoria
            </label>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todas as categorias</option>
              {categories.map(category => (
                <option key={category.slug} value={category.slug}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setStatusFilter('all')
                setCategoryFilter('all')
                setCurrentPage(1)
              }}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Limpar Filtros
            </button>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedAssessments.size > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-blue-800 dark:text-blue-200">
              {selectedAssessments.size} avaliação(ões) selecionada(s)
            </p>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => bulkOperationMutation.mutate({ operation: 'make_public', assessmentIds: Array.from(selectedAssessments) })}
                disabled={bulkOperationMutation.isPending}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
              >
                Tornar Públicas
              </button>
              <button
                onClick={() => bulkOperationMutation.mutate({ operation: 'make_private', assessmentIds: Array.from(selectedAssessments) })}
                disabled={bulkOperationMutation.isPending}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50"
              >
                Tornar Privadas
              </button>
              <button
                onClick={() => bulkOperationMutation.mutate({ operation: 'feature', assessmentIds: Array.from(selectedAssessments) })}
                disabled={bulkOperationMutation.isPending}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 disabled:opacity-50"
              >
                Destacar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Assessments Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={selectAllAssessments}
                    className="flex items-center space-x-2"
                  >
                    {selectedAssessments.size === assessments.length && assessments.length > 0 ? (
                      <CheckSquare className="w-4 h-4 text-blue-600" />
                    ) : (
                      <Square className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Avaliação
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Métricas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  SEO
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto text-gray-400" />
                    <p className="mt-2 text-gray-500 dark:text-gray-400">Carregando avaliações...</p>
                  </td>
                </tr>
              ) : assessments.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <p className="text-gray-500 dark:text-gray-400">Nenhuma avaliação encontrada</p>
                  </td>
                </tr>
              ) : (
                assessments.map((assessment) => (
                  <tr key={assessment.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4">
                      <button
                        onClick={() => toggleAssessmentSelection(assessment.id)}
                        className="flex items-center"
                      >
                        {selectedAssessments.has(assessment.id) ? (
                          <CheckSquare className="w-4 h-4 text-blue-600" />
                        ) : (
                          <Square className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {assessment.titulo}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {assessment.disciplina} • {assessment.serie}
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          Por: {assessment.profiles?.nome}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          assessment.is_public 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {assessment.is_public ? 'Pública' : 'Privada'}
                        </span>
                        {assessment.is_featured && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            Destaque
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Eye className="w-4 h-4 text-gray-400" />
                            <span>{assessment.view_count || 0}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Download className="w-4 h-4 text-gray-400" />
                            <span>{assessment.download_count || 0}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MousePointer className="w-4 h-4 text-gray-400" />
                            <span>{assessment.conversion_count || 0}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {assessment.slug ? (
                          <div>
                            <div className="text-green-600 dark:text-green-400">✓ Slug configurado</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-32">
                              /{assessment.slug}
                            </div>
                          </div>
                        ) : (
                          <div className="text-orange-600 dark:text-orange-400">⚠ Sem slug</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => togglePublicMutation.mutate({ assessmentId: assessment.id, isPublic: assessment.is_public })}
                          disabled={togglePublicMutation.isPending}
                          className={`p-2 rounded-lg transition-colors ${
                            assessment.is_public
                              ? 'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900 dark:text-red-300'
                              : 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-300'
                          }`}
                          title={assessment.is_public ? 'Tornar privada' : 'Tornar pública'}
                        >
                          {assessment.is_public ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                        
                        <button
                          onClick={() => setShowSeoEditor(assessment.id)}
                          className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300"
                          title="Editar SEO"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        
                        {assessment.is_public && assessment.slug && (
                          <a
                            href={`/avaliacoes/${assessment.slug}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-2 bg-purple-100 text-purple-600 rounded-lg hover:bg-purple-200 dark:bg-purple-900 dark:text-purple-300"
                            title="Ver página pública"
                          >
                            <Globe className="w-4 h-4" />
                          </a>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Mostrando {Math.min((currentPage - 1) * itemsPerPage + 1, stats.total)} a {Math.min(currentPage * itemsPerPage, stats.total)} de {stats.total} avaliações
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          >
            Anterior
          </button>
          <span className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Página {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={assessments.length < itemsPerPage}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          >
            Próxima
          </button>
        </div>
      </div>

      {/* SEO Metadata Editor Modal */}
      {showSeoEditor && (
        <SEOMetadataEditor
          assessmentId={showSeoEditor}
          isOpen={!!showSeoEditor}
          onClose={() => setShowSeoEditor(null)}
        />
      )}
    </div>
  )
}

export default PublicAssessmentManagement
