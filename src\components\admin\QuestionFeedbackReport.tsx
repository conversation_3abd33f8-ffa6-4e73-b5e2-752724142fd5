import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  FileText,
  Star,
  MessageSquare,
  AlertTriangle,
  TrendingDown,
  Filter,
  Search,
  Eye,
  Edit,
  Flag
} from 'lucide-react'
import { useQuestionFeedback } from '../../hooks/useQuestionFeedback'
import { useAuth } from '../../contexts/AuthContext'
import { Database } from '../../types/database'

type QuestionFeedback = Database['public']['Tables']['question_feedback']['Row'] & {
  profiles: { nome: string; email: string } | null
  questions: { enunciado: string; disciplina: string; serie: string; topico: string } | null
}

interface QuestionReport {
  questionId: string
  question: {
    enunciado: string
    disciplina: string
    serie: string
    topico: string
  } | null
  feedbackCount: number
  avgRating: number
  approvedCount: number
  rejectedCount: number
  pendingCount: number
  commonIssues: string[]
  lastFeedback: string
  needsAttention: boolean
}

const QuestionFeedbackReport: React.FC = () => {
  const { isAdmin } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState<'all' | 'low-rating' | 'high-feedback' | 'needs-attention'>('all')
  const [sortBy, setSortBy] = useState<'feedback-count' | 'rating' | 'recent'>('feedback-count')

  const { feedback, isLoading } = useQuestionFeedback({ limit: 1000 })

  // Process feedback data to create question reports
  const questionReports = useMemo(() => {
    if (!feedback.length) return []

    const questionMap = new Map<string, QuestionReport>()

    feedback.forEach(f => {
      const questionId = f.question_id
      
      if (!questionMap.has(questionId)) {
        questionMap.set(questionId, {
          questionId,
          question: f.questions,
          feedbackCount: 0,
          avgRating: 0,
          approvedCount: 0,
          rejectedCount: 0,
          pendingCount: 0,
          commonIssues: [],
          lastFeedback: f.created_at,
          needsAttention: false
        })
      }

      const report = questionMap.get(questionId)!
      report.feedbackCount++
      
      if (f.is_approved) report.approvedCount++
      else if (f.is_reviewed && !f.is_approved) report.rejectedCount++
      else report.pendingCount++

      // Update last feedback date
      if (new Date(f.created_at) > new Date(report.lastFeedback)) {
        report.lastFeedback = f.created_at
      }

      // Collect suggestions
      if (f.suggestions) {
        report.commonIssues.push(...f.suggestions)
      }
    })

    // Calculate averages and determine attention needed
    const reports = Array.from(questionMap.values()).map(report => {
      const questionFeedback = feedback.filter(f => f.question_id === report.questionId)
      
      // Calculate average rating
      const ratings = questionFeedback.map(f => f.rating)
      report.avgRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0

      // Get most common issues
      const issueCounts = report.commonIssues.reduce((acc, issue) => {
        acc[issue] = (acc[issue] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      report.commonIssues = Object.entries(issueCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3)
        .map(([issue]) => issue)

      // Determine if needs attention
      report.needsAttention = (
        report.avgRating < 3 || // Low rating
        report.feedbackCount > 10 || // High feedback volume
        report.rejectedCount > report.approvedCount || // More rejections than approvals
        report.pendingCount > 5 // Many pending reviews
      )

      return report
    })

    return reports
  }, [feedback])

  // Filter and sort reports
  const filteredReports = useMemo(() => {
    let filtered = questionReports

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(report => 
        report.question?.enunciado?.toLowerCase().includes(searchLower) ||
        report.question?.disciplina?.toLowerCase().includes(searchLower) ||
        report.question?.topico?.toLowerCase().includes(searchLower) ||
        report.commonIssues.some(issue => issue.toLowerCase().includes(searchLower))
      )
    }

    // Apply category filter
    switch (filterBy) {
      case 'low-rating':
        filtered = filtered.filter(report => report.avgRating < 3)
        break
      case 'high-feedback':
        filtered = filtered.filter(report => report.feedbackCount > 5)
        break
      case 'needs-attention':
        filtered = filtered.filter(report => report.needsAttention)
        break
    }

    // Apply sorting
    switch (sortBy) {
      case 'feedback-count':
        filtered.sort((a, b) => b.feedbackCount - a.feedbackCount)
        break
      case 'rating':
        filtered.sort((a, b) => a.avgRating - b.avgRating)
        break
      case 'recent':
        filtered.sort((a, b) => new Date(b.lastFeedback).getTime() - new Date(a.lastFeedback).getTime())
        break
    }

    return filtered
  }, [questionReports, searchTerm, filterBy, sortBy])

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Acesso Negado
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Você precisa ter permissões de administrador para acessar esta página.
        </p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4" />
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Relatório de Questões
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Análise detalhada de feedback por questão
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Questões com Feedback
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {questionReports.length}
              </p>
            </div>
            <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Precisam de Atenção
              </p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                {questionReports.filter(r => r.needsAttention).length}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Avaliação Baixa (&lt;3)
              </p>
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {questionReports.filter(r => r.avgRating < 3).length}
              </p>
            </div>
            <TrendingDown className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Alto Volume (&gt;5)
              </p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {questionReports.filter(r => r.feedbackCount > 5).length}
              </p>
            </div>
            <MessageSquare className="w-8 h-8 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Buscar
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por questão, disciplina, problema..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Filtrar por
            </label>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todas as questões</option>
              <option value="needs-attention">Precisam de atenção</option>
              <option value="low-rating">Avaliação baixa</option>
              <option value="high-feedback">Alto volume de feedback</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Ordenar por
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="feedback-count">Quantidade de feedback</option>
              <option value="rating">Avaliação (menor primeiro)</option>
              <option value="recent">Feedback mais recente</option>
            </select>
          </div>
        </div>
      </div>

      {/* Question Reports */}
      <div className="space-y-4">
        {filteredReports.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma questão encontrada
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Não há questões que correspondam aos filtros selecionados.
            </p>
          </div>
        ) : (
          filteredReports.map((report, index) => (
            <motion.div
              key={report.questionId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-white dark:bg-gray-800 rounded-lg border-2 p-6 ${
                report.needsAttention 
                  ? 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/10' 
                  : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {report.needsAttention && (
                      <Flag className="w-4 h-4 text-red-600 dark:text-red-400" />
                    )}
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                      {report.question?.disciplina} • {report.question?.serie} • {report.question?.topico}
                    </span>
                  </div>
                  <p className="text-gray-900 dark:text-white font-medium mb-2 line-clamp-2">
                    {report.question?.enunciado || 'Questão não encontrada'}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {report.feedbackCount}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Feedbacks
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-lg font-bold text-gray-900 dark:text-white">
                      {report.avgRating.toFixed(1)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Avaliação Média
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {report.approvedCount}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Aprovados
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {report.pendingCount}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Pendentes
                  </div>
                </div>
              </div>

              {report.commonIssues.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Principais Problemas:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {report.commonIssues.map((issue, i) => (
                      <span
                        key={i}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                      >
                        {issue}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>
                  Último feedback: {new Date(report.lastFeedback).toLocaleDateString('pt-BR')}
                </span>
                <div className="flex items-center space-x-2">
                  <button className="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                    <Eye className="w-4 h-4" />
                    <span>Ver detalhes</span>
                  </button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  )
}

export default QuestionFeedbackReport
