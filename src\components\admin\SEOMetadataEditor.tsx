import React, { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { X, Save, RefreshCw, Globe, Search, Tag } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

interface SEOMetadataEditorProps {
  assessmentId: string
  isOpen: boolean
  onClose: () => void
}

interface SEOData {
  titulo: string
  slug: string | null
  seo_title: string | null
  seo_description: string | null
  seo_keywords: string[]
  public_category: string | null
  difficulty_level: string | null
  featured_image_url: string | null
  is_featured: boolean
}

/**
 * Modal for editing SEO metadata of assessments
 */
const SEOMetadataEditor: React.FC<SEOMetadataEditorProps> = ({
  assessmentId,
  isOpen,
  onClose
}) => {
  const [seoData, setSeoData] = useState<SEOData>({
    titulo: '',
    slug: null,
    seo_title: null,
    seo_description: null,
    seo_keywords: [],
    public_category: null,
    difficulty_level: null,
    featured_image_url: null,
    is_featured: false
  })
  const [keywordInput, setKeywordInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  
  const queryClient = useQueryClient()

  // Load assessment data when modal opens
  useEffect(() => {
    if (isOpen && assessmentId) {
      loadAssessmentData()
    }
  }, [isOpen, assessmentId])

  const loadAssessmentData = async () => {
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from('assessments')
        .select(`
          titulo, slug, seo_title, seo_description, seo_keywords,
          public_category, difficulty_level, featured_image_url, is_featured
        `)
        .eq('id', assessmentId)
        .single()

      if (error) throw error

      setSeoData({
        titulo: data.titulo || '',
        slug: data.slug,
        seo_title: data.seo_title,
        seo_description: data.seo_description,
        seo_keywords: data.seo_keywords || [],
        public_category: data.public_category,
        difficulty_level: data.difficulty_level,
        featured_image_url: data.featured_image_url,
        is_featured: data.is_featured || false
      })
    } catch (error) {
      console.error('Error loading assessment data:', error)
      toast.error('Erro ao carregar dados da avaliação')
    } finally {
      setIsLoading(false)
    }
  }

  // Generate SEO suggestions
  const generateSeoSuggestions = () => {
    if (!seoData.titulo) return

    // Auto-generate SEO title if empty
    if (!seoData.seo_title) {
      setSeoData(prev => ({
        ...prev,
        seo_title: `${prev.titulo} - Atividade Pronta`
      }))
    }

    // Auto-generate SEO description if empty
    if (!seoData.seo_description) {
      const category = seoData.public_category || 'educacional'
      setSeoData(prev => ({
        ...prev,
        seo_description: `Baixe gratuitamente a avaliação "${prev.titulo}". Material ${category} de qualidade para professores. Atividade Pronta - sua plataforma educacional.`
      }))
    }

    // Auto-generate keywords if empty
    if (seoData.seo_keywords.length === 0) {
      const baseKeywords = ['avaliação', 'atividade', 'educação', 'professor', 'ensino']
      if (seoData.public_category) {
        baseKeywords.push(seoData.public_category)
      }
      setSeoData(prev => ({
        ...prev,
        seo_keywords: baseKeywords
      }))
    }

    toast.success('Sugestões SEO geradas!')
  }

  // Add keyword
  const addKeyword = () => {
    if (keywordInput.trim() && !seoData.seo_keywords.includes(keywordInput.trim())) {
      setSeoData(prev => ({
        ...prev,
        seo_keywords: [...prev.seo_keywords, keywordInput.trim()]
      }))
      setKeywordInput('')
    }
  }

  // Remove keyword
  const removeKeyword = (keyword: string) => {
    setSeoData(prev => ({
      ...prev,
      seo_keywords: prev.seo_keywords.filter(k => k !== keyword)
    }))
  }

  // Save SEO data mutation
  const saveSeoMutation = useMutation({
    mutationFn: async (data: Partial<SEOData>) => {
      const { error } = await supabase
        .from('assessments')
        .update({
          slug: data.slug,
          seo_title: data.seo_title,
          seo_description: data.seo_description,
          seo_keywords: data.seo_keywords,
          public_category: data.public_category,
          difficulty_level: data.difficulty_level,
          featured_image_url: data.featured_image_url,
          is_featured: data.is_featured,
          updated_at: new Date().toISOString()
        })
        .eq('id', assessmentId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-public-assessments'] })
      toast.success('Metadados SEO salvos com sucesso!')
      onClose()
    },
    onError: (error) => {
      console.error('Error saving SEO data:', error)
      toast.error('Erro ao salvar metadados SEO')
    }
  })

  const handleSave = () => {
    saveSeoMutation.mutate(seoData)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Search className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Editor de SEO
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Configure metadados para otimização de busca
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="w-8 h-8 animate-spin text-gray-400" />
                <p className="ml-3 text-gray-600 dark:text-gray-300">Carregando dados...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* SEO Suggestions */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-blue-900 dark:text-blue-100">
                        Sugestões Automáticas de SEO
                      </h3>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Gere automaticamente título, descrição e palavras-chave otimizadas
                      </p>
                    </div>
                    <button
                      onClick={generateSeoSuggestions}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Gerar Sugestões
                    </button>
                  </div>
                </div>

                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Título Original
                    </label>
                    <input
                      type="text"
                      value={seoData.titulo}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Slug (URL)
                    </label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm rounded-l-lg">
                        /avaliacoes/
                      </span>
                      <input
                        type="text"
                        value={seoData.slug || ''}
                        onChange={(e) => setSeoData(prev => ({ ...prev, slug: e.target.value }))}
                        placeholder="slug-da-avaliacao"
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* SEO Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Título SEO
                  </label>
                  <input
                    type="text"
                    value={seoData.seo_title || ''}
                    onChange={(e) => setSeoData(prev => ({ ...prev, seo_title: e.target.value }))}
                    placeholder="Título otimizado para mecanismos de busca"
                    maxLength={60}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {(seoData.seo_title || '').length}/60 caracteres (recomendado: 50-60)
                  </p>
                </div>

                {/* SEO Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descrição SEO
                  </label>
                  <textarea
                    value={seoData.seo_description || ''}
                    onChange={(e) => setSeoData(prev => ({ ...prev, seo_description: e.target.value }))}
                    placeholder="Descrição otimizada que aparecerá nos resultados de busca"
                    maxLength={160}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {(seoData.seo_description || '').length}/160 caracteres (recomendado: 150-160)
                  </p>
                </div>

                {/* Keywords */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Palavras-chave
                  </label>
                  <div className="flex space-x-2 mb-3">
                    <input
                      type="text"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                      placeholder="Digite uma palavra-chave"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <button
                      onClick={addKeyword}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Adicionar
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {seoData.seo_keywords.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-sm"
                      >
                        <Tag className="w-3 h-3 mr-1" />
                        {keyword}
                        <button
                          onClick={() => removeKeyword(keyword)}
                          className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                {/* Category and Difficulty */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Categoria Pública
                    </label>
                    <select
                      value={seoData.public_category || ''}
                      onChange={(e) => setSeoData(prev => ({ ...prev, public_category: e.target.value || null }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Selecione uma categoria</option>
                      <option value="matematica">Matemática</option>
                      <option value="portugues">Português</option>
                      <option value="ciencias">Ciências</option>
                      <option value="historia">História</option>
                      <option value="geografia">Geografia</option>
                      <option value="ingles">Inglês</option>
                      <option value="educacao-infantil">Educação Infantil</option>
                      <option value="ensino-fundamental">Ensino Fundamental</option>
                      <option value="ensino-medio">Ensino Médio</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Nível de Dificuldade
                    </label>
                    <select
                      value={seoData.difficulty_level || ''}
                      onChange={(e) => setSeoData(prev => ({ ...prev, difficulty_level: e.target.value || null }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Selecione a dificuldade</option>
                      <option value="Fácil">Fácil</option>
                      <option value="Médio">Médio</option>
                      <option value="Difícil">Difícil</option>
                    </select>
                  </div>
                </div>

                {/* Featured Image URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    URL da Imagem Destacada
                  </label>
                  <input
                    type="url"
                    value={seoData.featured_image_url || ''}
                    onChange={(e) => setSeoData(prev => ({ ...prev, featured_image_url: e.target.value || null }))}
                    placeholder="https://exemplo.com/imagem.jpg"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* Featured Toggle */}
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="is_featured"
                    checked={seoData.is_featured}
                    onChange={(e) => setSeoData(prev => ({ ...prev, is_featured: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label htmlFor="is_featured" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Marcar como avaliação em destaque
                  </label>
                </div>

                {/* Preview */}
                {seoData.seo_title && seoData.seo_description && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-3">
                      Preview do Resultado de Busca
                    </h3>
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="text-blue-600 dark:text-blue-400 text-lg font-medium hover:underline cursor-pointer">
                        {seoData.seo_title}
                      </div>
                      <div className="text-green-700 dark:text-green-400 text-sm">
                        https://atvpronta.com.br/avaliacoes/{seoData.slug || 'slug-da-avaliacao'}
                      </div>
                      <div className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                        {seoData.seo_description}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              disabled={saveSeoMutation.isPending}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {saveSeoMutation.isPending ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>Salvar</span>
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default SEOMetadataEditor
