import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'
import {
  School,
  User,
  Mail,
  Plus,
  Trash2,
  Loader,
  AlertTriangle,
  RefreshCw,
  X,
  Crown,
  Loader2,
  PlusCircle,
  Edit
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

// Tipos para School e Profile da perspectiva da escola
type SchoolType = Database['public']['Tables']['schools']['Row'] & {
  plans?: { name: string; max_teachers: number | null } | null;
}
type ProfileType = Database['public']['Tables']['profiles']['Row']

// Tipos para as tabelas 'schools' e 'plans' baseado no schema do banco de dados
type School = Database['public']['Tables']['schools']['Row'];
type Plan = Database['public']['Tables']['plans']['Row']; // Adicionado tipo para Planos

interface InviteCollaboratorModalProps {
  onClose: () => void
  onInvite: (email: string) => Promise<void>
  isLoading: boolean
}

const InviteCollaboratorModal: React.FC<InviteCollaboratorModalProps> = ({ onClose, onInvite, isLoading }) => {
  const [email, setEmail] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) {
      toast.error('Por favor, insira o email do colaborador.')
      return
    }
    try {
      await onInvite(email)
    } catch (error) {
      console.error('Erro ao enviar convite no modal (tratado pelo useMutation):', error)
    } finally {
      setEmail('')
      onClose()
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Convidar Colaborador</h2>
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="collaborator-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email do Colaborador
            </label>
            <input
              type="email"
              id="collaborator-email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Mail className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Convidando...' : 'Convidar'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

interface CollaboratorManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  school: SchoolType;
}

const CollaboratorManagementModal: React.FC<CollaboratorManagementModalProps> = ({ isOpen, onClose, school }) => {
  const queryClient = useQueryClient();
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Query para buscar os perfis associados a esta escola
  const { data: collaborators, isLoading: isLoadingCollaborators, error: collaboratorsError, refetch: refetchCollaborators } = useQuery<ProfileType[], Error>({
    queryKey: ['school-collaborators', school.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('school_id', school.id)
        .order('nome', { ascending: true });
      if (error) throw error;
      return data;
    },
    enabled: isOpen && !!school.id, // Habilita a query apenas quando o modal está aberto e a escola está definida
    staleTime: 5 * 60 * 1000,
    retry: 1,
  });

  // Mutação para convidar um novo colaborador (associar perfil a uma escola)
  const inviteCollaboratorMutation = useMutation<any, Error, string>({
    mutationFn: async (email: string) => {
      // Chama a Edge Function para convidar o colaborador usando invoke
      const { data, error } = await supabase.functions.invoke('invite-collaborator', {
        body: { email, schoolId: school.id },
      });

      if (error) {
        throw new Error(error.message);
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['school-collaborators', school.id] });
      toast.success('Convite enviado com sucesso! O colaborador precisa se registrar.');
      setShowInviteModal(false);
    },
    onError: (err) => {
      toast.error(`Erro ao convidar colaborador: ${err.message}`);
    },
  });

  // Mutação para remover um colaborador (desassociar perfil da escola)
  const removeCollaboratorMutation = useMutation<void, Error, string>({
    mutationFn: async (profileId) => {
      const { error } = await supabase
        .from('profiles')
        .update({ school_id: null, is_school_admin: false }) // Remover associação e status de admin da escola
        .eq('id', profileId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['school-collaborators', school.id] });
      toast.success('Colaborador removido com sucesso!');
    },
    onError: (err) => {
      toast.error(`Erro ao remover colaborador: ${err.message}`);
    },
  });

  // Mutação para alternar o status is_school_admin
  const toggleSchoolAdminStatusMutation = useMutation<ProfileType, Error, { profileId: string, currentStatus: boolean }>({
    mutationFn: async ({ profileId, currentStatus }) => {
      console.log('Attempting to toggle admin status for profileId:', profileId, 'from', currentStatus, 'to', !currentStatus);
      
      // Chama a Edge Function para alternar o status de admin da escola usando invoke
      const { data, error } = await supabase.functions.invoke('toggle-school-admin', {
        body: { profileId, isSchoolAdminStatus: !currentStatus },
      });

      if (error) {
        throw new Error(error.message);
      }
      console.log('Successfully toggled admin status. New data:', data.profile);
      return data.profile;
    },
    onSuccess: (updatedProfile, variables) => {
      queryClient.invalidateQueries({ queryKey: ['school-collaborators', school.id] });
      toast.success(`Status de administrador da escola ${variables.currentStatus ? 'removido' : 'concedido'} com sucesso!`);
    },
    onError: (err) => {
      toast.error(`Erro ao alterar status de admin da escola: ${err.message}`);
    },
  });

  if (!isOpen) return null;

  if (isLoadingCollaborators) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg text-gray-700 dark:text-gray-300">Carregando colaboradores...</span>
      </div>
    );
  }

  if (collaboratorsError) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative text-red-500">
          <p>Erro ao carregar colaboradores: {collaboratorsError.message}</p>
          <button onClick={onClose} className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors">
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    );
  }

  // Lógica de validação do limite de professores
  const currentTeachers = collaborators?.length || 0;
  const maxTeachers = school.plans?.max_teachers;
  const canInviteMoreTeachers = maxTeachers === null || currentTeachers < maxTeachers;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-3xl relative"
            onClick={e => e.stopPropagation()}
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Colaboradores da Escola: {school.name}</h2>
            <button
              onClick={onClose}
              className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
              title="Fechar"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="flex justify-between items-center mb-4">
              <p className={`text-sm ${canInviteMoreTeachers ? 'text-gray-600 dark:text-gray-400' : 'text-red-600 dark:text-red-400'}`}>
                Total de Professores: {currentTeachers} / {maxTeachers === null ? 'Ilimitado' : maxTeachers}
                {!canInviteMoreTeachers && (
                  <span className="ml-2 font-semibold"> (Limite Atingido!)</span>
                )}
              </p>
              <button
                onClick={() => setShowInviteModal(true)}
                disabled={!canInviteMoreTeachers} // Desabilita se o limite for atingido
                className={`bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md flex items-center transition-colors ${!canInviteMoreTeachers ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <User className="mr-2 h-4 w-4" /> Convidar Colaborador
              </button>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg shadow overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead className="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Nome</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Função</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Ações</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {collaborators?.map((collaborator) => (
                    <tr key={collaborator.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{collaborator.nome || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{collaborator.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={collaborator.is_school_admin || false}
                            onChange={() => toggleSchoolAdminStatusMutation.mutate({ profileId: collaborator.id, currentStatus: collaborator.is_school_admin || false })}
                            disabled={toggleSchoolAdminStatusMutation.isLoading}
                          />
                          <div className="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                          <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                            {collaborator.is_school_admin ? 'Admin da Escola' : 'Professor'}
                          </span>
                        </label>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => removeCollaboratorMutation.mutate(collaborator.id)}
                          disabled={removeCollaboratorMutation.isLoading}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-600 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Remover Colaborador"
                        >
                          {removeCollaboratorMutation.isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                        </button>
                      </td>
                    </tr>
                  ))}
                  {collaborators && collaborators.length === 0 && (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Nenhum colaborador encontrado para esta escola.</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            
            {showInviteModal && (
              <InviteCollaboratorModal
                onClose={() => setShowInviteModal(false)}
                onInvite={inviteCollaboratorMutation.mutate}
                isLoading={inviteCollaboratorMutation.isLoading}
              />
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const SchoolManagement: React.FC = () => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [schoolToEdit, setSchoolToEdit] = useState<School | null>(null)
  const [selectedSchoolForCollaborators, setSelectedSchoolForCollaborators] = useState<SchoolType | null>(null)
  const [newSchool, setNewSchool] = useState({
    name: '',
    cnpj: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    max_teachers: 0,
    plan_id: '' as string | null, // Para armazenar o ID do plano selecionado
  })

  // Fetch schools (com join para o nome do plano)
  const { data: schools, isLoading, isError, error } = useQuery<Array<School & { plans?: { name: string } | null }>, Error>({
    queryKey: ['adminSchools'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('schools')
        .select('*, plans(name)'); // Seleciona todos os campos da escola e o nome do plano
      if (error) throw error;
      return data;
    },
  });

  // Fetch plans (para o dropdown de seleção de planos)
  const { data: plans, isLoading: isLoadingPlans, isError: isErrorPlans, error: plansError } = useQuery<Plan[], Error>({
    queryKey: ['plans'],
    queryFn: async () => {
      const { data, error } = await supabase.from('plans').select('*');
      if (error) throw error;
      return data;
    },
  });

  // Create school mutation
  const createSchoolMutation = useMutation<School, Error, typeof newSchool>({
    mutationFn: async (schoolData) => {
      const { data, error } = await supabase.from('schools').insert(schoolData).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminSchools'] });
      toast.success('Escola criada com sucesso!');
      setIsCreateModalOpen(false);
      setNewSchool({ name: '', cnpj: '', contact_email: '', contact_phone: '', address: '', max_teachers: 0, plan_id: null }); // Reset form
    },
    onError: (err) => {
      toast.error(`Erro ao criar escola: ${err.message}`);
    },
  });

  // Update school mutation
  const updateSchoolMutation = useMutation<School, Error, School>({
    mutationFn: async (schoolData) => {
      const { id, ...updates } = schoolData;
      const { data, error } = await supabase.from('schools').update(updates).eq('id', id).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminSchools'] });
      toast.success('Escola atualizada com sucesso!');
      setIsEditModalOpen(false);
      setSchoolToEdit(null);
    },
    onError: (err) => {
      toast.error(`Erro ao atualizar escola: ${err.message}`);
    },
  });

  // Delete school mutation
  const deleteSchoolMutation = useMutation<void, Error, string>({
    mutationFn: async (schoolId) => {
      const { error } = await supabase.from('schools').delete().eq('id', schoolId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminSchools'] });
      toast.success('Escola excluída com sucesso!');
    },
    onError: (err) => {
      toast.error(`Erro ao excluir escola: ${err.message}`);
    },
  });

  const handleEditClick = (school: School) => {
    setSchoolToEdit(school);
    setIsEditModalOpen(true);
  };

  const handleManageCollaboratorsClick = (school: SchoolType) => {
    setSelectedSchoolForCollaborators(school);
  };

  const handleSaveSchool = () => {
    if (schoolToEdit) {
      updateSchoolMutation.mutate(schoolToEdit);
    } else {
      createSchoolMutation.mutate(newSchool);
    }
  };

  const closeModal = (modalType: 'create' | 'edit') => {
    if (modalType === 'create') {
      setIsCreateModalOpen(false);
      setNewSchool({ name: '', cnpj: '', contact_email: '', contact_phone: '', address: '', max_teachers: 0, plan_id: null });
    } else {
      setIsEditModalOpen(false);
      setSchoolToEdit(null);
    }
  };

  if (isLoading || isLoadingPlans) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg text-gray-700 dark:text-gray-300">Carregando escolas e planos...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-full text-red-500">
        <p>Erro ao carregar escolas: {error?.message}</p>
      </div>
    );
  }

  if (isErrorPlans) {
    return (
      <div className="flex justify-center items-center h-full text-red-500">
        <p>Erro ao carregar planos: {plansError?.message}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-100">Gerenciamento de Escolas</h1>

        <div className="flex justify-end mb-4">
          <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex items-center transition-colors"
          >
          <PlusCircle className="mr-2 h-4 w-4" /> Adicionar Nova Escola
          </button>
        </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Nome</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">CNPJ</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Email Contato</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Telefone Contato</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Endereço</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Max Professores</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Plano</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">Ações</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {schools?.map((school) => (
              <tr key={school.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{school.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.cnpj}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.contact_email}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.contact_phone}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.address}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.max_teachers}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{school.plans?.name || 'N/A'}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium flex items-center">
                  <button
                    onClick={() => handleEditClick(school)}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-600 mr-3 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    title="Editar Escola"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleManageCollaboratorsClick(school)}
                    className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-600 mr-3 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    title="Gerenciar Colaboradores"
                  >
                    <User className="h-4 w-4" />
                  </button>
                      <button
                    onClick={() => deleteSchoolMutation.mutate(school.id)}
                    disabled={deleteSchoolMutation.isLoading}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-600 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Excluir Escola"
                      >
                    {deleteSchoolMutation.isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                      </button>
                    </td>
                  </tr>
                ))}
            {schools && schools.length === 0 && (
              <tr>
                <td colSpan={8} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Nenhuma escola encontrada.</td>
              </tr>
            )}
              </tbody>
            </table>
          </div>

      {/* Create School Modal */}
      <AnimatePresence>
        {isCreateModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => closeModal('create')}
          >
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
              onClick={e => e.stopPropagation()}
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Nova Escola</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">Preencha os detalhes da nova escola.</p>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="name" className="text-right text-gray-700 dark:text-gray-300">Nome</label>
                  <input id="name" type="text" value={newSchool.name} onChange={(e) => setNewSchool({ ...newSchool, name: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="cnpj" className="text-right text-gray-700 dark:text-gray-300">CNPJ</label>
                  <input id="cnpj" type="text" value={newSchool.cnpj} onChange={(e) => setNewSchool({ ...newSchool, cnpj: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="contact_email" className="text-right text-gray-700 dark:text-gray-300">Email Contato</label>
                  <input id="contact_email" type="email" value={newSchool.contact_email} onChange={(e) => setNewSchool({ ...newSchool, contact_email: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="contact_phone" className="text-right text-gray-700 dark:text-gray-300">Telefone Contato</label>
                  <input id="contact_phone" type="text" value={newSchool.contact_phone} onChange={(e) => setNewSchool({ ...newSchool, contact_phone: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="address" className="text-right text-gray-700 dark:text-gray-300">Endereço</label>
                  <input id="address" type="text" value={newSchool.address} onChange={(e) => setNewSchool({ ...newSchool, address: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="max_teachers" className="text-right text-gray-700 dark:text-gray-300">Max Professores</label>
                  <input id="max_teachers" type="number" value={newSchool.max_teachers} onChange={(e) => setNewSchool({ ...newSchool, max_teachers: parseInt(e.target.value) || 0 })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="plan_id" className="text-right text-gray-700 dark:text-gray-300">Plano</label>
                  <select
                    id="plan_id"
                    value={newSchool.plan_id || ''}
                    onChange={(e) => setNewSchool({ ...newSchool, plan_id: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Selecione um plano</option>
                    {plans?.map((plan) => (
                      <option key={plan.id} value={plan.id}>
                        {plan.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => closeModal('create')}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition-colors dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-white"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveSchool}
                  disabled={createSchoolMutation.isLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {createSchoolMutation.isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Criar Escola'}
                </button>
          </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit School Modal */}
      <AnimatePresence>
        {isEditModalOpen && schoolToEdit && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => closeModal('edit')}
          >
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
              onClick={e => e.stopPropagation()}
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Editar Escola</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">Edite os detalhes da escola.</p>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-name" className="text-right text-gray-700 dark:text-gray-300">Nome</label>
                  <input id="edit-name" type="text" value={schoolToEdit.name} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, name: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-cnpj" className="text-right text-gray-700 dark:text-gray-300">CNPJ</label>
                  <input id="edit-cnpj" type="text" value={schoolToEdit.cnpj || ''} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, cnpj: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-contact_email" className="text-right text-gray-700 dark:text-gray-300">Email Contato</label>
                  <input id="edit-contact_email" type="email" value={schoolToEdit.contact_email || ''} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, contact_email: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-contact_phone" className="text-right text-gray-700 dark:text-gray-300">Telefone Contato</label>
                  <input id="edit-contact_phone" type="text" value={schoolToEdit.contact_phone || ''} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, contact_phone: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-address" className="text-right text-gray-700 dark:text-gray-300">Endereço</label>
                  <input id="edit-address" type="text" value={schoolToEdit.address || ''} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, address: e.target.value })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-max_teachers" className="text-right text-gray-700 dark:text-gray-300">Max Professores</label>
                  <input id="edit-max_teachers" type="number" value={schoolToEdit.max_teachers || 0} onChange={(e) => setSchoolToEdit({ ...schoolToEdit, max_teachers: parseInt(e.target.value) || 0 })} className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="edit-plan_id" className="text-right text-gray-700 dark:text-gray-300">Plano</label>
                  <select
                    id="edit-plan_id"
                    value={schoolToEdit.plan_id || ''}
                    onChange={(e) => setSchoolToEdit({ ...schoolToEdit, plan_id: e.target.value })}
                    className="col-span-3 mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Selecione um plano</option>
                    {plans?.map((plan) => (
                      <option key={plan.id} value={plan.id}>
                        {plan.name}
                      </option>
                    ))}
                  </select>
                </div>
      </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => closeModal('edit')}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition-colors dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-white"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveSchool}
                  disabled={updateSchoolMutation.isLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updateSchoolMutation.isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Salvar Alterações'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Collaborator Management Modal */}
      {selectedSchoolForCollaborators && (
        <CollaboratorManagementModal
          isOpen={!!selectedSchoolForCollaborators}
          onClose={() => setSelectedSchoolForCollaborators(null)}
          school={selectedSchoolForCollaborators}
        />
      )}
    </div>
  );
};

export default SchoolManagement; 