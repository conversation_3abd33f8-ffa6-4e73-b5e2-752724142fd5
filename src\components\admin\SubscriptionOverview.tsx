import React, { useState } from 'react'
import {
  CreditCard,
  Search,
  Filter,
  ExternalLink,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader,
  RefreshCw
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '../../contexts/AuthContext'
import SubscriptionStatsCard from './SubscriptionStatsCard'

type Subscription = Database['public']['Tables']['subscriptions']['Row']

interface SubscriptionWithProfile extends Subscription {
  profiles?: {
    nome: string
    email: string
  }
}

const SubscriptionOverview: React.FC = () => {
  const { isAdmin } = useAuth()
  const queryClient = useQueryClient()

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [planFilter, setPlanFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [subscriptionsPerPage] = useState(10)

  const { data: subscriptions, isLoading, error, refetch, isRefetching } = useQuery<SubscriptionWithProfile[], Error>({
    queryKey: ['subscriptions', searchTerm, statusFilter, planFilter, currentPage],
    queryFn: async () => {
      let query = supabase
        .from('subscriptions')
        .select(`
          *,
          profiles (
            nome,
            email
          )
        `)

      if (searchTerm) {
        query = query.or(`profiles.nome.ilike.%${searchTerm}%,profiles.email.ilike.%${searchTerm}%`)
      }

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter)
      }

      if (planFilter !== 'all') {
        query = query.eq('plano', planFilter)
      }

      const from = (currentPage - 1) * subscriptionsPerPage
      const to = from + subscriptionsPerPage - 1
      query = query.order('created_at', { ascending: false }).range(from, to)

      const { data, error: fetchError } = await query

      if (fetchError) {
        console.error('Error fetching subscriptions:', fetchError)
        toast.error('Erro ao carregar assinaturas.')
        throw fetchError
      }
      return data || []
    },
    staleTime: 1000 * 60 * 5
  })

  const { data: totalSubscriptionsCount } = useQuery<number, Error>({
    queryKey: ['totalSubscriptionsCount', searchTerm, statusFilter, planFilter],
    queryFn: async () => {
      let query = supabase.from('subscriptions').select('id', { count: 'exact', head: true })

      if (searchTerm) {
        query = query.or(`profiles.nome.ilike.%${searchTerm}%,profiles.email.ilike.%${searchTerm}%`)
      }

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter)
      }

      if (planFilter !== 'all') {
        query = query.eq('plano', planFilter)
      }

      const { count, error: countError } = await query
      if (countError) {
        console.error('Error fetching total subscription count:', countError)
        throw countError
      }
      return count || 0
    },
    staleTime: 1000 * 60 * 5
  })

  const pageCount = Math.ceil((totalSubscriptionsCount || 0) / subscriptionsPerPage)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleNextPage = () => {
    if (currentPage < pageCount) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300'
      case 'canceled': return 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300'
      case 'past_due': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300'
      case 'incomplete': return 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300'
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />
      case 'canceled': return <XCircle className="w-4 h-4" />
      case 'past_due': return <AlertTriangle className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'premium': return 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300'
      case 'escolar': return 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300'
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
    }
  }

  const stats = {
    total: totalSubscriptionsCount || 0,
    active: (subscriptions || []).filter(s => s.status === 'active').length,
    canceled: (subscriptions || []).filter(s => s.status === 'canceled').length,
    revenue: (subscriptions || []).filter(s => s.status === 'active').reduce((acc, s) => {
      const amount = s.plano === 'premium' ? 29.90 : s.plano === 'escolar' ? 199.90 : 0
      return acc + amount
    }, 0)
  }

  // Admin permission guard
  if (!isAdmin) {
    return (
      <div className="p-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Acesso Negado
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Você precisa ter permissões de administrador para acessar esta página.
          </p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Erro ao Carregar Assinaturas
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error.message}
          </p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Visão Geral de Assinaturas</h1>
          <p className="text-gray-600 dark:text-gray-400">{totalSubscriptionsCount} assinaturas encontradas</p>
        </div>
        <button
          onClick={() => refetch()}
          disabled={isRefetching}
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRefetching ? <Loader className="w-5 h-5 mr-2 animate-spin" /> : <RefreshCw className="w-5 h-5 mr-2" />}
          Atualizar Lista
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <SubscriptionStatsCard
          title="Total de Assinaturas"
          value={stats.total}
          icon={CreditCard}
          color="gray"
          delay={0}
        />
        <SubscriptionStatsCard
          title="Assinaturas Ativas"
          value={stats.active}
          icon={CheckCircle}
          color="green"
          delay={0.1}
        />
        <SubscriptionStatsCard
          title="Assinaturas Canceladas"
          value={stats.canceled}
          icon={XCircle}
          color="red"
          delay={0.2}
        />
        <SubscriptionStatsCard
          title="Receita Mensal Ativa"
          value={stats.revenue.toFixed(2)}
          icon={DollarSign}
          color="purple"
          delay={0.3}
          prefix="R$ "
        />
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Buscar
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por nome ou email..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1)
                }}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value)
                setCurrentPage(1)
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos os Status</option>
              <option value="active">Ativas</option>
              <option value="canceled">Canceladas</option>
              <option value="past_due">Atrasadas</option>
              <option value="incomplete">Incompletas</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Plano
            </label>
            <select
              value={planFilter}
              onChange={(e) => {
                setPlanFilter(e.target.value)
                setCurrentPage(1)
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos os Planos</option>
              <option value="premium">Premium</option>
              <option value="escolar">Escolar</option>
              <option value="gratuito">Gratuito</option>
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Usuário
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Plano
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Data Início
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {(subscriptions || []).map((subscription, index) => (
                <motion.tr
                  key={subscription.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{subscription.profiles?.nome || 'N/A'}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{subscription.profiles?.email || 'N/A'}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPlanColor(subscription.plano)}`}>
                      {subscription.plano.charAt(0).toUpperCase() + subscription.plano.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(subscription.status)} flex items-center gap-1`}>
                      {getStatusIcon(subscription.status)} {subscription.status.replace(/_/g, ' ').charAt(0).toUpperCase() + subscription.status.replace(/_/g, ' ').slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {subscription.created_at ? new Date(subscription.created_at).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {subscription.stripe_customer_id && (
                      <a
                        href={`https://dashboard.stripe.com/customers/${subscription.stripe_customer_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200 ml-2"
                        title="Ver Cliente no Stripe"
                      >
                        <ExternalLink className="w-5 h-5 inline" />
                      </a>
                    )}
                    {subscription.stripe_subscription_id && (
                      <a
                        href={`https://dashboard.stripe.com/subscriptions/${subscription.stripe_subscription_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200 ml-2"
                        title="Ver Assinatura no Stripe"
                      >
                        <ExternalLink className="w-5 h-5 inline" />
                      </a>
                    )}
                  </td>
                </motion.tr>
              ))}
              {(!subscriptions || subscriptions.length === 0) && (
                <tr>
                  <td colSpan={5} className="py-6 text-center text-gray-500 dark:text-gray-400">Nenhuma assinatura encontrada.</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {pageCount > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6 rounded-b-lg">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Anterior
            </button>
            <button
              onClick={handleNextPage}
              disabled={currentPage === pageCount}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Próximo
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-200">
                Mostrando <span className="font-medium">{(currentPage - 1) * subscriptionsPerPage + 1}</span> a{' '}
                <span className="font-medium">{Math.min(currentPage * subscriptionsPerPage, totalSubscriptionsCount || 0)}</span> de{' '}
                <span className="font-medium">{totalSubscriptionsCount || 0}</span> resultados
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                >
                  <span className="sr-only">Anterior</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 010 1.06L9.56 10l3.23 3.71a.75.75 0 11-1.06 1.06l-4-4a.75.75 0 010-1.06l4-4a.75.75 0 011.06 0z" clipRule="evenodd" />
                  </svg>
                </button>
                {[...Array(pageCount)].map((_, i) => (
                  <button
                    key={i + 1}
                    onClick={() => handlePageChange(i + 1)}
                    aria-current={currentPage === i + 1 ? 'page' : undefined}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      currentPage === i + 1
                        ? 'z-10 bg-red-500 border-red-500 text-white'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {i + 1}
                  </button>
                ))}
                <button
                  onClick={handleNextPage}
                  disabled={currentPage === pageCount}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                >
                  <span className="sr-only">Próximo</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 010-1.06L10.44 10 7.21 6.29a.75.75 0 111.06-1.06l4 4a.75.75 0 010 1.06l-4 4a.75.75 0 01-1.06 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SubscriptionOverview