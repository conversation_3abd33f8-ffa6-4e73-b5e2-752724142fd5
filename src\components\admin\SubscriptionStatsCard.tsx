import React from 'react'
import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

interface SubscriptionStatsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  color: 'blue' | 'green' | 'red' | 'purple' | 'yellow' | 'gray'
  delay?: number
  suffix?: string
  prefix?: string
}

const colorClasses = {
  blue: {
    icon: 'text-blue-600 dark:text-blue-400',
    value: 'text-blue-600 dark:text-blue-400'
  },
  green: {
    icon: 'text-green-600 dark:text-green-400',
    value: 'text-green-600 dark:text-green-400'
  },
  red: {
    icon: 'text-red-600 dark:text-red-400',
    value: 'text-red-600 dark:text-red-400'
  },
  purple: {
    icon: 'text-purple-600 dark:text-purple-400',
    value: 'text-purple-600 dark:text-purple-400'
  },
  yellow: {
    icon: 'text-yellow-600 dark:text-yellow-400',
    value: 'text-yellow-600 dark:text-yellow-400'
  },
  gray: {
    icon: 'text-gray-600 dark:text-gray-400',
    value: 'text-gray-900 dark:text-white'
  }
}

const SubscriptionStatsCard: React.FC<SubscriptionStatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color,
  delay = 0,
  suffix = '',
  prefix = ''
}) => {
  const colors = colorClasses[color]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
      className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {title}
          </p>
          <p className={`text-3xl font-bold ${colors.value}`}>
            {prefix}{value}{suffix}
          </p>
        </div>
        <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <Icon className={`w-6 h-6 ${colors.icon}`} />
        </div>
      </div>
    </motion.div>
  )
}

export default SubscriptionStatsCard
