import React, { useEffect, useState } from 'react'
import { Loader, CheckCircle, AlertTriangle, Server, Mail, CreditCard } from 'lucide-react'
import { supabase } from '../../lib/supabase'

const SystemMonitoring: React.FC = () => {
  const [status, setStatus] = useState({ stripe: 'ok', supabase: 'ok', email: 'ok' })
  const [logs, setLogs] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simular checagem de status das integrações
    setTimeout(() => {
      setStatus({ stripe: 'ok', supabase: 'ok', email: 'ok' })
      setLoading(false)
    }, 1000)
    // Buscar logs de erros recentes
    supabase.from('system_logs').select('*').order('created_at', { ascending: false }).limit(10)
      .then(({ data }) => setLogs(data || []))
  }, [])

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Monitoramento do Sistema</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className={`flex items-center space-x-3 p-4 rounded-xl border ${status.stripe==='ok'?'border-green-300 bg-green-50':'border-red-300 bg-red-50'}`}>
          <CreditCard className="w-6 h-6" />
          <span>Stripe</span>
          {status.stripe==='ok'
            ? <CheckCircle className="w-5 h-5 text-green-600" />
            : <AlertTriangle className="w-5 h-5 text-red-600" />}
        </div>
        <div className={`flex items-center space-x-3 p-4 rounded-xl border ${status.supabase==='ok'?'border-green-300 bg-green-50':'border-red-300 bg-red-50'}`}>
          <Server className="w-6 h-6" />
          <span>Supabase</span>
          {status.supabase==='ok'
            ? <CheckCircle className="w-5 h-5 text-green-600" />
            : <AlertTriangle className="w-5 h-5 text-red-600" />}
        </div>
        <div className={`flex items-center space-x-3 p-4 rounded-xl border ${status.email==='ok'?'border-green-300 bg-green-50':'border-red-300 bg-red-50'}`}>
          <Mail className="w-6 h-6" />
          <span>Email</span>
          {status.email==='ok'
            ? <CheckCircle className="w-5 h-5 text-green-600" />
            : <AlertTriangle className="w-5 h-5 text-red-600" />}
        </div>
      </div>
      <div>
        <h2 className="text-lg font-semibold mb-2">Logs de Erros Recentes</h2>
        {loading ? (
          <Loader className="w-6 h-6 animate-spin" />
        ) : logs.length === 0 ? (
          <div className="text-gray-500">Nenhum erro recente.</div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {logs.map((log, idx) => (
              <li key={idx} className="py-2 text-sm text-red-600 dark:text-red-400">
                [{new Date(log.created_at).toLocaleString('pt-BR')}] {log.message}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  )
}

export default SystemMonitoring 