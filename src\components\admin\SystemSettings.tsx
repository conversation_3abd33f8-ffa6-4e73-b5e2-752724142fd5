import React, { useEffect, useMemo, useState } from 'react'
import { Navigate } from 'react-router-dom'
import {
  Save,
  AlertTriangle,
  Shield,
  Loader,
  RefreshCw,
  History
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useAuditLog } from '../../hooks/useAuditLog'
import { useSystemSettings } from '../../hooks/useSystemSettings'
import { SettingsCategory } from './SystemSettings/index'
import ConfirmationModal from './SystemSettings/ConfirmationModal'
import KeyboardShortcuts from './SystemSettings/KeyboardShortcuts'
import SearchAndFilters from './SystemSettings/SearchAndFilters'
import ChangeHistory from './SystemSettings/ChangeHistory'
import toast from 'react-hot-toast'

const SystemSettings: React.FC = () => {
  const { user, loading, isAdmin, refreshProfile } = useAuth()
  const { logAction, logAdminAccess } = useAuditLog()
  
  // 🔧 ARCHITECTURE: Use centralized hook for all settings logic
  const {
    settings,
    filteredSettings,
    groupedSettings,
    formData,
    isLoading,
    isSaving,
    error,
    modifiedFields,
    hasUnsavedChanges,
    searchTerm,
    categoryFilters,
    showModifiedOnly,
    handleInputChange,
    handleSave,
    refetch,
    getSettingConfig,
    setSearchTerm,
    setCategoryFilters,
    setShowModifiedOnly,
    ERROR_MESSAGES
  } = useSystemSettings()

  // 🎨 UX: State for modals
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [showHistory, setShowHistory] = useState(false)

  // 🔒 SECURITY: Admin validation guard + Audit logging
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user && !isAdmin && !loading) {
        try {
          await refreshProfile()
          
          if (!isAdmin) {
            toast.error(ERROR_MESSAGES.UNAUTHORIZED_ACCESS)
            await logAction(
              'UNAUTHORIZED_ACCESS_ATTEMPT',
              'system_settings',
              {
                attempted_action: 'view_system_settings',
                user_id: user.id,
                timestamp: new Date().toISOString()
              }
            )
          } else {
            await logAdminAccess('system_settings', 'VIEW')
          }
        } catch (error) {
          toast.error(ERROR_MESSAGES.ADMIN_VERIFICATION)
        }
      } else if (user && isAdmin && !loading) {
        await logAdminAccess('system_settings', 'VIEW')
      }
    }
    
    checkAdminStatus()
  }, [user, isAdmin, loading, refreshProfile, logAction, logAdminAccess, ERROR_MESSAGES])

  // 🔒 SECURITY: Show loading while auth is initializing
  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600 dark:bg-red-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Verificando permissões...</p>
          </div>
        </div>
      </div>
    )
  }

  // 🔒 SECURITY: Redirect if not admin
  if (!isAdmin) {
    return <Navigate to="/app" replace />
  }

  // ⚡ PERFORMANCE: Memoize ordered categories for consistent rendering
  const orderedCategories = useMemo(() => {
    const categoryOrder = ['Geral', 'Recursos', 'Limites', 'Outros']
    return categoryOrder.filter(category => groupedSettings[category])
  }, [groupedSettings])

  // 🎨 UX: Handle save with confirmation for critical changes
  const handleSaveClick = () => {
    if (hasUnsavedChanges) {
      setShowConfirmModal(true)
    } else {
      toast.success('Nenhuma alteração a ser salva.')
    }
  }

  // 🎨 UX: Confirm and save changes
  const handleConfirmSave = async () => {
    setShowConfirmModal(false)
    await handleSave()
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando configurações...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-600 dark:text-red-400">
            <AlertTriangle className="w-8 h-8 mx-auto mb-4" />
            <p>Erro ao carregar configurações: {error.message}</p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6 max-w-7xl mx-auto">
      {/* 🎯 ACCESSIBILITY: Page header with proper heading hierarchy */}
      <header className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 lg:gap-6">
        <div className="min-w-0 flex-1">
          <h1
            className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white truncate"
            id="page-title"
          >
            Configurações do Sistema
          </h1>
          <p
            className="text-gray-600 dark:text-gray-400 mt-1 text-sm lg:text-base"
            aria-describedby="page-title"
          >
            Gerencie as configurações globais da plataforma.
          </p>
        </div>
        <div className="flex flex-col lg:flex-row lg:items-center gap-3 lg:gap-4 w-full lg:w-auto">
          {/* 🎨 UX: Unsaved changes indicator */}
          {hasUnsavedChanges && (
            <div className="flex items-center text-sm lg:text-base text-yellow-600 dark:text-yellow-400 order-3 lg:order-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse flex-shrink-0" />
              <span className="truncate">
                {modifiedFields.size} alteração{modifiedFields.size !== 1 ? 'ões' : ''} não salva{modifiedFields.size !== 1 ? 's' : ''}
              </span>
            </div>
          )}

          {/* 📅 HISTORY: History button */}
          <button
            onClick={() => setShowHistory(true)}
            className="flex items-center justify-center px-3 lg:px-4 py-2 lg:py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 text-sm lg:text-base font-medium order-2 lg:order-2"
            title="Ver histórico de mudanças"
          >
            <History className="w-5 h-5 mr-2 flex-shrink-0" />
            <span className="hidden sm:inline">Histórico</span>
          </button>

          <button
            onClick={handleSaveClick}
            disabled={isSaving || !hasUnsavedChanges}
            className={`flex items-center justify-center px-4 lg:px-6 py-2 lg:py-3 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm lg:text-base font-medium order-1 lg:order-3 w-full lg:w-auto ${
              hasUnsavedChanges
                ? 'bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl'
                : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
            }`}
          >
            {isSaving ? (
              <Loader className="w-5 h-5 mr-2 animate-spin flex-shrink-0" />
            ) : (
              <Save className="w-5 h-5 mr-2 flex-shrink-0" />
            )}
            <span className="truncate">
              {isSaving ? 'Salvando...' : hasUnsavedChanges ? 'Salvar Configurações' : 'Nenhuma Alteração'}
            </span>
          </button>
        </div>
      </header>

      {/* 🔍 SEARCH: Search and Filters */}
      <SearchAndFilters
        onSearchChange={setSearchTerm}
        onCategoryFilter={setCategoryFilters}
        onModifiedFilter={setShowModifiedOnly}
        availableCategories={Object.keys(groupedSettings)}
        modifiedCount={modifiedFields.size}
        totalCount={filteredSettings.length}
      />

      {/* Settings Sections - Responsive Grid Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
        {orderedCategories.map((category, index) => (
          <div
            key={category}
            className={`${orderedCategories.length === 1 ? 'xl:col-span-2' : ''}`}
          >
            <SettingsCategory
              category={category}
              settings={groupedSettings[category]}
              formData={formData}
              onChange={handleInputChange}
              getSettingConfig={getSettingConfig}
              disabled={isSaving}
              animationDelay={0.1 * (index + 1)}
              modifiedFields={modifiedFields}
            />
          </div>
        ))}
      </div>

      {/* 🎨 UX: Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmSave}
        title="Confirmar Alterações"
        message={`Você tem ${modifiedFields.size} configuração${modifiedFields.size !== 1 ? 'ões' : ''} modificada${modifiedFields.size !== 1 ? 's' : ''}. Deseja salvar as alterações?`}
        confirmText="Salvar Alterações"
        cancelText="Cancelar"
        type="warning"
        isLoading={isSaving}
      />

      {/* 📅 HISTORY: Change History Modal */}
      <ChangeHistory
        isOpen={showHistory}
        onClose={() => setShowHistory(false)}
        changes={[]} // TODO: Implement actual change history
      />

      {/* 🎯 ACCESSIBILITY: Keyboard shortcuts */}
      <KeyboardShortcuts
        onSave={handleSaveClick}
        onRefresh={refetch}
        canSave={hasUnsavedChanges && !isSaving}
      />
    </div>
  )
}

export default SystemSettings
