import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { History, X, Clock, User, ChevronRight } from 'lucide-react'

interface ChangeRecord {
  id: string
  timestamp: string
  user: string
  changes: Array<{
    key: string
    description: string
    oldValue: any
    newValue: any
  }>
}

interface ChangeHistoryProps {
  isOpen: boolean
  onClose: () => void
  changes: ChangeRecord[]
}

const ChangeHistory: React.FC<ChangeHistoryProps> = ({
  isOpen,
  onClose,
  changes
}) => {
  const [expandedChange, setExpandedChange] = useState<string | null>(null)

  // 📅 FORMAT: Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return {
      date: date.toLocaleDateString('pt-BR'),
      time: date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  // 🎨 FORMAT: Format value for display
  const formatValue = (value: any) => {
    if (typeof value === 'boolean') {
      return value ? 'Ativado' : 'Desativado'
    }
    if (typeof value === 'number') {
      return value.toString()
    }
    if (typeof value === 'string') {
      return value || '(vazio)'
    }
    return JSON.stringify(value)
  }

  // 🎨 COLOR: Get color for value change
  const getChangeColor = (oldValue: any, newValue: any) => {
    if (typeof oldValue === 'boolean' && typeof newValue === 'boolean') {
      return newValue ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
    }
    return 'text-blue-600 dark:text-blue-400'
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <div className="flex min-h-full items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
              role="dialog"
              aria-modal="true"
              aria-labelledby="history-title"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 
                  id="history-title"
                  className="text-xl font-semibold text-gray-900 dark:text-white flex items-center"
                >
                  <History className="w-5 h-5 mr-2" />
                  Histórico de Mudanças
                </h2>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-lg p-1"
                  aria-label="Fechar histórico"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                {changes.length === 0 ? (
                  <div className="text-center py-12">
                    <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      Nenhuma mudança registrada ainda.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {changes.map((change) => {
                      const { date, time } = formatTimestamp(change.timestamp)
                      const isExpanded = expandedChange === change.id

                      return (
                        <div
                          key={change.id}
                          className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                        >
                          {/* Change Summary */}
                          <button
                            onClick={() => setExpandedChange(isExpanded ? null : change.id)}
                            className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-inset"
                            aria-expanded={isExpanded}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                  <Clock className="w-4 h-4 mr-1" />
                                  {date} às {time}
                                </div>
                                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                  <User className="w-4 h-4 mr-1" />
                                  {change.user}
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  {change.changes.length} alteração{change.changes.length !== 1 ? 'ões' : ''}
                                </span>
                                <ChevronRight 
                                  className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                    isExpanded ? 'rotate-90' : ''
                                  }`} 
                                />
                              </div>
                            </div>
                          </button>

                          {/* Change Details */}
                          <AnimatePresence>
                            {isExpanded && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/30"
                              >
                                <div className="p-4 space-y-3">
                                  {change.changes.map((item, index) => (
                                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                                      <div className="font-medium text-gray-900 dark:text-white mb-2">
                                        {item.description}
                                      </div>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                        <div>
                                          <span className="text-gray-500 dark:text-gray-400">Valor anterior:</span>
                                          <div className="mt-1 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-700 dark:text-red-300 font-mono">
                                            {formatValue(item.oldValue)}
                                          </div>
                                        </div>
                                        <div>
                                          <span className="text-gray-500 dark:text-gray-400">Novo valor:</span>
                                          <div className={`mt-1 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded font-mono ${getChangeColor(item.oldValue, item.newValue)}`}>
                                            {formatValue(item.newValue)}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
                >
                  Fechar
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default ChangeHistory
