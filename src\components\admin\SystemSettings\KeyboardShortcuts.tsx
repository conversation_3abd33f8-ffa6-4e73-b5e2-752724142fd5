import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Keyboard, X } from 'lucide-react'

interface KeyboardShortcutsProps {
  onSave?: () => void
  onRefresh?: () => void
  canSave?: boolean
}

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  onSave,
  onRefresh,
  canSave = false
}) => {
  const [showHelp, setShowHelp] = useState(false)

  // 🎯 ACCESSIBILITY: Keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      // Ctrl/Cmd + S: Save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (canSave && onSave) {
          onSave()
        }
      }
      
      // Ctrl/Cmd + R: Refresh (prevent browser refresh)
      else if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault()
        if (onRefresh) {
          onRefresh()
        }
      }
      
      // ?: Show help
      else if (e.key === '?' && !e.ctrlKey && !e.metaKey && !e.altKey) {
        e.preventDefault()
        setShowHelp(true)
      }
      
      // Escape: Close help
      else if (e.key === 'Escape') {
        setShowHelp(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onSave, onRefresh, canSave])

  const shortcuts = [
    {
      keys: ['Ctrl', 'S'],
      description: 'Salvar configurações',
      available: canSave
    },
    {
      keys: ['Ctrl', 'R'],
      description: 'Recarregar configurações',
      available: true
    },
    {
      keys: ['?'],
      description: 'Mostrar atalhos',
      available: true
    },
    {
      keys: ['Esc'],
      description: 'Fechar modais/ajuda',
      available: true
    }
  ]

  return (
    <>
      {/* 🎯 ACCESSIBILITY: Keyboard shortcuts indicator - Responsive positioning */}
      <button
        onClick={() => setShowHelp(true)}
        className="fixed bottom-4 right-4 lg:bottom-6 lg:right-6 p-3 lg:p-4 bg-gray-800 dark:bg-gray-700 text-white rounded-full shadow-lg hover:bg-gray-700 dark:hover:bg-gray-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 z-40 hover:scale-110"
        aria-label="Mostrar atalhos de teclado"
        title="Atalhos de teclado (?)"
      >
        <Keyboard className="w-5 h-5 lg:w-6 lg:h-6" />
      </button>

      {/* 🎯 ACCESSIBILITY: Keyboard shortcuts help modal */}
      <AnimatePresence>
        {showHelp && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => setShowHelp(false)}
            />

            {/* Modal */}
            <div className="flex min-h-full items-end lg:items-center justify-center p-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                className="relative bg-white dark:bg-gray-800 rounded-t-lg lg:rounded-lg shadow-xl max-w-md w-full p-4 lg:p-6 max-h-[80vh] overflow-y-auto"
                role="dialog"
                aria-modal="true"
                aria-labelledby="shortcuts-title"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <h3 
                    id="shortcuts-title"
                    className="text-lg font-semibold text-gray-900 dark:text-white flex items-center"
                  >
                    <Keyboard className="w-5 h-5 mr-2" />
                    Atalhos de Teclado
                  </h3>
                  <button
                    onClick={() => setShowHelp(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-lg p-1"
                    aria-label="Fechar ajuda"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Shortcuts list */}
                <div className="space-y-3">
                  {shortcuts.map((shortcut, index) => (
                    <div 
                      key={index}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        shortcut.available 
                          ? 'bg-gray-50 dark:bg-gray-700' 
                          : 'bg-gray-100 dark:bg-gray-600 opacity-50'
                      }`}
                    >
                      <span className={`text-sm ${
                        shortcut.available 
                          ? 'text-gray-900 dark:text-white' 
                          : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {shortcut.description}
                      </span>
                      <div className="flex items-center space-x-1">
                        {shortcut.keys.map((key, keyIndex) => (
                          <React.Fragment key={keyIndex}>
                            {keyIndex > 0 && (
                              <span className="text-gray-400 text-xs">+</span>
                            )}
                            <kbd className={`px-2 py-1 text-xs font-mono rounded border ${
                              shortcut.available
                                ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white'
                                : 'bg-gray-200 dark:bg-gray-500 border-gray-400 dark:border-gray-500 text-gray-500 dark:text-gray-400'
                            }`}>
                              {key}
                            </kbd>
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Footer */}
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    Pressione <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">?</kbd> a qualquer momento para ver esta ajuda
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        )}
      </AnimatePresence>
    </>
  )
}

export default KeyboardShortcuts
