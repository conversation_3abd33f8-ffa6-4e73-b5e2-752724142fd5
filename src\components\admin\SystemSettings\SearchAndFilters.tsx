import React, { useState, useMemo } from 'react'
import { Search, Filter, X, ChevronDown } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface SearchAndFiltersProps {
  onSearchChange: (search: string) => void
  onCategoryFilter: (categories: string[]) => void
  onModifiedFilter: (showModified: boolean) => void
  availableCategories: string[]
  modifiedCount: number
  totalCount: number
}

const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  onSearchChange,
  onCategoryFilter,
  onModifiedFilter,
  availableCategories,
  modifiedCount,
  totalCount
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showModifiedOnly, setShowModifiedOnly] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  // 🔍 SEARCH: Handle search input changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    onSearchChange(value)
  }

  // 🔍 FILTER: Handle category selection
  const handleCategoryToggle = (category: string) => {
    const newCategories = selectedCategories.includes(category)
      ? selectedCategories.filter(c => c !== category)
      : [...selectedCategories, category]
    
    setSelectedCategories(newCategories)
    onCategoryFilter(newCategories)
  }

  // 🔍 FILTER: Handle modified filter
  const handleModifiedToggle = (checked: boolean) => {
    setShowModifiedOnly(checked)
    onModifiedFilter(checked)
  }

  // 🔍 FILTER: Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategories([])
    setShowModifiedOnly(false)
    onSearchChange('')
    onCategoryFilter([])
    onModifiedFilter(false)
  }

  // 🔍 FILTER: Check if any filters are active
  const hasActiveFilters = searchTerm || selectedCategories.length > 0 || showModifiedOnly

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          placeholder="Buscar configurações..."
          aria-label="Buscar configurações"
        />
        {searchTerm && (
          <button
            onClick={() => handleSearchChange('')}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            aria-label="Limpar busca"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
          aria-expanded={showFilters}
          aria-controls="filter-panel"
        >
          <Filter className="h-4 w-4" />
          Filtros
          <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
        </button>

        {/* Active Filters Summary */}
        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          <span>{totalCount} configuração{totalCount !== 1 ? 'ões' : ''}</span>
          {modifiedCount > 0 && (
            <>
              <span>•</span>
              <span className="text-yellow-600 dark:text-yellow-400">
                {modifiedCount} modificada{modifiedCount !== 1 ? 's' : ''}
              </span>
            </>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            id="filter-panel"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-4"
          >
            {/* Category Filters */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Categorias
              </h3>
              <div className="flex flex-wrap gap-2">
                {availableCategories.map(category => (
                  <button
                    key={category}
                    onClick={() => handleCategoryToggle(category)}
                    className={`px-3 py-1.5 text-sm rounded-full border transition-colors duration-200 ${
                      selectedCategories.includes(category)
                        ? 'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-700 text-red-700 dark:text-red-300'
                        : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                    aria-pressed={selectedCategories.includes(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Modified Filter */}
            <div>
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={showModifiedOnly}
                  onChange={(e) => handleModifiedToggle(e.target.checked)}
                  className="w-4 h-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <span className="text-gray-700 dark:text-gray-300">
                  Mostrar apenas configurações modificadas
                </span>
                {modifiedCount > 0 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    ({modifiedCount})
                  </span>
                )}
              </label>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <div className="flex justify-end">
                <button
                  onClick={clearFilters}
                  className="text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors duration-200"
                >
                  Limpar filtros
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SearchAndFilters
