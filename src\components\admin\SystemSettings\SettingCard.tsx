import React, { memo, useMemo } from 'react'
import {
  Settings,
  AlertTriangle,
  Database,
  Zap,
  Globe,
  HelpCircle,
  Circle
} from 'lucide-react'
import SettingInput from './SettingInput'

interface SettingCardProps {
  setting: {
    id: string
    key: string
    value: any
    description: string | null
  }
  value: any
  onChange: (key: string, value: any) => void
  getSettingConfig: (key: any) => {
    inputType: 'text' | 'number' | 'boolean'
    helpText?: string
  }
  disabled?: boolean
  isModified?: boolean
}

const SettingCard: React.FC<SettingCardProps> = memo(({
  setting,
  value,
  onChange,
  getSettingConfig,
  disabled = false,
  isModified = false
}) => {
  // ⚡ PERFORMANCE: Memoize config to prevent unnecessary recalculations
  const config = useMemo(() => getSettingConfig(setting.key), [getSettingConfig, setting.key])

  // ⚡ PERFORMANCE: Memoize icon to prevent unnecessary re-renders
  const settingIcon = useMemo(() => {
    switch (setting.key) {
      case 'platform_name':
        return <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
      case 'maintenance_mode':
        return <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
      case 'max_questions_per_assessment':
      case 'default_question_limit_free':
        return <Database className="w-5 h-5 text-green-600 dark:text-green-400" />
      case 'enable_ai_generation':
      case 'enable_collaboration':
        return <Zap className="w-5 h-5 text-purple-600 dark:text-purple-400" />
      default:
        return <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
    }
  }, [setting.key])

  return (
    <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 lg:gap-4 py-4 px-2 lg:px-0 border-b last:border-b-0 border-gray-100 dark:border-gray-700 transition-all duration-200 rounded-lg lg:rounded-none ${isModified ? 'bg-yellow-50 dark:bg-yellow-900/10 border-yellow-200 dark:border-yellow-800 shadow-sm lg:shadow-none' : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'}`}>
      {/* Setting Info */}
      <div className="flex items-start lg:items-center space-x-3 flex-1 min-w-0">
        <div className="relative">
          {settingIcon}
          {/* 🎨 UX: Modified indicator */}
          {isModified && (
            <Circle className="absolute -top-1 -right-1 w-3 h-3 text-yellow-500 fill-current" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <label
              htmlFor={setting.key}
              className={`block text-base lg:text-lg font-medium transition-colors duration-200 ${isModified ? 'text-yellow-700 dark:text-yellow-300' : 'text-gray-900 dark:text-white'}`}
            >
              {setting.description || setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              {isModified && <span className="ml-2 text-xs text-yellow-600 dark:text-yellow-400 font-normal">(modificado)</span>}
            </label>
            {config.helpText && (
              <div className="group relative flex-shrink-0">
                <HelpCircle className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 max-w-xs lg:max-w-none">
                  {config.helpText}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                </div>
              </div>
            )}
          </div>
          <div className="mt-1 flex flex-col sm:flex-row sm:items-center gap-2">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Chave: <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-xs font-mono">{setting.key}</code>
            </p>
            {config.helpText && (
              <p className="text-xs text-gray-400 dark:text-gray-500 lg:hidden">
                Toque no ícone de ajuda para mais informações
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Setting Input */}
      <div className="flex-shrink-0 w-full lg:w-1/3 lg:max-w-xs">
        <SettingInput
          setting={setting}
          value={value}
          onChange={onChange}
          inputType={config.inputType}
          disabled={disabled}
          helpText={config.helpText}
          isModified={isModified}
        />
      </div>
    </div>
  )
})

// ⚡ PERFORMANCE: Display name for React DevTools
SettingCard.displayName = 'SettingCard'

export default SettingCard
