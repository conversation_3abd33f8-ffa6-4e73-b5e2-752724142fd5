import React, { memo, useCallback } from 'react'

interface SettingInputProps {
  setting: {
    id: string
    key: string
    value: any
    description: string | null
  }
  value: any
  onChange: (key: string, value: any) => void
  inputType: 'text' | 'number' | 'boolean'
  disabled?: boolean
  helpText?: string
  isModified?: boolean
}

const SettingInput: React.FC<SettingInputProps> = memo(({
  setting,
  value,
  onChange,
  inputType,
  disabled = false,
  helpText,
  isModified = false
}) => {
  // ⚡ PERFORMANCE: Memoized change handler to prevent unnecessary re-renders
  const handleChange = useCallback((newValue: any) => {
    onChange(setting.key, newValue)
  }, [setting.key, onChange])

  // 🎯 ACCESSIBILITY: Generate unique IDs for ARIA relationships
  const inputId = `setting-${setting.key}`
  const helpId = helpText ? `${inputId}-help` : undefined
  const statusId = isModified ? `${inputId}-status` : undefined

  switch (inputType) {
    case 'boolean':
      return (
        <div className="flex items-center space-x-3">
          <input
            id={inputId}
            type="checkbox"
            checked={value === true || value === 'true'}
            onChange={(e) => handleChange(e.target.checked)}
            disabled={disabled}
            className="w-4 h-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded disabled:opacity-50 focus:ring-2 focus:ring-offset-2"
            aria-describedby={[helpId, statusId].filter(Boolean).join(' ') || undefined}
            aria-label={setting.description || `Configuração ${setting.key}`}
          />
          <label htmlFor={inputId} className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
            {value === true || value === 'true' ? 'Ativado' : 'Desativado'}
          </label>
          {/* 🎯 ACCESSIBILITY: Status for screen readers */}
          {isModified && (
            <span id={statusId} className="sr-only">
              Configuração modificada
            </span>
          )}
        </div>
      )
    
    case 'number':
      return (
        <div className="w-full">
          <input
            id={inputId}
            type="number"
            value={value || 0}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            min="1"
            max={setting.key === 'max_questions_per_assessment' ? 1000 : 500}
            aria-describedby={[helpId, statusId].filter(Boolean).join(' ') || undefined}
            aria-label={setting.description || `Configuração ${setting.key}`}
            aria-invalid={isModified ? 'false' : undefined}
          />
          {/* 🎯 ACCESSIBILITY: Status for screen readers */}
          {isModified && (
            <span id={statusId} className="sr-only">
              Configuração modificada
            </span>
          )}
        </div>
      )
    
    default: // text
      return (
        <div className="w-full">
          <input
            id={inputId}
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            placeholder={setting.description || 'Digite o valor...'}
            aria-describedby={[helpId, statusId].filter(Boolean).join(' ') || undefined}
            aria-label={setting.description || `Configuração ${setting.key}`}
            aria-invalid={isModified ? 'false' : undefined}
          />
          {/* 🎯 ACCESSIBILITY: Status for screen readers */}
          {isModified && (
            <span id={statusId} className="sr-only">
              Configuração modificada
            </span>
          )}
        </div>
      )
  }
})

// ⚡ PERFORMANCE: Display name for React DevTools
SettingInput.displayName = 'SettingInput'

export default SettingInput
