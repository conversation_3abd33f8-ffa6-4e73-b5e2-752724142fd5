import React, { memo } from 'react'
import { motion } from 'framer-motion'
import SettingCard from './SettingCard'

interface SettingsCategoryProps {
  category: string
  settings: Array<{
    id: string
    key: string
    value: any
    description: string | null
  }>
  formData: Record<string, any>
  onChange: (key: string, value: any) => void
  getSettingConfig: (key: any) => {
    inputType: 'text' | 'number' | 'boolean'
    helpText?: string
  }
  disabled?: boolean
  animationDelay?: number
  modifiedFields?: Set<string>
}

const SettingsCategory: React.FC<SettingsCategoryProps> = memo(({
  category,
  settings,
  formData,
  onChange,
  getSettingConfig,
  disabled = false,
  animationDelay = 0.1,
  modifiedFields = new Set()
}) => {
  // ⚡ PERFORMANCE: Component is memoized to prevent unnecessary re-renders
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: animationDelay }}
      className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6 space-y-4 shadow-sm lg:shadow-none"
    >
      {/* Category Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-3 mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <h2 className="text-xl lg:text-2xl font-semibold text-gray-900 dark:text-white">
            {category}
          </h2>
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {settings.length} configuração{settings.length !== 1 ? 'ões' : ''}
            </span>
            {/* Show modified count if any */}
            {modifiedFields && Array.from(modifiedFields).some(key => settings.some(s => s.key === key)) && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200">
                {Array.from(modifiedFields).filter(key => settings.some(s => s.key === key)).length} modificada{Array.from(modifiedFields).filter(key => settings.some(s => s.key === key)).length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Settings List */}
      <div className="space-y-0">
        {settings.map((setting, index) => (
          <SettingCard
            key={setting.id}
            setting={setting}
            value={formData[setting.key]}
            onChange={onChange}
            getSettingConfig={getSettingConfig}
            disabled={disabled}
            isModified={modifiedFields.has(setting.key)}
          />
        ))}
      </div>
    </motion.div>
  )
})

// ⚡ PERFORMANCE: Display name for React DevTools
SettingsCategory.displayName = 'SettingsCategory'

export default SettingsCategory
