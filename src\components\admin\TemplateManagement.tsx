import React, { useState } from 'react'
import {
  Layout,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Crown,
  Star,
  User,
  Calendar,
  Loader,
  AlertTriangle,
  EyeOff,
  RefreshCw,
  Plus,
  MoreVertical,
  Save
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { Database } from '../../types/database'
import { EnhancedTemplate, TemplateWithProfile, CreateTemplateDTO, TemplateContent, TemplateMetadata } from '../../types/templates'
import TemplatePreviewModal from './TemplatePreviewModal'
import toast from 'react-hot-toast'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Link } from 'react-router-dom'

type Template = Database['public']['Tables']['templates']['Row']
type TemplateUpdate = Database['public']['Tables']['templates']['Update']
type TemplateInsert = Database['public']['Tables']['templates']['Insert']

interface TemplateCreateModalProps {
  onClose: () => void
  onSave: (templateData: CreateTemplateDTO) => Promise<void>
  isLoading: boolean
}

const TemplateCreateModal: React.FC<TemplateCreateModalProps> = ({ onClose, onSave, isLoading }) => {
  const [newTemplateData, setNewTemplateData] = useState<CreateTemplateDTO>({
    nome: '',
    categoria: '',
    descricao: '',
    content: {
      structure: {
        includeHeader: true,
        includeInstructions: true,
        includeAnswerSheet: false,
        pageBreaks: true
      },
      questions: {
        includeQuestions: false,
        questionIds: [],
        preserveOrder: true,
        allowModification: true
      },
      layout: {
        fontSize: 'medium',
        fontFamily: 'times',
        lineSpacing: 'normal',
        paperSize: 'A4',
        orientation: 'portrait'
      },
      customization: {
        headerConfig: {
          nomeEscola: 'Nome da Escola',
          nomeProva: 'Avaliação',
          serie: '',
          data: new Date().toLocaleDateString('pt-BR'),
          instrucoes: 'Leia atentamente cada questão antes de responder.'
        }
      }
    },
    metadata: {
      version: '2.0',
      createdWith: 'manual'
    },
    is_premium: false,
    is_system: false,
    tags: []
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement

    if (name === 'is_premium') {
      setNewTemplateData(prev => ({
        ...prev,
        is_premium: type === 'checkbox' ? checked : false
      }))
    } else if (name === 'is_system') {
      setNewTemplateData(prev => ({
        ...prev,
        is_system: type === 'checkbox' ? checked : false
      }))
    } else {
      setNewTemplateData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newTemplateData.nome || !newTemplateData.categoria) {
      toast.error('Nome e Categoria são obrigatórios.')
      return
    }

    // Update metadata with current timestamp
    const templateDataWithMetadata = {
      ...newTemplateData,
      metadata: {
        ...newTemplateData.metadata,
        createdAt: new Date().toISOString()
      }
    }

    await onSave(templateDataWithMetadata)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Novo Template</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
        <div>
            <label htmlFor="nome" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Nome do Template</label>
            <input
              type="text"
              id="nome"
              name="nome"
              value={newTemplateData.nome}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
        </div>
          <div>
            <label htmlFor="categoria" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Categoria</label>
          <input
            type="text"
              id="categoria"
              name="categoria"
              value={newTemplateData.categoria}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
          />
        </div>
          <div>
            <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Descrição (Opcional)</label>
            <textarea
              id="descricao"
              name="descricao"
              value={newTemplateData.descricao || ''}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Criando...' : 'Criar Template'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

const TemplateManagement: React.FC = () => {
  const queryClient = useQueryClient()

  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [templatesPerPage] = useState(9)
  const [showCreateTemplateModal, setShowCreateTemplateModal] = useState(false)
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [selectedTemplateForPreview, setSelectedTemplateForPreview] = useState<TemplateWithProfile | null>(null)
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)

  const { data: templates, isLoading, error, refetch } = useQuery<TemplateWithProfile[], Error>(
    {
      queryKey: ['templates', searchTerm, filterBy, currentPage],
      queryFn: async (): Promise<TemplateWithProfile[]> => {
        let query = supabase
          .from('templates')
          .select(`
            *,
            profiles (
              nome,
              email
            )
          `)

        if (searchTerm) {
          query = query.or(`nome.ilike.%${searchTerm}%,categoria.ilike.%${searchTerm}%,profiles.nome.ilike.%${searchTerm}%`)
        }

        if (filterBy === 'system') {
          query = query.eq('is_system', true)
        } else if (filterBy === 'user') {
          query = query.eq('is_system', false)
        }

        const from = (currentPage - 1) * templatesPerPage
        const to = from + templatesPerPage - 1
        query = query.order('created_at', { ascending: false }).range(from, to)

        const { data, error: fetchError } = await query

        if (fetchError) {
          throw fetchError
        }
        return data || []
      },
      staleTime: 1000 * 60 * 1
    }
  )

  const { data: totalTemplatesCount } = useQuery<number, Error>(
    {
      queryKey: ['totalTemplatesCount', searchTerm, filterBy],
      queryFn: async () => {
        let query = supabase.from('templates').select('id', { count: 'exact', head: true })

        if (searchTerm) {
          query = query.or(`nome.ilike.%${searchTerm}%,categoria.ilike.%${searchTerm}%,profiles.nome.ilike.%${searchTerm}%`)
        }

        if (filterBy === 'premium') {
          query = query.eq('is_premium', true)
        } else if (filterBy === 'free') {
          query = query.eq('is_premium', false)
        } else if (filterBy === 'system') {
          query = query.eq('is_system', true)
        } else if (filterBy === 'user') {
          query = query.eq('is_system', false)
        }

        const { count, error: countError } = await query
        if (countError) throw countError
        return count || 0
      },
      staleTime: 1000 * 60 * 1,
    }
  )

  const pageCount = Math.ceil((totalTemplatesCount || 0) / templatesPerPage)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleNextPage = () => {
    if (currentPage < pageCount) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }



  const deleteTemplateMutation = useMutation<void, Error, string>(
    {
      mutationFn: async (templateId) => {
        if (!confirm('Tem certeza que deseja excluir este template? Esta ação não pode ser desfeita.')) {
          throw new Error('Deletion cancelled by user.')
        }
        const { error } = await supabase
          .from('templates')
          .delete()
          .eq('id', templateId)

        if (error) throw error
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['templates'] })
        queryClient.invalidateQueries({ queryKey: ['totalTemplatesCount'] })
        toast.success('Template excluído com sucesso')
      },
      onError: (err) => {
        console.error('Error deleting template:', err)
        if (err.message !== 'Deletion cancelled by user.') {
          toast.error('Erro ao excluir template')
        }
      }
    }
  )

  const createTemplateMutation = useMutation({
    mutationFn: async (templateData: CreateTemplateDTO) => {
      const { data: user } = await supabase.auth.getUser()

      const insertData: TemplateInsert = {
        nome: templateData.nome,
        categoria: templateData.categoria,
        descricao: templateData.descricao || null,
        content: templateData.content,
        metadata: {
          ...templateData.metadata,
          createdAt: new Date().toISOString()
        },
        layout_config: templateData.content.layout, // Backward compatibility
        is_premium: templateData.is_premium || false,
        is_system: templateData.is_system || false,
        autor_id: user.user?.id || null,
        tags: templateData.tags || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('templates')
        .insert(insertData)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] })
      queryClient.invalidateQueries({ queryKey: ['totalTemplatesCount'] })
      toast.success('Template criado com sucesso!')
      setShowCreateTemplateModal(false)
    },
    onError: (error) => {
      console.error('Error creating template:', error)
      toast.error(`Erro ao criar template: ${error.message}`)
    },
  })



  const handleDeleteTemplate = (templateId: string) => {
    deleteTemplateMutation.mutate(templateId)
  }

  const handleCreateTemplate = async (templateData: CreateTemplateDTO) => {
    await createTemplateMutation.mutateAsync(templateData)
  }

  const handleOpenCreateTemplateModal = () => {
    setShowCreateTemplateModal(true)
  }

  const handleCloseCreateTemplateModal = () => {
    setShowCreateTemplateModal(false)
  }

  const handleOpenPreview = (template: TemplateWithProfile) => {
    setSelectedTemplateForPreview(template)
    setShowPreviewModal(true)
    setOpenDropdownId(null)
  }

  const handleClosePreview = () => {
    setShowPreviewModal(false)
    setSelectedTemplateForPreview(null)
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando templates...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">
              Erro ao carregar templates: {error.message}
            </p>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Por favor, tente novamente mais tarde.
            </p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Gerenciamento de Templates</h1>
          <p className="text-gray-600 dark:text-gray-400">{totalTemplatesCount} templates encontrados</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleOpenCreateTemplateModal}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5 mr-2" /> Criar Novo Template
          </button>
          <button
            onClick={() => refetch()}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <RefreshCw className="w-5 h-5 mr-2" /> Atualizar
          </button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar templates por nome, categoria ou autor..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        <select
          value={filterBy}
          onChange={(e) => {
            setFilterBy(e.target.value)
            setCurrentPage(1)
          }}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">Todos os Tipos</option>
          <option value="system">Sistema</option>
          <option value="user">Usuário</option>
        </select>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates && templates.length > 0 ? (
          templates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 flex flex-col"
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white line-clamp-2">{template.nome}</h3>
                <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                  {template.is_system && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">
                      Sistema
                    </span>
                  )}
                  <div className="relative inline-block text-left">
                    <button
                      onClick={() => setOpenDropdownId(openDropdownId === template.id ? null : template.id)}
                      className="p-1 rounded-full text-gray-500 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                      aria-expanded={openDropdownId === template.id ? 'true' : 'false'}
                      aria-haspopup="true"
                    >
                      <MoreVertical className="w-5 h-5" />
                    </button>
                    {openDropdownId === template.id && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="menu-button"
                        tabIndex={-1}
                      >
                        <div className="py-1" role="none">
                          <Link
                            to={`/app/editor/${template.id}`}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            <Edit className="mr-3 h-4 w-4 text-blue-500" />
                            Editar Template
                          </Link>
                          <button
                            onClick={() => handleOpenPreview(template)}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            <Eye className="mr-3 h-4 w-4 text-green-500" />
                            Preview do Template
                          </button>
                          <button
                            onClick={() => {
                              handleDeleteTemplate(template.id)
                              setOpenDropdownId(null)
                            }}
                            disabled={deleteTemplateMutation.isPending}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            {deleteTemplateMutation.isPending ? (
                              <Loader className="mr-3 h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="mr-3 h-4 w-4 text-red-500" />
                            )}
                            Excluir Template
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 flex-1 line-clamp-3">
                {template.descricao || 'Nenhuma descrição disponível.'}
              </p>
              {template.content && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 mr-2">
                    {template.content.questions?.includeQuestions ?
                      `${template.content.questions.questionIds?.length || 0} questões` :
                      'Apenas estrutura'
                    }
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    {template.content.layout?.fontSize || 'medium'} • {template.content.layout?.paperSize || 'A4'}
                  </span>
                </div>
              )}
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-auto">
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>{template.profiles?.nome || 'Desconhecido'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(template.created_at || '').toLocaleDateString('pt-BR')}</span>
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500 dark:text-gray-400">
            Nenhum template encontrado.
          </div>
        )}
      </div>

      {pageCount > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-4">
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Anterior
          </button>
          {Array.from({ length: pageCount }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 rounded-md ${
                currentPage === page
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={handleNextPage}
            disabled={currentPage === pageCount}
            className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Próxima
          </button>
        </div>
      )}

      {showCreateTemplateModal && (
        <TemplateCreateModal
          onClose={handleCloseCreateTemplateModal}
          onSave={handleCreateTemplate}
          isLoading={createTemplateMutation.isPending}
        />
      )}

      <TemplatePreviewModal
        isOpen={showPreviewModal}
        onClose={handleClosePreview}
        template={selectedTemplateForPreview}
      />
    </div>
  )
}

export default TemplateManagement