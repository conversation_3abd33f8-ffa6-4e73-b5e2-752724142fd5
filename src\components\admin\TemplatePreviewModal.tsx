import React from 'react'
import { X, <PERSON>Text, Settings, Layout, Palette, CheckCircle2, Circle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { EnhancedTemplate } from '../../types/templates'

interface TemplatePreviewModalProps {
  isOpen: boolean
  onClose: () => void
  template: EnhancedTemplate | null
}

const TemplatePreviewModal: React.FC<TemplatePreviewModalProps> = ({
  isOpen,
  onClose,
  template
}) => {
  if (!template) return null

  const templateContent = template.content || {}
  const templateMetadata = template.metadata || {}

  // Generate preview content based on template structure
  const generatePreviewContent = () => {
    const structure = templateContent.structure || {}
    const layout = templateContent.layout || {}
    const customization = templateContent.customization || {}
    const questions = templateContent.questions || {}

    return {
      header: structure.includeHeader ? customization.headerConfig : null,
      hasInstructions: structure.includeInstructions,
      hasAnswerSheet: structure.includeAnswerSheet,
      pageBreaks: structure.pageBreaks,
      fontSize: layout.fontSize || 'medium',
      fontFamily: layout.fontFamily || 'times',
      lineSpacing: layout.lineSpacing || 'normal',
      paperSize: layout.paperSize || 'A4',
      orientation: layout.orientation || 'portrait',
      includesQuestions: questions.includeQuestions,
      questionCount: questions.questionIds?.length || 0,
      watermark: customization.watermark
    }
  }

  const preview = generatePreviewContent()

  const getFontSizeClass = (size: string) => {
    switch (size) {
      case 'small': return 'text-xs'
      case 'large': return 'text-base'
      default: return 'text-sm'
    }
  }

  const getLineSpacingClass = (spacing: string) => {
    switch (spacing) {
      case 'compact': return 'leading-tight'
      case 'expanded': return 'leading-loose'
      default: return 'leading-normal'
    }
  }

  const getFontFamilyClass = (family: string) => {
    switch (family) {
      case 'helvetica': return 'font-sans'
      case 'courier': return 'font-mono'
      default: return 'font-serif'
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex-shrink-0 p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Preview do Template
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {template.nome} • {template.categoria}
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
                {/* Template Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Informações do Template
                    </h3>
                    
                    <div className="space-y-3">
                      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Básico</h4>
                        <div className="space-y-2 text-sm">
                          <div><strong>Nome:</strong> {template.nome}</div>
                          <div><strong>Categoria:</strong> {template.categoria}</div>
                          {template.descricao && (
                            <div><strong>Descrição:</strong> {template.descricao}</div>
                          )}
                          <div><strong>Tipo:</strong> {template.is_premium ? 'Premium' : 'Gratuito'}</div>
                          <div><strong>Versão:</strong> {templateMetadata.version || '1.0'}</div>
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Elementos Incluídos</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center space-x-2">
                            {templateContent.structure?.includeHeader ? (
                              <CheckCircle2 className="w-4 h-4 text-green-500" />
                            ) : (
                              <Circle className="w-4 h-4 text-gray-400" />
                            )}
                            <span>Cabeçalho personalizado</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {templateContent.structure?.includeInstructions ? (
                              <CheckCircle2 className="w-4 h-4 text-green-500" />
                            ) : (
                              <Circle className="w-4 h-4 text-gray-400" />
                            )}
                            <span>Instruções</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {templateContent.questions?.includeQuestions ? (
                              <CheckCircle2 className="w-4 h-4 text-green-500" />
                            ) : (
                              <Circle className="w-4 h-4 text-gray-400" />
                            )}
                            <span>Questões específicas ({preview.questionCount})</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {templateContent.structure?.includeAnswerSheet ? (
                              <CheckCircle2 className="w-4 h-4 text-green-500" />
                            ) : (
                              <Circle className="w-4 h-4 text-gray-400" />
                            )}
                            <span>Folha de respostas</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Configurações de Layout</h4>
                        <div className="space-y-2 text-sm">
                          <div><strong>Fonte:</strong> {preview.fontSize} ({preview.fontFamily})</div>
                          <div><strong>Espaçamento:</strong> {preview.lineSpacing}</div>
                          <div><strong>Papel:</strong> {preview.paperSize} - {preview.orientation}</div>
                          <div><strong>Quebras de página:</strong> {preview.pageBreaks ? 'Sim' : 'Não'}</div>
                          {preview.watermark && (
                            <div><strong>Marca d'água:</strong> {preview.watermark}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Visual Preview */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Preview Visual
                    </h3>
                    
                    <div className="bg-white border-2 border-gray-200 rounded-lg shadow-sm overflow-hidden">
                      {/* Paper simulation */}
                      <div className={`
                        ${preview.orientation === 'landscape' ? 'aspect-[4/3]' : 'aspect-[3/4]'}
                        ${getFontFamilyClass(preview.fontFamily)}
                        ${getFontSizeClass(preview.fontSize)}
                        ${getLineSpacingClass(preview.lineSpacing)}
                        p-6 bg-white relative
                      `}>
                        {/* Watermark */}
                        {preview.watermark && (
                          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <div className="text-gray-200 text-4xl font-bold transform rotate-45 opacity-20">
                              {preview.watermark}
                            </div>
                          </div>
                        )}

                        {/* Header */}
                        {preview.header && (
                          <div className="text-center mb-6 pb-4 border-b border-gray-200">
                            <h1 className="text-lg font-bold mb-2">
                              {preview.header.nomeEscola || 'Nome da Escola'}
                            </h1>
                            <h2 className="text-base font-semibold mb-1">
                              {preview.header.nomeProva || 'Nome da Avaliação'}
                            </h2>
                            <div className="text-sm text-gray-600 space-y-1">
                              {preview.header.serie && <div>Série: {preview.header.serie}</div>}
                              {preview.header.data && <div>Data: {preview.header.data}</div>}
                            </div>
                          </div>
                        )}

                        {/* Instructions */}
                        {preview.hasInstructions && (
                          <div className="mb-6">
                            <h3 className="font-semibold mb-2">Instruções:</h3>
                            <p className="text-gray-700">
                              {preview.header?.instrucoes || 'Leia atentamente cada questão antes de responder.'}
                            </p>
                          </div>
                        )}

                        {/* Sample Questions */}
                        <div className="space-y-4">
                          <div className="border-l-4 border-blue-500 pl-4">
                            <h4 className="font-semibold mb-2">1. Questão de exemplo</h4>
                            <p className="text-gray-700 mb-2">
                              Esta é uma questão de exemplo para demonstrar como o template será aplicado.
                            </p>
                            <div className="space-y-1 text-sm">
                              <div>a) Alternativa A</div>
                              <div>b) Alternativa B</div>
                              <div>c) Alternativa C</div>
                              <div>d) Alternativa D</div>
                            </div>
                          </div>

                          {preview.includesQuestions && preview.questionCount > 0 && (
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <p className="text-sm text-blue-800">
                                <strong>Questões do template:</strong> Este template inclui {preview.questionCount} questões específicas que serão carregadas automaticamente.
                              </p>
                            </div>
                          )}

                          {preview.pageBreaks && (
                            <div className="border-t-2 border-dashed border-gray-300 pt-4 mt-6">
                              <p className="text-xs text-gray-500 text-center">
                                --- Quebra de página ---
                              </p>
                            </div>
                          )}
                        </div>

                        {/* Answer Sheet Preview */}
                        {preview.hasAnswerSheet && (
                          <div className="mt-6 pt-4 border-t border-gray-200">
                            <h3 className="font-semibold mb-3">Folha de Respostas</h3>
                            <div className="grid grid-cols-5 gap-2 text-xs">
                              {Array.from({ length: 10 }, (_, i) => (
                                <div key={i} className="flex items-center space-x-1">
                                  <span>{i + 1}.</span>
                                  <div className="flex space-x-1">
                                    {['A', 'B', 'C', 'D'].map(letter => (
                                      <div key={letter} className="w-4 h-4 border border-gray-300 rounded-sm" />
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mt-4 text-xs text-gray-500 text-center">
                      Preview simulado • Tamanho: {preview.paperSize} {preview.orientation}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex-shrink-0 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Criado em {new Date(template.created_at).toLocaleDateString('pt-BR')}
                  {template.profiles && (
                    <span> por {template.profiles.nome}</span>
                  )}
                </div>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Fechar
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default TemplatePreviewModal
