import React, { useState, useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  MessageSquare,
  BarChart3,
  FileText,
  Shield,
  AlertTriangle
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import FeedbackManagementTab from './feedback-tabs/FeedbackManagementTab'
import FeedbackDashboardTab from './feedback-tabs/FeedbackDashboardTab'
import FeedbackReportsTab from './feedback-tabs/FeedbackReportsTab'
import FeedbackAuditTab from './feedback-tabs/FeedbackAuditTab'

type TabId = 'manage' | 'dashboard' | 'reports' | 'audit'

interface Tab {
  id: TabId
  label: string
  icon: React.ComponentType<any>
  component: React.ComponentType<any>
  description: string
}

const tabs: Tab[] = [
  {
    id: 'manage',
    label: 'Gerenciar',
    icon: MessageSquare,
    component: FeedbackManagementTab,
    description: 'Moderar e responder feedbacks dos usuários'
  },
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    component: FeedbackDashboardTab,
    description: 'Métricas e estatísticas de feedback'
  },
  {
    id: 'reports',
    label: 'Relatórios',
    icon: FileText,
    component: FeedbackReportsTab,
    description: 'Relatórios detalhados por questão'
  },
  {
    id: 'audit',
    label: 'Auditoria',
    icon: Shield,
    component: FeedbackAuditTab,
    description: 'Histórico de ações administrativas'
  }
]

const UnifiedFeedbackManagement: React.FC = () => {
  const { isAdmin } = useAuth()
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  
  // Get active tab from URL params, default to 'manage'
  const activeTabId = (searchParams.get('tab') as TabId) || 'manage'
  const activeTab = tabs.find(tab => tab.id === activeTabId) || tabs[0]

  const handleTabChange = (tabId: TabId) => {
    setSearchParams({ tab: tabId })
  }

  // Handle legacy routes - redirect to unified page with appropriate tab
  useEffect(() => {
    const currentPath = window.location.pathname
    if (currentPath.includes('/admin/feedback/dashboard')) {
      navigate('/admin/feedback?tab=dashboard', { replace: true })
    } else if (currentPath.includes('/admin/feedback/report')) {
      navigate('/admin/feedback?tab=reports', { replace: true })
    } else if (currentPath.includes('/admin/feedback/audit')) {
      navigate('/admin/feedback?tab=audit', { replace: true })
    }
  }, [navigate])

  if (!isAdmin) {
    return (
      <div className="p-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Acesso Negado
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Você precisa ter permissões de administrador para acessar esta página.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Sistema de Feedbacks
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {activeTab.description}
          </p>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = tab.id === activeTabId
            
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`group inline-flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-red-500 text-red-600 dark:text-red-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                aria-current={isActive ? 'page' : undefined}
              >
                <Icon className={`w-5 h-5 ${
                  isActive 
                    ? 'text-red-500 dark:text-red-400' 
                    : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                }`} />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        <motion.div
          key={activeTabId}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="h-full"
        >
          <activeTab.component />
        </motion.div>
      </div>
    </div>
  )
}

export default UnifiedFeedbackManagement
