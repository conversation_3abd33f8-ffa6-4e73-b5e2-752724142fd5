import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  Search,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  User,
  Calendar,
  Eye,
  MessageSquare
} from 'lucide-react'
import { useQuestionFeedback, useFeedbackStats, getFeedbackStatus, getFeedbackTypeLabel } from '../../../hooks/useQuestionFeedback'
import { Database } from '../../../types/database'
import FeedbackDetailModal from '../FeedbackDetailModal'
import FeedbackStatsCard from '../FeedbackStatsCard'

type QuestionFeedback = Database['public']['Tables']['question_feedback']['Row'] & {
  profiles: { nome: string; email: string } | null
  questions: { enunciado: string; disciplina: string; serie: string; topico: string } | null
}

interface FeedbackFilters {
  search: string
  status: 'all' | 'pending' | 'approved' | 'rejected'
  feedbackType: string
  rating: string
}

const FeedbackManagementTab: React.FC = () => {
  const [filters, setFilters] = useState<FeedbackFilters>({
    search: '',
    status: 'all',
    feedbackType: '',
    rating: ''
  })
  const [selectedFeedback, setSelectedFeedback] = useState<QuestionFeedback | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 20

  // Build query filters for the hook
  const queryFilters = useMemo(() => {
    const filters_obj: any = {
      limit: itemsPerPage,
      page: currentPage
    }

    if (filters.status !== 'all') {
      if (filters.status === 'pending') {
        filters_obj.is_reviewed = false
      } else if (filters.status === 'approved') {
        filters_obj.is_approved = true
        filters_obj.is_reviewed = true
      } else if (filters.status === 'rejected') {
        filters_obj.is_approved = false
        filters_obj.is_reviewed = true
      }
    }

    if (filters.feedbackType) {
      filters_obj.feedback_type = filters.feedbackType
    }

    if (filters.rating) {
      filters_obj.rating = parseInt(filters.rating)
    }

    return filters_obj
  }, [filters, currentPage])

  const { 
    feedback, 
    isLoading, 
    moderateFeedback, 
    deleteFeedback, 
    isModerating, 
    isDeleting 
  } = useQuestionFeedback(queryFilters)

  const { data: stats } = useFeedbackStats()

  // Filter feedback by search term (client-side for now)
  const filteredFeedback = useMemo(() => {
    if (!filters.search) return feedback

    const searchLower = filters.search.toLowerCase()
    return feedback.filter((item) => {
      const user = item.profiles?.nome?.toLowerCase() || ''
      const email = item.profiles?.email?.toLowerCase() || ''
      const comment = item.comment?.toLowerCase() || ''
      const question = item.questions?.enunciado?.toLowerCase() || ''
      const disciplina = item.questions?.disciplina?.toLowerCase() || ''
      
      return user.includes(searchLower) ||
             email.includes(searchLower) ||
             comment.includes(searchLower) ||
             question.includes(searchLower) ||
             disciplina.includes(searchLower)
    })
  }, [feedback, filters.search])

  const handleViewDetails = (feedback: QuestionFeedback) => {
    setSelectedFeedback(feedback)
    setShowDetailModal(true)
  }

  const handleApproveFeedback = async (feedbackId: string, adminResponse?: string) => {
    try {
      moderateFeedback({ 
        id: feedbackId, 
        is_approved: true, 
        admin_response: adminResponse 
      })
    } catch (error) {
      console.error('Error approving feedback:', error)
    }
  }

  const handleRejectFeedback = async (feedbackId: string, adminResponse?: string) => {
    try {
      moderateFeedback({ 
        id: feedbackId, 
        is_approved: false, 
        admin_response: adminResponse 
      })
    } catch (error) {
      console.error('Error rejecting feedback:', error)
    }
  }

  const handleDeleteFeedback = async (feedbackId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este feedback? Esta ação não pode ser desfeita.')) {
      try {
        deleteFeedback(feedbackId)
      } catch (error) {
        console.error('Error deleting feedback:', error)
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FeedbackStatsCard
            title="Total de Feedbacks"
            value={stats.total_feedback}
            icon={MessageSquare}
            color="blue"
          />
          <FeedbackStatsCard
            title="Pendentes"
            value={stats.pending_feedback}
            icon={Clock}
            color="yellow"
          />
          <FeedbackStatsCard
            title="Aprovados"
            value={stats.approved_feedback}
            icon={CheckCircle}
            color="green"
          />
          <FeedbackStatsCard
            title="Avaliação Média"
            value={stats.avg_rating.toFixed(1)}
            icon={Star}
            color="purple"
            suffix="/5"
          />
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Buscar
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Buscar por usuário, comentário, questão..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos</option>
              <option value="pending">Pendentes</option>
              <option value="approved">Aprovados</option>
              <option value="rejected">Rejeitados</option>
            </select>
          </div>

          {/* Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tipo
            </label>
            <select
              value={filters.feedbackType}
              onChange={(e) => setFilters(prev => ({ ...prev, feedbackType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todos os tipos</option>
              <option value="rating">Avaliação</option>
              <option value="improvement">Melhoria</option>
              <option value="error">Erro</option>
              <option value="general">Geral</option>
            </select>
          </div>

          {/* Rating Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Avaliação
            </label>
            <select
              value={filters.rating}
              onChange={(e) => setFilters(prev => ({ ...prev, rating: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todas</option>
              <option value="1">1 estrela</option>
              <option value="2">2 estrelas</option>
              <option value="3">3 estrelas</option>
              <option value="4">4 estrelas</option>
              <option value="5">5 estrelas</option>
            </select>
          </div>
        </div>
      </div>

      {/* Feedback List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando feedbacks...</p>
          </div>
        ) : filteredFeedback.length === 0 ? (
          <div className="p-8 text-center">
            <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhum feedback encontrado
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Não há feedbacks que correspondam aos filtros selecionados.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredFeedback.map((item) => {
              const status = getFeedbackStatus(item)
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* Header */}
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="flex items-center space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`w-4 h-4 ${
                                star <= item.rating
                                  ? 'text-yellow-500 fill-current'
                                  : 'text-gray-300 dark:text-gray-600'
                              }`}
                            />
                          ))}
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                            ({item.rating}/5)
                          </span>
                        </div>
                        
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          status.status === 'pending' 
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                            : status.status === 'approved'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}>
                          {status.label}
                        </span>
                        
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                          {getFeedbackTypeLabel(item.feedback_type)}
                        </span>
                      </div>

                      {/* User and Question Info */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                            <User className="w-4 h-4" />
                            <span>{item.profiles?.nome || 'Usuário desconhecido'}</span>
                            <span className="text-gray-400">•</span>
                            <span>{item.profiles?.email}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(item.created_at).toLocaleDateString('pt-BR')}</span>
                          </div>
                        </div>
                      </div>

                      {/* Question Preview */}
                      {item.questions && (
                        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3">
                          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                            {item.questions.disciplina} • {item.questions.serie} • {item.questions.topico}
                          </div>
                          <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                            {item.questions.enunciado}
                          </p>
                        </div>
                      )}

                      {/* Comment */}
                      {item.comment && (
                        <div className="mb-3">
                          <p className="text-gray-900 dark:text-white text-sm">
                            {item.comment}
                          </p>
                        </div>
                      )}

                      {/* Suggestions */}
                      {item.suggestions && item.suggestions.length > 0 && (
                        <div className="mb-3">
                          <div className="flex flex-wrap gap-1">
                            {item.suggestions.map((suggestion, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded text-xs bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                              >
                                {suggestion}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Admin Response */}
                      {item.admin_response && (
                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border-l-4 border-blue-500">
                          <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mb-1">
                            Resposta da Equipe:
                          </div>
                          <p className="text-sm text-blue-800 dark:text-blue-200">
                            {item.admin_response}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleViewDetails(item)}
                        className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="Ver detalhes"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      
                      {!item.is_reviewed && (
                        <>
                          <button
                            onClick={() => handleApproveFeedback(item.id)}
                            disabled={isModerating}
                            className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors disabled:opacity-50"
                            title="Aprovar"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </button>
                          
                          <button
                            onClick={() => handleRejectFeedback(item.id)}
                            disabled={isModerating}
                            className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors disabled:opacity-50"
                            title="Rejeitar"
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {selectedFeedback && (
        <FeedbackDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false)
            setSelectedFeedback(null)
          }}
          feedback={selectedFeedback}
          onApprove={handleApproveFeedback}
          onReject={handleRejectFeedback}
          onDelete={handleDeleteFeedback}
          isLoading={isModerating || isDeleting}
        />
      )}
    </div>
  )
}

export default FeedbackManagementTab
