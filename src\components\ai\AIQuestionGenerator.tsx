import React, { useState } from 'react'
import { <PERSON><PERSON><PERSON>, Wand2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, X } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useQuestions } from '../../hooks/useQuestions'
import { supabase } from '../../lib/supabase'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'

type QuestionInsert = Database['public']['Tables']['questions']['Insert']

interface AIGenerationRequest {
  disciplina: string
  serie: string
  topico: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  contexto?: string
  quantidade: number
}

interface GeneratedQuestion {
  id: string
  enunciado: string
  alternativas?: string[]
  resposta_correta: string
  explicacao: string
  competencia_bncc?: string
  subtopico?: string
  confidence: number
}

const AIQuestionGenerator: React.FC = () => {
  const { canAccess } = useSubscription()
  const { createQuestion } = useQuestions()
  const [generating, setGenerating] = useState(false)
  const [request, setRequest] = useState<AIGenerationRequest>({
    disciplina: '',
    serie: '',
    topico: '',
    dificuldade: 'Médio',
    tipo: 'multipla_escolha',
    contexto: '',
    quantidade: 1
  })
  const [generatedQuestions, setGeneratedQuestions] = useState<GeneratedQuestion[]>([])

  if (!canAccess('ai_generation')) {
    return (
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Geração por IA</h3>
            <p className="text-sm text-gray-600">Recurso Premium</p>
          </div>
        </div>
        <p className="text-gray-700 mb-4">
          Gere questões automaticamente usando inteligência artificial. 
          Disponível apenas para assinantes Premium.
        </p>
        <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
          Fazer Upgrade
        </button>
      </div>
    )
  }

  const handleGenerate = async () => {
    if (!request.disciplina || !request.serie || !request.topico) {
      toast.error('Preencha todos os campos obrigatórios')
      return
    }

    setGenerating(true)

    try {
      // Try to call the Edge Function if it exists
      try {
        const { data, error } = await supabase.functions.invoke('generate-question-ai', {
          body: request
        })

        if (error) throw error

        // The Edge Function should return an array of questions or a single question
        const newQuestions: GeneratedQuestion[] = Array.isArray(data) ? data : [data]
        setGeneratedQuestions(newQuestions)
        toast.success(`${newQuestions.length} questão(ões) gerada(s) com sucesso!`)
        return
      } catch (error) {
        console.warn('Edge function not available or failed:', error)
        // Fall back to mock generation if the Edge Function fails or doesn't exist
      }

      // Mock AI generation as fallback
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const mockQuestions: GeneratedQuestion[] = Array.from({ length: request.quantidade }, (_, i) => ({
        id: `ai-${Date.now()}-${i}`,
        enunciado: `Questão gerada por IA sobre ${request.topico} para ${request.serie} em ${request.disciplina}. Esta é uma questão de exemplo que seria gerada pela inteligência artificial baseada nos parâmetros fornecidos.`,
        alternativas: request.tipo === 'multipla_escolha' ? [
          'Primeira alternativa gerada',
          'Segunda alternativa gerada',
          'Terceira alternativa gerada',
          'Quarta alternativa gerada'
        ] : undefined,
        resposta_correta: request.tipo === 'multipla_escolha' ? 'a' : 'Resposta dissertativa gerada pela IA',
        explicacao: 'Explicação detalhada gerada pela IA sobre como chegar à resposta correta, incluindo conceitos relevantes e metodologia.',
        competencia_bncc: 'EF07MA01',
        confidence: 0.85 + Math.random() * 0.1
      }))

      setGeneratedQuestions(mockQuestions)
      toast.success(`${mockQuestions.length} questão(ões) gerada(s) com sucesso!`)
    } catch (error: any) {
      console.error('AI generation error:', error)
      toast.error(`Erro ao gerar questões: ${error.message}`)
    } finally {
      setGenerating(false)
    }
  }

  const handleAcceptQuestion = async (question: GeneratedQuestion) => {
    try {
      // Map GeneratedQuestion to QuestionInsert
      const questionToSave: QuestionInsert = {
        disciplina: request.disciplina,
        serie: request.serie,
        topico: request.topico,
        subtopico: question.subtopico || null,
        dificuldade: request.dificuldade,
        tipo: request.tipo,
        competencia_bncc: question.competencia_bncc || null,
        enunciado: question.enunciado,
        alternativas: question.alternativas || null,
        resposta_correta: question.resposta_correta,
        explicacao: question.explicacao,
        imagem_url: null, // AI doesn't generate images
        tags: [], // AI doesn't generate tags, you could add logic for this
        uso_count: 0,
        rating: 0,
        rating_count: 0,
        is_public: true, // AI-generated questions are public by default
        is_verified: false, // They need to be manually verified
        metadata: { ai_confidence: question.confidence }
      }

      await createQuestion(questionToSave) // Save the question to the database
      
      // Track AI question acceptance
      try {
        await supabase.from('usage_stats').insert({
          action_type: 'ai_question_accepted',
          resource_type: 'question',
          metadata: {
            disciplina: request.disciplina,
            serie: request.serie,
            topico: request.topico,
            confidence: question.confidence
          }
        })
      } catch (error) {
        console.error('Error tracking AI question acceptance:', error)
      }
      
      toast.success('Questão aceita e salva!')
      setGeneratedQuestions(prev => prev.filter(q => q.id !== question.id))
    } catch (error: any) {
      console.error('Error accepting AI question:', error)
      toast.error(`Erro ao aceitar questão: ${error.message}`)
    }
  }

  const handleRejectQuestion = (questionId: string) => {
    // Track AI question rejection
    try {
      supabase.from('usage_stats').insert({
        action_type: 'ai_question_rejected',
        resource_type: 'question',
        metadata: {
          disciplina: request.disciplina,
          serie: request.serie,
          topico: request.topico
        }
      })
    } catch (error) {
      console.error('Error tracking AI question rejection:', error)
    }
    
    setGeneratedQuestions(prev => prev.filter(q => q.id !== questionId))
    toast.success('Questão rejeitada')
  }

  const handleRegenerateQuestion = async (questionId: string) => {
    // Track regeneration request
    try {
      supabase.from('usage_stats').insert({
        action_type: 'ai_question_regenerated',
        resource_type: 'question',
        metadata: {
          disciplina: request.disciplina,
          serie: request.serie,
          topico: request.topico
        }
      })
    } catch (error) {
      console.error('Error tracking AI question regeneration:', error)
    }
    
    toast.success('Regenerando questão...')
    // Remove the current question and generate a new one
    setGeneratedQuestions(prev => prev.filter(q => q.id !== questionId))
    
    // Generate a new question with the same parameters but quantity = 1
    try {
      setGenerating(true)
      
      // Mock regeneration for now
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const newQuestion: GeneratedQuestion = {
        id: `ai-${Date.now()}`,
        enunciado: `Questão regenerada sobre ${request.topico} para ${request.serie} em ${request.disciplina}. Esta é uma nova versão da questão anterior.`,
        alternativas: request.tipo === 'multipla_escolha' ? [
          'Nova alternativa A',
          'Nova alternativa B',
          'Nova alternativa C',
          'Nova alternativa D'
        ] : undefined,
        resposta_correta: request.tipo === 'multipla_escolha' ? 'a' : 'Nova resposta dissertativa',
        explicacao: 'Nova explicação detalhada gerada pela IA.',
        competencia_bncc: 'EF07MA01',
        confidence: 0.9
      }
      
      setGeneratedQuestions(prev => [...prev, newQuestion])
      toast.success('Questão regenerada com sucesso!')
    } catch (error: any) {
      console.error('Error regenerating question:', error)
      toast.error(`Erro ao regenerar questão: ${error.message}`)
    } finally {
      setGenerating(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Generator Form */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Gerador de Questões por IA
            </h3>
            <p className="text-sm text-gray-600">
              Crie questões automaticamente usando inteligência artificial
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Disciplina *
            </label>
            <select
              value={request.disciplina}
              onChange={(e) => setRequest(prev => ({ ...prev, disciplina: e.target.value }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="">Selecione...</option>
              <option value="Matemática">Matemática</option>
              <option value="Português">Português</option>
              <option value="Ciências">Ciências</option>
              <option value="História">História</option>
              <option value="Geografia">Geografia</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Série *
            </label>
            <select
              value={request.serie}
              onChange={(e) => setRequest(prev => ({ ...prev, serie: e.target.value }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="">Selecione...</option>
              <option value="6º Ano">6º Ano</option>
              <option value="7º Ano">7º Ano</option>
              <option value="8º Ano">8º Ano</option>
              <option value="9º Ano">9º Ano</option>
              <option value="1º Médio">1º Médio</option>
              <option value="2º Médio">2º Médio</option>
              <option value="3º Médio">3º Médio</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dificuldade
            </label>
            <select
              value={request.dificuldade}
              onChange={(e) => setRequest(prev => ({ ...prev, dificuldade: e.target.value as any }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="Fácil">Fácil</option>
              <option value="Médio">Médio</option>
              <option value="Difícil">Difícil</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Questão
            </label>
            <select
              value={request.tipo}
              onChange={(e) => setRequest(prev => ({ ...prev, tipo: e.target.value as any }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="multipla_escolha">Múltipla Escolha</option>
              <option value="dissertativa">Dissertativa</option>
              <option value="verdadeiro_falso">Verdadeiro/Falso</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantidade
            </label>
            <select
              value={request.quantidade}
              onChange={(e) => setRequest(prev => ({ ...prev, quantidade: parseInt(e.target.value) }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value={1}>1 questão</option>
              <option value={3}>3 questões</option>
              <option value={5}>5 questões</option>
              <option value={10}>10 questões</option>
            </select>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tópico *
          </label>
          <input
            type="text"
            value={request.topico}
            onChange={(e) => setRequest(prev => ({ ...prev, topico: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="Ex: Frações, Interpretação de texto, Ecologia..."
          />
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Contexto Adicional (opcional)
          </label>
          <textarea
            value={request.contexto}
            onChange={(e) => setRequest(prev => ({ ...prev, contexto: e.target.value }))}
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="Forneça contexto adicional, exemplos específicos ou requisitos especiais para as questões..."
          />
        </div>

        <button
          onClick={handleGenerate}
          disabled={generating}
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-400 text-white px-6 py-3 rounded-lg transition-all duration-200"
        >
          {generating ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Gerando questões...</span>
            </>
          ) : (
            <>
              <Wand2 className="w-5 h-5" />
              <span>Gerar Questões</span>
            </>
          )}
        </button>
      </div>

      {/* Generated Questions */}
      <AnimatePresence>
        {generatedQuestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-semibold text-gray-900">
              Questões Geradas ({generatedQuestions.length})
            </h4>

            {generatedQuestions.map((question, index) => (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl border border-gray-200 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium">
                      IA Gerada
                    </span>
                    <span className="text-sm text-gray-500">
                      Confiança: {(question.confidence * 100).toFixed(0)}%
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleRegenerateQuestion(question.id)}
                      className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                      title="Regenerar"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => handleAcceptQuestion(question)}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Aceitar"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => handleRejectQuestion(question.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Rejeitar"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Enunciado:</h5>
                    <p className="text-gray-700">{question.enunciado}</p>
                  </div>

                  {question.alternativas && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Alternativas:</h5>
                      <div className="space-y-1">
                        {question.alternativas.map((alt, altIndex) => (
                          <div key={altIndex} className="text-gray-700">
                            {String.fromCharCode(97 + altIndex)}) {alt}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Resposta Correta:</h5>
                    <p className="text-gray-700">{question.resposta_correta}</p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Explicação:</h5>
                    <p className="text-gray-700">{question.explicacao}</p>
                  </div>

                  {question.competencia_bncc && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Competência BNCC:</h5>
                      <p className="text-gray-700">{question.competencia_bncc}</p>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default AIQuestionGenerator