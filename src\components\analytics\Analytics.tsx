import React from 'react'
import { BarChart3, TrendingUp, Users, FileText, Crown } from 'lucide-react'
import { motion } from 'framer-motion'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useUserAnalytics } from '../../hooks/useUserAnalytics'
import LoadingSpinner from '../common/LoadingSpinner'

const Analytics: React.FC = () => {
  const { canAccess } = useSubscription()
  const { analytics, isLoading } = useUserAnalytics()

  if (!canAccess('analytics')) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl border border-gray-200 p-8 lg:p-12 text-center"
        >
          <div className="w-16 h-16 lg:w-20 lg:h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Crown className="w-8 h-8 lg:w-10 lg:h-10 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Analytics Premium
          </h3>
          <p className="text-gray-600 mb-4">
            Acesse relatórios detalhados e insights sobre suas avaliações com o plano Premium.
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
            Fazer Upgrade
          </button>
        </motion.div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Carregando analytics..." />
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50">
        <div className="bg-white rounded-xl border border-gray-200 p-8 lg:p-12 text-center">
          <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <BarChart3 className="w-8 h-8 lg:w-10 lg:h-10 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Dados de Analytics Indisponíveis
          </h3>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados de analytics.
          </p>
        </div>
      </div>
    )
  }

  const stats = [
    {
      label: 'Questões Criadas',
      value: analytics.questionsCreated,
      change: '+12%',
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      label: 'Avaliações Geradas',
      value: analytics.assessmentsGenerated,
      change: '+8%',
      icon: BarChart3,
      color: 'bg-green-500'
    },
    {
      label: 'PDFs Baixados',
      value: analytics.pdfDownloads,
      change: '+15%',
      icon: TrendingUp,
      color: 'bg-purple-500'
    },
    {
      label: 'Tópicos Mais Usados',
      value: analytics.mostUsedTopics.length > 0 ? analytics.mostUsedTopics[0].topic : 'N/A',
      change: '+22%',
      icon: Users,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50">
      <div className="mb-6">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Analytics</h1>
        <p className="text-gray-600">
          Acompanhe o desempenho das suas avaliações e questões
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.label}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                  <p className="text-sm font-medium text-green-600 mt-1">
                    {stat.change}
                  </p>
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white rounded-xl border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Atividade dos Últimos 7 Dias
          </h3>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            {/* Renderizar gráfico de barras com analytics.weeklyActivity */}
            <div className="flex items-end space-x-2 h-full w-full p-4">
              {analytics.weeklyActivity.map((day, index) => (
                <div key={index} className="flex-1 flex flex-col items-center justify-end h-full">
                  <div
                    className="bg-blue-600 w-full rounded-t"
                    style={{ height: `${Math.max((day.count / Math.max(...analytics.weeklyActivity.map(d => d.count), 1)) * 100, 5)}%` }}
                  />
                  <span className="text-xs text-gray-600 mt-1">{day.date.split('/')[0]}</span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-white rounded-xl border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Tópicos Mais Utilizados
          </h3>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            {/* Renderizar lista de tópicos com analytics.mostUsedTopics */}
            <div className="space-y-2 w-full p-4">
              {analytics.mostUsedTopics.length === 0 ? (
                <p className="text-gray-500 text-center">Nenhum tópico utilizado recentemente.</p>
              ) : (
                analytics.mostUsedTopics.map((topic, index) => (
                  <div key={topic.topic} className="flex justify-between items-center">
                    <span>{topic.topic}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(topic.count / analytics.mostUsedTopics[0].count) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">{topic.count}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Analytics