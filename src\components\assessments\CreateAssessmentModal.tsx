import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON><PERSON>, Settings, ArrowRight, Zap, Cog } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface CreateAssessmentModalProps {
  isOpen: boolean
  onClose: () => void
}

const CreateAssessmentModal: React.FC<CreateAssessmentModalProps> = ({
  isOpen,
  onClose
}) => {
  const navigate = useNavigate()

  if (!isOpen) return null

  const handleModeSelect = (mode: 'wizard' | 'advanced') => {
    onClose()
    navigate(`/app/${mode}`)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="relative p-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Criar Nova Avaliação</h2>
            <p className="text-blue-100">
              Escolha o modo de criação que melhor se adapta às suas necessidades
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Easy Mode */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="group cursor-pointer"
              onClick={() => handleModeSelect('wizard')}
            >
              <div className="relative h-full p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-700 rounded-xl hover:border-green-400 dark:hover:border-green-500 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
                {/* Badge */}
                <div className="absolute -top-3 left-6">
                  <span className="bg-green-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                    RECOMENDADO
                  </span>
                </div>

                {/* Icon */}
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  Modo Fácil
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  Criação guiada passo a passo. Perfeito para iniciantes ou quando você quer rapidez e simplicidade.
                </p>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Interface guiada em 4 etapas
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Busca inteligente de questões
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Configurações simplificadas
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Preview antes de finalizar
                    </span>
                  </div>
                </div>

                {/* Action */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                    <Zap className="w-4 h-4" />
                    <span className="text-sm font-medium">Rápido e fácil</span>
                  </div>
                  <ArrowRight className="w-5 h-5 text-green-600 dark:text-green-400 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </motion.div>

            {/* Advanced Mode */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="group cursor-pointer"
              onClick={() => handleModeSelect('editor')}
            >
              <div className="h-full p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-2 border-blue-200 dark:border-blue-700 rounded-xl hover:border-blue-400 dark:hover:border-blue-500 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
                {/* Icon */}
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Settings className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  Modo Avançado
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  Controle total sobre todos os aspectos da avaliação. Ideal para usuários experientes.
                </p>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Interface completa em uma tela
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Filtros avançados de busca
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Personalização completa
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Drag & drop para reordenar
                    </span>
                  </div>
                </div>

                {/* Action */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
                    <Cog className="w-4 h-4" />
                    <span className="text-sm font-medium">Controle total</span>
                  </div>
                  <ArrowRight className="w-5 h-5 text-blue-600 dark:text-blue-400 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl"
          >
            <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
              💡 <strong>Dica:</strong> Se você está começando ou quer criar uma avaliação rapidamente, 
              recomendamos o <strong>Modo Fácil</strong>. Você sempre pode usar o Modo Avançado depois.
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}

export default CreateAssessmentModal
