import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../../lib/supabase'
import { generatePDF, downloadPDF } from '../../utils/pdfGenerator'
import toast from 'react-hot-toast'
import { 
  FileText, 
  Search, 
  Filter, 
  Download, 
  Edit, 
  Trash2, 
  Eye,
  Copy,
  Share2,
  Calendar,
  Clock,
  MoreVertical
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useAssessments } from '../../hooks/useAssessments'
import AssessmentPreview from '../editor/AssessmentPreview'
import { Database } from '../../types/database'
import ConfirmationModal from '../common/ConfirmationModal'
import CreateAssessmentModal from './CreateAssessmentModal'

type Question = Database['public']['Tables']['questions']['Row']
type TextBlock = {
  id: string
  type: 'text'
  content: string
  style?: 'normal' | 'heading' | 'subheading' | 'instruction'
  textAlign?: 'left' | 'center' | 'right' | 'justify'
}
type AssessmentItem = Question | TextBlock

const MyAssessments: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  const { assessments, isLoading, deleteAssessment } = useAssessments()
  const navigate = useNavigate()

  const [previewAssessment, setPreviewAssessment] = useState<any | null>(null)
  const [previewItems, setPreviewItems] = useState<(Question | TextBlock)[]>([])
  const [previewConfig, setPreviewConfig] = useState<any | null>(null)
  const [isViewLoading, setIsViewLoading] = useState(false)
  const [isDownloadLoading, setIsDownloadLoading] = useState(false)

  // Estados para o modal de confirmação
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [assessmentToDelete, setAssessmentToDelete] = useState<any | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Estado para o modal de criação
  const [showCreateModal, setShowCreateModal] = useState(false)

  const assembleItems = (questions: Question[], textBlocks: TextBlock[] = []) => {
    console.log('=== INICIANDO ASSEMBLEITEMS ===')
    console.log('Recebido:', { questions: questions.length, textBlocks: textBlocks.length })
    
    // Criar um array com todas as questões
    const questionItems: AssessmentItem[] = questions.map((q, index) => {
      console.log(`Questão ${index + 1}:`, q.id, q.enunciado?.substring(0, 50) + '...')
      return q as AssessmentItem
    })
    
    // Criar um array com todos os blocos de texto
    const textBlockItems: AssessmentItem[] = textBlocks.map((tb, index) => {
      console.log(`Bloco de texto ${index + 1}:`, tb.id, tb.content?.substring(0, 50) + '...')
      return {
        ...tb,
        type: 'text' as const
      }
    })
    
    // Combinar todos os itens (questões primeiro, depois blocos de texto)
    const allItems = [...questionItems, ...textBlockItems]
    console.log('=== ASSEMBLEITEMS FINALIZADO ===')
    console.log('Total de items montados:', allItems.length)
    
    return allItems
  }

  const fetchAssessmentData = async (assessment: any) => {
    console.log('=== INICIANDO FETCHASSESSMENTDATA ===')
    console.log('Assessment configuracao:', assessment.configuracao)
    
    const questoes_ids = assessment.questoes_ids || []
    const textBlocks = assessment.configuracao?.textBlocks || []

    console.log('Questões IDs:', questoes_ids)
    console.log('Text Blocks:', textBlocks)

    let questions: Question[] = []
    
    if (questoes_ids.length > 0) {
      try {
        console.log('Consultando Supabase para questões:', questoes_ids)
        
        const { data: questionsData, error } = await supabase
          .from('questions')
          .select('*')
          .in('id', questoes_ids)

        console.log('Resposta do Supabase:', { data: questionsData, error })

        if (error) {
          console.error('Erro ao buscar questões:', error)
          throw new Error(`Erro ao buscar questões: ${error.message}`)
        }

        questions = questionsData || []
        console.log('Questões encontradas (antes da ordenação):', questions.length)
        
        // Ordenar questões na ordem dos IDs
        questions = questoes_ids.map((id: string) => {
          const found = questions.find(q => q.id === id)
          console.log(`Buscando questão ${id}:`, found ? 'encontrada' : 'não encontrada')
          return found
        }).filter(Boolean)
        
        console.log('Questões ordenadas:', questions.length)
        
      } catch (error) {
        console.error('Erro na consulta de questões:', error)
        throw error
      }
    } else {
      console.log('Nenhuma questão para carregar')
    }

    console.log('=== FETCHASSESSMENTDATA FINALIZADO ===')
    console.log('Retornando:', { questions: questions.length, textBlocks: textBlocks.length })
    return { questions, textBlocks }
  }

  const handleView = async (assessment: any) => {
    console.log('=== INICIANDO HANDLEVIEW ===')
    console.log('Assessment recebido:', assessment)
    
    // Prevenir execuções simultâneas
    if (isViewLoading) {
      console.log('HandleView já está executando, ignorando...')
      return
    }
    
    setIsViewLoading(true)
    
    // Limpar states anteriores primeiro
    setPreviewAssessment(null)
    setPreviewItems([])
    setPreviewConfig(null)
    
    // Limpar toasts anteriores
    toast.dismiss()
    
    // Timeout de segurança
    const timeoutId = setTimeout(() => {
      console.log('Timeout atingido, resetando estados...')
      setIsViewLoading(false)
      toast.dismiss()
      toast.error('Timeout ao carregar avaliação')
    }, 10000) // 10 segundos
    
    try {
      const toastId = 'preview-load'
      toast.loading('Carregando avaliação...', { id: toastId })
      
      if (!assessment || !assessment.id) {
        console.error('Assessment inválido:', assessment)
        throw new Error('Avaliação inválida')
      }
      
      console.log('Carregando dados da avaliação:', assessment.id)
      
      // Configuração simples
      const simpleConfig = {
        titulo: assessment.titulo || 'Avaliação',
        disciplina: assessment.disciplina || 'Geral',
        serie: assessment.serie || 'Geral',
        headerConfig: {
          nomeEscola: 'Nome da Escola',
          nomeProva: assessment.titulo || 'Avaliação',
          serie: assessment.serie || 'Geral',
          data: new Date().toLocaleDateString('pt-BR'),
          instrucoes: 'Leia atentamente cada questão antes de responder.'
        },
        pdfOptions: {
          paperSize: 'A4' as const,
          orientation: 'portrait' as const,
          fontSize: 'medium' as const,
          lineSpacing: 'normal' as const,
          includeAnswerSheet: true,
          generateVersions: 1
        },
        showFooter: true
      }
      
      console.log('Config definida:', simpleConfig)
      
      // Carregar questões e blocos de texto
      console.log('Iniciando fetchAssessmentData...')
      const { questions, textBlocks } = await fetchAssessmentData(assessment)
      console.log('Dados carregados:', { questions: questions.length, textBlocks: textBlocks.length })
      
      // Montar items
      console.log('Iniciando assembleItems...')
      const items = assembleItems(questions, textBlocks)
      console.log('Items montados:', items.length)
      
      console.log('Definindo states...')
      setPreviewConfig(simpleConfig)
      setPreviewItems(items)
      setPreviewAssessment(assessment)
      
      clearTimeout(timeoutId) // Limpar timeout se sucesso
      toast.dismiss(toastId)
      console.log('=== HANDLEVIEW FINALIZADO COM SUCESSO ===')
    } catch (error: any) {
      console.error('ERRO em handleView:', error)
      clearTimeout(timeoutId) // Limpar timeout se erro
      toast.dismiss()
      toast.error(`Erro ao carregar avaliação: ${error.message}`)
      
      // Limpar states em caso de erro
      setPreviewAssessment(null)
      setPreviewItems([])
      setPreviewConfig(null)
    } finally {
      setIsViewLoading(false)
    }
  }

  const handleEdit = (assessment: any) => {
    navigate(`/app/editor?aid=${assessment.id}`)
  }

  const handleDownload = async (assessment: any) => {
    // Prevenir execuções simultâneas
    if (isDownloadLoading) {
      return
    }
    
    setIsDownloadLoading(true)
    
    try {
      toast.loading('Gerando PDF...', { id: 'pdf-generation' })

      // Carregar dados da avaliação
      const { questions, textBlocks } = await fetchAssessmentData(assessment)

      if (questions.length === 0 && textBlocks.length === 0) {
        toast.error('Avaliação sem conteúdo', { id: 'pdf-generation' })
        return
      }

      // Montar items - usar mesma lógica do editor
      const items = assembleItems(questions, textBlocks)

      // Configuração simples como no editor
      const pdfOptions = {
        paperSize: 'A4' as const,
        orientation: 'portrait' as const,
        fontSize: 'medium' as const,
        lineSpacing: 'normal' as const,
        includeAnswerSheet: true,
        generateVersions: 1,
        headerConfig: {
          nomeEscola: 'Nome da Escola',
          nomeProva: assessment.titulo || 'Avaliação',
          serie: assessment.serie || 'Geral',
          data: new Date().toLocaleDateString('pt-BR'),
          instrucoes: 'Leia atentamente cada questão antes de responder.'
        },
        showFooter: true
      }
      
      // Gerar PDF - mesma lógica do editor
      const blob = await generatePDF(items, pdfOptions)
      
      // Download - mesma lógica do editor
      downloadPDF(blob, `${assessment.titulo}.pdf`)
      
      toast.success('PDF gerado com sucesso!', { id: 'pdf-generation' })
    } catch (error: any) {
      console.error('Error generating PDF:', error)
      toast.error('Erro ao gerar PDF', { id: 'pdf-generation' })
    } finally {
      setIsDownloadLoading(false)
    }
  }

  const handleDeleteClick = (assessment: any) => {
    setAssessmentToDelete(assessment)
    setShowDeleteModal(true)
  }

  const handleDeleteConfirm = async () => {
    if (!assessmentToDelete) return
    
    setIsDeleting(true)
    try {
      await deleteAssessment(assessmentToDelete.id)
      setShowDeleteModal(false)
      setAssessmentToDelete(null)
    } catch (error) {
      console.error('Erro ao excluir avaliação:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setAssessmentToDelete(null)
  }

  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = assessment.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assessment.disciplina.toLowerCase().includes(searchTerm.toLowerCase())
    
    if (filterBy === 'all') return matchesSearch
    if (filterBy === 'recent') {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      return matchesSearch && new Date(assessment.created_at) > weekAgo
    }
    
    return matchesSearch
  })

  if (isLoading) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50 flex items-center justify-center dark:bg-gray-900">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Carregando avaliações...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900">
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">Minhas Avaliações</h1>
            <p className="text-gray-600 mt-1 dark:text-gray-400">
              {filteredAssessments.length} avaliações encontradas
            </p>
          </div>

          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors shadow-sm"
          >
            <FileText className="w-5 h-5" />
            <span>Nova Avaliação</span>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar avaliações..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
            />
          </div>
          
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
          >
            <option value="all">Todas</option>
            <option value="recent">Recentes</option>
          </select>
        </div>
      </div>

      {filteredAssessments.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl border border-gray-200 p-8 lg:p-12 text-center dark:bg-gray-800 dark:border-gray-700"
        >
          <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-gray-700">
            <FileText className="w-8 h-8 lg:w-10 lg:h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2 dark:text-white">
            Nenhuma avaliação encontrada
          </h3>
          <p className="text-gray-600 mb-4 dark:text-gray-400">
            Comece criando sua primeira avaliação.
          </p>
        </motion.div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
          {filteredAssessments.map((assessment, index) => (
            <motion.div
              key={assessment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 group dark:bg-gray-800 dark:border-gray-700"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center dark:bg-blue-900/30">
                    <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 truncate dark:text-white">
                      {assessment.titulo}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {assessment.disciplina} • {assessment.serie}
                    </p>
                  </div>
                </div>
                
                <div className="relative">
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-500 dark:hover:text-gray-400">
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <FileText className="w-4 h-4" />
                    <span>{assessment.questoes_ids?.length || 0} questões</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(assessment.created_at).toLocaleDateString('pt-BR')}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => {
                    console.log('BOTÃO VISUALIZAR CLICADO!')
                    console.log('Assessment para visualizar:', assessment)
                    handleView(assessment)
                  }} 
                  disabled={isViewLoading}
                  className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                    isViewLoading 
                      ? 'bg-gray-400 cursor-not-allowed' 
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  {isViewLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                  <span>{isViewLoading ? 'Carregando...' : 'Visualizar'}</span>
                </button>
                
                <button 
                  onClick={() => handleEdit(assessment)} 
                  className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700"
                >
                  <Edit className="w-4 h-4" />
                </button>
                
                <button 
                  onClick={() => {
                    console.log('BOTÃO DOWNLOAD CLICADO!')
                    console.log('Assessment para download:', assessment)
                    handleDownload(assessment)
                  }} 
                  disabled={isDownloadLoading}
                  className={`p-2 rounded-lg transition-colors ${
                    isDownloadLoading
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700'
                  }`}
                >
                  {isDownloadLoading ? (
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Download className="w-4 h-4" />
                  )}
                </button>
                
                <button 
                  onClick={() => handleDeleteClick(assessment)}
                  className="p-2 text-red-600 hover:text-red-800 rounded-lg hover:bg-red-50 transition-colors dark:hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {previewAssessment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
            {previewConfig && !isViewLoading ? (
              <AssessmentPreview
                items={previewItems}
                config={previewConfig}
                assessment={previewAssessment}
                onEdit={handleEdit}
                onDelete={handleDeleteClick}
                onClose={() => {
                  console.log('Fechando modal de preview')
                  setPreviewAssessment(null)
                  setPreviewItems([])
                  setPreviewConfig(null)
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-600 dark:text-gray-400">Carregando preview...</p>
                  <p className="text-xs text-gray-500 mt-2">
                    Config: {previewConfig ? 'OK' : 'Aguardando'} | Items: {previewItems.length}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal de Confirmação de Exclusão */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Excluir Avaliação"
        message={`Tem certeza que deseja excluir a avaliação "${assessmentToDelete?.titulo}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        type="danger"
        isLoading={isDeleting}
      />

      {/* Modal de Criação de Avaliação */}
      <CreateAssessmentModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  )
}

export default MyAssessments