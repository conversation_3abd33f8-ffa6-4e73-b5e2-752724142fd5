import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const AcceptInvitePage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { refreshProfile, user: authUser, loading: authLoading } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Verificando seu convite...');

  // Log de renderização do componente
  console.log('AcceptInvitePage: Render. Status:', status, 'Auth User ID:', authUser?.id || 'Nenhum', 'Auth Loading:', authLoading);

  useEffect(() => {
    // Log do início do useEffect
    console.log('AcceptInvitePage: useEffect. Location search:', location.search, 'Auth Loading no useEffect:', authLoading, 'Auth User no useEffect:', authUser?.id || 'Nenhum', 'Current Status:', status);
    const queryParams = new URLSearchParams(location.search);
    const token = queryParams.get('token');

    // NOVO LOG: Verificando o token diretamente no useEffect
    console.log('AcceptInvitePage: useEffect - Token lido:', token);

    if (!token) {
      console.error('AcceptInvitePage: Erro - Token não encontrado na URL.');
      setStatus('error');
      setMessage('Token de convite não encontrado na URL.');
      toast.error('Token de convite inválido.');
      return;
    }

    // Se AuthContext ainda está carregando, espere.
    if (authLoading) {
      console.log('AcceptInvitePage: AuthContext ainda carregando. Aguardando...');
      return;
    }

    // Se AuthContext terminou de carregar e o usuário NÃO está autenticado, redireciona para o cadastro
    if (!authUser) {
      console.log('AcceptInvitePage: Usuário não autenticado APÓS o carregamento do AuthContext. Redirecionando para cadastro.');
      navigate(`/register?redirect_to=/accept-invite&invite_token=${token}`);
      return;
    }

    // Agora, estamos seguros: token existe, authLoading é false, authUser existe.
    // Procede apenas se ainda estivermos no estado 'loading' para evitar chamadas múltiplas.
    if (status === 'loading') {
      console.log('AcceptInvitePage: Condições para acceptInvite() atendidas. Chamando acceptInvite...');
      
      const acceptInvite = async () => {
        console.log('AcceptInvitePage: [acceptInvite] Início da função.');
        console.log('AcceptInvitePage: [acceptInvite] Chamando supabase.functions.invoke(\'accept-invite\')...');
        
        try {
          const { data, error } = await supabase.functions.invoke('accept-invite', {
            body: { token },
          });

          console.log('AcceptInvitePage: [acceptInvite] PROMESSA RESOLVIDA. Resposta RAW da Edge Function:', data, 'Erro RAW:', error);

          if (error) {
            console.error('AcceptInvitePage: [acceptInvite] Erro ao invocar a Edge Function (supabase.functions.invoke error):', error);
            setStatus('error');
            setMessage(`Falha ao aceitar o convite: ${error.message}`);
            toast.error(`Erro: ${error.message}`);
            return;
          }

          if (data && data.error) {
            console.error('AcceptInvitePage: [acceptInvite] Erro retornado pela Edge Function (data.error):', data.error);
            setStatus('error');
            setMessage(`Falha ao aceitar o convite: ${data.error}`);
            toast.error(`Erro: ${data.error}`);
            return;
          }

          console.log('AcceptInvitePage: [acceptInvite] Convite aceito com sucesso pela Edge Function.');
          setStatus('success');
          setMessage('Convite aceito com sucesso! Redirecionando para o painel...');
          toast.success('Convite aceito com sucesso!');
          console.log('AcceptInvitePage: [acceptInvite] Chamando refreshProfile...');

          try {
            await refreshProfile();
            console.log('AcceptInvitePage: [acceptInvite] refreshProfile concluído.');
          } catch (profileError: any) {
            console.error('AcceptInvitePage: [acceptInvite] Erro ao chamar refreshProfile:', profileError);
            toast.error(`Erro ao atualizar o perfil: ${profileError.message}`);
          }

          setTimeout(() => {
            console.log('AcceptInvitePage: [acceptInvite] Redirecionando para /app/school-admin...');
            navigate('/app/school-admin');
          }, 3000);

        } catch (err: any) {
          console.error('AcceptInvitePage: [acceptInvite] ERRO INESPERADO AO INVOCAR A EF:', err);
          setStatus('error');
          setMessage(`Ocorreu um erro inesperado na chamada da função: ${err.message}`);
          toast.error(`Erro inesperado na chamada da função: ${err.message}`);
        }
        console.log('AcceptInvitePage: [acceptInvite] Fim da função.');
      };

      acceptInvite(); // Chama a função assíncrona
    }
    
  }, [location.search, navigate, refreshProfile, authUser, authLoading, status]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 text-center p-4">
      {console.log('AcceptInvitePage: Render final. Status ao renderizar:', status)}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 max-w-md w-full">
        {status === 'loading' && (
          <div className="flex flex-col items-center">
            <Loader2 className="w-12 h-12 text-blue-500 animate-spin mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Processando Convite...</h2>
            <p className="text-gray-600 dark:text-gray-400">{message}</p>
          </div>
        )}
        {status === 'success' && (
          <div className="flex flex-col items-center">
            <CheckCircle className="w-12 h-12 text-green-500 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Sucesso!</h2>
            <p className="text-gray-600 dark:text-gray-400">{message}</p>
          </div>
        )}
        {status === 'error' && (
          <div className="flex flex-col items-center">
            <XCircle className="w-12 h-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Erro</h2>
            <p className="text-gray-600 dark:text-gray-400">{message}</p>
            <button
              onClick={() => navigate('/login')}
              className="mt-6 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Voltar ao Login
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AcceptInvitePage; 