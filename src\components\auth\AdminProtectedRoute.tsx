import React, { useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { Shield, AlertTriangle } from 'lucide-react'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import toast from 'react-hot-toast'

interface AdminProtectedRouteProps {
  children: React.ReactNode
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { user, profile, loading, isAdmin, refreshProfile } = useAuth()

  // Add debugging logs
  console.log('AdminProtectedRoute - user:', user)
  console.log('AdminProtectedRoute - profile:', profile)
  console.log('AdminProtectedRoute - isAdmin:', isAdmin)
  console.log('AdminProtectedRoute - loading:', loading)

  // Force refresh profile when component mounts
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user && !isAdmin) {
        console.log('Refreshing profile to check admin status...')
        try {
          await refreshProfile()
          
          // Mostrar mensagem se ainda não for admin após atualização
          if (!isAdmin) {
            toast.error('Você precisa ter permissões de administrador para acessar esta área')
          }
        } catch (error) {
          console.error('Error refreshing profile:', error)
          toast.error('Erro ao verificar permissões de administrador')
        }
      }
    }
    
    checkAdminStatus()
  }, [user, isAdmin, refreshProfile])

  // Show loading screen while auth is initializing
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-600 dark:bg-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <div className="w-8 h-8 border-4 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Verificando permissões...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no user
  if (!user) {
    console.log('No user found, redirecting to home')
    return <Navigate to="/" replace />
  }

  // Show access denied if not admin
  if (!isAdmin) {
    console.log('User is not admin, showing access denied')
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-8 max-w-md w-full text-center"
        >
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Acesso Negado
          </h2>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Você não tem permissão para acessar o painel de administração.
            Verifique se você marcou a opção "Administrador" nas configurações do seu perfil.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={() => window.history.back()}
              className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-lg transition-colors"
            >
              Voltar
            </button>
            
            <button
              onClick={() => window.location.href = '/settings'}
              className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Ir para Configurações
            </button>
          </div>
        </motion.div>
      </div>
    )
  }

  // Show admin interface if user is admin
  console.log('User is admin, showing admin interface')
  return <>{children}</>
}

export default AdminProtectedRoute