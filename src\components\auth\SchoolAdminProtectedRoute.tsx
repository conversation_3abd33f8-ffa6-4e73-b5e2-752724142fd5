import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Loader } from 'lucide-react';

interface SchoolAdminProtectedRouteProps {
  children: React.ReactNode;
}

const SchoolAdminProtectedRoute: React.FC<SchoolAdminProtectedRouteProps> = ({ children }) => {
  const { user, profile, isSchoolAdmin, loading: authLoading } = useAuth();

  console.log('SchoolAdminProtectedRoute: Renderizando...');
  console.log('SchoolAdminProtectedRoute: authLoading:', authLoading);
  console.log('SchoolAdminProtectedRoute: user:', user);
  console.log('SchoolAdminProtectedRoute: profile:', profile);
  console.log('SchoolAdminProtectedRoute: isSchoolAdmin:', isSchoolAdmin);
  console.log('SchoolAdminProtectedRoute: profile?.school_id:', profile?.school_id);

  if (authLoading) {
    console.log('SchoolAdminProtectedRoute: authLoading is TRUE. Exibindo loader...');
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
        <Loader className="w-8 h-8 text-blue-600 animate-spin" />
        <p className="ml-2 text-gray-700 dark:text-gray-300">Verificando permissões...</p>
      </div>
    );
  }

  // Se não houver usuário, ou não for admin da escola, ou não tiver school_id, redireciona
  if (!user || !isSchoolAdmin || !profile?.school_id) {
    console.log('SchoolAdminProtectedRoute: Redirecionando para /app. Condições:');
    console.log('  !user:', !user);
    console.log('  !isSchoolAdmin:', !isSchoolAdmin);
    console.log('  !profile?.school_id:', !profile?.school_id);
    return <Navigate to="/app" replace />; // Redireciona para o dashboard principal
  }

  console.log('SchoolAdminProtectedRoute: Permissões concedidas. Renderizando children.');
  return <>{children}</>;
};

export default SchoolAdminProtectedRoute; 