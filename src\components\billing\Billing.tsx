import React, { useEffect } from 'react'
import { Crown } from 'lucide-react'
import { motion } from 'framer-motion'
import PricingPlans from './PricingPlans'
import { useSubscription } from '../../contexts/SubscriptionContext'
import toast from 'react-hot-toast'

const Billing: React.FC = () => {
  const { refreshSubscription } = useSubscription()

  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    if (params.get('success') === 'true') {
      toast.success('Pagamento realizado com sucesso! Bem-vindo ao plano premium.')
      refreshSubscription()
      params.delete('success')
      params.delete('session_id')
      window.history.replaceState({}, '', window.location.pathname)
    } else if (params.get('canceled') === 'true') {
      toast('Pagamento cancelado. Sua assinatura não foi alterada.', {
        icon: '⚠️'
      })
      params.delete('canceled')
      window.history.replaceState({}, '', window.location.pathname)
    }
  }, [])

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          {/* Título removido conforme solicitado */}
        </motion.div>

        {/* Planos de Preços */}
        <PricingPlans />
      </div>
    </div>
  )
}

export default Billing