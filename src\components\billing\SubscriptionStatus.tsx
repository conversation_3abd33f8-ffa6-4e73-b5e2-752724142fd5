import React from 'react'
import { CheckCircle, AlertCircle, Clock, Crown } from 'lucide-react'
import { motion } from 'framer-motion'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useAuth } from '../../contexts/AuthContext'

const SubscriptionStatus: React.FC = () => {
  const { subscription, isPremium, isEscolar, isTrialing, daysLeftInTrial, loading } = useSubscription()
  const { user } = useAuth()

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-gray-600">Carregando status da assinatura...</span>
        </div>
      </div>
    )
  }

  const getStatusInfo = () => {
    if (!user) {
      return {
        icon: AlertCircle,
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        title: 'Não Autenticado',
        description: 'Faça login para gerenciar sua assinatura',
        status: 'error'
      }
    }

    if (isEscolar) {
      const title = isTrialing ? 'Teste Escolar Ativo' : 'Plano Escolar Ativo'
      const description = isTrialing
        ? `Teste gratuito - ${daysLeftInTrial} dias restantes`
        : 'Acesso completo a todos os recursos institucionais'

      return {
        icon: Crown,
        color: isTrialing ? 'text-orange-600' : 'text-purple-600',
        bgColor: isTrialing ? 'bg-orange-50' : 'bg-purple-50',
        borderColor: isTrialing ? 'border-orange-200' : 'border-purple-200',
        title,
        description,
        status: 'escolar'
      }
    }

    if (isPremium) {
      const title = isTrialing ? 'Teste Premium Ativo' : 'Plano Premium Ativo'
      const description = isTrialing
        ? `Teste gratuito - ${daysLeftInTrial} dias restantes`
        : 'Acesso a recursos avançados e geração por IA'

      return {
        icon: Crown,
        color: isTrialing ? 'text-orange-600' : 'text-blue-600',
        bgColor: isTrialing ? 'bg-orange-50' : 'bg-blue-50',
        borderColor: isTrialing ? 'border-orange-200' : 'border-blue-200',
        title,
        description,
        status: 'premium'
      }
    }

    return {
      icon: Clock,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      title: 'Plano Gratuito',
      description: 'Atualize para acessar recursos premium',
      status: 'free'
    }
  }

  const statusInfo = getStatusInfo()
  const Icon = statusInfo.icon

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`rounded-xl shadow-sm border p-6 ${statusInfo.bgColor} ${statusInfo.borderColor}`}
    >
      <div className="flex items-start space-x-4">
        <div className={`p-3 rounded-full ${statusInfo.bgColor}`}>
          <Icon className={`w-6 h-6 ${statusInfo.color}`} />
        </div>
        
        <div className="flex-1">
          <h3 className={`text-lg font-semibold ${statusInfo.color} mb-1`}>
            {statusInfo.title}
          </h3>
          <p className="text-gray-600 mb-4">
            {statusInfo.description}
          </p>

          {/* Subscription Details */}
          {subscription && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Status:</span>
                <span className={`font-medium ${
                  subscription.status === 'active' ? 'text-green-600' : 
                  subscription.status === 'trialing' ? 'text-blue-600' : 
                  'text-red-600'
                }`}>
                  {subscription.status === 'active' ? 'Ativo' :
                   subscription.status === 'trialing' ? 'Em teste' :
                   subscription.status === 'canceled' ? 'Cancelado' :
                   subscription.status}
                </span>
              </div>

              {subscription.current_period_end && (
                <div className="flex justify-between">
                  <span className="text-gray-500">
                    {subscription.status === 'trialing' ? 'Teste até:' : 'Próxima cobrança:'}
                  </span>
                  <span className="font-medium">
                    {new Date(subscription.current_period_end).toLocaleDateString('pt-BR')}
                  </span>
                </div>
              )}

              {subscription.cancel_at_period_end && (
                <div className="flex items-center space-x-2 text-orange-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">Cancelamento agendado para o fim do período</span>
                </div>
              )}
            </div>
          )}

          {/* Action Button for Free Users */}
          {statusInfo.status === 'free' && user && (
            <div className="mt-4">
              <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                Ver planos disponíveis ↓
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default SubscriptionStatus 