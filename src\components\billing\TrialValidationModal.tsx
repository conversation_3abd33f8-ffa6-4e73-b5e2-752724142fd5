import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AlertTriangle, Clock, Shield, X } from 'lucide-react'
import { useTrialValidation } from '../../hooks/useTrialValidation'

interface TrialValidationModalProps {
  isOpen: boolean
  onClose: () => void
  onProceed: () => void
  planType: 'premium' | 'escolar'
  planName: string
}

const TrialValidationModal: React.FC<TrialValidationModalProps> = ({
  isOpen,
  onClose,
  onProceed,
  planType,
  planName
}) => {
  const {
    isValidating,
    validateTrialEligibility,
    getErrorMessage
  } = useTrialValidation()
  
  const [validationResult, setValidationResult] = useState<any>(null)
  const [showDetails, setShowDetails] = useState(false)

  // Validate eligibility when modal opens
  useEffect(() => {
    if (isOpen && !validationResult) {
      validateTrialEligibility(planType).then(setValidationResult)
    }
  }, [isOpen, planType, validateTrialEligibility, validationResult])

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setValidationResult(null)
      setShowDetails(false)
    }
  }, [isOpen])

  const handleProceed = () => {
    if (validationResult?.allowed) {
      onProceed()
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full mx-4 p-6"
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>

          {/* Loading State */}
          {isValidating && (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Verificando Elegibilidade
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Validando se você pode iniciar o teste gratuito...
              </p>
            </div>
          )}

          {/* Validation Results */}
          {!isValidating && validationResult && (
            <>
              {/* Success State */}
              {validationResult.allowed && (
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-green-600 dark:text-green-400" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Teste Gratuito Aprovado!
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Você está elegível para iniciar o teste gratuito de 7 dias do plano {planName}.
                  </p>

                  <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      O que você receberá:
                    </h4>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• 7 dias de acesso completo</li>
                      <li>• Todos os recursos premium</li>
                      <li>• Cancele a qualquer momento</li>
                      <li>• Sem cobrança durante o teste</li>
                    </ul>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={onClose}
                      className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={handleProceed}
                      className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                    >
                      Iniciar Teste
                    </button>
                  </div>
                </div>
              )}

              {/* Error State */}
              {!validationResult.allowed && (
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Teste Não Disponível
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {getErrorMessage(validationResult)}
                  </p>

                  {/* Show cooldown information if available */}
                  {validationResult.cooldownEnds && (
                    <div className="bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-4">
                      <div className="flex items-center justify-center space-x-2 text-orange-800 dark:text-orange-200">
                        <Clock className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          Próximo teste disponível em: {new Date(validationResult.cooldownEnds).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Show details toggle */}
                  <button
                    onClick={() => setShowDetails(!showDetails)}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline mb-4"
                  >
                    {showDetails ? 'Ocultar detalhes' : 'Ver detalhes'}
                  </button>

                  {/* Details */}
                  {showDetails && (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4 text-left">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        Detalhes da Validação:
                      </h4>
                      <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <div><strong>Motivo:</strong> {validationResult.reason}</div>
                        <div><strong>Mensagem:</strong> {validationResult.message}</div>
                      </div>
                    </div>
                  )}

                  <div className="space-y-3">
                    <button
                      onClick={onClose}
                      className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                    >
                      Entendi
                    </button>
                    
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Você ainda pode assinar o plano {planName} sem o teste gratuito.
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default TrialValidationModal
