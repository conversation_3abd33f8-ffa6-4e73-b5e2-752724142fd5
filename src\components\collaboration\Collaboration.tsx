import React from 'react'
import { Users, Share2, MessageSquare, UserPlus } from 'lucide-react'
import FeatureComingSoon from '../common/FeatureComingSoon'

const Collaboration: React.FC = () => {
  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50">
      <div className="max-w-4xl mx-auto">
        <FeatureComingSoon
          title="Colaboração entre Professores"
          description="Em breve você poderá colaborar com outros professores, compartilhar questões e trabalhar em equipe na criação de avaliações."
          expectedDate="Q2 2024"
          features={[
            'Compartilhamento de questões entre professores',
            'Grupos de trabalho por escola ou disciplina',
            'Comentários e sugestões em questões',
            'Histórico de colaborações',
            'Permissões de acesso granulares',
            'Chat integrado para discussões'
          ]}
        />
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Share2 className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Compartilhamento
            </h3>
            <p className="text-sm text-gray-600">
              Compartilhe questões e avaliações com colegas de forma segura
            </p>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Grupos
            </h3>
            <p className="text-sm text-gray-600">
              Crie grupos de trabalho por disciplina ou projeto
            </p>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <MessageSquare className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Feedback
            </h3>
            <p className="text-sm text-gray-600">
              Receba e dê feedback sobre questões e avaliações
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Collaboration