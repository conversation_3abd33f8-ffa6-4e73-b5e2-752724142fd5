import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { motion } from 'framer-motion'

interface FeatureComingSoonProps {
  title: string
  description: string
  expectedDate?: string
  features?: string[]
}

const FeatureComingSoon: React.FC<FeatureComingSoonProps> = ({
  title,
  description,
  expectedDate,
  features = []
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-800 rounded-xl p-8 text-center"
    >
      <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
        <Sparkles className="w-8 h-8 text-blue-600 dark:text-blue-400" />
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
        {description}
      </p>
      
      {expectedDate && (
        <div className="flex items-center justify-center space-x-2 text-sm text-blue-700 dark:text-blue-400 mb-6">
          <Clock className="w-4 h-4" />
          <span>Previsão: {expectedDate}</span>
        </div>
      )}
      
      {features.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            Recursos planejados:
          </h4>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full" />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="text-xs text-gray-500 dark:text-gray-500">
        Acompanhe as atualizações em nosso roadmap
      </div>
    </motion.div>
  )
}

export default FeatureComingSoon