import React, { useState, useRef, useCallback } from 'react'
import { Upload, X, Image as ImageIcon, Trash2, Eye } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAssets, AssessmentAsset } from '../../hooks/useAssets'
import { formatFileSize } from '../../utils/imageUtils'

interface ImageUploadProps {
  assetType: 'custom_header' | 'school_logo'
  selectedAsset?: AssessmentAsset | null
  onAssetSelect: (asset: AssessmentAsset | null) => void
  title: string
  description?: string
  disabled?: boolean
  maxWidth?: string
  maxHeight?: string
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  assetType,
  selectedAsset,
  onAssetSelect,
  title,
  description,
  disabled = false,
  maxWidth = "400px",
  maxHeight = "200px"
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [dragActive, setDragActive] = useState(false)
  const [showAssetList, setShowAssetList] = useState(false)
  
  const {
    assets,
    isLoading,
    uploadProgress,
    uploadAsset,
    isUploading,
    deleteAsset,
    isDeleting,
    getAssetUrl
  } = useAssets(assetType)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }, [disabled])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const handleFileUpload = useCallback((file: File) => {
    uploadAsset({
      file,
      type: assetType,
      description: `${title} - ${file.name}`,
      onAssetCreated: (asset) => {
        // Auto-selecionar o asset recém-criado
        onAssetSelect(asset)
      }
    })
  }, [uploadAsset, assetType, title, onAssetSelect])

  const handleAssetSelect = useCallback((asset: AssessmentAsset) => {
    onAssetSelect(asset)
    setShowAssetList(false)
  }, [onAssetSelect])

  const handleRemoveSelected = useCallback(() => {
    onAssetSelect(null)
  }, [onAssetSelect])

  const handleDeleteAsset = useCallback((assetId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (selectedAsset?.id === assetId) {
      onAssetSelect(null)
    }
    deleteAsset(assetId)
  }, [deleteAsset, selectedAsset, onAssetSelect])

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {title}
        </h4>
        {description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
            {description}
          </p>
        )}
      </div>

      {/* Selected Asset Preview */}
      {selectedAsset && (
        <div className="relative border border-gray-300 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center space-x-3">
            <div 
              className="flex-shrink-0 w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden"
              style={{ maxWidth, maxHeight }}
            >
              <img
                src={getAssetUrl(selectedAsset.file_path)}
                alt={selectedAsset.file_name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {selectedAsset.file_name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatFileSize(selectedAsset.file_size)}
              </p>
            </div>
            <button
              onClick={handleRemoveSelected}
              disabled={disabled}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors disabled:opacity-50"
              title="Remover seleção"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Upload Area */}
      {!selectedAsset && (
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
            ${dragActive 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/png,image/jpeg,image/jpg"
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled}
          />
          
          {isUploading ? (
            <div className="space-y-2">
              <Upload className="w-8 h-8 text-blue-500 mx-auto animate-pulse" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enviando... {uploadProgress.progress}%
              </p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress.progress}%` }}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <ImageIcon className="w-8 h-8 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Clique ou arraste uma imagem aqui
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                PNG, JPG até 5MB
              </p>
            </div>
          )}
        </div>
      )}

      {/* Asset List Toggle */}
      {assets.length > 0 && (
        <div className="space-y-2">
          <button
            onClick={() => setShowAssetList(!showAssetList)}
            disabled={disabled}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline disabled:opacity-50"
          >
            {showAssetList ? 'Ocultar' : 'Escolher'} imagens salvas ({assets.length})
          </button>

          <AnimatePresence>
            {showAssetList && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-gray-50 dark:bg-gray-800"
              >
                <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                  {assets.map((asset) => (
                    <div
                      key={asset.id}
                      className={`
                        relative group cursor-pointer border rounded-lg p-2 transition-colors
                        ${selectedAsset?.id === asset.id 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                        }
                      `}
                      onClick={() => handleAssetSelect(asset)}
                    >
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 rounded overflow-hidden mb-2">
                        <img
                          src={getAssetUrl(asset.file_path)}
                          alt={asset.file_name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {asset.file_name}
                      </p>
                      
                      <button
                        onClick={(e) => handleDeleteAsset(asset.id, e)}
                        disabled={isDeleting}
                        className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600 disabled:opacity-50"
                        title="Excluir imagem"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </div>
  )
}

export default ImageUpload
