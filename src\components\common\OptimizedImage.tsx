import React, { forwardRef, useState, useEffect } from 'react'
import { useImageCache, useResponsiveImages } from '../../hooks/useAssetCache'

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  lazy?: boolean
  quality?: number
  priority?: 'high' | 'low'
  responsive?: boolean
  sizes?: number[]
  fallback?: string
  placeholder?: string
  onLoad?: () => void
  onError?: (error: string) => void
}

/**
 * Componente de imagem otimizada com cache inteligente
 */
export const OptimizedImage = forwardRef<HTMLImageElement, OptimizedImageProps>(({
  src,
  alt,
  lazy = true,
  quality = 0.8,
  priority = 'low',
  responsive = false,
  sizes = [320, 640, 1024, 1920],
  fallback,
  placeholder,
  className = '',
  onLoad,
  onError,
  ...props
}, ref) => {
  const [showPlaceholder, setShowPlaceholder] = useState(true)
  const [imageLoaded, setImageLoaded] = useState(false)
  
  // Hook para cache de imagem
  const { 
    imageUrl, 
    isLoading, 
    error, 
    dimensions, 
    elementRef 
  } = useImageCache(src, {
    lazy,
    quality,
    priority
  })
  
  // Hook para imagens responsivas
  const { srcSet, isLoading: srcSetLoading } = useResponsiveImages(
    responsive ? src : '',
    sizes
  )
  
  // Combinar refs
  const combinedRef = (element: HTMLImageElement | null) => {
    if (elementRef) {
      elementRef.current = element
    }
    if (ref) {
      if (typeof ref === 'function') {
        ref(element)
      } else {
        ref.current = element
      }
    }
  }
  
  // Lidar com carregamento da imagem
  const handleImageLoad = () => {
    setImageLoaded(true)
    setShowPlaceholder(false)
    onLoad?.()
  }
  
  // Lidar com erro da imagem
  const handleImageError = () => {
    setShowPlaceholder(false)
    onError?.(error || 'Failed to load image')
  }
  
  // Efeito para lidar com mudanças no estado de carregamento
  useEffect(() => {
    if (error) {
      handleImageError()
    }
  }, [error])
  
  // Classes CSS para transições suaves
  const imageClasses = `
    transition-opacity duration-300 ease-in-out
    ${imageLoaded ? 'opacity-100' : 'opacity-0'}
    ${className}
  `.trim()
  
  const placeholderClasses = `
    absolute inset-0 flex items-center justify-center
    bg-gray-200 dark:bg-gray-700
    transition-opacity duration-300 ease-in-out
    ${showPlaceholder && (isLoading || srcSetLoading) ? 'opacity-100' : 'opacity-0'}
  `.trim()
  
  return (
    <div className="relative overflow-hidden">
      {/* Placeholder */}
      {(showPlaceholder || isLoading || srcSetLoading) && (
        <div className={placeholderClasses}>
          {placeholder ? (
            <img 
              src={placeholder} 
              alt="" 
              className="w-full h-full object-cover blur-sm"
            />
          ) : (
            <div className="flex flex-col items-center justify-center text-gray-400">
              <svg 
                className="w-8 h-8 mb-2 animate-pulse" 
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path 
                  fillRule="evenodd" 
                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
                  clipRule="evenodd" 
                />
              </svg>
              <span className="text-sm">Carregando...</span>
            </div>
          )}
        </div>
      )}
      
      {/* Imagem principal */}
      {imageUrl && (
        <img
          ref={combinedRef}
          src={imageUrl}
          srcSet={responsive && srcSet ? srcSet : undefined}
          sizes={responsive ? "(max-width: 320px) 320px, (max-width: 640px) 640px, (max-width: 1024px) 1024px, 1920px" : undefined}
          alt={alt}
          className={imageClasses}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading={lazy ? 'lazy' : 'eager'}
          decoding="async"
          {...props}
        />
      )}
      
      {/* Imagem de fallback */}
      {error && fallback && (
        <img
          src={fallback}
          alt={alt}
          className={`${className} opacity-75`}
          onLoad={handleImageLoad}
          {...props}
        />
      )}
      
      {/* Indicador de erro */}
      {error && !fallback && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path 
                fillRule="evenodd" 
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
                clipRule="evenodd" 
              />
            </svg>
            <span className="text-sm">Erro ao carregar</span>
          </div>
        </div>
      )}
      
      {/* Informações de debug (apenas em desenvolvimento) */}
      {process.env.NODE_ENV === 'development' && dimensions && (
        <div className="absolute top-0 left-0 bg-black bg-opacity-50 text-white text-xs p-1">
          {dimensions.width}x{dimensions.height}
        </div>
      )}
    </div>
  )
})

OptimizedImage.displayName = 'OptimizedImage'

/**
 * Componente para avatar otimizado
 */
export const OptimizedAvatar: React.FC<{
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}> = ({ 
  src, 
  alt, 
  size = 'md', 
  fallback, 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }
  
  const defaultFallback = `data:image/svg+xml,${encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
      <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" clip-rule="evenodd" />
    </svg>
  `)}`
  
  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden ${className}`}>
      <OptimizedImage
        src={src || defaultFallback}
        alt={alt}
        fallback={fallback || defaultFallback}
        className="w-full h-full object-cover"
        lazy={false}
        priority="high"
        quality={0.9}
      />
    </div>
  )
}

/**
 * Componente para galeria de imagens otimizada
 */
export const OptimizedImageGallery: React.FC<{
  images: Array<{ src: string; alt: string; caption?: string }>
  columns?: number
  gap?: number
  className?: string
}> = ({ 
  images, 
  columns = 3, 
  gap = 4, 
  className = '' 
}) => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null)
  
  const gridClasses = `
    grid grid-cols-1 md:grid-cols-${columns} gap-${gap}
    ${className}
  `.trim()
  
  return (
    <>
      <div className={gridClasses}>
        {images.map((image, index) => (
          <div 
            key={index}
            className="cursor-pointer group"
            onClick={() => setSelectedImage(index)}
          >
            <div className="aspect-square overflow-hidden rounded-lg">
              <OptimizedImage
                src={image.src}
                alt={image.alt}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                lazy={true}
                responsive={true}
              />
            </div>
            {image.caption && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {image.caption}
              </p>
            )}
          </div>
        ))}
      </div>
      
      {/* Modal para visualização em tela cheia */}
      {selectedImage !== null && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="max-w-4xl max-h-full p-4">
            <OptimizedImage
              src={images[selectedImage].src}
              alt={images[selectedImage].alt}
              className="max-w-full max-h-full object-contain"
              lazy={false}
              priority="high"
              responsive={true}
            />
            <button
              className="absolute top-4 right-4 text-white text-2xl"
              onClick={() => setSelectedImage(null)}
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  )
}
