import React, { useState, useEffect } from 'react'
import { useWebVitals, WebVitalMetric } from '../../lib/performance/webVitals'

/**
 * Componente para monitorar performance em tempo real
 */
export const PerformanceMonitor: React.FC = () => {
  const { metrics, performanceScore } = useWebVitals()
  const [isVisible, setIsVisible] = useState(false)
  const [expandedMetric, setExpandedMetric] = useState<string | null>(null)
  
  // Apenas mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  const getMetricColor = (rating: WebVitalMetric['rating']) => {
    switch (rating) {
      case 'good':
        return 'text-green-600 bg-green-100'
      case 'needs-improvement':
        return 'text-yellow-600 bg-yellow-100'
      case 'poor':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }
  
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }
  
  const formatValue = (name: string, value: number) => {
    switch (name) {
      case 'CLS':
        return value.toFixed(3)
      case 'FID':
      case 'FCP':
      case 'LCP':
      case 'TTFB':
      case 'INP':
        return `${Math.round(value)}ms`
      default:
        return value.toString()
    }
  }
  
  const getMetricDescription = (name: string) => {
    const descriptions = {
      CLS: 'Cumulative Layout Shift - Estabilidade visual',
      FID: 'First Input Delay - Responsividade',
      FCP: 'First Contentful Paint - Velocidade de carregamento',
      LCP: 'Largest Contentful Paint - Velocidade de carregamento',
      TTFB: 'Time to First Byte - Tempo de resposta do servidor',
      INP: 'Interaction to Next Paint - Responsividade',
    }
    return descriptions[name as keyof typeof descriptions] || name
  }
  
  return (
    <>
      {/* Botão flutuante */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`fixed bottom-20 right-4 z-50 p-3 rounded-full shadow-lg transition-colors ${
          getScoreColor(performanceScore.score)
        } bg-white border-2 border-current`}
        title={`Performance Score: ${performanceScore.score} (${performanceScore.grade})`}
      >
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span className="absolute -top-1 -right-1 bg-current text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
          {performanceScore.grade}
        </span>
      </button>
      
      {/* Modal de métricas */}
      {isVisible && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Performance Monitor
                </h3>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold mr-2">
                    <span className={getScoreColor(performanceScore.score)}>
                      {performanceScore.score}
                    </span>
                  </span>
                  <span className={`px-2 py-1 rounded text-sm font-medium ${getScoreColor(performanceScore.score)} bg-current bg-opacity-10`}>
                    Grade {performanceScore.grade}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            {/* Métricas */}
            <div className="space-y-4">
              {metrics.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p>Coletando métricas de performance...</p>
                </div>
              ) : (
                metrics.map((metric) => (
                  <div
                    key={metric.name}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                  >
                    <div
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => setExpandedMetric(
                        expandedMetric === metric.name ? null : metric.name
                      )}
                    >
                      <div className="flex items-center">
                        <span className="font-medium text-gray-900 dark:text-white mr-3">
                          {metric.name}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getMetricColor(metric.rating)}`}>
                          {metric.rating.replace('-', ' ')}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-lg font-bold text-gray-900 dark:text-white mr-2">
                          {formatValue(metric.name, metric.value)}
                        </span>
                        <svg
                          className={`w-4 h-4 transition-transform ${
                            expandedMetric === metric.name ? 'rotate-180' : ''
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    
                    {expandedMetric === metric.name && (
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {getMetricDescription(metric.name)}
                        </p>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">ID:</span>
                            <span className="ml-2 font-mono text-xs">{metric.id}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Delta:</span>
                            <span className="ml-2">{formatValue(metric.name, metric.delta)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Navigation:</span>
                            <span className="ml-2">{metric.navigationType}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Rating:</span>
                            <span className={`ml-2 ${getMetricColor(metric.rating).split(' ')[0]}`}>
                              {metric.rating}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
            
            {/* Breakdown do score */}
            {Object.keys(performanceScore.breakdown).length > 0 && (
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Score Breakdown
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {Object.entries(performanceScore.breakdown).map(([name, score]) => (
                    <div key={name} className="text-center">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">
                        {score}
                      </div>
                      <div className="text-xs text-gray-500">{name}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Ações */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Recarregar Página
              </button>
              <button
                onClick={() => {
                  const data = {
                    score: performanceScore.score,
                    grade: performanceScore.grade,
                    metrics: metrics,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                  }
                  navigator.clipboard.writeText(JSON.stringify(data, null, 2))
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Copiar Dados
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

/**
 * Componente simples para mostrar score na barra de status
 */
export const PerformanceScore: React.FC = () => {
  const { performanceScore } = useWebVitals()
  
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  return (
    <div className="fixed top-4 left-4 z-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 border">
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 rounded-full bg-green-500"></div>
        <span className="text-sm font-medium">
          Performance: {performanceScore.score} ({performanceScore.grade})
        </span>
      </div>
    </div>
  )
}

/**
 * Hook para alertas de performance
 */
export const usePerformanceAlerts = () => {
  const { metrics } = useWebVitals()
  const [alerts, setAlerts] = useState<string[]>([])
  
  useEffect(() => {
    const newAlerts: string[] = []
    
    metrics.forEach(metric => {
      if (metric.rating === 'poor') {
        newAlerts.push(`${metric.name} está com performance ruim: ${metric.value}`)
      }
    })
    
    setAlerts(newAlerts)
  }, [metrics])
  
  return alerts
}
