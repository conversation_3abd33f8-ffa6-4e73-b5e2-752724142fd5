import React, { useState, useEffect } from 'react'
import { ChevronDown, Plus, School } from 'lucide-react'
import { useSchoolNames } from '../../hooks/useSchoolNames'

interface SchoolNameSelectProps {
  value: string
  onChange: (value: string) => void
  className?: string
  placeholder?: string
}

const SchoolNameSelect: React.FC<SchoolNameSelectProps> = ({
  value,
  onChange,
  className = '',
  placeholder = 'Selecione ou crie uma escola'
}) => {
  const { schoolNames, isLoading, isCreating, createSchoolName, hasLoaded } = useSchoolNames()
  const [isCreatingNew, setIsCreatingNew] = useState(false)
  const [newSchoolName, setNewSchoolName] = useState('')
  const [isOpen, setIsOpen] = useState(false)

  // Verificar se o valor atual é um nome existente ou novo
  useEffect(() => {
    if (value && !schoolNames.find(school => school.name === value)) {
      // Se o valor não está na lista, pode ser um nome novo ou carregado de uma avaliação existente
      // Não fazer nada aqui, apenas manter o valor
    }
  }, [value, schoolNames])

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'CREATE_NEW') {
      setIsCreatingNew(true)
      setNewSchoolName('')
      setIsOpen(false)
    } else {
      setIsCreatingNew(false)
      onChange(selectedValue)
      setIsOpen(false)
    }
  }

  const handleCreateNew = async () => {
    if (!newSchoolName.trim()) return

    const trimmedName = newSchoolName.trim()

    // Verificar se já existe localmente
    const existingSchool = schoolNames.find(
      school => school.name.toLowerCase() === trimmedName.toLowerCase()
    )

    if (existingSchool) {
      // Se já existe, apenas selecionar
      onChange(existingSchool.name)
      setIsCreatingNew(false)
      setNewSchoolName('')
      return
    }

    const success = await createSchoolName(trimmedName)
    if (success) {
      onChange(trimmedName)
      setIsCreatingNew(false)
      setNewSchoolName('')
    }
  }

  const handleCancelCreate = () => {
    setIsCreatingNew(false)
    setNewSchoolName('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreateNew()
    } else if (e.key === 'Escape') {
      handleCancelCreate()
    }
  }

  // Só mostrar loading se realmente estiver carregando E não tiver dados em cache
  if (isLoading && !hasLoaded && schoolNames.length === 0) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center space-x-2">
            <School className="w-4 h-4 text-gray-400 animate-pulse" />
            <span className="text-gray-500 dark:text-gray-400">Carregando escolas...</span>
          </div>
        </div>
      </div>
    )
  }

  if (isCreatingNew) {
    const existingSchool = schoolNames.find(
      school => school.name.toLowerCase() === newSchoolName.trim().toLowerCase()
    )

    return (
      <div className={`relative ${className}`}>
        <div className="flex space-x-2">
          <div className="flex-1">
            <input
              type="text"
              value={newSchoolName}
              onChange={(e) => setNewSchoolName(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Digite o nome da nova escola"
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:border-transparent dark:bg-gray-700 dark:text-white ${
                existingSchool
                  ? 'border-amber-300 dark:border-amber-600 focus:ring-amber-500'
                  : 'border-blue-300 dark:border-blue-600 focus:ring-blue-500'
              }`}
              autoFocus
            />
            {existingSchool && (
              <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                Esta escola já existe. Clique em "Salvar" para selecioná-la.
              </p>
            )}
          </div>
          <button
            onClick={handleCreateNew}
            disabled={!newSchoolName.trim() || isCreating}
            className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isCreating ? '...' : existingSchool ? 'Selecionar' : 'Salvar'}
          </button>
          <button
            onClick={handleCancelCreate}
            className="px-4 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Cancelar
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-left flex items-center justify-between"
      >
        <div className="flex items-center space-x-2">
          <School className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          <span className={value ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
            {value || placeholder}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {/* Opção para criar novo */}
          <button
            type="button"
            onClick={() => handleSelectChange('CREATE_NEW')}
            className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-blue-600 dark:text-blue-400 border-b border-gray-200 dark:border-gray-600"
          >
            <Plus className="w-4 h-4" />
            <span>Criar nova escola</span>
          </button>

          {/* Lista de escolas existentes */}
          {schoolNames.length > 0 ? (
            schoolNames.map((school) => (
              <button
                key={school.id}
                type="button"
                onClick={() => handleSelectChange(school.name)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 ${
                  value === school.name ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                }`}
              >
                <School className="w-4 h-4" />
                <span>{school.name}</span>
              </button>
            ))
          ) : (
            <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm italic">
              Nenhuma escola salva ainda. Use "Criar nova escola" para começar.
            </div>
          )}
        </div>
      )}

      {/* Overlay para fechar o dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default SchoolNameSelect
