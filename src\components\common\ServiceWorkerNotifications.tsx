import React, { useState, useEffect } from 'react'
import { useUpdateNotification, useOfflineDetection, useServiceWorker } from '../../hooks/useServiceWorker'

/**
 * Componente para notificações de atualização do Service Worker
 */
export const UpdateNotification: React.FC = () => {
  const { showUpdatePrompt, acceptUpdate, dismissUpdate } = useUpdateNotification()
  
  if (!showUpdatePrompt) return null
  
  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-blue-600 text-white rounded-lg shadow-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-6 h-6 text-blue-200" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium">
              Nova versão disponível
            </h3>
            <p className="mt-1 text-sm text-blue-200">
              Uma nova versão da aplicação está disponível. Deseja atualizar agora?
            </p>
            <div className="mt-3 flex space-x-2">
              <button
                onClick={acceptUpdate}
                className="bg-blue-500 hover:bg-blue-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
              >
                Atualizar
              </button>
              <button
                onClick={dismissUpdate}
                className="bg-transparent hover:bg-blue-500 text-blue-200 hover:text-white px-3 py-1 rounded text-sm font-medium transition-colors border border-blue-400"
              >
                Depois
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Componente para indicador de status offline
 */
export const OfflineIndicator: React.FC = () => {
  const { isOffline, wasOffline } = useOfflineDetection()
  const [showReconnected, setShowReconnected] = useState(false)
  
  useEffect(() => {
    if (wasOffline && !isOffline) {
      setShowReconnected(true)
      const timer = setTimeout(() => {
        setShowReconnected(false)
      }, 3000)
      
      return () => clearTimeout(timer)
    }
  }, [wasOffline, isOffline])
  
  if (showReconnected) {
    return (
      <div className="fixed bottom-4 left-4 z-50">
        <div className="bg-green-600 text-white rounded-lg shadow-lg p-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium">Conexão restaurada</span>
        </div>
      </div>
    )
  }
  
  if (!isOffline) return null
  
  return (
    <div className="fixed bottom-4 left-4 z-50">
      <div className="bg-yellow-600 text-white rounded-lg shadow-lg p-3 flex items-center">
        <svg className="w-5 h-5 mr-2 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span className="text-sm font-medium">Modo offline</span>
      </div>
    </div>
  )
}

/**
 * Componente para estatísticas do cache (apenas em desenvolvimento)
 */
export const CacheStats: React.FC = () => {
  const { cacheStats, getCacheSize } = useServiceWorker()
  const [totalSize, setTotalSize] = useState<number>(0)
  const [isVisible, setIsVisible] = useState(false)
  
  useEffect(() => {
    getCacheSize().then(setTotalSize)
  }, [getCacheSize])
  
  // Apenas mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  const totalItems = Object.values(cacheStats).reduce((sum, cache) => sum + cache.count, 0)
  
  return (
    <>
      {/* Botão para mostrar/ocultar */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
        title="Cache Stats"
      >
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
      </button>
      
      {/* Modal com estatísticas */}
      {isVisible && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Cache Statistics
              </h3>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Items:</span>
                <span className="font-medium text-gray-900 dark:text-white">{totalItems}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Size:</span>
                <span className="font-medium text-gray-900 dark:text-white">{formatBytes(totalSize)}</span>
              </div>
              
              <div className="border-t pt-3 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Por Cache:
                </h4>
                <div className="space-y-2">
                  {Object.entries(cacheStats).map(([cacheName, stats]) => (
                    <div key={cacheName} className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 truncate">
                        {cacheName}:
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white ml-2">
                        {stats.count} items
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

/**
 * Componente principal que agrupa todas as notificações
 */
export const ServiceWorkerNotifications: React.FC = () => {
  return (
    <>
      <UpdateNotification />
      <OfflineIndicator />
      <CacheStats />
    </>
  )
}

/**
 * Hook para preload inteligente baseado na navegação
 */
export const useNavigationPreload = () => {
  const { preloadCriticalResources } = useServiceWorker()
  
  const preloadForRoute = (route: string) => {
    const routeAssets: Record<string, string[]> = {
      '/dashboard': [
        '/images/dashboard-hero.webp',
        '/images/icons/questions.svg',
        '/images/icons/assessments.svg'
      ],
      '/questions': [
        '/images/question-placeholder.webp',
        '/api/questions/categories'
      ],
      '/assessments': [
        '/images/assessment-template.webp',
        '/api/assessments/templates'
      ],
      '/editor': [
        '/images/editor-toolbar.webp',
        '/fonts/editor-font.woff2'
      ]
    }
    
    const assets = routeAssets[route] || []
    if (assets.length > 0) {
      preloadCriticalResources(assets)
    }
  }
  
  return { preloadForRoute }
}
