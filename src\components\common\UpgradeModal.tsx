import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Crown, Check, Zap, FileText, Download, Sparkles } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useUsageLimits } from '../../hooks/useUsageLimits'
import {
  USAGE_LIMITS,
  PLAN_TYPES,
  PREMIUM_FEATURES,
  UPGRADE_PROMPTS,
  getUsageLimits
} from '../../constants/usageLimits'

interface UpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  trigger?: 'assessment_limit' | 'pdf_limit' | 'general'
  title?: string
  description?: string
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  trigger = 'general',
  title,
  description
}) => {
  const navigate = useNavigate()
  const { usageData, limitStatus } = useUsageLimits()
  const currentLimits = getUsageLimits(PLAN_TYPES.FREE)

  const handleUpgrade = () => {
    navigate('/app/billing')
    onClose()
  }

  const getTriggerContent = () => {
    const currentLimits = getUsageLimits(PLAN_TYPES.FREE)

    switch (trigger) {
      case 'assessment_limit':
        return {
          icon: FileText,
          title: title || UPGRADE_PROMPTS.ASSESSMENT_LIMIT.title,
          description: description || `Você criou ${usageData?.assessmentsCreated || 0} de ${currentLimits.ASSESSMENTS_PER_MONTH} avaliações este mês. Faça upgrade para criar avaliações ilimitadas!`,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        }
      case 'pdf_limit':
        return {
          icon: Download,
          title: title || UPGRADE_PROMPTS.PDF_LIMIT.title,
          description: description || `Você fez ${usageData?.pdfDownloads || 0} de ${currentLimits.PDF_DOWNLOADS_PER_MONTH} downloads este mês. Faça upgrade para downloads ilimitados!`,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        }
      default:
        return {
          icon: Sparkles,
          title: title || UPGRADE_PROMPTS.GENERAL.title,
          description: description || UPGRADE_PROMPTS.GENERAL.description,
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-50',
          borderColor: 'border-indigo-200'
        }
    }
  }

  const content = getTriggerContent()
  const IconComponent = content.icon

  const premiumFeatures = PREMIUM_FEATURES

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className={`${content.bgColor} dark:bg-gray-700 p-6 rounded-t-2xl border-b ${content.borderColor} dark:border-gray-600`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 ${content.bgColor} dark:bg-gray-600 rounded-lg`}>
                    <IconComponent className={`w-6 h-6 ${content.color} dark:text-gray-300`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {content.title}
                    </h3>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mt-3">
                {content.description}
              </p>
            </div>

            {/* Current Usage Stats */}
            {(trigger === 'assessment_limit' || trigger === 'pdf_limit') && usageData && (
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Seu Uso Atual</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Avaliações criadas</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {usageData.assessmentsCreated}/{currentLimits.ASSESSMENTS_PER_MONTH}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all"
                      style={{ width: `${Math.min(100, (usageData.assessmentsCreated / currentLimits.ASSESSMENTS_PER_MONTH) * 100)}%` }}
                    />
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Downloads de PDF</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {usageData.pdfDownloads}/{currentLimits.PDF_DOWNLOADS_PER_MONTH}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-purple-500 h-2 rounded-full transition-all"
                      style={{ width: `${Math.min(100, (usageData.pdfDownloads / currentLimits.PDF_DOWNLOADS_PER_MONTH) * 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Premium Features */}
            <div className="p-6">
              <h4 className="font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                <Crown className="w-5 h-5 text-yellow-500 mr-2" />
                Com o Premium você terá:
              </h4>
              <div className="space-y-3">
                {premiumFeatures.map((feature, index) => (
                  <motion.div
                    key={feature}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center space-x-3"
                  >
                    <div className="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                    </div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Pricing */}
            <div className="p-6 bg-gray-50 dark:bg-gray-700/50 rounded-b-2xl">
              <div className="text-center mb-4">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-3xl font-bold text-gray-900 dark:text-white">R$ 29,90</span>
                  <span className="text-gray-500 dark:text-gray-400">/mês</span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Cancele a qualquer momento
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleUpgrade}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-all transform hover:scale-105 flex items-center justify-center space-x-2"
                >
                  <Crown className="w-5 h-5" />
                  <span>Fazer Upgrade Agora</span>
                </button>
                
                <button
                  onClick={onClose}
                  className="w-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  Continuar com Plano Gratuito
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default UpgradeModal
