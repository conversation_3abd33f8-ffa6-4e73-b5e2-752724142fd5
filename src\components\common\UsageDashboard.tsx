import React from 'react'
import { motion } from 'framer-motion'
import {
  FileText,
  Download,
  Crown,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Zap
} from 'lucide-react'
import { useUsageLimits } from '../../hooks/useUsageLimits'
import { useNavigate } from 'react-router-dom'
import {
  USAGE_LIMITS,
  PLAN_TYPES,
  getUsageLimits
} from '../../constants/usageLimits'

interface UsageDashboardProps {
  variant?: 'full' | 'compact' | 'minimal'
  showUpgradePrompt?: boolean
  className?: string
}

const UsageDashboard: React.FC<UsageDashboardProps> = ({
  variant = 'full',
  showUpgradePrompt = true,
  className = ''
}) => {
  const navigate = useNavigate()
  const { 
    usageData, 
    limitStatus, 
    isLoading, 
    getUsagePercentage,
    isPaidUser 
  } = useUsageLimits()

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">Carregando uso...</span>
        </div>
      </div>
    )
  }

  if (isPaidUser && variant !== 'full') {
    return null // Don't show for paid users unless explicitly requested
  }

  const assessmentPercentage = getUsagePercentage('assessments')
  const pdfPercentage = getUsagePercentage('pdfs')

  // Get current limits for display
  const currentLimits = getUsageLimits(PLAN_TYPES.FREE)

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getProgressBgColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-50 dark:bg-red-900/20'
    if (percentage >= 70) return 'bg-yellow-50 dark:bg-yellow-900/20'
    return 'bg-green-50 dark:bg-green-900/20'
  }

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center space-x-4 ${className}`}>
        {!isPaidUser && limitStatus.shouldShowUpgrade && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/app/billing')}
            className="flex items-center space-x-2 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
          >
            <Crown className="w-4 h-4" />
            <span>Upgrade</span>
          </motion.button>
        )}
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-gray-900 dark:text-white">Uso Mensal</h3>
          {limitStatus.shouldShowUpgrade && (
            <button
              onClick={() => navigate('/app/billing')}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium"
            >
              Upgrade
            </button>
          )}
        </div>
        
        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600 dark:text-gray-400">Avaliações</span>
              <span className="font-medium">
                {isPaidUser ? 'Ilimitado' : `${usageData?.assessmentsCreated || 0}/${currentLimits.ASSESSMENTS_PER_MONTH}`}
              </span>
            </div>
            {!isPaidUser && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${getProgressColor(assessmentPercentage)}`}
                  style={{ width: `${assessmentPercentage}%` }}
                />
              </div>
            )}
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600 dark:text-gray-400">Downloads PDF</span>
              <span className="font-medium">
                {isPaidUser ? 'Ilimitado' : `${usageData?.pdfDownloads || 0}/${currentLimits.PDF_DOWNLOADS_PER_MONTH}`}
              </span>
            </div>
            {!isPaidUser && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${getProgressColor(pdfPercentage)}`}
                  style={{ width: `${pdfPercentage}%` }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Full variant
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">Uso Mensal</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {usageData?.currentPeriodStart.toLocaleDateString('pt-BR', { 
                month: 'long', 
                year: 'numeric' 
              })}
            </p>
          </div>
        </div>
        
        {isPaidUser ? (
          <div className="flex items-center space-x-2 px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-medium rounded-lg">
            <Crown className="w-4 h-4" />
            <span>Premium</span>
          </div>
        ) : (
          <div className="text-right">
            <div className="text-sm text-gray-500 dark:text-gray-400">Plano</div>
            <div className="font-medium text-gray-900 dark:text-white">Gratuito</div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Assessments Usage */}
        <div className={`p-4 rounded-lg border ${getProgressBgColor(assessmentPercentage)} border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">Avaliações</span>
            </div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {isPaidUser ? 'Ilimitado' : `${usageData?.assessmentsCreated || 0}/${currentLimits.ASSESSMENTS_PER_MONTH}`}
            </span>
          </div>
          
          {!isPaidUser && (
            <>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
                <div 
                  className={`h-3 rounded-full transition-all ${getProgressColor(assessmentPercentage)}`}
                  style={{ width: `${assessmentPercentage}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {limitStatus.assessmentsRemaining} restantes este mês
              </div>
            </>
          )}
        </div>

        {/* PDF Downloads Usage */}
        <div className={`p-4 rounded-lg border ${getProgressBgColor(pdfPercentage)} border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Download className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">Downloads PDF</span>
            </div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {isPaidUser ? 'Ilimitado' : `${usageData?.pdfDownloads || 0}/${currentLimits.PDF_DOWNLOADS_PER_MONTH}`}
            </span>
          </div>
          
          {!isPaidUser && (
            <>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
                <div 
                  className={`h-3 rounded-full transition-all ${getProgressColor(pdfPercentage)}`}
                  style={{ width: `${pdfPercentage}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {limitStatus.pdfDownloadsRemaining} restantes este mês
              </div>
            </>
          )}
        </div>
      </div>

      {/* Upgrade Prompt */}
      {!isPaidUser && showUpgradePrompt && limitStatus.shouldShowUpgrade && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4"
        >
          <div className="flex items-start space-x-3">
            <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded">
              <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                {limitStatus.isNearLimit ? 'Você está próximo do limite!' : 'Desbloqueie todo o potencial'}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Faça upgrade para o Premium e tenha acesso ilimitado a avaliações, downloads e recursos avançados.
              </p>
              <button
                onClick={() => navigate('/app/billing')}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
              >
                <Crown className="w-4 h-4" />
                <span>Fazer Upgrade</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default UsageDashboard
