import React from 'react'
import { motion } from 'framer-motion'
import { AlertTriangle, Crown, Zap } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useUsageLimits } from '../../hooks/useUsageLimits'

interface UsageLimitGuardProps {
  action: 'create_assessment' | 'download_pdf'
  children: React.ReactNode
  fallback?: React.ReactNode
  showUpgradePrompt?: boolean
}

/**
 * Component that guards actions based on usage limits for free users
 * Shows upgrade prompts and prevents actions when limits are reached
 */
const UsageLimitGuard: React.FC<UsageLimitGuardProps> = ({
  action,
  children,
  fallback,
  showUpgradePrompt = true
}) => {
  const navigate = useNavigate()
  const { limitStatus, isPaidUser } = useUsageLimits()

  // Always allow for paid users
  if (isPaidUser) {
    return <>{children}</>
  }

  // Check if action is allowed
  const canPerformAction = action === 'create_assessment' 
    ? limitStatus.canCreateAssessment 
    : limitStatus.canDownloadPDF

  if (canPerformAction) {
    return <>{children}</>
  }

  // Show fallback or default blocked state
  if (fallback) {
    return <>{fallback}</>
  }

  // Default blocked state with upgrade prompt
  return (
    <div className="relative">
      <div className="opacity-50 pointer-events-none">
        {children}
      </div>
      
      {showUpgradePrompt && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute inset-0 flex items-center justify-center bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border-2 border-dashed border-orange-300 dark:border-orange-600"
        >
          <div className="text-center p-4">
            <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full mx-auto mb-3">
              <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">
              {action === 'create_assessment' ? 'Limite de Avaliações Atingido' : 'Limite de Downloads Atingido'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {action === 'create_assessment' 
                ? 'Você atingiu o limite de 5 avaliações por mês do plano gratuito.'
                : 'Você atingiu o limite de 10 downloads de PDF por mês do plano gratuito.'
              }
            </p>
            <button
              onClick={() => navigate('/app/billing')}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
            >
              <Crown className="w-4 h-4" />
              <span>Fazer Upgrade</span>
            </button>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default UsageLimitGuard

// Helper hook for checking limits in components
export const useCanPerformAction = (action: 'create_assessment' | 'download_pdf') => {
  const { limitStatus, isPaidUser } = useUsageLimits()
  
  if (isPaidUser) return true
  
  return action === 'create_assessment' 
    ? limitStatus.canCreateAssessment 
    : limitStatus.canDownloadPDF
}

// Helper component for upgrade prompts
export const UpgradePrompt: React.FC<{
  title?: string
  description?: string
  className?: string
}> = ({ 
  title = "Desbloqueie Todo o Potencial",
  description = "Faça upgrade para o Premium e tenha acesso ilimitado a todos os recursos.",
  className = ""
}) => {
  const navigate = useNavigate()
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start space-x-3">
        <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded">
          <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">
            {title}
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            {description}
          </p>
          <button
            onClick={() => navigate('/app/billing')}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
          >
            <Crown className="w-4 h-4" />
            <span>Fazer Upgrade</span>
          </button>
        </div>
      </div>
    </motion.div>
  )
}
