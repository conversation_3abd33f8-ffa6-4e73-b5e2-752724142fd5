import React, { useEffect, useState } from 'react'
import { 
  FileText, 
  Database, 
  TrendingUp, 
  Clock, 
  Star,
  Users,
  BookOpen,
  Calendar,
  Plus,
  Eye,
  Download,
  BarChart3,
  Crown,
  Activity,
  Shield,
  RefreshCw
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useQuestions } from '../../hooks/useQuestions'
import { useAssessments } from '../../hooks/useAssessments'
import { useFavorites } from '../../hooks/useQuestions'
import { useUserAnalytics } from '../../hooks/useUserAnalytics'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../../lib/supabase'
import TaskManager from './TaskManager'

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const { profile, isAdmin } = useAuth()
  const { isPremium, isEscolar, getUsageLimits } = useSubscription()
  const { questions } = useQuestions({ autor_id: profile?.id })
  const { assessments } = useAssessments()
  const { favorites } = useFavorites()
  const { analytics, isLoading: analyticsLoading } = useUserAnalytics()
  const [recentActivities, setRecentActivities] = useState<any[]>([])
  const [loadingActivities, setLoadingActivities] = useState(true)
  const [errorActivities, setErrorActivities] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  const limits = getUsageLimits()

  // Fetch recent activities
  const fetchRecentActivities = async () => {
    if (!profile?.id) return
    setLoadingActivities(true)
    setErrorActivities(null)
    try {
      const { data, error } = await supabase
        .from('usage_stats')
        .select('*')
        .eq('user_id', profile.id)
        .order('created_at', { ascending: false })
        .limit(5)
      if (error) {
        setErrorActivities('Erro ao carregar atividades recentes.')
        return
      }
      setRecentActivities(data || [])
    } catch (error) {
      setErrorActivities('Erro inesperado ao carregar atividades recentes.')
    } finally {
      setLoadingActivities(false)
    }
  }

  useEffect(() => {
    fetchRecentActivities()
    // eslint-disable-next-line
  }, [profile?.id])

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchRecentActivities()
    setRefreshing(false)
  }

  const stats = [
    {
      label: 'Questões Criadas',
      value: analytics?.questionsCreated || questions.length,
      change: '+12%', // Manter mockado ou calcular se houver dados históricos
      icon: Database,
      color: 'bg-blue-500',
      limit: limits.questionsPerMonth === -1 ? null : limits.questionsPerMonth
    },
    {
      label: 'Avaliações Geradas',
      value: analytics?.assessmentsGenerated || assessments.length,
      change: '+8%', // Manter mockado
      icon: FileText,
      color: 'bg-green-500',
      limit: limits.assessmentsPerMonth === -1 ? null : limits.assessmentsPerMonth
    },
    {
      label: 'Questões Favoritas',
      value: favorites.length,
      change: '+15%', // Manter mockado
      icon: Star,
      color: 'bg-yellow-500',
      limit: null
    },
    {
      label: 'Tópicos Mais Usados',
      value: analytics?.mostUsedTopics.length > 0 ? analytics.mostUsedTopics[0].topic : 'N/A',
      change: '+22%',
      icon: Users,
      color: 'bg-purple-500',
      limit: null,
      premium: true
    }
  ]

  const quickActions = [
    {
      title: 'Nova Avaliação',
      description: 'Criar uma nova prova',
      icon: Plus,
      color: 'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-800/50',
      action: () => navigate('/app/editor')
    },
    {
      title: 'Buscar Questões',
      description: 'Explorar banco de questões',
      icon: Database,
      color: 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-800/50',
      action: () => navigate('/app/questions')
    },
    {
      title: 'Modelos Prontos',
      description: 'Escolher um modelo',
      icon: BookOpen,
      color: 'bg-purple-100 text-purple-600 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:hover:bg-purple-800/50',
      action: () => navigate('/app/templates')
    }
  ]

  // Add admin panel action if user is admin
  if (isAdmin) {
    quickActions.push({
      title: 'Painel Admin',
      description: 'Acessar painel administrativo',
      icon: Shield,
      color: 'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-800/50',
      action: () => navigate('/admin')
    })
  }

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
      {/* Header */}
      <div className="mb-6 lg:mb-8 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Bem-vindo de volta, {profile?.nome?.split(' ')[0] || 'Professor'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Aqui está um resumo das suas atividades recentes e estatísticas.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            disabled={refreshing || loadingActivities || analyticsLoading}
            title="Atualizar estatísticas e atividades"
            aria-label="Atualizar estatísticas e atividades"
          >
            <RefreshCw className={`w-5 h-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </button>
          {!isPremium && !isEscolar && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/app/billing')}
              className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-medium shadow-lg"
              aria-label="Upgrade Premium"
              title="Upgrade Premium"
            >
              <Crown className="w-5 h-5" />
              <span>Upgrade Premium</span>
            </motion.button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
        {(analyticsLoading || refreshing) ? (
          Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-4 lg:p-6 border border-gray-200 dark:border-gray-700 animate-pulse">
              <div className="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-8 w-1/2 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-3 w-1/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          ))
        ) : stats.map((stat, index) => {
          const IconComponent = stat.icon
          const isPremiumFeature = stat.premium && !isPremium && !isEscolar
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`bg-white dark:bg-gray-800 rounded-xl p-4 lg:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 ${isPremiumFeature ? 'opacity-60' : ''}`}
            >
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 truncate">
                      {stat.label}
                    </p>
                    {isPremiumFeature && (
                      <Crown className="w-4 h-4 text-yellow-500" />
                    )}
                  </div>
                  <p className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  {stat.limit && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      Limite: {stat.limit}/mês
                    </p>
                  )}
                  <p className={`text-xs font-medium mt-1 ${
                    stat.change.startsWith('+') ? 'text-green-600 dark:text-green-400' : 
                    stat.change === 'Ativo' ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {stat.change}
                  </p>
                </div>
                <div className={`w-10 h-10 lg:w-12 lg:h-12 ${stat.color} rounded-xl flex items-center justify-center flex-shrink-0 ml-3`}>
                  <IconComponent className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
        {/* Recent Activities */}
        <div className="xl:col-span-2 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6">
          <div className="flex items-center justify-between mb-4 lg:mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Atividades Recentes
            </h2>
            <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
              aria-label="Ver todas atividades"
              title="Ver todas atividades"
            >
              Ver todas
            </button>
          </div>

          {loadingActivities || refreshing ? (
            <div className="space-y-3 lg:space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))}
            </div>
          ) : errorActivities ? (
            <div className="text-center text-red-500 py-8">{errorActivities}</div>
          ) : (
            <div className="space-y-3 lg:space-y-4">
              {recentActivities.length === 0 ? (
                <p className="text-gray-600 dark:text-gray-400 text-center py-8">Nenhuma atividade recente encontrada.</p>
              ) : (
                recentActivities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="flex items-start space-x-3 lg:space-x-4 p-3 lg:p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                  >
                    <div className="w-8 h-8 lg:w-10 lg:h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center flex-shrink-0">
                      {activity.action_type === 'assessment_created' && <FileText className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600 dark:text-blue-400" />}
                      {activity.action_type === 'question_created' && <Database className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600 dark:text-blue-400" />}
                      {activity.action_type === 'pdf_downloaded' && <Download className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600 dark:text-blue-400" />}
                      {!['assessment_created', 'question_created', 'pdf_downloaded'].includes(activity.action_type) && 
                        <Activity className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600 dark:text-blue-400" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1 truncate">
                        {activity.action_type === 'assessment_created' ? `Avaliação criada: ${activity.metadata?.titulo || 'Sem título'}` :
                         activity.action_type === 'question_created' ? `Questão criada: ${activity.metadata?.enunciado?.substring(0, 50) || 'Sem enunciado'}...` :
                         activity.action_type === 'pdf_downloaded' ? `PDF baixado: ${activity.metadata?.assessment_title || 'Sem título'}` :
                         activity.action_type.replace(/_/g, ' ')}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                        {activity.metadata?.disciplina ? `Disciplina: ${activity.metadata.disciplina}` : ''}
                        {activity.metadata?.serie ? `, Série: ${activity.metadata.serie}` : ''}
                      </p>
                      <div className="flex flex-wrap items-center gap-2 lg:gap-4 text-xs text-gray-500 dark:text-gray-500">
                        <span>{new Date(activity.created_at).toLocaleDateString('pt-BR')}</span>
                        <span className="px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300">
                          {activity.action_type.replace(/_/g, ' ')}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-1 flex-shrink-0">
                      <button className="p-1.5 lg:p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        aria-label="Ver detalhes da atividade"
                        title="Ver detalhes da atividade"
                      >
                        <Eye className="w-3 h-3 lg:w-4 lg:h-4" />
                      </button>
                      <button className="p-1.5 lg:p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        aria-label="Baixar PDF da atividade"
                        title="Baixar PDF da atividade"
                      >
                        <Download className="w-3 h-3 lg:w-4 lg:h-4" />
                      </button>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Task Manager */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6">
          <TaskManager />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 lg:mt-8 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Ações Rápidas
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
          {quickActions.map((action, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={action.action}
              className={`flex items-center space-x-3 p-3 lg:p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 text-left group ${action.color}`}
              aria-label={action.title}
              title={action.title}
            >
              <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                <action.icon className="w-4 h-4 lg:w-5 lg:h-5" />
              </div>
              <div className="min-w-0">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">{action.title}</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400">{action.description}</p>
              </div>
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Dashboard