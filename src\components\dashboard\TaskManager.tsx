import React, { useState, useEffect } from 'react'
import { 
  CheckCircle, 
  Clock, 
  Calendar, 
  Plus, 
  Trash2, 
  Edit,
  X,
  Save,
  AlertTriangle
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

interface Task {
  id: string
  user_id: string
  title: string
  description?: string
  due_date: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'in_progress' | 'completed'
  subject?: string
  created_at: string
}

// Utility functions moved to module level
const isTaskOverdue = (dueDate: string) => {
  return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString()
}

const isTaskDueToday = (dueDate: string) => {
  return new Date(dueDate).toDateString() === new Date().toDateString()
}

const getPriorityColor = (priority: Task['priority']) => {
  switch (priority) {
    case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
    case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
    case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getStatusColor = (status: Task['status']) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300'
    case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300'
    case 'pending': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getStatusLabel = (status: Task['status']) => {
  switch (status) {
    case 'completed': return 'Concluída'
    case 'in_progress': return 'Em Progresso'
    case 'pending': return 'Pendente'
    default: return status
  }
}

const getPriorityLabel = (priority: Task['priority']) => {
  switch (priority) {
    case 'high': return 'Alta'
    case 'medium': return 'Média'
    case 'low': return 'Baixa'
    default: return priority
  }
}

const TaskManager: React.FC = () => {
  const { user } = useAuth()
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddTask, setShowAddTask] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  
  // New task form state
  const [newTask, setNewTask] = useState<Omit<Task, 'id' | 'user_id' | 'created_at'>>({
    title: '',
    description: '',
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Default to 1 week from now
    priority: 'medium',
    status: 'pending',
    subject: ''
  })

  useEffect(() => {
    if (user) {
      fetchTasks()
    }
  }, [user])

  const fetchTasks = async () => {
    if (!user) return
    
    try {
      setLoading(true)
      
      const { data, error } = await supabase
        .from('teacher_tasks')
        .select('*')
        .eq('user_id', user.id)
        .order('due_date', { ascending: true })
      
      if (error) throw error
      
      setTasks(data || [])
    } catch (error) {
      console.error('Error fetching tasks:', error)
      toast.error('Erro ao carregar tarefas')
    } finally {
      setLoading(false)
    }
  }

  const createTask = async () => {
    if (!user) return
    if (!newTask.title) {
      toast.error('O título da tarefa é obrigatório')
      return
    }
    
    try {
      const { data, error } = await supabase
        .from('teacher_tasks')
        .insert({
          user_id: user.id,
          title: newTask.title,
          description: newTask.description,
          due_date: newTask.due_date,
          priority: newTask.priority,
          status: newTask.status,
          subject: newTask.subject
        })
        .select()
        .single()
      
      if (error) throw error
      
      setTasks(prev => [...prev, data])
      setShowAddTask(false)
      
      // Reset form
      setNewTask({
        title: '',
        description: '',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        priority: 'medium',
        status: 'pending',
        subject: ''
      })
      
      toast.success('Tarefa criada com sucesso!')
    } catch (error) {
      console.error('Error creating task:', error)
      toast.error('Erro ao criar tarefa')
    }
  }

  const updateTask = async (taskId: string, updates: Partial<Task>) => {
    try {
      const { data, error } = await supabase
        .from('teacher_tasks')
        .update(updates)
        .eq('id', taskId)
        .select()
        .single()
      
      if (error) throw error
      
      setTasks(prev => prev.map(task => task.id === taskId ? data : task))
      
      if (editingTask) {
        setEditingTask(null)
      }
      
      toast.success('Tarefa atualizada com sucesso!')
    } catch (error) {
      console.error('Error updating task:', error)
      toast.error('Erro ao atualizar tarefa')
    }
  }

  const deleteTask = async (taskId: string) => {
    try {
      const { error } = await supabase
        .from('teacher_tasks')
        .delete()
        .eq('id', taskId)
      
      if (error) throw error
      
      setTasks(prev => prev.filter(task => task.id !== taskId))
      toast.success('Tarefa excluída com sucesso!')
    } catch (error) {
      console.error('Error deleting task:', error)
      toast.error('Erro ao excluir tarefa')
    }
  }

  const handleStatusChange = (taskId: string, status: Task['status']) => {
    updateTask(taskId, { status })
  }

  // Group tasks by status
  const pendingTasks = tasks.filter(task => task.status === 'pending')
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress')
  const completedTasks = tasks.filter(task => task.status === 'completed')

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Minhas Tarefas
        </h2>
        <button
          onClick={() => setShowAddTask(true)}
          className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Nova Tarefa</span>
        </button>
      </div>

      {/* Add Task Form */}
      <AnimatePresence>
        {showAddTask && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-blue-900 dark:text-blue-300">Nova Tarefa</h3>
              <button
                onClick={() => setShowAddTask(false)}
                className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-blue-900 dark:text-blue-300 mb-1">
                  Título *
                </label>
                <input
                  type="text"
                  value={newTask.title}
                  onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full p-2 border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Título da tarefa"
                />
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-900 dark:text-blue-300 mb-1">
                    Data de Entrega
                  </label>
                  <input
                    type="date"
                    value={newTask.due_date}
                    onChange={(e) => setNewTask(prev => ({ ...prev, due_date: e.target.value }))}
                    className="w-full p-2 border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-900 dark:text-blue-300 mb-1">
                    Prioridade
                  </label>
                  <select
                    value={newTask.priority}
                    onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value as Task['priority'] }))}
                    className="w-full p-2 border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  >
                    <option value="low">Baixa</option>
                    <option value="medium">Média</option>
                    <option value="high">Alta</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-900 dark:text-blue-300 mb-1">
                    Disciplina (opcional)
                  </label>
                  <input
                    type="text"
                    value={newTask.subject || ''}
                    onChange={(e) => setNewTask(prev => ({ ...prev, subject: e.target.value }))}
                    className="w-full p-2 border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                    placeholder="Ex: Matemática"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-blue-900 dark:text-blue-300 mb-1">
                  Descrição (opcional)
                </label>
                <textarea
                  value={newTask.description || ''}
                  onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                  rows={2}
                  className="w-full p-2 border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Detalhes da tarefa..."
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddTask(false)}
                  className="px-4 py-2 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={createTask}
                  className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Salvar Tarefa</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Task Lists */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando tarefas...</span>
        </div>
      ) : tasks.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
          <Clock className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Nenhuma tarefa encontrada
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Crie sua primeira tarefa para organizar suas atividades.
          </p>
          <button
            onClick={() => setShowAddTask(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Criar Tarefa
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {/* Pending Tasks */}
          {pendingTasks.length > 0 && (
            <div>
              <div className="flex items-center mb-2">
                <Clock className="w-4 h-4 mr-2 text-gray-600 dark:text-gray-400" />
                <h3 className="font-medium text-gray-900 dark:text-white flex items-center">
                  Pendentes
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
                    {pendingTasks.length}
                  </span>
                </h3>
              </div>
              
              <div className="space-y-2">
                {pendingTasks.map(task => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onStatusChange={handleStatusChange}
                    onEdit={setEditingTask}
                    onDelete={deleteTask}
                    isEditing={editingTask?.id === task.id}
                    onSaveEdit={(updates) => updateTask(task.id, updates)}
                    onCancelEdit={() => setEditingTask(null)}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* In Progress Tasks */}
          {inProgressTasks.length > 0 && (
            <div>
              <div className="flex items-center mb-2">
                <Clock className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                <h3 className="font-medium text-gray-900 dark:text-white flex items-center">
                  Em Progresso
                  <span className="ml-2 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
                    {inProgressTasks.length}
                  </span>
                </h3>
              </div>
              
              <div className="space-y-2">
                {inProgressTasks.map(task => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onStatusChange={handleStatusChange}
                    onEdit={setEditingTask}
                    onDelete={deleteTask}
                    isEditing={editingTask?.id === task.id}
                    onSaveEdit={(updates) => updateTask(task.id, updates)}
                    onCancelEdit={() => setEditingTask(null)}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* Completed Tasks */}
          {completedTasks.length > 0 && (
            <div>
              <div className="flex items-center mb-2">
                <CheckCircle className="w-4 h-4 mr-2 text-green-600 dark:text-green-400" />
                <h3 className="font-medium text-gray-900 dark:text-white flex items-center">
                  Concluídas
                  <span className="ml-2 bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300 text-xs px-2 py-1 rounded-full">
                    {completedTasks.length}
                  </span>
                </h3>
              </div>
              
              <div className="space-y-2">
                {completedTasks.map(task => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onStatusChange={handleStatusChange}
                    onEdit={setEditingTask}
                    onDelete={deleteTask}
                    isEditing={editingTask?.id === task.id}
                    onSaveEdit={(updates) => updateTask(task.id, updates)}
                    onCancelEdit={() => setEditingTask(null)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

interface TaskCardProps {
  task: Task
  onStatusChange: (taskId: string, status: Task['status']) => void
  onEdit: (task: Task) => void
  onDelete: (taskId: string) => void
  isEditing: boolean
  onSaveEdit: (updates: Partial<Task>) => void
  onCancelEdit: () => void
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onStatusChange,
  onEdit,
  onDelete,
  isEditing,
  onSaveEdit,
  onCancelEdit
}) => {
  const [editForm, setEditForm] = useState({
    title: task.title,
    description: task.description || '',
    due_date: task.due_date,
    priority: task.priority,
    subject: task.subject || ''
  })

  const handleEditChange = (field: string, value: string) => {
    setEditForm(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    if (!editForm.title) {
      toast.error('O título da tarefa é obrigatório')
      return
    }
    
    onSaveEdit(editForm)
  }

  const isOverdue = isTaskOverdue(task.due_date)
  const isDueToday = isTaskDueToday(task.due_date)

  if (isEditing) {
    return (
      <motion.div
        initial={{ opacity: 0.8 }}
        animate={{ opacity: 1 }}
        className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
      >
        <div className="space-y-3">
          <input
            type="text"
            value={editForm.title}
            onChange={(e) => handleEditChange('title', e.target.value)}
            className="w-full p-2 text-sm border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
            placeholder="Título da tarefa"
          />
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <input
                type="date"
                value={editForm.due_date}
                onChange={(e) => handleEditChange('due_date', e.target.value)}
                className="w-full p-2 text-sm border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              />
            </div>
            
            <div>
              <select
                value={editForm.priority}
                onChange={(e) => handleEditChange('priority', e.target.value)}
                className="w-full p-2 text-sm border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              >
                <option value="low">Baixa</option>
                <option value="medium">Média</option>
                <option value="high">Alta</option>
              </select>
            </div>
          </div>
          
          <input
            type="text"
            value={editForm.subject || ''}
            onChange={(e) => handleEditChange('subject', e.target.value)}
            className="w-full p-2 text-sm border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
            placeholder="Disciplina (opcional)"
          />
          
          <textarea
            value={editForm.description || ''}
            onChange={(e) => handleEditChange('description', e.target.value)}
            rows={2}
            className="w-full p-2 text-sm border border-blue-300 dark:border-blue-700 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
            placeholder="Descrição (opcional)"
          />
          
          <div className="flex justify-end space-x-2">
            <button
              onClick={onCancelEdit}
              className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded"
            >
              Salvar
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      layout
      className={`border rounded-lg p-3 ${
        task.status === 'completed' 
          ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20' 
          : isOverdue && task.status !== 'completed'
            ? 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20'
            : isDueToday && task.status !== 'completed'
              ? 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20'
              : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
      }`}
    >
      <div className="flex items-start justify-between mb-2">
        <h3 className={`font-medium text-sm ${
          task.status === 'completed' ? 'text-gray-500 dark:text-gray-400 line-through' : 'text-gray-900 dark:text-white'
        }`}>
          {task.title}
        </h3>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={() => onEdit(task)}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded"
          >
            <Edit className="w-3 h-3" />
          </button>
          <button
            onClick={() => onDelete(task.id)}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>
      
      {task.description && (
        <p className={`text-xs mb-2 ${
          task.status === 'completed' ? 'text-gray-400 dark:text-gray-500 line-through' : 'text-gray-600 dark:text-gray-400'
        }`}>
          {task.description}
        </p>
      )}
      
      <div className="flex flex-wrap items-center gap-2 mb-2">
        <span className={`text-xs px-2 py-0.5 rounded-full ${getPriorityColor(task.priority)}`}>
          {getPriorityLabel(task.priority)}
        </span>
        
        <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(task.status)}`}>
          {getStatusLabel(task.status)}
        </span>
        
        {task.subject && (
          <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300">
            {task.subject}
          </span>
        )}
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1 text-xs">
          <Calendar className="w-3 h-3 text-gray-500 dark:text-gray-400" />
          <span className={`${
            isOverdue && task.status !== 'completed' 
              ? 'text-red-600 dark:text-red-400 font-medium' 
              : isDueToday && task.status !== 'completed'
                ? 'text-yellow-600 dark:text-yellow-400 font-medium'
                : 'text-gray-500 dark:text-gray-400'
          }`}>
            {new Date(task.due_date).toLocaleDateString('pt-BR')}
            {isOverdue && task.status !== 'completed' && ' (Atrasada)'}
            {isDueToday && task.status !== 'completed' && ' (Hoje)'}
          </span>
        </div>
        
        <select
          value={task.status}
          onChange={(e) => onStatusChange(task.id, e.target.value as Task['status'])}
          className="text-xs p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 dark:text-white"
        >
          <option value="pending">Pendente</option>
          <option value="in_progress">Em Progresso</option>
          <option value="completed">Concluída</option>
        </select>
      </div>
    </motion.div>
  )
}

export default TaskManager