import React, { useEffect, useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'

/**
 * Debug component to monitor AuthContext state
 * Only renders in development mode
 * 
 * Usage: Add <AuthDebugger /> to your App component during testing
 */
const AuthDebugger: React.FC = () => {
  const { user, profile, session, loading, initialized } = useAuth()
  const [renderCount, setRenderCount] = useState(0)
  const [stateHistory, setStateHistory] = useState<Array<{
    timestamp: string
    loading: boolean
    initialized: boolean
    hasUser: boolean
    hasProfile: boolean
    hasSession: boolean
  }>>([])

  // Track renders
  useEffect(() => {
    setRenderCount(prev => prev + 1)
  })

  // Track state changes
  useEffect(() => {
    const newState = {
      timestamp: new Date().toLocaleTimeString(),
      loading,
      initialized,
      hasUser: !!user,
      hasProfile: !!profile,
      hasSession: !!session
    }

    setStateHistory(prev => {
      const updated = [...prev, newState]
      // Keep only last 10 states
      return updated.slice(-10)
    })
  }, [loading, initialized, user, profile, session])

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div 
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        maxWidth: '300px',
        maxHeight: '400px',
        overflow: 'auto'
      }}
    >
      <div style={{ fontWeight: 'bold', marginBottom: '10px' }}>
        🔍 AuthContext Debug
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Current State:</strong>
        <div>Loading: {loading ? '🔄' : '✅'}</div>
        <div>Initialized: {initialized ? '✅' : '❌'}</div>
        <div>User: {user ? '👤' : '❌'}</div>
        <div>Profile: {profile ? '📋' : '❌'}</div>
        <div>Session: {session ? '🔐' : '❌'}</div>
        <div>Renders: {renderCount}</div>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>User Info:</strong>
        {user ? (
          <div>
            <div>ID: {user.id.slice(0, 8)}...</div>
            <div>Email: {user.email}</div>
          </div>
        ) : (
          <div>No user</div>
        )}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Profile Info:</strong>
        {profile ? (
          <div>
            <div>Name: {profile.nome}</div>
            <div>Admin: {profile.is_admin ? '✅' : '❌'}</div>
            <div>School Admin: {profile.is_school_admin ? '✅' : '❌'}</div>
          </div>
        ) : (
          <div>No profile</div>
        )}
      </div>

      <div>
        <strong>State History:</strong>
        <div style={{ maxHeight: '150px', overflow: 'auto', fontSize: '10px' }}>
          {stateHistory.map((state, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              <span>{state.timestamp}</span>
              <span> L:{state.loading ? '1' : '0'}</span>
              <span> I:{state.initialized ? '1' : '0'}</span>
              <span> U:{state.hasUser ? '1' : '0'}</span>
              <span> P:{state.hasProfile ? '1' : '0'}</span>
              <span> S:{state.hasSession ? '1' : '0'}</span>
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '10px', fontSize: '10px', opacity: 0.7 }}>
        L=Loading, I=Initialized, U=User, P=Profile, S=Session
      </div>
    </div>
  )
}

export default AuthDebugger
