import React, { useState } from 'react'
import { X, Download, Edit, Trash2 } from 'lucide-react'
import { motion } from 'framer-motion'
import { Database } from '../../types/database'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useUsageLimits } from '../../hooks/useUsageLimits'
import { generatePDF, downloadPDF } from '../../utils/pdfGenerator'
import { WATERMARK_CONFIG } from '../../constants/usageLimits'
import { AssessmentConfig, TextBlock, AssessmentItem } from '../../types/assessment'
import { useAssets } from '../../hooks/useAssets'
import toast from 'react-hot-toast'

type Question = Database['public']['Tables']['questions']['Row']

interface AssessmentPreviewProps {
  items: AssessmentItem[]
  config: AssessmentConfig
  onClose: () => void
  assessment?: any // Dados da avaliação para editar/excluir
  onEdit?: (assessment: any) => void
  onDelete?: (assessment: any) => void
}

const AssessmentPreview: React.FC<AssessmentPreviewProps> = ({
  items,
  config,
  onClose,
  assessment,
  onEdit,
  onDelete
}) => {
  const { isPremium, isEscolar } = useSubscription();
  const isPaid = isPremium || isEscolar;
  const {
    checkCanPerformAction,
    trackUsage,
    isPaidUser
  } = useUsageLimits()
  const [isDownloading, setIsDownloading] = useState(false);

  // Count questions (excluding text blocks)
  const questionCount = items.filter(item => 'tipo' in item).length

  // Mapeamento para classes de alinhamento do Tailwind CSS
  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  const handleDownload = async () => {
    if (isDownloading) return;

    // Check usage limits for free users
    const canDownload = await checkCanPerformAction('download_pdf')
    if (!canDownload) {
      return // Error message already shown by checkCanPerformAction
    }

    setIsDownloading(true);

    try {
      toast.loading('Gerando PDF...', { id: 'pdf-generation' });

      if (items.length === 0) {
        toast.error('Avaliação sem conteúdo', { id: 'pdf-generation' });
        return;
      }

      // Preparar configuração do cabeçalho com URLs das imagens
      const customization = config.headerConfig.customization;

      const headerConfigWithUrls = {
        ...config.headerConfig,
        customization: customization ? {
          customHeader: customization.customHeader?.enabled && customization.customHeader.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.customHeader.asset.file_path)
          } : customization.customHeader ? {
            enabled: customization.customHeader.enabled || false
          } : undefined,
          schoolLogo: customization.schoolLogo?.enabled && customization.schoolLogo.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.schoolLogo.asset.file_path)
          } : customization.schoolLogo ? {
            enabled: customization.schoolLogo.enabled || false
          } : undefined
        } : undefined
      };

      // Usar mesma configuração simples
      const pdfOptions = {
        ...config.pdfOptions,
        headerConfig: headerConfigWithUrls,
        showFooter: isPaid ? config.showFooter : true,
        watermark: isPaidUser ? undefined : WATERMARK_CONFIG.FREE_PLAN_TEXT
      };

      console.log('PDF Options finais:', pdfOptions);

      // Gerar PDF - mesma lógica do editor
      const blob = await generatePDF(items, pdfOptions);

      // Download - mesma lógica do editor
      downloadPDF(blob, `${config.titulo}.pdf`);

      // Track PDF download with new system
      await trackUsage('pdf_downloaded', {
        assessment_title: config.titulo,
        questions_count: items.filter(item => 'tipo' in item && item.tipo !== undefined).length,
        text_blocks_count: items.length - items.filter(item => 'tipo' in item && item.tipo !== undefined).length
      })

      toast.success('PDF gerado com sucesso!', { id: 'pdf-generation' });
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      toast.error('Erro ao gerar PDF', { id: 'pdf-generation' });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleEdit = () => {
    if (onEdit && assessment) {
      onEdit(assessment);
      onClose(); // Fechar o modal após editar
    }
  };

  const handleDelete = () => {
    if (onDelete && assessment) {
      onDelete(assessment);
      // Não fechar o modal aqui, deixar para o componente pai decidir
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Preview da Avaliação</h3>
        <div className="flex items-center space-x-2">
          {/* Botões de ação da avaliação */}
          {assessment && onEdit && (
            <button 
              onClick={handleEdit}
              className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Editar Avaliação"
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          
          {assessment && onDelete && (
            <button 
              onClick={handleDelete}
              className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Excluir Avaliação"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}
          
          {/* Separador visual se há botões de ação */}
          {assessment && (onEdit || onDelete) && (
            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
          )}
          
          <button 
            onClick={handleDownload}
            disabled={isDownloading}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Download PDF"
          >
            {isDownloading ? (
              <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Download className="w-4 h-4" />
            )}
          </button>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Fechar"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8"
        >
          {/* Header */}
          <HeaderSection config={config} />


          {/* Content */}
          <div className="space-y-6">
            {items.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">Esta avaliação não possui questões ou conteúdo.</p>
              </div>
            ) : (
              items.map((item, index) => {
              if ('type' in item && item.type === 'text') {
                // Text Block
                return (
                  <div key={item.id} className="mb-6">
                    <div className={`${textAlignClasses[item.textAlign || 'left']} ${
                      item.style === 'heading' ? 'text-xl font-bold text-gray-900 dark:text-white' :
                      item.style === 'subheading' ? 'text-lg font-semibold text-gray-800 dark:text-gray-200' :
                      item.style === 'instruction' ? 'text-sm italic text-gray-700 dark:text-gray-300' :
                      'text-base text-gray-800 dark:text-gray-200'
                    }`}>
                      {item.content}
                    </div>
                  </div>
                )
              } else {
                // Question
                const question = item as Question
                const questionIndex = items.filter((i, idx) => idx <= index && 'tipo' in i).length

                return (
                  <div key={question.id} className="mb-6">
                    <div className="flex items-start space-x-3">
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {questionIndex}.
                      </span>
                      <div className="flex-1">
                        <p className="text-gray-900 dark:text-white mb-3 leading-relaxed">
                          {question.enunciado}
                        </p>

                        {question.tipo === 'multipla_escolha' && question.alternativas && (
                          <div className="space-y-2 ml-4">
                            {question.alternativas.map((alt, altIndex) => (
                              <div key={altIndex} className="flex items-start space-x-2">
                                <span className="text-gray-700 dark:text-gray-300 font-medium">
                                  {String.fromCharCode(97 + altIndex)})
                                </span>
                                <span className="text-gray-700 dark:text-gray-300">{alt}</span>
                              </div>
                            ))}
                          </div>
                        )}

                        {question.tipo === 'dissertativa' && (
                          <div className="ml-4 mt-4 space-y-2">
                            {[...Array(5)].map((_, i) => (
                              <div key={i} className="border-b border-gray-300 dark:border-gray-600 h-6"></div>
                            ))}
                          </div>
                        )}

                        {question.tipo === 'verdadeiro_falso' && (
                          <div className="space-y-2 ml-4">
                            <div className="flex items-center space-x-2">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">(  ) Verdadeiro</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">(  ) Falso</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              }
            })
            )}
          </div>

          {/* Footer */}
          {(config.showFooter || !isPaid) && (
            <div className="mt-8 pt-4 border-t border-gray-300 dark:border-gray-600 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-500">
                Gerado pela plataforma Atividade Pronta
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

// Componente para renderizar o cabeçalho com personalizações
const HeaderSection: React.FC<{ config: AssessmentConfig }> = ({ config }) => {
  const { getAssetUrl } = useAssets()

  const customHeader = config.headerConfig.customization?.customHeader
  const schoolLogo = config.headerConfig.customization?.schoolLogo

  // Se cabeçalho personalizado estiver ativo e tiver asset
  if (customHeader?.enabled && customHeader.asset) {
    return (
      <div className="mb-8">
        <img
          src={getAssetUrl(customHeader.asset.file_path)}
          alt="Cabeçalho personalizado"
          className="w-full max-h-48 object-contain mx-auto"
        />
      </div>
    )
  }

  // Cabeçalho padrão (com ou sem logo da escola)
  return (
    <div className="mb-8">
      {/* Layout com logo da escola */}
      {schoolLogo?.enabled && schoolLogo.asset ? (
        <div className="flex items-start space-x-6 mb-6">
          {/* Logo da escola */}
          <div className="flex-shrink-0">
            <img
              src={getAssetUrl(schoolLogo.asset.file_path)}
              alt="Logo da escola"
              className="w-20 h-20 object-contain"
            />
          </div>

          {/* Conteúdo do cabeçalho ao lado do logo */}
          <div className="flex-1">
            <div className="text-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {config.headerConfig.nomeEscola}
              </h1>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {config.headerConfig.nomeProva}
              </h2>
            </div>
          </div>
        </div>
      ) : (
        /* Cabeçalho sem logo - layout centralizado */
        <div className="text-center mb-6">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {config.headerConfig.nomeEscola}
          </h1>
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            {config.headerConfig.nomeProva}
          </h2>
        </div>
      )}

      {/* Informações adicionais (sempre centralizadas) */}
      <div className="text-center">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
          <span>Série: {config.headerConfig.serie || config.serie}</span>
          <span>Data: {config.headerConfig.data}</span>
        </div>
        <div className="border-b border-gray-300 dark:border-gray-600 mb-2"></div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Nome: ________________________________________________
        </p>
        {config.headerConfig.instrucoes && (
          <p className="text-sm text-gray-700 dark:text-gray-300 italic mb-6">
            {config.headerConfig.instrucoes}
          </p>
        )}
      </div>
    </div>
  )
}

export default AssessmentPreview