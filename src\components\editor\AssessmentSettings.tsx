import React, { useState } from 'react'
import { X, Save, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { AssessmentConfig } from '../../types/assessment'
import ImageUpload from '../common/ImageUpload'
import { AssessmentAsset } from '../../hooks/useAssets'
import SchoolNameSelect from '../common/SchoolNameSelect'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'

interface AssessmentSettingsProps {
  isOpen: boolean
  config: AssessmentConfig
  onConfigChange: (config: AssessmentConfig) => void
  onClose: () => void
}

const AssessmentSettings: React.FC<AssessmentSettingsProps> = ({
  isOpen,
  config,
  onConfigChange,
  onClose
}) => {
  const { isPremium, isEscolar } = useSubscription();

  const handleHeaderChange = (field: string, value: string) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        [field]: value
      }
    })
  }

  const handlePdfOptionChange = (field: string, value: any) => {
    onConfigChange({
      ...config,
      pdfOptions: {
        ...config.pdfOptions,
        [field]: value
      }
    })
  }

  // Handlers para personalização de cabeçalho
  const handleCustomHeaderToggle = (enabled: boolean) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          customHeader: {
            enabled,
            asset: enabled ? config.headerConfig.customization?.customHeader?.asset : null,
            imageUrl: enabled ? config.headerConfig.customization?.customHeader?.imageUrl : undefined
          },
          // Desabilitar logo da escola quando cabeçalho personalizado estiver ativo
          schoolLogo: {
            ...config.headerConfig.customization?.schoolLogo,
            enabled: enabled ? false : config.headerConfig.customization?.schoolLogo?.enabled || false
          }
        }
      }
    })
  }

  const handleCustomHeaderAssetSelect = (asset: AssessmentAsset | null) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          customHeader: {
            enabled: config.headerConfig.customization?.customHeader?.enabled || false,
            asset,
            imageUrl: asset ? undefined : config.headerConfig.customization?.customHeader?.imageUrl
          }
        }
      }
    })
  }

  const handleSchoolLogoToggle = (enabled: boolean) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          schoolLogo: {
            enabled,
            asset: enabled ? config.headerConfig.customization?.schoolLogo?.asset : null,
            imageUrl: enabled ? config.headerConfig.customization?.schoolLogo?.imageUrl : undefined
          }
        }
      }
    })
  }

  const handleSchoolLogoAssetSelect = (asset: AssessmentAsset | null) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          schoolLogo: {
            enabled: config.headerConfig.customization?.schoolLogo?.enabled || false,
            asset,
            imageUrl: asset ? undefined : config.headerConfig.customization?.schoolLogo?.imageUrl
          }
        }
      }
    })
  }

  const isCustomHeaderEnabled = config.headerConfig.customization?.customHeader?.enabled || false
  const isSchoolLogoEnabled = config.headerConfig.customization?.schoolLogo?.enabled || false

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-5xl max-h-[85vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Configurações da Avaliação
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Basic Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Informações Básicas
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Disciplina
                  </label>
                  <select
                    value={config.disciplina}
                    onChange={(e) => onConfigChange({ ...config, disciplina: e.target.value })}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Selecione uma disciplina</option>
                    {DISCIPLINAS.map((disciplina) => (
                      <option key={disciplina} value={disciplina}>
                        {disciplina}
                      </option>
                    ))}
                    {/* Preservar valores personalizados de avaliações existentes */}
                    {config.disciplina && !DISCIPLINAS.includes(config.disciplina as any) && (
                      <option key={config.disciplina} value={config.disciplina}>
                        {config.disciplina} (personalizado)
                      </option>
                    )}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Série
                  </label>
                  <select
                    value={config.serie}
                    onChange={(e) => onConfigChange({
                      ...config,
                      serie: e.target.value,
                      headerConfig: {
                        ...config.headerConfig,
                        serie: e.target.value
                      }
                    })}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Selecione uma série</option>
                    {SERIES.map((serie) => (
                      <option key={serie} value={serie}>
                        {serie}
                      </option>
                    ))}
                    {/* Preservar valores personalizados de avaliações existentes */}
                    {config.serie && !SERIES.includes(config.serie as any) && (
                      <option key={config.serie} value={config.serie}>
                        {config.serie} (personalizado)
                      </option>
                    )}
                  </select>
                </div>
              </div>
            </div>

            {/* Header Customization */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Personalização de Cabeçalho
              </h3>

              {/* Two columns layout for header customization */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Custom Header Section */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white">
                        Cabeçalho Personalizado
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Substitui completamente o cabeçalho padrão por uma imagem personalizada
                      </p>
                    </div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isCustomHeaderEnabled}
                        onChange={(e) => handleCustomHeaderToggle(e.target.checked)}
                        className="mr-2 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Ativar</span>
                    </label>
                  </div>

                  {isCustomHeaderEnabled && (
                    <ImageUpload
                      assetType="custom_header"
                      selectedAsset={config.headerConfig.customization?.customHeader?.asset}
                      onAssetSelect={handleCustomHeaderAssetSelect}
                      title="Imagem do Cabeçalho"
                      description="Recomendado: 800x200px ou similar. Esta imagem substituirá completamente o cabeçalho padrão."
                      maxWidth="100%"
                      maxHeight="150px"
                    />
                  )}
                </div>

                {/* School Logo Section */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white">
                        Logo da Escola
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Adiciona logo/brasão no canto superior esquerdo do cabeçalho padrão
                      </p>
                    </div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isSchoolLogoEnabled}
                        onChange={(e) => handleSchoolLogoToggle(e.target.checked)}
                        disabled={isCustomHeaderEnabled}
                        className="mr-2 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded disabled:opacity-50"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Ativar</span>
                    </label>
                  </div>

                  {isCustomHeaderEnabled && (
                    <div className="flex items-center space-x-2 text-sm text-amber-600 dark:text-amber-400 mb-3">
                      <AlertCircle className="w-4 h-4" />
                      <span>Desabilitado quando cabeçalho personalizado está ativo</span>
                    </div>
                  )}

                  {isSchoolLogoEnabled && !isCustomHeaderEnabled && (
                    <ImageUpload
                      assetType="school_logo"
                      selectedAsset={config.headerConfig.customization?.schoolLogo?.asset}
                      onAssetSelect={handleSchoolLogoAssetSelect}
                      title="Logo/Brasão da Escola"
                      description="Recomendado: formato quadrado, 200x200px. Será exibido no canto superior esquerdo."
                      maxWidth="120px"
                      maxHeight="120px"
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Header Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Cabeçalho da Prova
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome da Escola
                  </label>
                  {isCustomHeaderEnabled ? (
                    <div className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                      {config.headerConfig.nomeEscola || 'Nome da Escola'}
                    </div>
                  ) : (
                    <SchoolNameSelect
                      value={config.headerConfig.nomeEscola}
                      onChange={(value) => handleHeaderChange('nomeEscola', value)}
                      placeholder="Selecione ou crie uma escola"
                    />
                  )}
                  {isCustomHeaderEnabled && (
                    <p className="text-xs text-gray-500 mt-1">
                      Desabilitado quando cabeçalho personalizado está ativo
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome da Prova
                  </label>
                  <input
                    type="text"
                    value={config.headerConfig.nomeProva}
                    onChange={(e) => handleHeaderChange('nomeProva', e.target.value)}
                    disabled={isCustomHeaderEnabled}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:bg-gray-100 dark:disabled:bg-gray-800"
                  />
                  {isCustomHeaderEnabled && (
                    <p className="text-xs text-gray-500 mt-1">
                      Desabilitado quando cabeçalho personalizado está ativo
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Instruções
                  </label>
                  <textarea
                    value={config.headerConfig.instrucoes}
                    onChange={(e) => handleHeaderChange('instrucoes', e.target.value)}
                    disabled={isCustomHeaderEnabled}
                    rows={3}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:bg-gray-100 dark:disabled:bg-gray-800"
                  />
                  {isCustomHeaderEnabled && (
                    <p className="text-xs text-gray-500 mt-1">
                      Desabilitado quando cabeçalho personalizado está ativo
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* PDF Options */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Opções de PDF
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tamanho do Papel
                  </label>
                  <select
                    value={config.pdfOptions.paperSize}
                    onChange={(e) => handlePdfOptionChange('paperSize', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="A4">A4</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Orientação
                  </label>
                  <select
                    value={config.pdfOptions.orientation}
                    onChange={(e) => handlePdfOptionChange('orientation', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="portrait">Retrato</option>
                    <option value="landscape">Paisagem</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tamanho da Fonte
                  </label>
                  <select
                    value={config.pdfOptions.fontSize}
                    onChange={(e) => handlePdfOptionChange('fontSize', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="small">Pequeno</option>
                    <option value="medium">Médio</option>
                    <option value="large">Grande</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Família da Fonte
                  </label>
                  <select
                    value={config.pdfOptions.fontFamily || 'times'}
                    onChange={(e) => handlePdfOptionChange('fontFamily', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="helvetica">Helvetica</option>
                    <option value="times">Times New Roman</option>
                    <option value="arial">Arial</option>
                    <option value="verdana">Verdana</option>
                    <option value="georgia">Georgia</option>
                    <option value="courier">Courier</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Espaçamento entre Linhas
                  </label>
                  <select
                    value={config.pdfOptions.lineSpacing}
                    onChange={(e) => handlePdfOptionChange('lineSpacing', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="compact">Compacto</option>
                    <option value="normal">Normal</option>
                    <option value="expanded">Expandido</option>
                  </select>
                </div>
              </div>
              
              {/* Two columns layout for watermark and versions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Marca d'água (opcional)
                  </label>
                  <input
                    type="text"
                    value={config.pdfOptions.watermark || ''}
                    onChange={(e) => handlePdfOptionChange('watermark', e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Ex: RASCUNHO, CONFIDENCIAL, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Número de Versões
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="4"
                    value={config.pdfOptions.generateVersions}
                    onChange={(e) => handlePdfOptionChange('generateVersions', parseInt(e.target.value))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Gera múltiplas versões da mesma avaliação com questões em ordem diferente.
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.pdfOptions.includeAnswerSheet}
                    onChange={(e) => handlePdfOptionChange('includeAnswerSheet', e.target.checked)}
                    className="mr-3 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Incluir folha de gabarito</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.pdfOptions.addBorder || false}
                    onChange={(e) => handlePdfOptionChange('addBorder', e.target.checked)}
                    className="mr-3 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Adicionar borda na avaliação</span>
                </label>
              </div>
            </div>

            {/* Opção de rodapé para usuários pagos */}
            {(isPremium || isEscolar) && (
              <div>
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <input
                    type="checkbox"
                    checked={config.showFooter}
                    onChange={e => onConfigChange({ ...config, showFooter: e.target.checked })}
                  />
                  Exibir rodapé "Gerado pela plataforma Atividade Pronta" no PDF e na visualização
                </label>
                <p className="text-xs text-gray-500 mt-1">Usuários gratuitos sempre terão o rodapé exibido.</p>
              </div>
            )}
          </div>

          <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={onClose}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Salvar</span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default AssessmentSettings