import React from 'react'
import { motion } from 'framer-motion'
import { Search, Edit3, Settings, Eye, FileText, Clock } from 'lucide-react'

interface MobileEditorTabsProps {
  activeTab: 'questions' | 'editor' | 'settings' | 'preview'
  onTabChange: (tab: 'questions' | 'editor' | 'settings' | 'preview') => void
  questionCount: number
  hasUnsavedChanges?: boolean
  showQuestionsTab?: boolean
}

const MobileEditorTabs: React.FC<MobileEditorTabsProps> = ({
  activeTab,
  onTabChange,
  questionCount,
  hasUnsavedChanges = false,
  showQuestionsTab = true
}) => {
  const allTabs = [
    {
      id: 'questions' as const,
      label: 'Questões',
      icon: Search,
      badge: null
    },
    {
      id: 'editor' as const,
      label: 'Editor',
      icon: Edit3,
      badge: questionCount > 0 ? questionCount : null
    },
    {
      id: 'settings' as const,
      label: 'Config',
      icon: Settings,
      badge: null
    },
    {
      id: 'preview' as const,
      label: 'Preview',
      icon: Eye,
      badge: hasUnsavedChanges ? '!' : null
    }
  ]

  const tabs = showQuestionsTab ? allTabs : allTabs.filter(tab => tab.id !== 'questions')

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-50">
      <div className="flex items-center justify-around px-2 py-2 safe-area-pb">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className="relative flex flex-col items-center justify-center p-2 min-w-0 flex-1"
            >
              {/* Indicador de aba ativa */}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute inset-0 bg-blue-50 dark:bg-blue-900/30 rounded-lg"
                  initial={false}
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              
              {/* Conteúdo da aba */}
              <div className="relative z-10 flex flex-col items-center">
                <div className="relative">
                  <Icon 
                    className={`w-6 h-6 transition-colors ${
                      isActive 
                        ? 'text-blue-600 dark:text-blue-400' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`} 
                  />
                  
                  {/* Badge */}
                  {tab.badge && (
                    <div className="absolute -top-2 -right-2 min-w-[18px] h-[18px] bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center px-1">
                      {tab.badge}
                    </div>
                  )}
                </div>
                
                <span 
                  className={`text-xs font-medium mt-1 transition-colors ${
                    isActive 
                      ? 'text-blue-600 dark:text-blue-400' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}
                >
                  {tab.label}
                </span>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default MobileEditorTabs 