import React from 'react'
import { X, Plus, Star, Tag, Eye, User, Clock, CheckCircle, XCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database'
import { useAuth } from '../../contexts/AuthContext'
import toast from 'react-hot-toast'

type Question = Database['public']['Tables']['questions']['Row']

interface QuestionDetailModalProps {
  isOpen: boolean
  onClose: () => void
  question: Question | null
  onAddToAssessment?: () => void
  isQuestionInAssessment?: boolean
  onRemoveFromAssessment?: () => void
  isAdmin: boolean
  onApproveQuestion?: (questionId: string) => Promise<void>
  onRejectQuestion?: (questionId: string) => Promise<void>
  isApproving?: boolean
  isRejecting?: boolean
}

const QuestionDetailModal: React.FC<QuestionDetailModalProps> = ({
  isOpen,
  onClose,
  question,
  onAddToAssessment,
  isQuestionInAssessment,
  onRemoveFromAssessment,
  isAdmin,
  onApproveQuestion,
  onRejectQuestion,
  isApproving,
  isRejecting
}) => {
  if (!isOpen || !question) return null

  const difficultyColors = {
    'Fácil': 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300',
    'Médio': 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300',
    'Difícil': 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300'
  }

  const typeLabels = {
    'multipla_escolha': 'Múltipla Escolha',
    'dissertativa': 'Dissertativa',
    'verdadeiro_falso': 'V/F'
  }

  const statusColors = {
    'pending': 'bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-300',
    'approved': 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
    'rejected': 'bg-gray-100 text-gray-800 dark:bg-gray-700/50 dark:text-gray-300',
    'draft': 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300',
  };

  const statusLabels = {
    'pending': 'Pendente',
    'approved': 'Aprovada',
    'rejected': 'Rejeitada',
    'draft': 'Rascunho',
  };

  const handleApprove = async () => {
    if (question && onApproveQuestion) {
      try {
        await onApproveQuestion(question.id);
        toast.success('Questão aprovada com sucesso!');
        onClose();
      } catch (error) {
        console.error('Erro ao aprovar questão:', error);
        toast.error('Erro ao aprovar questão.');
      }
    }
  };

  const handleReject = async () => {
    if (question && onRejectQuestion) {
      try {
        await onRejectQuestion(question.id);
        toast.success('Questão rejeitada com sucesso!');
        onClose();
      } catch (error) {
        console.error('Erro ao rejeitar questão:', error);
        toast.error('Erro ao rejeitar questão.');
      }
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Detalhes da Questão</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Question Header */}
            <div className="flex flex-wrap items-center gap-3 mb-4">
              <span className="text-base font-medium text-blue-600 dark:text-blue-400">{question.disciplina}</span>
              <span className="text-gray-300 dark:text-gray-600">•</span>
              <span className="text-base text-gray-600 dark:text-gray-400">{question.serie}</span>
              <span className="text-gray-300 dark:text-gray-600">•</span>
              <span className="text-base text-gray-600 dark:text-gray-400">{question.topico}</span>
              {question.subtopico && (
                <>
                  <span className="text-gray-300 dark:text-gray-600">•</span>
                  <span className="text-base text-gray-600 dark:text-gray-400">{question.subtopico}</span>
                </>
              )}
              {question.status && (
                <span className={`px-2 py-1 rounded-full text-sm ${statusColors[question.status as keyof typeof statusColors]}`}>
                  {statusLabels[question.status as keyof typeof statusLabels]}
                </span>
              )}
            </div>

            {/* Question Content */}
            <div className="bg-gray-50 dark:bg-gray-700/50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Enunciado</h3>
              <p className="text-gray-800 dark:text-gray-200 mb-6 leading-relaxed">
                {question.enunciado}
              </p>

              {question.tipo === 'multipla_escolha' && question.alternativas && (
                <div className="space-y-3 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Alternativas</h3>
                  {question.alternativas.map((alt, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className="text-gray-800 dark:text-gray-200 font-medium">
                        {String.fromCharCode(97 + index)})
                      </span>
                      <span className={`text-gray-800 dark:text-gray-200 ${
                        question.resposta_correta === String.fromCharCode(97 + index) ? 'font-medium' : ''
                      }`}>
                        {alt}
                      </span>
                    </div>
                  ))}
                </div>
              )}

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Resposta Correta</h3>
                <p className="text-gray-800 dark:text-gray-200">
                  {question.resposta_correta}
                </p>
              </div>
            </div>

            {/* Explanation */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Explicação</h3>
              <p className="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                {question.explicacao}
              </p>
            </div>

            {/* Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Metadados</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full ${difficultyColors[question.dificuldade as keyof typeof difficultyColors]}`}>
                      {question.dificuldade}
                    </span>
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
                      {typeLabels[question.tipo as keyof typeof typeLabels]}
                    </span>
                  </div>
                  
                  {question.competencia_bncc && (
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600 dark:text-gray-400">BNCC:</span>
                      <span className="font-medium text-gray-800 dark:text-gray-200">{question.competencia_bncc}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Autor: {question.autor_id?.slice(0, 8)}...
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Criada em: {new Date(question.created_at).toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Eye className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Usos: {question.uso_count}
                    </span>
                  </div>
                  
                  {question.rating > 0 && (
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {question.rating.toFixed(1)} ({question.rating_count} avaliações)
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {question.tags && question.tags.length > 0 ? (
                    question.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs"
                      >
                        <Tag className="w-3 h-3" />
                        <span>{tag}</span>
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 dark:text-gray-400 text-sm">Nenhuma tag</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            {isAdmin && question.status === 'pending' && (
              <>
                <button
                  onClick={handleReject}
                  disabled={isRejecting}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-700 dark:hover:bg-red-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRejecting ? <span className="flex items-center"><Loader className="w-4 h-4 mr-2 animate-spin" /> Rejeitando...</span> : <><XCircle className="h-5 w-5 mr-2" /> Rejeitar</>}
                </button>
                <button
                  onClick={handleApprove}
                  disabled={isApproving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isApproving ? <span className="flex items-center"><Loader className="w-4 h-4 mr-2 animate-spin" /> Aprovando...</span> : <><CheckCircle className="h-5 w-5 mr-2" /> Aprovar</>}
                </button>
              </>
            )}
            {onAddToAssessment && !isQuestionInAssessment && (
              <button
                onClick={onAddToAssessment}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800"
              >
                <Plus className="h-5 w-5 mr-2" />
                Adicionar à Avaliação
              </button>
            )}
            {onRemoveFromAssessment && isQuestionInAssessment && (
              <button
                onClick={onRemoveFromAssessment}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-700 dark:hover:bg-red-800"
              >
                <X className="h-5 w-5 mr-2" />
                Remover da Avaliação
              </button>
            )}
            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Fechar
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default QuestionDetailModal