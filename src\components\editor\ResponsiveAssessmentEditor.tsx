import React, { Suspense } from 'react'
import { useIsMobile } from '../../hooks/useIsMobile'
import LoadingSpinner from '../common/LoadingSpinner'

// Lazy loading dos componentes
const DesktopAssessmentEditor = React.lazy(() => import('./AssessmentEditor'))
const MobileAssessmentEditor = React.lazy(() => import('./MobileAssessmentEditor'))

/**
 * Wrapper responsivo que escolhe entre layout mobile e desktop
 * baseado no tamanho da tela do dispositivo
 */
const ResponsiveAssessmentEditor: React.FC = () => {
  const { isMobile } = useIsMobile()

  return (
    <Suspense 
      fallback={
        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <LoadingSpinner size="lg" text="Carregando editor..." />
        </div>
      }
    >
      {isMobile ? (
        <MobileAssessmentEditor />
      ) : (
        <DesktopAssessmentEditor />
      )}
    </Suspense>
  )
}

export default ResponsiveAssessmentEditor 