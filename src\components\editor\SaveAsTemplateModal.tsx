import React, { useState } from 'react'
import { X, Save, Layout, Crown, Settings, FileText, <PERSON>lette, CheckCircle2, Circle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'
import { CreateTemplateDTO, TemplateCreationOptions } from '../../types/templates'
import { Question } from '../../types/question'
import toast from 'react-hot-toast'

interface AssessmentConfig {
  titulo: string
  disciplina: string
  serie: string
  headerConfig: {
    nomeEscola: string
    nomeProva: string
    serie: string
    data: string
    instrucoes: string
  }
  pdfOptions: {
    paperSize: 'A4' | 'Letter'
    orientation: 'portrait' | 'landscape'
    fontSize: 'small' | 'medium' | 'large'
    lineSpacing: 'compact' | 'normal' | 'expanded'
    includeAnswerSheet: boolean
    generateVersions: number
    watermark?: string
  }
}

interface SaveAsTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (templateData: CreateTemplateDTO) => void
  config: AssessmentConfig
  selectedQuestions?: Question[]
  selectedItems?: any[]
}

const CATEGORIAS = ['Padrão', 'Design', 'Escolar', 'Minimalista', 'Colorido', 'Personalizado']

const SaveAsTemplateModal: React.FC<SaveAsTemplateModalProps> = ({
  isOpen,
  onClose,
  onSave,
  config,
  selectedQuestions = [],
  selectedItems = []
}) => {
  const { user } = useAuth()
  const [step, setStep] = useState<'basic' | 'options' | 'questions' | 'review'>('basic')
  const [nome, setNome] = useState('')
  const [descricao, setDescricao] = useState('')
  const [categoria, setCategoria] = useState('Personalizado')
  const [isPremium, setIsPremium] = useState(false)
  const [saving, setSaving] = useState(false)

  // Template creation options
  const [options, setOptions] = useState<TemplateCreationOptions>({
    includeStructure: true,
    includeQuestions: false,
    includeLayout: true,
    includeCustomization: true,
    selectedQuestionIds: [],
    customName: '',
    customDescription: ''
  })

  // Available questions from selected items
  const availableQuestions = selectedItems.filter(item => 'tipo' in item && item.tipo !== undefined) as Question[]

  // Handle question selection
  const handleQuestionToggle = (questionId: string) => {
    setOptions(prev => ({
      ...prev,
      selectedQuestionIds: prev.selectedQuestionIds?.includes(questionId)
        ? prev.selectedQuestionIds.filter(id => id !== questionId)
        : [...(prev.selectedQuestionIds || []), questionId]
    }))
  }

  // Handle option changes
  const handleOptionChange = (key: keyof TemplateCreationOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Reset form
  const resetForm = () => {
    setStep('basic')
    setNome('')
    setDescricao('')
    setCategoria('Personalizado')
    setIsPremium(false)
    setOptions({
      includeStructure: true,
      includeQuestions: false,
      includeLayout: true,
      includeCustomization: true,
      selectedQuestionIds: [],
      customName: '',
      customDescription: ''
    })
  }

  // Handle close
  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleSave = async () => {
    if (!nome) {
      toast.error('Nome do template é obrigatório')
      return
    }

    if (!user) {
      toast.error('Você precisa estar logado para salvar templates')
      return
    }

    setSaving(true)

    try {
      // Build template content based on selected options
      const templateData: CreateTemplateDTO = {
        nome,
        categoria,
        descricao,
        content: {
          structure: options.includeStructure ? {
            includeHeader: true,
            includeInstructions: true,
            includeAnswerSheet: config.pdfOptions.includeAnswerSheet,
            pageBreaks: true
          } : {
            includeHeader: false,
            includeInstructions: false,
            includeAnswerSheet: false,
            pageBreaks: false
          },
          questions: {
            includeQuestions: options.includeQuestions,
            questionIds: options.selectedQuestionIds || [],
            preserveOrder: true,
            allowModification: true
          },
          layout: options.includeLayout ? {
            fontSize: config.pdfOptions.fontSize,
            fontFamily: config.pdfOptions.fontFamily || 'times',
            lineSpacing: config.pdfOptions.lineSpacing,
            paperSize: config.pdfOptions.paperSize,
            orientation: config.pdfOptions.orientation
          } : {
            fontSize: 'medium',
            lineSpacing: 'normal',
            paperSize: 'A4',
            orientation: 'portrait'
          },
          customization: options.includeCustomization ? {
            headerConfig: config.headerConfig,
            watermark: config.pdfOptions.watermark
          } : {
            headerConfig: {
              nomeEscola: 'Nome da Escola',
              nomeProva: 'Avaliação',
              serie: '',
              data: new Date().toLocaleDateString('pt-BR'),
              instrucoes: 'Leia atentamente cada questão antes de responder.'
            }
          }
        },
        metadata: {
          version: '2.0',
          createdWith: 'assessment',
          createdAt: new Date().toISOString(),
          originalAssessment: {
            titulo: config.titulo,
            disciplina: config.disciplina,
            serie: config.serie
          }
        },
        is_premium: isPremium,
        is_system: false,
        tags: [config.disciplina, config.serie].filter(Boolean)
      }

      await onSave(templateData)
      toast.success('Template salvo com sucesso!')
      resetForm()
      onClose()
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Erro ao salvar template')
    } finally {
      setSaving(false)
    }
  }

  if (!isOpen) return null

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case 'basic':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nome do Modelo *
              </label>
              <input
                type="text"
                value={nome}
                onChange={(e) => setNome(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Ex: Avaliação Bimestral"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Descrição
              </label>
              <textarea
                value={descricao}
                onChange={(e) => setDescricao(e.target.value)}
                rows={3}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Descreva o propósito e características deste modelo..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Categoria
              </label>
              <select
                value={categoria}
                onChange={(e) => setCategoria(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                {CATEGORIAS.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="isPremium"
                checked={isPremium}
                onChange={(e) => setIsPremium(e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="isPremium" className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                <Crown className="w-4 h-4 text-yellow-500 mr-2" />
                Modelo Premium
              </label>
            </div>
          </div>
        )

      case 'options':
        return (
          <div className="space-y-6">
            <div className="text-center mb-4 sm:mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                O que incluir no modelo?
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                Selecione os elementos que deseja salvar no modelo
              </p>
            </div>

            <div className="space-y-3 sm:space-y-4">
              <div className="flex items-center justify-between p-3 sm:p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                  <Settings className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">Estrutura</h4>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 hidden sm:block">Cabeçalho, instruções, configurações gerais</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 sm:hidden">Cabeçalho e instruções</p>
                  </div>
                </div>
                <button
                  onClick={() => handleOptionChange('includeStructure', !options.includeStructure)}
                  className="p-1 flex-shrink-0"
                >
                  {options.includeStructure ? (
                    <CheckCircle2 className="w-5 h-5 sm:w-6 sm:h-6 text-green-500" />
                  ) : (
                    <Circle className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400" />
                  )}
                </button>
              </div>

              <div className="flex items-center justify-between p-3 sm:p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">Questões</h4>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                      {availableQuestions.length} questões disponíveis
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => handleOptionChange('includeQuestions', !options.includeQuestions)}
                  className="p-1 flex-shrink-0"
                >
                  {options.includeQuestions ? (
                    <CheckCircle2 className="w-5 h-5 sm:w-6 sm:h-6 text-green-500" />
                  ) : (
                    <Circle className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400" />
                  )}
                </button>
              </div>

              <div className="flex items-center justify-between p-3 sm:p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                  <Layout className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">Layout</h4>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 hidden sm:block">Fonte, espaçamento, formato do papel</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 sm:hidden">Fonte e formatação</p>
                  </div>
                </div>
                <button
                  onClick={() => handleOptionChange('includeLayout', !options.includeLayout)}
                  className="p-1 flex-shrink-0"
                >
                  {options.includeLayout ? (
                    <CheckCircle2 className="w-5 h-5 sm:w-6 sm:h-6 text-green-500" />
                  ) : (
                    <Circle className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400" />
                  )}
                </button>
              </div>

              <div className="flex items-center justify-between p-3 sm:p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                  <Palette className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">Personalização</h4>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 hidden sm:block">Cabeçalho personalizado, marca d'água</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 sm:hidden">Cabeçalho e marca d'água</p>
                  </div>
                </div>
                <button
                  onClick={() => handleOptionChange('includeCustomization', !options.includeCustomization)}
                  className="p-1 flex-shrink-0"
                >
                  {options.includeCustomization ? (
                    <CheckCircle2 className="w-5 h-5 sm:w-6 sm:h-6 text-green-500" />
                  ) : (
                    <Circle className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
          </div>
        )

      case 'questions':
        return (
          <div className="space-y-6">
            <div className="text-center mb-4 sm:mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Selecionar Questões
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                Escolha as questões específicas que deseja incluir no modelo
              </p>
            </div>

            {availableQuestions.length === 0 ? (
              <div className="text-center py-6 sm:py-8">
                <FileText className="w-10 h-10 sm:w-12 sm:h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nenhuma questão disponível para seleção
                </p>
              </div>
            ) : (
              <div className="space-y-2 sm:space-y-3">
                {availableQuestions.map((question) => (
                  <div
                    key={question.id}
                    className="flex items-start space-x-3 p-3 sm:p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                  >
                    <button
                      onClick={() => handleQuestionToggle(question.id)}
                      className="mt-1 flex-shrink-0"
                    >
                      {options.selectedQuestionIds?.includes(question.id) ? (
                        <CheckCircle2 className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                      ) : (
                        <Circle className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      )}
                    </button>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                        {question.enunciado}
                      </p>
                      <div className="flex flex-wrap items-center gap-1 sm:gap-2 mt-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">
                          {question.tipo.replace('_', ' ')}
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          {question.dificuldade}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-300">
                <strong>{options.selectedQuestionIds?.length || 0}</strong> questões selecionadas
              </p>
            </div>
          </div>
        )

      case 'review':
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Revisar Modelo
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Confirme as configurações do seu modelo antes de salvar
              </p>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Informações Básicas</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Nome:</strong> {nome}</div>
                  <div><strong>Categoria:</strong> {categoria}</div>
                  {descricao && <div><strong>Descrição:</strong> {descricao}</div>}
                  <div><strong>Tipo:</strong> {isPremium ? 'Premium' : 'Gratuito'}</div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Elementos Incluídos</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    {options.includeStructure ? (
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    ) : (
                      <Circle className="w-4 h-4 text-gray-400" />
                    )}
                    <span>Estrutura (cabeçalho, instruções)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {options.includeQuestions ? (
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    ) : (
                      <Circle className="w-4 h-4 text-gray-400" />
                    )}
                    <span>Questões ({options.selectedQuestionIds?.length || 0} selecionadas)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {options.includeLayout ? (
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    ) : (
                      <Circle className="w-4 h-4 text-gray-400" />
                    )}
                    <span>Layout (fonte, espaçamento, papel)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {options.includeCustomization ? (
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                    ) : (
                      <Circle className="w-4 h-4 text-gray-400" />
                    )}
                    <span>Personalização (cabeçalho, marca d'água)</span>
                  </div>
                </div>
              </div>

              {options.includeLayout && (
                <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Configurações de Layout</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Fonte:</strong> {config.pdfOptions.fontSize}</div>
                    <div><strong>Espaçamento:</strong> {config.pdfOptions.lineSpacing}</div>
                    <div><strong>Papel:</strong> {config.pdfOptions.paperSize} - {config.pdfOptions.orientation}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2 sm:p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl h-full max-h-[95vh] sm:max-h-[90vh] flex flex-col overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header - Flexible and compact */}
          <div className="flex-shrink-0 p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h2 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate">Salvar como Modelo</h2>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  {step === 'basic' && 'Informações básicas do modelo'}
                  {step === 'options' && 'Selecione os elementos a incluir'}
                  {step === 'questions' && 'Escolha as questões específicas'}
                  {step === 'review' && 'Revise as configurações'}
                </p>
              </div>
              <button
                onClick={handleClose}
                className="flex-shrink-0 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors ml-2"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Progress indicator - More compact on mobile */}
            <div className="flex items-center justify-center space-x-2 mt-3 sm:mt-4">
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'basic' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'options' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'questions' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'review' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
            </div>
          </div>

          {/* Content - Scrollable area that takes remaining space */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="p-4 sm:p-6">
              {renderStepContent()}
            </div>
          </div>

          {/* Footer - Sticky at bottom with responsive padding */}
          <div className="flex-shrink-0 p-4 sm:p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 sm:gap-0">
              <button
                onClick={handleClose}
                className="order-2 sm:order-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-center"
              >
                Cancelar
              </button>

              <div className="order-1 sm:order-2 flex flex-col sm:flex-row gap-2 sm:gap-3">
                {step !== 'basic' && (
                  <button
                    onClick={() => {
                      if (step === 'options') setStep('basic')
                      else if (step === 'questions') setStep('options')
                      else if (step === 'review') setStep(options.includeQuestions ? 'questions' : 'options')
                    }}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-center"
                  >
                    Voltar
                  </button>
                )}

                {step === 'basic' && (
                  <button
                    onClick={() => setStep('options')}
                    disabled={!nome.trim()}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors text-center"
                  >
                    Próximo
                  </button>
                )}

                {step === 'options' && (
                  <button
                    onClick={() => setStep(options.includeQuestions ? 'questions' : 'review')}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-center"
                  >
                    <span className="hidden sm:inline">{options.includeQuestions ? 'Selecionar Questões' : 'Finalizar'}</span>
                    <span className="sm:hidden">{options.includeQuestions ? 'Questões' : 'Finalizar'}</span>
                  </button>
                )}

                {step === 'questions' && (
                  <button
                    onClick={() => setStep('review')}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-center"
                  >
                    Revisar
                  </button>
                )}

                {step === 'review' && (
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg transition-colors"
                  >
                    {saving ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    <span className="hidden sm:inline">Salvar Modelo</span>
                    <span className="sm:hidden">Salvar</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default SaveAsTemplateModal