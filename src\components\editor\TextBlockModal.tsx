import React, { useState, useEffect } from 'react'
import { X, Save, AlignLeft, AlignCenter, AlignRight, AlignJustify, Heading1, Heading2, AlertCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface TextBlock {
  id: string;
  type: 'text';
  content: string;
  style?: 'normal' | 'heading' | 'subheading' | 'instruction';
  textAlign?: 'left' | 'center' | 'right' | 'justify';
}

interface TextBlockModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (textBlock: TextBlock) => void
  initialBlock?: TextBlock
}

const TextBlockModal: React.FC<TextBlockModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialBlock
}) => {
  const [content, setContent] = useState(initialBlock?.content || '')
  const [style, setStyle] = useState<TextBlock['style']>(initialBlock?.style || 'normal')
  const [textAlign, setTextAlign] = useState<TextBlock['textAlign']>(initialBlock?.textAlign || 'left')

  useEffect(() => {
    if (initialBlock) {
      setContent(initialBlock.content)
      setStyle(initialBlock.style || 'normal')
      setTextAlign(initialBlock.textAlign || 'left')
    } else {
      setContent('')
      setStyle('normal')
      setTextAlign('left')
    }
  }, [initialBlock]);

  const handleSave = () => {
    if (!content.trim()) {
      return
    }

    onSave({
      id: initialBlock?.id || `text-${Date.now()}`,
      type: 'text',
      content,
      style,
      textAlign
    })
  }

  if (!isOpen) return null

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {initialBlock ? 'Editar Bloco de Texto' : 'Adicionar Bloco de Texto'}
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Estilo do Texto
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                <button
                  type="button"
                  onClick={() => setStyle('normal')}
                  className={`flex flex-col items-center p-3 border rounded-lg transition-colors ${
                    style === 'normal'
                      ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                  }`}
                >
                  <AlignLeft className="w-5 h-5 mb-1 text-gray-700 dark:text-gray-300" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Normal</span>
                </button>
                
                <button
                  type="button"
                  onClick={() => setStyle('heading')}
                  className={`flex flex-col items-center p-3 border rounded-lg transition-colors ${
                    style === 'heading'
                      ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                  }`}
                >
                  <Heading1 className="w-5 h-5 mb-1 text-gray-700 dark:text-gray-300" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Título</span>
                </button>
                
                <button
                  type="button"
                  onClick={() => setStyle('subheading')}
                  className={`flex flex-col items-center p-3 border rounded-lg transition-colors ${
                    style === 'subheading'
                      ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                  }`}
                >
                  <Heading2 className="w-5 h-5 mb-1 text-gray-700 dark:text-gray-300" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Subtítulo</span>
                </button>
                
                <button
                  type="button"
                  onClick={() => setStyle('instruction')}
                  className={`flex flex-col items-center p-3 border rounded-lg transition-colors ${
                    style === 'instruction'
                      ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                  }`}
                >
                  <AlertCircle className="w-5 h-5 mb-1 text-gray-700 dark:text-gray-300" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Instrução</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Alinhamento do Texto
              </label>
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 space-x-1">
                <button
                  type="button"
                  onClick={() => setTextAlign('left')}
                  className={`flex-1 p-2 rounded-md transition-colors flex items-center justify-center ${
                    textAlign === 'left'
                      ? 'bg-blue-500 text-white shadow'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                  title="Alinhar à Esquerda"
                >
                  <AlignLeft className="w-5 h-5" />
                </button>
                <button
                  type="button"
                  onClick={() => setTextAlign('center')}
                  className={`flex-1 p-2 rounded-md transition-colors flex items-center justify-center ${
                    textAlign === 'center'
                      ? 'bg-blue-500 text-white shadow'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                  title="Alinhar ao Centro"
                >
                  <AlignCenter className="w-5 h-5" />
                </button>
                <button
                  type="button"
                  onClick={() => setTextAlign('right')}
                  className={`flex-1 p-2 rounded-md transition-colors flex items-center justify-center ${
                    textAlign === 'right'
                      ? 'bg-blue-500 text-white shadow'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                  title="Alinhar à Direita"
                >
                  <AlignRight className="w-5 h-5" />
                </button>
                <button
                  type="button"
                  onClick={() => setTextAlign('justify')}
                  className={`flex-1 p-2 rounded-md transition-colors flex items-center justify-center ${
                    textAlign === 'justify'
                      ? 'bg-blue-500 text-white shadow'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                  title="Justificar"
                >
                  <AlignJustify className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Conteúdo do Texto
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={6}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Digite o conteúdo do texto..."
              />
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Preview</h3>
              <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <div className={`${textAlignClasses[textAlign || 'left']} ${
                  style === 'heading' ? 'text-xl font-bold text-gray-900 dark:text-white' : 
                  style === 'subheading' ? 'text-lg font-semibold text-gray-800 dark:text-gray-200' : 
                  style === 'instruction' ? 'text-sm italic text-gray-700 dark:text-gray-300' : 
                  'text-base text-gray-800 dark:text-gray-200'
                }`}>
                  {content || 'Preview do texto...'}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              disabled={!content.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:disabled:bg-blue-800/50 text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Salvar</span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default TextBlockModal