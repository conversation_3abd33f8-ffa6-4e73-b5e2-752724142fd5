import React, { useState, useEffect } from 'react'
import { Star, ThumbsUp, ThumbsDown, MessageSquare, Send, CheckCircle, Clock, XCircle } from 'lucide-react'
import { motion } from 'framer-motion'
import { Database } from '../../types/database'
import { useQuestionFeedback, canEditFeedback, getFeedbackStatus } from '../../hooks/useQuestionFeedback'
import { useAuth } from '../../contexts/AuthContext'
import toast from 'react-hot-toast'

type Question = Database['public']['Tables']['questions']['Row']
type QuestionFeedbackInsert = Database['public']['Tables']['question_feedback']['Insert']

interface FeedbackSystemProps {
  question: Question
  onFeedbackSubmit?: (feedback: QuestionFeedbackInsert) => void
}

const FeedbackSystem: React.FC<FeedbackSystemProps> = ({
  question,
  onFeedbackSubmit
}) => {
  const { user } = useAuth()
  const {
    feedback,
    createFeedback,
    updateFeedback,
    isCreating,
    isUpdating
  } = useQuestionFeedback({ question_id: question.id })

  const [rating, setRating] = useState(0)
  const [comment, setComment] = useState('')
  const [helpful, setHelpful] = useState<boolean | null>(null)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [feedbackType, setFeedbackType] = useState<'rating' | 'improvement' | 'error' | 'general'>('rating')
  const [showFeedbackForm, setShowFeedbackForm] = useState(false)
  const [editingFeedback, setEditingFeedback] = useState<string | null>(null)

  // Check if user has already submitted feedback for this question
  const userFeedback = feedback.find(f => f.user_id === user?.id)
  const canEdit = userFeedback ? canEditFeedback(userFeedback, user?.id) : false

  const suggestionOptions = [
    'Enunciado confuso',
    'Alternativas inadequadas',
    'Nível de dificuldade incorreto',
    'Explicação insuficiente',
    'Erro de português',
    'Conteúdo desatualizado'
  ]

  const feedbackTypeOptions = [
    { value: 'rating', label: 'Avaliação Geral' },
    { value: 'improvement', label: 'Sugestão de Melhoria' },
    { value: 'error', label: 'Erro Encontrado' },
    { value: 'general', label: 'Comentário Geral' }
  ]

  // Load existing feedback data when editing
  useEffect(() => {
    if (userFeedback && editingFeedback === userFeedback.id) {
      setRating(userFeedback.rating)
      setComment(userFeedback.comment || '')
      setHelpful(userFeedback.helpful)
      setSuggestions(userFeedback.suggestions || [])
      setFeedbackType(userFeedback.feedback_type as any)
    }
  }, [userFeedback, editingFeedback])

  const handleSuggestionToggle = (suggestion: string) => {
    setSuggestions(prev =>
      prev.includes(suggestion)
        ? prev.filter(s => s !== suggestion)
        : [...prev, suggestion]
    )
  }

  const resetForm = () => {
    setRating(0)
    setComment('')
    setHelpful(null)
    setSuggestions([])
    setFeedbackType('rating')
    setEditingFeedback(null)
  }

  const handleSubmitFeedback = async () => {
    if (rating === 0) {
      toast.error('Por favor, dê uma nota para a questão')
      return
    }

    if (!user) {
      toast.error('Você precisa estar logado para enviar feedback')
      return
    }

    try {
      const feedbackData: QuestionFeedbackInsert = {
        question_id: question.id,
        rating,
        comment: comment.trim() || null,
        helpful,
        suggestions,
        feedback_type: feedbackType
      }

      if (editingFeedback) {
        // Update existing feedback
        updateFeedback({
          id: editingFeedback,
          updates: feedbackData
        })
      } else {
        // Create new feedback
        createFeedback(feedbackData)
      }

      // Call optional callback
      onFeedbackSubmit?.(feedbackData)

      setShowFeedbackForm(false)
      resetForm()
    } catch (error) {
      console.error('Error submitting feedback:', error)
    }
  }

  const handleEditFeedback = () => {
    if (userFeedback && canEdit) {
      setEditingFeedback(userFeedback.id)
      setShowFeedbackForm(true)
    }
  }

  return (
    <div className="space-y-4">
      {/* Existing User Feedback */}
      {userFeedback && (
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Seu Feedback
              </h4>
              {(() => {
                const status = getFeedbackStatus(userFeedback)
                const statusIcons = {
                  pending: <Clock className="w-4 h-4" />,
                  approved: <CheckCircle className="w-4 h-4" />,
                  rejected: <XCircle className="w-4 h-4" />
                }
                const statusColors = {
                  pending: 'text-yellow-600 dark:text-yellow-400',
                  approved: 'text-green-600 dark:text-green-400',
                  rejected: 'text-red-600 dark:text-red-400'
                }
                return (
                  <div className={`flex items-center space-x-1 ${statusColors[status.status]}`}>
                    {statusIcons[status.status]}
                    <span className="text-xs font-medium">{status.label}</span>
                  </div>
                )
              })()}
            </div>
            {canEdit && (
              <button
                onClick={handleEditFeedback}
                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
              >
                Editar
              </button>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-blue-700 dark:text-blue-300">Avaliação:</span>
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-3 h-3 ${
                      star <= userFeedback.rating
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300 dark:text-gray-600'
                    }`}
                  />
                ))}
                <span className="ml-1 text-xs text-blue-700 dark:text-blue-300">
                  ({userFeedback.rating}/5)
                </span>
              </div>
            </div>

            {userFeedback.comment && (
              <div>
                <span className="text-xs text-blue-700 dark:text-blue-300">Comentário:</span>
                <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                  {userFeedback.comment}
                </p>
              </div>
            )}

            {userFeedback.admin_response && (
              <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded border">
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Resposta da Equipe:
                </span>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {userFeedback.admin_response}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Rating */}
      <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Esta questão foi útil?
          </span>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setHelpful(true)}
              className={`p-2 rounded-lg transition-colors ${
                helpful === true
                  ? 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400'
                  : 'text-gray-400 dark:text-gray-500 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/30'
              }`}
            >
              <ThumbsUp className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => setHelpful(false)}
              className={`p-2 rounded-lg transition-colors ${
                helpful === false
                  ? 'bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400'
                  : 'text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30'
              }`}
            >
              <ThumbsDown className="w-4 h-4" />
            </button>
          </div>
        </div>

        <button
          onClick={() => setShowFeedbackForm(!showFeedbackForm)}
          className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium"
        >
          <MessageSquare className="w-4 h-4" />
          <span>
            {userFeedback
              ? (canEdit ? 'Editar feedback' : 'Ver feedback')
              : 'Dar feedback detalhado'
            }
          </span>
        </button>
      </div>

      {/* Detailed Feedback Form */}
      {showFeedbackForm && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 space-y-6"
        >
          <h4 className="font-semibold text-gray-900 dark:text-white">
            {editingFeedback ? 'Editar Feedback' : 'Ajude-nos a melhorar esta questão'}
          </h4>

          {/* Feedback Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tipo de feedback
            </label>
            <select
              value={feedbackType}
              onChange={(e) => setFeedbackType(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {feedbackTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Star Rating */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Avaliação geral (obrigatório)
            </label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  className={`p-1 transition-colors ${
                    star <= rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-500'
                  }`}
                >
                  <Star className="w-6 h-6 fill-current" />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                  {rating === 1 && 'Muito ruim'}
                  {rating === 2 && 'Ruim'}
                  {rating === 3 && 'Regular'}
                  {rating === 4 && 'Bom'}
                  {rating === 5 && 'Excelente'}
                </span>
              )}
            </div>
          </div>

          {/* Suggestions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              O que pode ser melhorado? (opcional)
            </label>
            <div className="grid grid-cols-2 gap-2">
              {suggestionOptions.map((suggestion) => (
                <label
                  key={suggestion}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={suggestions.includes(suggestion)}
                    onChange={() => handleSuggestionToggle(suggestion)}
                    className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{suggestion}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Comment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Comentário adicional (opcional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Compartilhe suas sugestões específicas..."
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={() => {
                setShowFeedbackForm(false)
                resetForm()
              }}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Cancelar
            </button>

            <button
              onClick={handleSubmitFeedback}
              disabled={(isCreating || isUpdating) || rating === 0}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {(isCreating || isUpdating) ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              <span>
                {editingFeedback ? 'Atualizar' : 'Enviar'} Feedback
              </span>
            </button>
          </div>
        </motion.div>
      )}

      {/* Question Stats */}
      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-500 pt-2 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Star className="w-4 h-4 text-yellow-500" />
            <span>{question.rating?.toFixed(1) || '0.0'}</span>
            <span>({question.rating_count || 0} avaliações)</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <ThumbsUp className="w-4 h-4" />
            <span>{question.uso_count || 0} usos</span>
          </div>
        </div>

        <div className="text-xs text-gray-400 dark:text-gray-600">
          ID: {question.id.slice(0, 8)}...
        </div>
      </div>
    </div>
  )
}

export default FeedbackSystem