import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { 
  Menu, 
  Search, 
  PlusCircle,
  Crown,
  Shield
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useSubscription } from '../../contexts/SubscriptionContext'
import NotificationCenter from '../notifications/NotificationCenter'
import { motion } from 'framer-motion'
import * as ReactHelmetAsync from 'react-helmet-async'
const { Helmet } = ReactHelmetAsync

interface HeaderProps {
  onMenuClick: () => void
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, profile, signOut, isAdmin } = useAuth()
  const { isPremium, isEscolar } = useSubscription()
  const [showUserMenu, setShowUserMenu] = React.useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const getPageTitle = () => {
    const path = location.pathname
    const titles = {
      '/': 'Dashboard',
      '/questions': 'Banco de Questões',
      '/editor': 'Editor de Avaliações',
      '/assessments': 'Minhas Avaliações',
      '/templates': 'Modelos Prontos',
      '/analytics': 'Analytics',
      '/settings': 'Configurações',
      '/billing': 'Planos e Cobrança'
    }
    return titles[path as keyof typeof titles] || 'Atividade Pronta'
  }

  React.useEffect(() => {
    document.title = `${getPageTitle()} | Atividade Pronta`;
  }, [location.pathname]);

  const pageTitle = `${getPageTitle()} | Atividade Pronta`;
  const pageDescription = 'A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados.';

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={window.location.href} />
        <meta property="og:image" content="https://atvpronta.com.br/og-image.png" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        <meta name="twitter:image" content="https://atvpronta.com.br/og-image.png" />
        <link rel="canonical" href={window.location.href} />
      </Helmet>
      <header className="h-16 bg-white border-b border-gray-200 flex items-center px-4 lg:px-6 sticky top-0 z-40">
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuClick}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors lg:hidden"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">EA</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold text-gray-900">{getPageTitle()}</h1>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-2xl mx-4 lg:mx-8 hidden md:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar questões, avaliações ou modelos..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2 lg:space-x-4">
          {/* New Assessment Button */}
          <button
            onClick={() => navigate('/editor')}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 lg:px-4 py-2 rounded-lg transition-colors text-sm font-medium"
          >
            <PlusCircle className="w-4 h-4" />
            <span className="hidden sm:inline">Nova Avaliação</span>
          </button>

          {/* Admin Panel Button (if admin) */}
          {isAdmin && (
            <button
              onClick={() => navigate('/admin')}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 lg:px-4 py-2 rounded-lg transition-colors text-sm font-medium"
            >
              <Shield className="w-4 h-4" />
              <span className="hidden sm:inline">Admin</span>
            </button>
          )}

          {/* Notifications */}
          <NotificationCenter />

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="text-right hidden lg:block">
                <p className="text-sm font-medium text-gray-900 flex items-center space-x-1">
                  <span>{profile?.nome || user?.email}</span>
                  {(isPremium || isEscolar) && (
                    <Crown className="w-4 h-4 text-yellow-500" />
                  )}
                </p>
                <p className="text-xs text-gray-500">
                  {isEscolar ? 'Plano Escolar' : isPremium ? 'Premium' : 'Gratuito'}
                </p>
              </div>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600">
                  {profile?.nome?.charAt(0) || user?.email?.charAt(0) || 'U'}
                </span>
              </div>
            </button>

            {showUserMenu && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
              >
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="font-medium text-gray-900">{profile?.nome || user?.email}</p>
                  <p className="text-sm text-gray-500">{profile?.escola || 'Sem escola'}</p>
                </div>
                
                <button
                  onClick={() => {
                    navigate('/settings')
                    setShowUserMenu(false)
                  }}
                  className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                >
                  <span className="text-sm text-gray-700">Configurações</span>
                </button>
                
                <button
                  onClick={() => {
                    navigate('/billing')
                    setShowUserMenu(false)
                  }}
                  className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                >
                  <Crown className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-700">Planos e Cobrança</span>
                </button>
                
                {isAdmin && (
                  <button
                    onClick={() => {
                      navigate('/admin')
                      setShowUserMenu(false)
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                  >
                    <Shield className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-gray-700">Painel Admin</span>
                  </button>
                )}
                
                <hr className="my-2" />
                
                <button
                  onClick={handleSignOut}
                  className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors text-red-600"
                >
                  <span className="text-sm">Sair</span>
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </header>
    </>
  )
}

export default Header