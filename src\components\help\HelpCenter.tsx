import React, { useState, useEffect } from 'react'
import { 
  HelpCircle, 
  Book, 
  MessageCircle, 
  Video, 
  Search,
  ChevronRight,
  ExternalLink
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { supabase } from '../../lib/supabase'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

const HelpCenter: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const [faqs, setFaqs] = useState<FAQItem[]>([])
  const [loading, setLoading] = useState(true)

  const categories = ['all', 'Primeiros Passos', 'Questões', 'Avaliações', 'Planos', 'Técnico']

  useEffect(() => {
    fetchFAQs()
  }, [])

  const fetchFAQs = async () => {
    try {
      // Try to fetch FAQs from the database
      const { data, error } = await supabase
        .from('faq_items')
        .select('*')
        .order('category')
        .order('id')

      if (error) {
        console.error('Error fetching FAQs:', error)
        // Fall back to mock data
        setFaqs(getMockFAQs())
      } else if (data && data.length > 0) {
        setFaqs(data)
      } else {
        // If no FAQs in database, use mock data
        setFaqs(getMockFAQs())
      }
    } catch (error) {
      console.error('Error in fetchFAQs:', error)
      setFaqs(getMockFAQs())
    } finally {
      setLoading(false)
    }
  }

  const getMockFAQs = (): FAQItem[] => {
    return [
      {
        id: '1',
        question: 'Como criar minha primeira avaliação?',
        answer: 'Para criar uma avaliação, vá até o Editor, selecione questões do banco e configure o layout. Em seguida, gere o PDF para impressão.',
        category: 'Primeiros Passos'
      },
      {
        id: '2',
        question: 'Como importar questões de outros sistemas?',
        answer: 'Você pode importar questões através da funcionalidade de Import/Export no Banco de Questões, suportando formatos JSON e CSV.',
        category: 'Questões'
      },
      {
        id: '3',
        question: 'Qual a diferença entre os planos?',
        answer: 'O plano gratuito oferece 50 questões/mês. O Premium oferece questões ilimitadas, IA e templates premium. O Escolar inclui gestão para até 50 professores.',
        category: 'Planos'
      },
      {
        id: '4',
        question: 'Como personalizar o cabeçalho das avaliações?',
        answer: 'No Editor de Avaliações, clique em "Configurar" para acessar as opções de cabeçalho, onde você pode definir nome da escola, título da prova, instruções e outros detalhes.',
        category: 'Avaliações'
      },
      {
        id: '5',
        question: 'Como funciona a geração de questões por IA?',
        answer: 'A geração por IA está disponível para usuários Premium. Acesse o Banco de Questões, clique no botão "IA" e preencha os parâmetros desejados para gerar questões automaticamente.',
        category: 'Questões'
      },
      {
        id: '6',
        question: 'Posso compartilhar minhas avaliações com outros professores?',
        answer: 'Sim, usuários Premium podem compartilhar avaliações. Vá até "Minhas Avaliações", selecione a avaliação desejada e clique em "Compartilhar" para gerar um link ou convidar colegas diretamente.',
        category: 'Avaliações'
      },
      {
        id: '7',
        question: 'Como recuperar minha senha?',
        answer: 'Na tela de login, clique em "Esqueceu a senha?" e siga as instruções enviadas para seu email para criar uma nova senha.',
        category: 'Primeiros Passos'
      },
      {
        id: '8',
        question: 'O sistema funciona offline?',
        answer: 'Não, o Atividade Pronta requer conexão com a internet para funcionar corretamente. No entanto, PDFs gerados podem ser salvos localmente para uso offline.',
        category: 'Técnico'
      }
    ]
  }

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 overflow-y-auto dark:bg-gray-900">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-blue-900/30">
            <HelpCircle className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2 dark:text-white">
            Central de Ajuda
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Encontre respostas para suas dúvidas sobre o Atividade Pronta
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer dark:bg-gray-800 dark:border-gray-700">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 dark:bg-blue-900/30">
              <Book className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2 dark:text-white">
              Documentação
            </h3>
            <p className="text-sm text-gray-600 mb-3 dark:text-gray-400">
              Guias completos sobre todas as funcionalidades
            </p>
            <div className="flex items-center text-blue-600 text-sm dark:text-blue-400">
              <span>Acessar</span>
              <ExternalLink className="w-4 h-4 ml-1" />
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer dark:bg-gray-800 dark:border-gray-700">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 dark:bg-green-900/30">
              <Video className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2 dark:text-white">
              Tutoriais em Vídeo
            </h3>
            <p className="text-sm text-gray-600 mb-3 dark:text-gray-400">
              Aprenda através de vídeos passo a passo
            </p>
            <div className="flex items-center text-green-600 text-sm dark:text-green-400">
              <span>Assistir</span>
              <ExternalLink className="w-4 h-4 ml-1" />
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer dark:bg-gray-800 dark:border-gray-700">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 dark:bg-purple-900/30">
              <MessageCircle className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2 dark:text-white">
              Suporte Direto
            </h3>
            <p className="text-sm text-gray-600 mb-3 dark:text-gray-400">
              Fale conosco através do chat ou email
            </p>
            <div className="flex items-center text-purple-600 text-sm dark:text-purple-400">
              <span>Contatar</span>
              <ChevronRight className="w-4 h-4 ml-1" />
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 dark:bg-gray-800 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 dark:text-white">
            Perguntas Frequentes
          </h2>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Buscar nas perguntas frequentes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Todas as categorias' : category}
                </option>
              ))}
            </select>
          </div>

          {/* FAQ List */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-500 dark:text-gray-400">Carregando perguntas frequentes...</p>
              </div>
            ) : filteredFAQs.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  Nenhuma pergunta encontrada para sua busca.
                </p>
              </div>
            ) : (
              filteredFAQs.map((faq) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 rounded-lg dark:border-gray-700"
                >
                  <button
                    onClick={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
                    className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors dark:hover:bg-gray-700"
                  >
                    <div>
                      <h3 className="font-medium text-gray-900 mb-1 dark:text-white">
                        {faq.question}
                      </h3>
                      <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full dark:bg-blue-900/30 dark:text-blue-400">
                        {faq.category}
                      </span>
                    </div>
                    <ChevronRight 
                      className={`w-5 h-5 text-gray-400 transition-transform ${
                        expandedFAQ === faq.id ? 'rotate-90' : ''
                      } dark:text-gray-500`} 
                    />
                  </button>
                  
                  <AnimatePresence>
                    {expandedFAQ === faq.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="border-t border-gray-200 p-4 bg-gray-50 dark:border-gray-700 dark:bg-gray-700"
                      >
                        <p className="text-gray-700 dark:text-gray-300">{faq.answer}</p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            )}
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6 text-center dark:bg-blue-900/30 dark:border-blue-800">
          <h3 className="font-semibold text-gray-900 mb-2 dark:text-white">
            Não encontrou o que procurava?
          </h3>
          <p className="text-gray-600 mb-4 dark:text-gray-400">
            Nossa equipe de suporte está pronta para ajudar você.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
              Abrir Chat
            </button>
            <button className="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-2 rounded-lg transition-colors dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-400 dark:hover:text-white">
              Enviar Email
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HelpCenter