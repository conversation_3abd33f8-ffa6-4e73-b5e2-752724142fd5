import React, { useState } from 'react'
import { Upload, Download, FileText, AlertCircle, CheckCircle, X, Loader } from 'lucide-react'
import { motion } from 'framer-motion'
import { useQuestions } from '../../hooks/useQuestions'
import toast from 'react-hot-toast'

interface ImportResult {
  success: number
  errors: string[]
  total: number
}

interface ImportExportProps {
  onClose?: () => void
}

const ImportExport: React.FC<ImportExportProps> = ({ onClose }) => {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const { questions, createQuestion } = useQuestions()

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImporting(true)
    setImportResult(null)

    try {
      const text = await file.text()
      
      if (file.name.endsWith('.json')) {
        await handleJSONImport(text)
      } else if (file.name.endsWith('.csv')) {
        await handleCSVImport(text)
      } else {
        throw new Error('Formato de arquivo não suportado')
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Erro ao importar arquivo')
    } finally {
      setImporting(false)
    }
  }

  const handleJSONImport = async (jsonText: string) => {
    try {
      const data = JSON.parse(jsonText)
      const questionsToImport = Array.isArray(data) ? data : [data]
      
      let success = 0
      const errors: string[] = []
      
      for (const question of questionsToImport) {
        try {
          if (!question.disciplina || !question.serie || !question.enunciado || !question.resposta_correta || !question.explicacao) {
            errors.push(`Questão inválida: campos obrigatórios faltando para ${question.enunciado?.substring(0, 30)}...`)
            continue
          }
          
          // Call createQuestion mutation
          await createQuestion({
            disciplina: question.disciplina,
            serie: question.serie,
            topico: question.topico,
            subtopico: question.subtopico,
            dificuldade: question.dificuldade,
            tipo: question.tipo,
            competencia_bncc: question.competencia_bncc,
            enunciado: question.enunciado,
            alternativas: question.alternativas,
            resposta_correta: question.resposta_correta,
            explicacao: question.explicacao,
            tags: question.tags || [],
            is_public: question.is_public ?? true,
            is_verified: question.is_verified ?? false,
            uso_count: question.uso_count ?? 0,
            rating: question.rating ?? 0,
            rating_count: question.rating_count ?? 0,
            metadata: question.metadata ?? {}
          })
          success++
        } catch (error: any) {
          errors.push(`Erro ao importar questão "${question.enunciado?.substring(0, 30)}...": ${error.message}`)
        }
      }
      
      setImportResult({
        success,
        errors,
        total: questionsToImport.length
      })
      
      if (success > 0) {
        toast.success(`${success} questões importadas com sucesso!`)
      }
    } catch (error: any) {
      throw new Error(`Arquivo JSON inválido: ${error.message}`)
    }
  }

  const handleCSVImport = async (csvText: string) => {
    try {
      const lines = csvText.split('\n').filter(line => line.trim() !== '')
      if (lines.length < 2) throw new Error('Arquivo CSV vazio ou inválido.')

      const headers = lines[0].split(',').map(h => h.trim())
      const requiredHeaders = ['disciplina', 'serie', 'topico', 'dificuldade', 'tipo', 'enunciado', 'resposta_correta', 'explicacao']
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))
      if (missingHeaders.length > 0) {
        throw new Error(`Cabeçalhos obrigatórios faltando: ${missingHeaders.join(', ')}`)
      }

      let success = 0
      const errors: string[] = []

      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim())
          const question: any = {}

          headers.forEach((header, index) => {
            question[header] = values[index]
          })

          // Basic validation and type conversion
          if (!question.disciplina || !question.serie || !question.enunciado || !question.resposta_correta || !question.explicacao) {
            errors.push(`Linha ${i + 1}: campos obrigatórios faltando.`)
            continue
          }

          const parsedQuestion: any = {
            disciplina: question.disciplina,
            serie: question.serie,
            topico: question.topico || 'Geral',
            subtopico: question.subtopico || null,
            dificuldade: question.dificuldade as any, // 'Fácil' | 'Médio' | 'Difícil'
            tipo: question.tipo as any, // 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
            competencia_bncc: question.competencia_bncc || null,
            enunciado: question.enunciado,
            alternativas: question.alternativas ? JSON.parse(question.alternativas) : null,
            resposta_correta: question.resposta_correta,
            explicacao: question.explicacao,
            tags: question.tags ? question.tags.split(';').map((tag: string) => tag.trim()) : [],
            is_public: question.is_public === 'true',
            is_verified: question.is_verified === 'true',
            uso_count: parseInt(question.uso_count || '0'),
            rating: parseFloat(question.rating || '0'),
            rating_count: parseInt(question.rating_count || '0'),
            metadata: question.metadata ? JSON.parse(question.metadata) : {}
          }

          await createQuestion(parsedQuestion)
          success++
        } catch (error: any) {
          errors.push(`Linha ${i + 1}: ${error.message}`)
        }
      }

      setImportResult({
        success,
        errors,
        total: lines.length - 1
      })

      if (success > 0) {
        toast.success(`${success} questões importadas com sucesso!`)
      }
    } catch (error: any) {
      throw new Error(`Arquivo CSV inválido: ${error.message}`)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (!file) return;
    setImporting(true);
    setImportResult(null);
    try {
      const text = await file.text();
      if (file.name.endsWith('.json')) {
        await handleJSONImport(text);
      } else if (file.name.endsWith('.csv')) {
        await handleCSVImport(text);
      } else {
        throw new Error('Formato de arquivo não suportado');
      }
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Erro ao importar arquivo');
    } finally {
      setImporting(false);
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={onClose || (() => {})}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Importar Questões</h2>
            <button
              onClick={onClose || (() => {})}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Import Questions Section */}
          <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-sm mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Importar Questões</h2>
            <div className="mb-4 text-right">
              <a
                href="/exemplo-questoes.csv"
                download
                className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                target="_blank"
                rel="noopener noreferrer"
              >
                Baixar arquivo de exemplo (.csv)
              </a>
            </div>
            <div
              className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              <Upload className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
              <p className="text-gray-600 dark:text-gray-300 mb-2">Arraste arquivos aqui ou clique para selecionar</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">Suporte para arquivos JSON e CSV</p>
              <input
                id="file-upload"
                type="file"
                accept=".json,.csv"
                className="hidden"
                onChange={handleFileUpload}
              />
              <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-900">
                Selecionar Arquivo
              </button>
            </div>
            {importing && (
              <div className="mt-4 flex items-center justify-center text-blue-600 dark:text-blue-400">
                <Loader className="w-5 h-5 animate-spin mr-2" />
                <span>Importando...</span>
              </div>
            )}
            {importResult && (
              <div className="mt-4 p-3 rounded-md bg-gray-50 dark:bg-gray-900/50">
                <p className="font-semibold text-gray-800 dark:text-white mb-2">Resultado da Importação:</p>
                <p className="text-sm text-green-600 dark:text-green-400 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" /> Sucesso: {importResult.success} questões
                </p>
                {importResult.errors.length > 0 && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                    <p className="flex items-center mb-1"><AlertCircle className="w-4 h-4 mr-1" /> Erros: {importResult.errors.length}</p>
                    <ul className="list-disc list-inside">
                      {importResult.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* File Format Information */}
          <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Formato dos Arquivos</h2>
            <p className="text-gray-700 dark:text-gray-300 mb-2">
              <strong className="text-blue-600 dark:text-blue-400">JSON:</strong> Formato completo com todos os campos da questão
            </p>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              <strong className="text-green-600 dark:text-green-400">CSV:</strong> Formato tabular com campos principais (disciplina, série, tópico, etc.)
            </p>
            <p className="text-gray-800 dark:text-gray-200 text-sm">
              <strong className="text-red-600 dark:text-red-400">Campos obrigatórios:</strong> disciplina, serie, enunciado, resposta_correta, explicacao
            </p>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default ImportExport