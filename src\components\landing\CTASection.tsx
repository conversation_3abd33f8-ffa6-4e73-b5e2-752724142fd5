import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'

interface CTASectionProps {
  title?: string
  description?: string
  buttonText?: string
  buttonLink?: string
}

const CTASection: React.FC<CTASectionProps> = ({
  title = "Pronto para transformar suas avaliações?",
  description = "Comece agora e crie sua primeira avaliação em menos de 5 minutos",
  buttonText = "Começar Gratuitamente",
  buttonLink = "/register"
}) => {
  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
      <div className="container mx-auto px-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {title}
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            {description}
          </p>
          <Link 
            to={buttonLink} 
            className="px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 inline-flex items-center"
          >
            {buttonText}
            <ArrowRight className="ml-2 w-5 h-5" />
          </Link>
          <p className="mt-6 text-blue-100 text-sm">
            Não é necessário cartão de crédito • Cancele quando quiser
          </p>
        </motion.div>
      </div>
    </section>
  )
}

export default CTASection