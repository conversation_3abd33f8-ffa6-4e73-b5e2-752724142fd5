import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ChevronDown, ChevronUp } from 'lucide-react'

interface FAQ {
  question: string
  answer: string
}

const faqs: FAQ[] = [
  {
    question: "Como funciona o plano gratuito?",
    answer: "O plano gratuito permite criar até 50 questões e 5 avaliações por mês. Você tem acesso aos templates básicos e pode gerar PDFs otimizados para impressão. É perfeito para começar a usar a plataforma."
  },
  {
    question: "Posso cancelar minha assinatura a qualquer momento?",
    answer: "Sim, você pode cancelar sua assinatura a qualquer momento. Não há contratos de longo prazo ou taxas de cancelamento. Sua assinatura permanecerá ativa até o final do período pago."
  },
  {
    question: "Como funciona a geração de questões por IA?",
    answer: "Nossa tecnologia de IA permite criar questões automaticamente com base em parâmetros como disciplina, série, tópico e dificuldade. Você pode editar as questões geradas antes de salvá-las no seu banco."
  },
  {
    question: "Posso importar questões de outros sistemas?",
    answer: "Sim, o Atividade Pronta permite importar questões em formatos CSV e JSON. Também oferecemos assistência para migração em massa para escolas e instituições."
  },
  {
    question: "As avaliações geradas são compatíveis com todos os impressoras?",
    answer: "Sim, nossos PDFs são otimizados para qualquer impressora. Oferecemos opções de tamanho de papel (A4, Carta), orientação (retrato, paisagem) e ajustes de margem para garantir compatibilidade."
  },
  {
    question: "O que acontece com minhas questões se eu cancelar a assinatura?",
    answer: "Se você cancelar a assinatura Premium e voltar ao plano gratuito, suas questões permanecem salvas, mas você ficará limitado a criar 50 novas questões por mês. Todas as avaliações já criadas continuarão acessíveis."
  }
]

const FAQSection: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  
  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }
  
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Perguntas Frequentes
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Tudo o que você precisa saber sobre o Atividade Pronta
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="border border-gray-200 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full flex items-center justify-between p-6 text-left bg-white hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-lg font-semibold text-gray-900">{faq.question}</h3>
                {openIndex === index ? (
                  <ChevronUp className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                )}
              </button>
              
              {openIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="px-6 pb-6"
                >
                  <p className="text-gray-600">{faq.answer}</p>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQSection