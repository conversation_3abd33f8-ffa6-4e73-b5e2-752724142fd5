import React from 'react'
import { motion } from 'framer-motion'
import { Database, Layout, FileText, Clock } from 'lucide-react'

interface Feature {
  icon: React.ComponentType<any>
  title: string
  description: string
}

const features: Feature[] = [
  {
    icon: Clock,
    title: "Economize até 70% de tempo",
    description: "Crie avaliações completas em minutos, não em horas, liberando tempo para o que realmente importa: ensinar."
  },
  {
    icon: Database,
    title: "Banco com milhares de questões",
    description: "Acesse um banco inteligente com questões organizadas por disciplina, série, tópico e dificuldade."
  },
  {
    icon: Layout,
    title: "Templates profissionais",
    description: "Escolha entre diversos modelos prontos ou personalize o seu próprio para avaliações com aparência profissional."
  },
  {
    icon: FileText,
    title: "PDFs otimizados para impressão",
    description: "Gere PDFs perfeitos para impressão, com opções de cabeçalho, rodap<PERSON>, marca d'água e gabarito."
  }
]

const FeatureSection: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Por que professores escolhem o Atividade Pronta?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Nossa plataforma foi desenvolvida por educadores para educadores, focando nos problemas reais da sala de aula
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300"
              >
                <div className="w-14 h-14 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                  <Icon className="w-7 h-7 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default FeatureSection