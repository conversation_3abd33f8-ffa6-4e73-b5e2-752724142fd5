import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin, 
  Mail, 
  Phone, 
  MapPin,
  FileText,
  Shield,
  HelpCircle
} from 'lucide-react'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Coluna 1 - Sobre */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-10 h-10 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AP</span>
              </div>
              <span className="text-xl font-bold text-white">Atividade Pronta</span>
            </div>
            <p className="text-gray-400 dark:text-gray-300 mb-6">
              Plataforma completa para professores criarem e gerenciarem avaliações educacionais com banco de questões inteligente e geração de PDFs otimizados.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>
          
          {/* Coluna 2 - Links Rápidos */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Links Rápidos</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Início
                </Link>
              </li>
              <li>
                <Link to="/app/questions" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Banco de Questões
                </Link>
              </li>
              <li>
                <Link to="/app/templates" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Templates
                </Link>
              </li>
              <li>
                <Link to="/app/editor" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Editor de Avaliações
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Planos e Preços
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors">
                  Blog
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Coluna 3 - Legal */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/terms" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center">
                  <Shield className="w-4 h-4 mr-2" />
                  Política de Privacidade
                </Link>
              </li>
              <li>
                <Link to="/cookies" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Política de Cookies
                </Link>
              </li>
              <li>
                <Link to="/help" className="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center">
                  <HelpCircle className="w-4 h-4 mr-2" />
                  Central de Ajuda
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Coluna 4 - Contato */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Contato</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Mail className="w-5 h-5 text-gray-400 dark:text-gray-300 mr-3 mt-0.5" />
                <a href="mailto:<EMAIL>" className="text-gray-400 dark:text-gray-300 hover:text-gray-300 dark:hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-start">
                <Mail className="w-5 h-5 text-gray-400 dark:text-gray-300 mr-3 mt-0.5" />
                <a href="mailto:<EMAIL>" className="text-gray-400 dark:text-gray-300 hover:text-gray-300 dark:hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-start">
                <MapPin className="w-5 h-5 text-gray-400 dark:text-gray-300 mr-3 mt-0.5" />
                <span className="text-gray-400 dark:text-gray-300">
                  Brasil<br />
                  Atendimento online
                </span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 dark:border-gray-700 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 dark:text-gray-400 text-sm mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} Atividade Pronta. Todos os direitos reservados.
            </p>
            <div className="flex space-x-6">
              <Link to="/terms" className="text-gray-500 dark:text-gray-400 hover:text-gray-400 dark:hover:text-gray-300 text-sm">
                Termos
              </Link>
              <Link to="/privacy" className="text-gray-500 dark:text-gray-400 hover:text-gray-400 dark:hover:text-gray-300 text-sm">
                Privacidade
              </Link>
              <Link to="/cookies" className="text-gray-500 dark:text-gray-400 hover:text-gray-400 dark:hover:text-gray-300 text-sm">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer