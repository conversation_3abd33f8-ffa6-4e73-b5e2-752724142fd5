import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Sun, Moon, Monitor } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useTheme } from '../../contexts/ThemeContext'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { user } = useAuth()
  const { theme, setTheme } = useTheme()
  const location = useLocation()
  
  // Detectar scroll para mudar o estilo do header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }
    
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])
  
  // Fechar menu ao mudar de rota
  useEffect(() => {
    setIsMenuOpen(false)
  }, [location])

  // Toggle theme function
  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('light')
    } else if (theme === 'system') {
      setTheme('light')
    }
  }

  // Get theme icon
  const getThemeIcon = () => {
    if (theme === 'light') return <Sun className="w-5 h-5" />
    if (theme === 'dark') return <Moon className="w-5 h-5" />
    return <Monitor className="w-5 h-5" />
  }
  
  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white dark:bg-gray-800 shadow-md py-3'
        : 'bg-transparent py-5'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">AP</span>
            </div>
            <span className={`text-xl font-bold ${
              isScrolled
                ? 'text-gray-900 dark:text-white'
                : 'text-gray-900 dark:text-white'
            }`}>
              Atividade Pronta
            </span>
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Início
            </a>
            <Link to="/avaliacoes" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Avaliações
            </Link>
            <a href="#features" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Recursos
            </a>
            <a href="#pricing" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Preços
            </a>
            <a href="#testimonials" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Depoimentos
            </a>
            <a href="#contact" className={`${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300'
                : 'text-gray-800 dark:text-gray-200'
            } hover:text-blue-600 transition-colors`}>
              Contato
            </a>
          </nav>
          
          {/* Theme Toggle and Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-lg transition-colors ${
                isScrolled
                  ? 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-white/10'
              }`}
              title={`Alternar para tema ${theme === 'light' ? 'escuro' : theme === 'dark' ? 'claro' : 'claro'}`}
            >
              {getThemeIcon()}
            </button>

            {user ? (
              <Link
                to="/app"
                className="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Meu Painel
              </Link>
            ) : (
              <>
                <Link
                  to="/login"
                  className={`px-5 py-2 transition-colors ${
                    isScrolled
                      ? 'text-gray-700 dark:text-gray-300 hover:text-blue-600'
                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600'
                  }`}
                >
                  Entrar
                </Link>
                <Link
                  to="/register"
                  className="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Criar Conta
                </Link>
              </>
            )}
          </div>
          
          {/* Mobile Menu Button */}
          <button
            className={`md:hidden p-2 rounded-lg transition-colors ${
              isScrolled
                ? 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                : 'text-gray-700 dark:text-gray-300 hover:bg-white/10'
            }`}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>
      
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-2">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4">
              <a href="#" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Início
              </a>
              <Link to="/avaliacoes" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Avaliações
              </Link>
              <a href="#features" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Recursos
              </a>
              <a href="#pricing" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Preços
              </a>
              <a href="#testimonials" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Depoimentos
              </a>
              <a href="#contact" className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                Contato
              </a>

              {/* Theme Toggle Mobile */}
              <button
                onClick={toggleTheme}
                className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                {getThemeIcon()}
                <span>Alternar tema</span>
              </button>
              
              <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                {user ? (
                  <Link
                    to="/app"
                    className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors block text-center"
                  >
                    Meu Painel
                  </Link>
                ) : (
                  <div className="flex flex-col space-y-2">
                    <Link
                      to="/login"
                      className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors block text-center"
                    >
                      Entrar
                    </Link>
                    <Link
                      to="/register"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors block text-center"
                    >
                      Criar Conta
                    </Link>
                  </div>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}

export default Header