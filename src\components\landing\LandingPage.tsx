import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Link } from 'react-router-dom'
import {
  FileText,
  Clock,
  Database,
  Layout,
  Star,
  CheckCircle,
  ArrowRight,
  Play,
  Shield,
  Users,
  BookOpen,
  Crown,
  Zap,
  BarChart3,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { motion } from 'framer-motion'
import Header from './Header'
import * as ReactHelmetAsync from 'react-helmet-async'
const { Helmet } = ReactHelmetAsync
import { usePlans } from '../../hooks/usePlans'
import { useTheme } from '../../contexts/ThemeContext'

const scrollToHashSection = () => {
  if (window.location.hash) {
    const id = window.location.hash.replace('#', '')
    const el = document.getElementById(id)
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }
}

const LandingPage: React.FC = () => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null)
  const { theme } = useTheme()

  // Buscar planos dinâmicos do banco de dados
  const { plans, isLoading: plansLoading } = usePlans({ isActive: true })

  // Apply theme to document element
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else if (theme === 'light') {
      document.documentElement.classList.remove('dark')
    } else if (theme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }
  }, [theme])

  // Implementação de contador de usuários com incremento gradual
  const [userCount, setUserCount] = useState(0)
  const [assessmentCount, setAssessmentCount] = useState(0)

  useEffect(() => {
    let userInterval: NodeJS.Timeout | null = null
    let assessmentInterval: NodeJS.Timeout | null = null

    // Contador de usuários com cleanup adequado
    userInterval = setInterval(() => {
      setUserCount(prev => {
        if (prev >= 5327) {
          if (userInterval) clearInterval(userInterval)
          return 5327
        }
        return prev + 17
      })
    }, 100)

    // Contador de avaliações com cleanup adequado
    assessmentInterval = setInterval(() => {
      setAssessmentCount(prev => {
        if (prev >= 28945) {
          if (assessmentInterval) clearInterval(assessmentInterval)
          return 28945
        }
        return prev + 89
      })
    }, 50)

    // Cleanup function para evitar memory leaks
    return () => {
      if (userInterval) clearInterval(userInterval)
      if (assessmentInterval) clearInterval(assessmentInterval)
    }
  }, [])

  useEffect(() => {
    scrollToHashSection()
    const onHashChange = () => scrollToHashSection()
    window.addEventListener('hashchange', onHashChange)
    document.title = 'Atividade Pronta'
    return () => window.removeEventListener('hashchange', onHashChange)
  }, [])

  // Fechar modal com ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVideoModalOpen) {
        setIsVideoModalOpen(false)
      }
    }

    if (isVideoModalOpen) {
      document.addEventListener('keydown', handleKeyDown)
      // Prevenir scroll do body quando modal está aberto
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [isVideoModalOpen])

  // Função memoizada para mapear planos do banco para formato da landing page
  const mapPlansForLanding = useCallback(() => {
    if (!plans || plans.length === 0) {
      // Fallback para planos estáticos se não conseguir carregar do banco
      return [
        {
          name: "Gratuito",
          price: "R$ 0",
          period: "/mês",
          description: "Perfeito para começar",
          features: [
            "5 avaliações por mês",
            "10 downloads PDF por mês",
            "Templates básicos",
            "Suporte por email"
          ],
          cta: "Começar Agora",
          popular: false,
          color: "gray"
        },
        {
          name: "Premium",
          price: "R$ 29,90",
          period: "/mês",
          description: "Para professores exigentes",
          features: [
            "Questões ilimitadas",
            "Avaliações ilimitadas",
            "Templates premium",
            "Geração por IA",
            "Colaboração",
            "Analytics avançados",
            "Suporte prioritário"
          ],
          cta: "Assinar Premium",
          popular: true,
          color: "blue"
        },
        {
          name: "Escolar",
          price: "R$ 199,90",
          period: "/mês",
          description: "Para instituições de ensino",
          features: [
            "Tudo do Premium",
            "Até 50 professores",
            "Gestão centralizada",
            "Relatórios institucionais",
            "Treinamento incluído",
            "Suporte dedicado"
          ],
          cta: "Falar com Vendas",
          popular: false,
          color: "purple"
        }
      ]
    }

    // Mapear planos do banco para formato da landing page
    return plans
      .filter(plan => plan.is_active)
      .sort((a, b) => a.price - b.price) // Ordenar por preço
      .map(plan => ({
        name: plan.name,
        price: plan.price === 0 ? "R$ 0" : `R$ ${plan.price.toFixed(2).replace('.', ',')}`,
        period: plan.duration_months === 1 ? "/mês" : plan.duration_months === 12 ? "/ano" : `/${plan.duration_months} meses`,
        description: plan.description || "",
        features: plan.features || [],
        cta: plan.name === "Gratuito" ? "Começar Agora" :
             plan.name === "Escolar" ? "Falar com Vendas" :
             `Assinar ${plan.name}`,
        popular: plan.name === "Premium",
        color: plan.name === "Premium" ? "blue" :
               plan.name === "Escolar" ? "purple" : "gray"
      }))
  }, [plans])

  const dynamicPlans = useMemo(() => mapPlansForLanding(), [mapPlansForLanding])

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <Helmet>
        <title>Atividade Pronta | Crie avaliações em minutos</title>
        <meta name="description" content="A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados." />
        <meta property="og:title" content="Atividade Pronta | Crie avaliações em minutos" />
        <meta property="og:description" content="A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://atvpronta.com.br/" />
        <meta property="og:image" content="https://atvpronta.com.br/og-image.png" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Atividade Pronta | Crie avaliações em minutos" />
        <meta name="twitter:description" content="A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados." />
        <meta name="twitter:image" content="https://atvpronta.com.br/og-image.png" />
        <link rel="canonical" href="https://atvpronta.com.br/" />

        {/* Schema Markup JSON-LD */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "Atividade Pronta",
            "description": "Plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados.",
            "url": "https://atvpronta.com.br",
            "applicationCategory": "EducationalApplication",
            "operatingSystem": "Web",
            "offers": [
              {
                "@type": "Offer",
                "name": "Plano Gratuito",
                "price": "0",
                "priceCurrency": "BRL",
                "description": "5 avaliações por mês, 10 downloads PDF por mês"
              },
              {
                "@type": "Offer",
                "name": "Plano Premium",
                "price": "29.90",
                "priceCurrency": "BRL",
                "description": "Questões ilimitadas, avaliações ilimitadas, templates premium"
              }
            ],
            "publisher": {
              "@type": "Organization",
              "name": "Atividade Pronta",
              "url": "https://atvpronta.com.br",
              "contactPoint": {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer service"
              }
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "reviewCount": "127"
            }
          })}
        </script>
      </Helmet>
      <Header />
      {/* Hero Section */}
      <header className="relative overflow-hidden bg-gray-50 dark:bg-gray-900">
        <div className="absolute inset-0 bg-blue-600 opacity-5 dark:opacity-10"></div>
        <div className="container mx-auto px-4 py-20 lg:py-32">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Transforme a Criação de Avaliações em <span className="text-blue-600 dark:text-blue-400">Minutos</span>, Não em Horas
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              A plataforma completa que ajuda professores a criar avaliações profissionais com banco de questões inteligente e geração de PDFs otimizados
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
              <Link
                to="/register"
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-500 text-white rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 focus:outline-none"
                aria-label="Criar conta gratuita no Atividade Pronta"
              >
                Começar Gratuitamente
              </Link>
              <button
                onClick={() => setIsVideoModalOpen(true)}
                className="px-8 py-4 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-500 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-gray-600 rounded-lg text-lg font-semibold shadow-md hover:shadow-lg transition-all flex items-center focus:outline-none"
                aria-label="Abrir vídeo demonstrativo do Atividade Pronta"
              >
                <Play className="w-5 h-5 mr-2" aria-hidden="true" />
                Ver Demonstração
              </button>
            </div>
            
            <div className="flex flex-wrap items-center justify-center gap-4 md:gap-8 text-gray-500 dark:text-gray-400 text-sm">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mr-2" />
                <span>Sem cartão de crédito</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mr-2" />
                <span>Cancele quando quiser</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mr-2" />
                <span>Suporte gratuito</span>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-gray-50 dark:from-gray-900 to-transparent"></div>
      </header>
      
      {/* Estatísticas */}
      <section className="py-10 bg-white">
        <div className="container mx-auto px-4">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-xl p-8 text-white">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">{userCount.toLocaleString()}</div>
                <div className="text-blue-100">Professores Ativos</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">{assessmentCount.toLocaleString()}</div>
                <div className="text-blue-100">Avaliações Geradas</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">12</div>
                <div className="text-blue-100">Disciplinas Cobertas</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefícios Principais */}
      <section id="features" className="py-20 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Por que professores escolhem o Atividade Pronta?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Nossa plataforma foi desenvolvida por educadores para educadores, focando nos problemas reais da sala de aula
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Clock,
                title: "Economize até 70% de tempo",
                description: "Crie avaliações completas em minutos, não em horas, liberando tempo para o que realmente importa: ensinar."
              },
              {
                icon: Database,
                title: "Banco com milhares de questões",
                description: "Acesse um banco inteligente com questões organizadas por disciplina, série, tópico e dificuldade."
              },
              {
                icon: Layout,
                title: "Templates profissionais",
                description: "Escolha entre diversos modelos prontos ou personalize o seu próprio para avaliações com aparência profissional."
              },
              {
                icon: FileText,
                title: "PDFs otimizados para impressão",
                description: "Gere PDFs perfeitos para impressão, com opções de cabeçalho, rodapé, marca d'água e gabarito."
              }
            ].map((benefit, index) => {
              const Icon = benefit.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 p-6 hover:shadow-lg dark:hover:shadow-xl transition-all duration-300"
                >
                  <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-6">
                    <Icon className="w-7 h-7 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{benefit.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{benefit.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
      
      {/* Como Funciona */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Como o Atividade Pronta funciona
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Um processo simples e intuitivo para criar avaliações perfeitas em poucos cliques
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
            {/* Linha conectora (visível apenas em desktop) */}
            <div className="hidden lg:block absolute top-1/2 left-0 w-full h-0.5 bg-blue-200 dark:bg-blue-700 -z-10"></div>
            
            {[
              {
                step: 1,
                title: "Selecione questões",
                description: "Escolha do nosso banco ou crie suas próprias questões personalizadas.",
                icon: Database
              },
              {
                step: 2,
                title: "Organize sua avaliação",
                description: "Arraste e solte para organizar a ordem das questões facilmente.",
                icon: Layout
              },
              {
                step: 3,
                title: "Personalize o template",
                description: "Ajuste cabeçalho, rodapé, espaçamento e outras configurações.",
                icon: FileText
              },
              {
                step: 4,
                title: "Gere e imprima",
                description: "Obtenha um PDF perfeito para impressão com apenas um clique.",
                icon: ArrowRight
              }
            ].map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 p-6 shadow-md dark:shadow-lg relative z-10"
                >
                  <div className="w-12 h-12 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl mb-6 mx-auto">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 text-center">{step.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-center">{step.description}</p>
                  <div className="mt-6 flex justify-center">
                    <Icon className="w-10 h-10 text-blue-500 dark:text-blue-400" />
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
      
      {/* Demonstração do Produto */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Uma plataforma completa para professores exigentes
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                O Atividade Pronta reúne todas as ferramentas que você precisa para criar, gerenciar e compartilhar avaliações educacionais de alta qualidade.
              </p>
              
              <div className="space-y-6">
                {[
                  {
                    icon: Database,
                    title: "Banco de Questões Inteligente",
                    description: "Filtros avançados, busca por tópicos e favoritos para encontrar exatamente o que precisa."
                  },
                  {
                    icon: Zap,
                    title: "Geração por IA",
                    description: "Crie novas questões automaticamente com nossa tecnologia de inteligência artificial."
                  },
                  {
                    icon: Layout,
                    title: "Editor Drag-and-Drop",
                    description: "Interface intuitiva para montar avaliações com facilidade e visualização em tempo real."
                  },
                  {
                    icon: FileText,
                    title: "Geração de PDF Profissional",
                    description: "PDFs otimizados para impressão com múltiplas opções de personalização."
                  }
                ].map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <Icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                        <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                      </div>
                    </div>
                  )
                })}
              </div>
              
              <div className="mt-10">
                <Link 
                  to="/register" 
                  className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all inline-flex items-center"
                >
                  Experimentar Gratuitamente
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </div>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <div className="bg-gray-200 dark:bg-gray-700 rounded-xl overflow-hidden shadow-2xl">
                {/* Aqui seria uma imagem ou vídeo demonstrativo do produto */}
                <div className="aspect-w-16 aspect-h-9 bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                  <Play className="w-16 h-16 text-white opacity-80" />
                </div>
                <div className="p-4 bg-white dark:bg-gray-800">
                  <div className="h-8 bg-gray-100 dark:bg-gray-700 rounded-lg w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-100 dark:bg-gray-700 rounded-lg w-1/2"></div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
      
      {/* Depoimentos */}
      <section id="testimonials" className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              O que dizem nossos usuários
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Professores de todo o Brasil já transformaram sua forma de criar avaliações
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Profa. Maria Silva",
                role: "Professora de Matemática",
                school: "E.E. Dom Pedro II",
                image: "https://images.pexels.com/photos/5212317/pexels-photo-5212317.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
                quote: "Reduzi meu tempo de preparação de provas em 65%. Agora consigo focar mais no planejamento das aulas e no atendimento aos alunos."
              },
              {
                name: "Prof. Carlos Oliveira",
                role: "Professor de História",
                school: "Colégio Estadual Anísio Teixeira",
                image: "https://images.pexels.com/photos/8617942/pexels-photo-8617942.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
                quote: "A qualidade das minhas avaliações melhorou significativamente. Os alunos notaram a diferença e até comentaram sobre a organização e clareza."
              },
              {
                name: "Profa. Ana Beatriz",
                role: "Coordenadora Pedagógica",
                school: "Escola Municipal Paulo Freire",
                image: "https://images.pexels.com/photos/5212339/pexels-photo-5212339.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
                quote: "Implementamos o Atividade Pronta em toda a escola e os resultados foram impressionantes. Nossos professores economizam coletivamente mais de 30 horas por semana."
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 p-6 shadow-md dark:shadow-lg"
              >
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.image}
                    alt={`Foto de ${testimonial.name}, ${testimonial.role}`}
                    loading="lazy"
                    decoding="async"
                    className="w-16 h-16 rounded-full object-cover mr-4"
                    onError={(e) => {
                      // Fallback para imagem padrão se não carregar
                      const target = e.target as HTMLImageElement
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.name)}&background=3b82f6&color=fff&size=64`
                    }}
                  />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{testimonial.name}</h3>
                    <p className="text-gray-600 dark:text-gray-300">{testimonial.role}</p>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">{testimonial.school}</p>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex text-yellow-400 dark:text-yellow-300">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 italic">"{testimonial.quote}"</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Planos e Preços */}
      <section id="pricing" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Planos para cada necessidade
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comece gratuitamente e faça upgrade quando precisar de mais recursos
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {plansLoading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-xl border-2 border-gray-200 dark:border-gray-700 p-8 animate-pulse">
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-2xl mx-auto mb-4"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                  <div className="space-y-4 mb-8">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    ))}
                  </div>
                  <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              ))
            ) : (
              dynamicPlans.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative bg-white dark:bg-gray-800 rounded-xl border-2 transition-all duration-300 hover:shadow-xl dark:hover:shadow-2xl ${
                  plan.popular
                    ? 'border-blue-500 dark:border-blue-400 scale-105 shadow-lg dark:shadow-xl'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                      <Crown className="w-4 h-4" />
                      <span>Mais Popular</span>
                    </div>
                  </div>
                )}

                <div className="p-8">
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 ${
                      plan.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900' :
                      plan.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900' :
                      'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      {plan.color === 'blue' && <Crown className="w-8 h-8 text-blue-600 dark:text-blue-400" />}
                      {plan.color === 'purple' && <Users className="w-8 h-8 text-purple-600 dark:text-purple-400" />}
                      {plan.color === 'gray' && <Zap className="w-8 h-8 text-gray-600 dark:text-gray-400" />}
                    </div>

                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>

                    <div className="mb-4">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        {plan.price}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">{plan.period}</span>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300">{plan.description}</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    to={plan.name === "Escolar" ? "/contact" : "/register"}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                      plan.popular
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : plan.color === 'purple'
                          ? 'bg-purple-600 hover:bg-purple-700 text-white'
                          : 'bg-gray-900 hover:bg-gray-800 text-white'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              </motion.div>
            ))
            )}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              Todas as assinaturas incluem 7 dias de teste gratuito
            </p>
            <p className="text-sm text-gray-500">
              Cancele a qualquer momento. Sem taxas de cancelamento.
            </p>
          </div>
        </div>
      </section>
      
      {/* Recursos Avançados */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Recursos avançados para professores exigentes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Vá além das avaliações básicas com nossas ferramentas premium
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: "Geração por IA",
                description: "Crie questões automaticamente com nossa tecnologia de inteligência artificial. Especifique disciplina, série, tópico e dificuldade."
              },
              {
                icon: Users,
                title: "Colaboração",
                description: "Compartilhe questões e avaliações com colegas, trabalhe em equipe e aproveite o conhecimento coletivo."
              },
              {
                icon: BarChart3,
                title: "Analytics",
                description: "Acompanhe o desempenho das suas avaliações, identifique padrões e melhore continuamente."
              },
              {
                icon: Layout,
                title: "Templates Premium",
                description: "Acesse modelos exclusivos com designs profissionais para suas avaliações se destacarem."
              },
              {
                icon: BookOpen,
                title: "Banco Ilimitado",
                description: "Crie e armazene quantas questões quiser, organizadas por disciplina, série e tópico."
              },
              {
                icon: Shield,
                title: "Segurança Avançada",
                description: "Proteja suas avaliações com recursos de segurança como marca d'água e versões diferentes."
              }
            ].map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300"
                >
                  <div className="w-14 h-14 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <Icon className="w-7 h-7 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
      
      {/* FAQ */}
      <section id="faq" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Perguntas Frequentes
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Tudo o que você precisa saber sobre o Atividade Pronta
            </p>
          </div>
          
          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: "Como funciona o plano gratuito?",
                answer: "O plano gratuito permite criar até 50 questões e 5 avaliações por mês. Você tem acesso aos templates básicos e pode gerar PDFs otimizados para impressão. É perfeito para começar a usar a plataforma."
              },
              {
                question: "Posso cancelar minha assinatura a qualquer momento?",
                answer: "Sim, você pode cancelar sua assinatura a qualquer momento. Não há contratos de longo prazo ou taxas de cancelamento. Sua assinatura permanecerá ativa até o final do período pago."
              },
              {
                question: "Como funciona a geração de questões por IA?",
                answer: "Nossa tecnologia de IA permite criar questões automaticamente com base em parâmetros como disciplina, série, tópico e dificuldade. Você pode editar as questões geradas antes de salvá-las no seu banco."
              },
              {
                question: "Posso importar questões de outros sistemas?",
                answer: "Sim, o Atividade Pronta permite importar questões em formatos CSV e JSON. Também oferecemos assistência para migração em massa para escolas e instituições."
              },
              {
                question: "As avaliações geradas são compatíveis com todos os impressoras?",
                answer: "Sim, nossos PDFs são otimizados para qualquer impressora. Oferecemos opções de tamanho de papel (A4, Carta), orientação (retrato, paisagem) e ajustes de margem para garantir compatibilidade."
              },
              {
                question: "O que acontece com minhas questões se eu cancelar a assinatura?",
                answer: "Se você cancelar a assinatura Premium e voltar ao plano gratuito, suas questões permanecem salvas, mas você ficará limitado a criar 50 novas questões por mês. Todas as avaliações já criadas continuarão acessíveis."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 rounded-xl overflow-hidden"
              >
                <button
                  onClick={() => setOpenFaqIndex(openFaqIndex === index ? null : index)}
                  className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-gray-100 dark:focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-500 transition-colors"
                  aria-expanded={openFaqIndex === index}
                  aria-controls={`faq-answer-${index}`}
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">{faq.question}</h3>
                  {openFaqIndex === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0" aria-hidden="true" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0" aria-hidden="true" />
                  )}
                </button>
                {openFaqIndex === index && (
                  <div
                    id={`faq-answer-${index}`}
                    className="px-6 pb-6"
                  >
                    <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Ainda tem dúvidas? Entre em contato com nossa equipe de suporte
            </p>
            <Link
              to="/contact"
              className="px-6 py-3 bg-gray-900 dark:bg-gray-700 hover:bg-gray-800 dark:hover:bg-gray-600 text-white rounded-lg font-medium transition-colors inline-flex items-center"
            >
              Falar com Suporte
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>
      
      {/* CTA Final */}
      <section id="contact" className="py-20 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Pronto para transformar suas avaliações?
          </h2>
          <p className="text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-3xl mx-auto">
            Comece agora e crie sua primeira avaliação em menos de 5 minutos
          </p>
          <Link
            to="/register"
            className="px-8 py-4 bg-white hover:bg-gray-100 dark:bg-gray-100 dark:hover:bg-gray-200 text-blue-600 dark:text-blue-700 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 inline-block"
          >
            Começar Gratuitamente
          </Link>
          <p className="mt-6 text-blue-100 dark:text-blue-200 text-sm">
            Não é necessário cartão de crédito • Cancele quando quiser
          </p>
        </div>
      </section>
      
      {/* Modal de Vídeo */}
      {isVideoModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4"
          role="dialog"
          aria-modal="true"
          aria-labelledby="video-modal-title"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setIsVideoModalOpen(false)
            }
          }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden max-w-4xl w-full">
            <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 id="video-modal-title" className="text-lg font-semibold text-gray-900 dark:text-white">Demonstração do Atividade Pronta</h3>
              <button
                onClick={() => setIsVideoModalOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-500 rounded-full focus:outline-none text-gray-600 dark:text-gray-400"
                aria-label="Fechar modal de vídeo"
              >
                <X className="w-5 h-5" aria-hidden="true" />
              </button>
            </div>
            <div className="relative bg-gray-900" style={{ aspectRatio: '16/9' }}>
              {/* Player de vídeo funcional */}
              <iframe
                className="w-full h-full"
                src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0&modestbranding=1"
                title="Demonstração do Atividade Pronta"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
              {/* Fallback caso o vídeo não carregue */}
              <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white opacity-0 hover:opacity-100 transition-opacity">
                <div className="text-center">
                  <Play className="w-16 h-16 mx-auto mb-4 opacity-75" />
                  <p className="text-lg font-medium">Demonstração do Atividade Pronta</p>
                  <p className="text-sm text-gray-300 mt-2">
                    Veja como criar avaliações profissionais em minutos
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default LandingPage