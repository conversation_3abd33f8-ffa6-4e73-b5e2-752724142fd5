import React from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { CheckCircle, Crown, Users, Zap } from 'lucide-react'

interface PricingPlan {
  name: string
  price: string
  period: string
  description: string
  features: string[]
  cta: string
  ctaLink: string
  popular: boolean
  color: 'gray' | 'blue' | 'purple'
}

const plans: PricingPlan[] = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    price: "R$ 0",
    period: "/mês",
    description: "Perfeito para começar",
    features: [
      "50 questões por mês",
      "5 avaliações por mês",
      "Templates básicos",
      "Suporte por email"
    ],
    cta: "Começar Agora",
    ctaLink: "/register",
    popular: false,
    color: "gray"
  },
  {
    name: "Premium",
    price: "R$ 29,90",
    period: "/mês",
    description: "Para professores exigentes",
    features: [
      "Questões ilimitadas",
      "Avaliações ilimitadas",
      "Templates premium",
      "Geração por IA",
      "Colaboração",
      "Analytics avançados",
      "Suporte prioritário"
    ],
    cta: "Assinar Premium",
    ctaLink: "/register",
    popular: true,
    color: "blue"
  },
  {
    name: "Escolar",
    price: "R$ 199,90",
    period: "/mês",
    description: "Para instituições de ensino",
    features: [
      "Tudo do Premium",
      "Até 50 professores",
      "Gestão centralizada",
      "Relatórios institucionais",
      "Treinamento incluído",
      "Suporte dedicado"
    ],
    cta: "Falar com Consultor",
    ctaLink: "/contact",
    popular: false,
    color: "purple"
  }
]

const PricingSection: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Planos para cada necessidade
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comece gratuitamente e faça upgrade quando precisar de mais recursos
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative bg-white rounded-xl border-2 transition-all duration-300 hover:shadow-xl ${
                plan.popular 
                  ? 'border-blue-500 scale-105 shadow-lg' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Crown className="w-4 h-4" />
                    <span>Mais Popular</span>
                  </div>
                </div>
              )}

              <div className="p-8">
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 ${
                    plan.color === 'blue' ? 'bg-blue-100' :
                    plan.color === 'purple' ? 'bg-purple-100' :
                    'bg-gray-100'
                  }`}>
                    {plan.color === 'blue' && <Crown className="w-8 h-8 text-blue-600" />}
                    {plan.color === 'purple' && <Users className="w-8 h-8 text-purple-600" />}
                    {plan.color === 'gray' && <Zap className="w-8 h-8 text-gray-600" />}
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    <span className="text-gray-600">{plan.period}</span>
                  </div>
                  
                  <p className="text-gray-600">{plan.description}</p>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  to={plan.ctaLink}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors block text-center ${
                    plan.popular
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : plan.color === 'purple'
                        ? 'bg-purple-600 hover:bg-purple-700 text-white'
                        : 'bg-gray-900 hover:bg-gray-800 text-white'
                  }`}
                >
                  {plan.cta}
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            Todas as assinaturas incluem 7 dias de teste gratuito
          </p>
          <p className="text-sm text-gray-500">
            Cancele a qualquer momento. Sem taxas de cancelamento.
          </p>
        </div>
      </div>
    </section>
  )
}

export default PricingSection