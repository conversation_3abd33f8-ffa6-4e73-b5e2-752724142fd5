import React from 'react'
import { motion } from 'framer-motion'
import { Star } from 'lucide-react'

interface Testimonial {
  name: string
  role: string
  school: string
  image: string
  quote: string
}

const testimonials: Testimonial[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "<PERSON><PERSON> de Matemática",
    school: "<PERSON><PERSON><PERSON><PERSON>",
    image: "https://images.pexels.com/photos/5212317/pexels-photo-5212317.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Reduzi meu tempo de preparação de provas em 65%. Agora consigo focar mais no planejamento das aulas e no atendimento aos alunos."
  },
  {
    name: "Prof<PERSON> <PERSON>",
    role: "Professor <PERSON>",
    school: "Colégio Estadual Anísio Teixeira",
    image: "https://images.pexels.com/photos/8617942/pexels-photo-8617942.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "A qualidade das minhas avaliações melhorou significativamente. Os alunos notaram a diferença e até comentaram sobre a organização e clareza."
  },
  {
    name: "Profa. Ana Beatriz",
    role: "Coordenadora Pedagógica",
    school: "Escola Municipal Paulo Freire",
    image: "https://images.pexels.com/photos/5212339/pexels-photo-5212339.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Implementamos o Atividade Pronta em toda a escola e os resultados foram impressionantes. Nossos professores economizam coletivamente mais de 30 horas por semana."
  }
]

const TestimonialSection: React.FC = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            O que dizem nossos usuários
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Professores de todo o Brasil já transformaram sua forma de criar avaliações
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl border border-gray-200 p-6 shadow-md"
            >
              <div className="flex items-center mb-4">
                <img 
                  src={testimonial.image} 
                  alt={testimonial.name} 
                  loading="lazy"
                  className="w-16 h-16 rounded-full object-cover mr-4"
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{testimonial.name}</h3>
                  <p className="text-gray-600">{testimonial.role}</p>
                  <p className="text-gray-500 text-sm">{testimonial.school}</p>
                </div>
              </div>
              <div className="mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
              </div>
              <p className="text-gray-600 italic">"{testimonial.quote}"</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection