import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { 
  Home, 
  Database, 
  FileText, 
  Layout, 
  BarChart3, 
  Settings,
  Star,
  Crown,
  X,
  PlusCircle,
  Users,
  FileBarChart,
  HelpCircle,
  Shield,
  School,
  CheckCircle,
  ChevronRight,
  Sparkles
} from 'lucide-react'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useAuth } from '../../contexts/AuthContext' // Certifique-se que AuthContext está importado
import { motion, AnimatePresence } from 'framer-motion'

interface SidebarItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  path?: string
  badge?: number
  premium?: boolean
  children?: Omit<SidebarItem, 'children' | 'badge' | 'premium'>[]
}

const sidebarItems: SidebarItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: Home, path: '/app' },
  {
    id: 'assessments_parent',
    label: 'Avaliações Prontas',
    icon: FileText,
    path: '/app/assessments',
    children: [
      { id: 'create_assessment_wizard', label: 'Modo <PERSON>cil', icon: Sparkles, path: '/app/wizard' },
      { id: 'create_assessment', label: 'Modo Avançado', icon: PlusCircle, path: '/app/editor' },
    ],
  },
  {
    id: 'questions_parent',
    label: 'Banco de Questões',
    icon: Database,
    path: '/app/questions',
  },
  { id: 'templates', label: 'Modelos Prontos', icon: Layout, path: '/app/templates', premium: true },
  { id: 'reports_usage', label: 'Relatórios de uso', icon: FileBarChart, path: '/app/reports', premium: true },
  { id: 'help', label: 'Ajuda', icon: HelpCircle, path: '/app/help' },
  { id: 'settings', label: 'Configurações', icon: Settings, path: '/app/settings' }
]

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { isPremium, isEscolar, canAccess } = useSubscription() // Mantemos para a lógica de acesso, se necessário
  const { isAdmin, isSchoolAdmin, profile } = useAuth() // Adicionado 'profile' do useAuth

  const [openSubmenuId, setOpenSubmenuId] = useState<string | null>(null);

  const handleNavigation = (path: string, premium?: boolean) => {
    if (premium) {
      // Check specific feature access based on path
      const featureMap: { [key: string]: string } = {
        '/app/templates': 'premium_templates',
        '/app/reports': 'analytics'
      }

      const feature = featureMap[path] || 'analytics'

      if (!canAccess(feature)) {
        navigate('/app/billing')
        return
      }
    }

    navigate(path)
    onClose()
  }

  const isActive = (path: string) => {
    if (path === '/app') {
      return location.pathname === '/app' || location.pathname === '/app/'
    }
    return location.pathname.startsWith(path)
  }

  const handleItemClick = (item: SidebarItem) => {
    if (item.children) {
      setOpenSubmenuId(openSubmenuId === item.id ? null : item.id);
      if (item.path) {
        handleNavigation(item.path, item.premium);
      }
    } else if (item.path) {
      handleNavigation(item.path, item.premium);
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <aside className={`
        fixed lg:relative inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">EA</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">Atividade Pronta</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Plataforma Educacional</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {sidebarItems.map((item) => {
            const IconComponent = item.icon
            const active = isActive(item.path || '')
            const hasAccess = !item.premium || (item.path === '/app/templates' ? canAccess('premium_templates') : canAccess('analytics'))
            
            const isParentActive = item.children?.some(child => isActive(child.path || '')) || false;
            const itemIsActive = active || isParentActive;

            const isSubmenuOpen = openSubmenuId === item.id || isParentActive;

            return (
              <div key={item.id}>
                <button
                  onClick={() => handleItemClick(item)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left group ${
                    itemIsActive
                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border border-blue-200 dark:border-blue-800'
                      : hasAccess
                        ? 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                        : 'text-gray-400 dark:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <IconComponent className={`w-5 h-5 ${
                    itemIsActive
                      ? 'text-blue-700 dark:text-blue-400'
                      : hasAccess
                        ? 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                        : 'text-gray-400 dark:text-gray-600'
                  }`} />
                  <span className="flex-1 font-medium">{item.label}</span>
                  
                  {item.premium && !hasAccess && (
                    <Crown className="w-4 h-4 text-yellow-500" />
                  )}
                  
                  {item.badge && (
                    <span className="bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-xs px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                  {item.children && (
                    <motion.div
                      animate={{ rotate: isSubmenuOpen ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    </motion.div>
                  )}
                </button>

                <AnimatePresence>
                  {item.children && isSubmenuOpen && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden pl-8 pr-4 py-1 space-y-1"
                    >
                      {item.children.map((subItem) => {
                        const SubIconComponent = subItem.icon;
                        const subActive = isActive(subItem.path || '');
                        return (
                          <button
                            key={subItem.id}
                            onClick={() => handleNavigation(subItem.path || '')}
                            className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200 text-left group ${
                              subActive
                                ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border border-blue-200 dark:border-blue-800'
                                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                            }`}
                          >
                            <SubIconComponent className={`w-4 h-4 ${
                              subActive
                                ? 'text-blue-700 dark:text-blue-400'
                                : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                            }`} />
                            <span className="flex-1 font-medium text-sm">{subItem.label}</span>
                          </button>
                        );
                      })}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )
          })}

          {/* School Admin Panel Link */}
          {isSchoolAdmin && (
            <>
              <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>
              <button
                onClick={() => {
                  handleNavigation('/app/school-admin')
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left group ${
                  location.pathname.startsWith('/app/school-admin')
                    ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
              >
                <School className={`w-5 h-5 ${
                  location.pathname.startsWith('/app/school-admin')
                    ? 'text-red-700 dark:text-red-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                }`} />
                <span className="flex-1 font-medium">Gerenciamento da Escola</span>
              </button>
            </>
          )}

          {/* Admin Panel Link */}
          {isAdmin && (
            <>
              <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>
              <button
                onClick={() => {
                  navigate('/admin')
                  onClose()
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left group ${
                  location.pathname.startsWith('/admin')
                    ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
              >
                <Shield className={`w-5 h-5 ${
                  location.pathname.startsWith('/admin')
                    ? 'text-red-700 dark:text-red-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                }`} />
                <span className="flex-1 font-medium">Painel de Admin</span>
              </button>
            </>
          )}
        </nav>

        {/* Upgrade / Plan Status Section */}
        <div className="p-4 mt-auto">
          {isSchoolAdmin ? (
            // Mensagem para administradores de escola
            <div className="bg-blue-600/10 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-xl p-4 text-center">
              <School className="w-6 h-6 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold text-lg mb-1">Seu Plano: Escolar (Admin)</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Você gerencia os recursos escolares da sua instituição.
              </p>
            </div>
          ) : profile?.plano === 'escolar' ? (
            // Mensagem para plano escolar (usuário comum)
            <div className="bg-blue-600/10 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-xl p-4 text-center">
              <School className="w-6 h-6 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold text-lg mb-1">Seu Plano: Escolar</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Você tem acesso aos recursos escolares através da sua instituição.
              </p>
            </div>
          ) : profile?.plano === 'premium' ? (
            // Mensagem para plano premium
            <div className="bg-green-600/10 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded-xl p-4 text-center">
              <CheckCircle className="w-6 h-6 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold text-lg mb-1">Seu Plano: Premium</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Você tem acesso a todos os recursos premium.
              </p>
            </div>
          ) : (
            // Conteúdo original de upgrade para plano gratuito
            <div className="bg-blue-600/10 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-xl p-4 text-center">
              <Crown className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
              <h3 className="font-semibold text-lg mb-1">Upgrade para Premium</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                Acesse recursos avançados, templates premium e geração por IA.
              </p>
              <button
                onClick={() => {
                  navigate('/app/billing')
                  onClose()
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-medium transition-colors"
              >
                Ver Planos
              </button>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}

export default Sidebar