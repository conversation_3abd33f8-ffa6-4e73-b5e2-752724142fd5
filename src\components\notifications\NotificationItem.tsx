import React from 'react'
import { Check, X, Clock, AlertCircle, Info, CheckCircle } from 'lucide-react'
import { motion } from 'framer-motion'

interface NotificationItemProps {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  read: boolean
  createdAt: string
  action?: {
    label: string
    onClick: () => void
  }
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  id,
  type,
  title,
  message,
  read,
  createdAt,
  action,
  onMarkAsRead,
  onDelete
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
      default:
        return <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />
    }
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Agora'
    if (diffInMinutes < 60) return `${diffInMinutes}m atrás`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h atrás`
    return `${Math.floor(diffInMinutes / 1440)}d atrás`
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`p-4 border-b border-gray-50 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${
        !read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className={`text-sm font-medium ${
                !read ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
              }`}>
                {title}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {message}
              </p>
              
              {action && (
                <button
                  onClick={action.onClick}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mt-2"
                >
                  {action.label}
                </button>
              )}
            </div>
            
            <div className="flex items-center space-x-1 ml-2">
              {!read && (
                <button
                  onClick={() => onMarkAsRead(id)}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  title="Marcar como lida"
                >
                  <Check className="w-3 h-3 text-gray-400 dark:text-gray-500" />
                </button>
              )}
              <button
                onClick={() => onDelete(id)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                title="Remover"
              >
                <X className="w-3 h-3 text-gray-400 dark:text-gray-500" />
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-1 mt-2">
            <Clock className="w-3 h-3 text-gray-400 dark:text-gray-500" />
            <span className="text-xs text-gray-500 dark:text-gray-500">
              {getTimeAgo(createdAt)}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default NotificationItem