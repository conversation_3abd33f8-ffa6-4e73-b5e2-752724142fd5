import React, { useState, useEffect, useCallback } from 'react'
import { X, Download, Crown, UserPlus, Loader2 } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { ConversionModalProps } from '../../types/public'
import { downloadPublicAssessment, canDownloadAssessment } from '../../lib/public/assessmentDownload'
import LoginForm from '../auth/LoginForm'
import RegisterForm from '../auth/RegisterForm'
import toast from 'react-hot-toast'

/**
 * Modal para conversão de usuários (signup, upgrade, download)
 */
const ConversionModal: React.FC<ConversionModalProps> = ({
  isOpen,
  onClose,
  assessment,
  conversionType,
  onConvert
}) => {
  const [authMode, setAuthMode] = useState<'login' | 'register'>('register')
  const [isConverting, setIsConverting] = useState(false)
  const [hasTriggeredConversion, setHasTriggeredConversion] = useState(false)
  const [downloadAttempted, setDownloadAttempted] = useState(false)
  const { user } = useAuth()
  const { isPremium, isEscolar } = useSubscription()
  const navigate = useNavigate()

  const isPaidUser = isPremium || isEscolar

  // Handle conversion based on type
  const handleConversion = useCallback(async () => {
    console.log('ConversionModal: handleConversion called', {
      assessmentId: assessment?.id,
      conversionType,
      isConverting,
      downloadAttempted,
      hasTriggeredConversion,
      timestamp: new Date().toISOString(),
      stackTrace: new Error().stack
    })

    if (!assessment) {
      console.warn('ConversionModal: No assessment provided to handleConversion')
      return
    }

    // Prevent multiple downloads in the same session
    const recentDownloads = JSON.parse(sessionStorage.getItem('recentDownloads') || '[]')
    const recentDownload = recentDownloads.find((d: any) =>
      d.assessmentId === assessment.id &&
      Date.now() - d.timestamp < 30000 // 30 seconds cooldown
    )

    if (recentDownload) {
      console.warn('ConversionModal: Recent download detected, blocking request', recentDownload)
      toast.error('Aguarde alguns segundos antes de tentar baixar novamente')
      setIsConverting(false)
      return
    }

    // Check if already attempted download for this assessment in this modal session
    if (downloadAttempted) {
      console.warn('ConversionModal: Download already attempted for this assessment in this session')
      return
    }

    // Additional guard: Check if modal is actually open
    if (!isOpen) {
      console.warn('ConversionModal: Attempted to download while modal is closed')
      return
    }

    console.log('ConversionModal: Starting conversion process')
    setIsConverting(true)
    setDownloadAttempted(true)

    try {
      // Check if user can download
      const { canDownload, reason } = await canDownloadAssessment(assessment.id, user?.id)

      if (!canDownload) {
        toast.error(reason || 'Não é possível baixar esta avaliação')
        setIsConverting(false)
        return
      }

      const conversionData = {
        assessmentId: assessment.id,
        userId: user?.id,
        conversionType,
        sourcePage: window.location.href,
        metadata: {
          assessmentTitle: assessment.titulo,
          assessmentSlug: assessment.slug,
          disciplina: assessment.disciplina,
          serie: assessment.serie
        }
      }

      await onConvert(conversionData)

      // Handle post-conversion actions
      if (conversionType === 'download') {
        console.log('ConversionModal: Triggering PDF download', {
          assessmentId: assessment.id,
          userId: user?.id,
          timestamp: new Date().toISOString()
        })

        // Trigger actual download
        await downloadPublicAssessment(assessment.id, user?.id)

        // Track successful download in session
        const recentDownloads = JSON.parse(sessionStorage.getItem('recentDownloads') || '[]')
        recentDownloads.push({
          assessmentId: assessment.id,
          timestamp: Date.now()
        })
        // Keep only last 10 downloads
        if (recentDownloads.length > 10) {
          recentDownloads.splice(0, recentDownloads.length - 10)
        }
        sessionStorage.setItem('recentDownloads', JSON.stringify(recentDownloads))

        console.log('ConversionModal: Download completed successfully')
        toast.success('Download iniciado!')
        onClose()
      } else if (conversionType === 'upgrade') {
        // Redirect to billing page
        navigate('/app/billing')
      }
    } catch (error) {
      console.error('Conversion error:', error)
      toast.error('Erro ao processar download. Tente novamente.')
    } finally {
      setIsConverting(false)
    }
  }, [assessment, user, conversionType, onConvert, onClose, navigate])

  // Watch for user login to trigger conversion
  useEffect(() => {
    if (user && !isConverting && !hasTriggeredConversion && conversionType === 'download' && isOpen) {
      // Log for debugging
      console.log('ConversionModal: Auto-triggering conversion after user login', {
        userId: user.id,
        assessmentId: assessment?.id,
        conversionType,
        timestamp: new Date().toISOString()
      })

      // User just logged in, proceed with conversion
      setHasTriggeredConversion(true)
      handleConversion()
    }
  }, [user, isConverting, hasTriggeredConversion, conversionType, isOpen, handleConversion])

  // Reset conversion trigger when modal closes
  useEffect(() => {
    if (!isOpen) {
      setHasTriggeredConversion(false)
      setDownloadAttempted(false)
    }
  }, [isOpen])

  // Handle upgrade redirect
  const handleUpgradeRedirect = () => {
    // Store return URL for after upgrade
    localStorage.setItem('returnUrl', window.location.href)
    navigate('/app/billing')
    onClose()
  }



  if (!isOpen || !assessment) return null

  // Determine modal content based on conversion type and user status
  const getModalContent = () => {
    // For premium assessments, always require upgrade if not paid user
    const isPremiumAssessment = assessment.public_category === 'premium' // You can adjust this logic

    if (isPremiumAssessment && !isPaidUser) {
      return {
        title: 'Avaliação Premium',
        description: 'Esta avaliação está disponível apenas para usuários premium.',
        icon: Crown,
        iconColor: 'text-yellow-500',
        buttonText: 'Assinar Premium',
        buttonColor: 'bg-yellow-600 hover:bg-yellow-700',
        action: handleUpgradeRedirect
      }
    }

    // For free assessments, require signup if not logged in
    if (!user) {
      return {
        title: 'Criar conta gratuita',
        description: 'Crie sua conta gratuita para baixar esta avaliação e acessar milhares de outras.',
        icon: UserPlus,
        iconColor: 'text-green-500',
        showAuthForm: true
      }
    }

    // User is logged in and can download
    return {
      title: 'Baixar avaliação',
      description: 'Sua avaliação será baixada em instantes.',
      icon: Download,
      iconColor: 'text-blue-500',
      buttonText: 'Baixar agora',
      buttonColor: 'bg-blue-600 hover:bg-blue-700',
      action: handleConversion
    }
  }

  const modalContent = getModalContent()

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <modalContent.icon className={`w-6 h-6 ${modalContent.iconColor}`} />
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {modalContent.title}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Assessment Info */}
              <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                  {assessment.titulo}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {assessment.disciplina} • {assessment.serie}
                </p>
              </div>

              {/* Description */}
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {modalContent.description}
              </p>

              {/* Auth Form */}
              {modalContent.showAuthForm && (
                <div className="mb-6">
                  <div className="flex mb-4 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    <button
                      onClick={() => setAuthMode('register')}
                      className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                        authMode === 'register'
                          ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-600 dark:text-gray-300'
                      }`}
                    >
                      Criar conta
                    </button>
                    <button
                      onClick={() => setAuthMode('login')}
                      className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                        authMode === 'login'
                          ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-600 dark:text-gray-300'
                      }`}
                    >
                      Fazer login
                    </button>
                  </div>

                  {authMode === 'register' ? (
                    <RegisterForm
                      onToggleMode={() => setAuthMode('login')}
                      onBackToLogin={() => setAuthMode('login')}
                    />
                  ) : (
                    <LoginForm
                      onToggleMode={() => setAuthMode('register')}
                      onForgotPassword={() => {}}
                    />
                  )}
                </div>
              )}

              {/* Action Button */}
              {modalContent.action && (
                <div className="flex space-x-3">
                  <button
                    onClick={onClose}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={modalContent.action}
                    disabled={isConverting}
                    className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center space-x-2 ${
                      modalContent.buttonColor || 'bg-blue-600 hover:bg-blue-700'
                    } disabled:opacity-50`}
                  >
                    {isConverting && <Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isConverting ? 'Processando...' : modalContent.buttonText}</span>
                  </button>
                </div>
              )}

              {/* Benefits for free users */}
              {!user && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Benefícios da conta gratuita:
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      5 avaliações por mês
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      10 downloads PDF por mês
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      Acesso a templates básicos
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      Suporte por email
                    </li>
                  </ul>
                </div>
              )}

              {/* Upgrade benefits for logged users */}
              {user && !isPaidUser && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Upgrade para Premium:
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      Avaliações ilimitadas
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      Downloads ilimitados
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      Acesso a todos os templates
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      Geração de questões com IA
                    </li>
                  </ul>
                  
                  <button
                    onClick={handleUpgradeRedirect}
                    className="w-full mt-4 bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    Ver planos Premium
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default ConversionModal
