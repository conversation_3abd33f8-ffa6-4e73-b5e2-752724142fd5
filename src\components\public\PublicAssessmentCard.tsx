import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Eye, 
  Download, 
  Clock, 
  BookOpen, 
  GraduationCap,
  Star,
  User,
  School
} from 'lucide-react'
import { PublicAssessmentCardProps } from '../../types/public'
import { motion } from 'framer-motion'

/**
 * Card para exibir avaliação pública na listagem
 */
const PublicAssessmentCard: React.FC<PublicAssessmentCardProps> = ({
  assessment,
  showCategory = true,
  showAuthor = true,
  showStats = true,
  onView,
  onDownload
}) => {
  const handleCardClick = () => {
    if (onView) {
      onView(assessment)
    }
  }

  const handleDownloadClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (onDownload) {
      onDownload(assessment)
    }
  }

  // Get difficulty color
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Fácil':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'Médio':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'Difícil':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  // Format duration
  const formatDuration = (minutes?: number) => {
    if (!minutes) return null
    if (minutes < 60) return `${minutes}min`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group"
    >
      <Link 
        to={`/avaliacoes/${assessment.slug}`}
        onClick={handleCardClick}
        className="block"
      >
        {/* Featured Image or Placeholder */}
        <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
          {assessment.featured_image_url ? (
            <img
              src={assessment.featured_image_url}
              alt={assessment.titulo}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center text-white">
                <BookOpen className="w-12 h-12 mx-auto mb-2 opacity-80" />
                <p className="text-sm font-medium opacity-90">
                  {assessment.disciplina}
                </p>
              </div>
            </div>
          )}
          
          {/* Featured Badge */}
          {assessment.is_featured && (
            <div className="absolute top-3 left-3">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <Star className="w-3 h-3 mr-1" />
                Destaque
              </span>
            </div>
          )}

          {/* Difficulty Badge */}
          {assessment.difficulty_level && (
            <div className="absolute top-3 right-3">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(assessment.difficulty_level)}`}>
                {assessment.difficulty_level}
              </span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Category */}
          {showCategory && assessment.public_category && (
            <div className="mb-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {assessment.public_category}
              </span>
            </div>
          )}

          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {assessment.titulo}
          </h3>

          {/* Description */}
          {assessment.seo_description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
              {assessment.seo_description}
            </p>
          )}

          {/* Meta Information */}
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
            <div className="flex items-center space-x-3">
              <span className="flex items-center">
                <GraduationCap className="w-4 h-4 mr-1" />
                {assessment.serie}
              </span>
              <span className="flex items-center">
                <BookOpen className="w-4 h-4 mr-1" />
                {assessment.disciplina}
              </span>
            </div>
            
            {assessment.estimated_duration && (
              <span className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {formatDuration(assessment.estimated_duration)}
              </span>
            )}
          </div>

          {/* Author */}
          {showAuthor && assessment.profiles && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-3">
              <User className="w-4 h-4 mr-1" />
              <span>{assessment.profiles.nome}</span>
              {assessment.profiles.escola && (
                <>
                  <span className="mx-2">•</span>
                  <School className="w-4 h-4 mr-1" />
                  <span>{assessment.profiles.escola}</span>
                </>
              )}
            </div>
          )}

          {/* Stats */}
          {showStats && (
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {assessment.view_count || 0}
                </span>
                <span className="flex items-center">
                  <Download className="w-4 h-4 mr-1" />
                  {assessment.download_count || 0}
                </span>
              </div>
              
              <span className="text-xs">
                {new Date(assessment.created_at).toLocaleDateString('pt-BR')}
              </span>
            </div>
          )}
        </div>
      </Link>

      {/* Action Buttons */}
      <div className="px-4 pb-4">
        <div className="flex space-x-2">
          <Link
            to={`/avaliacoes/${assessment.slug}`}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors text-center"
          >
            Ver Detalhes
          </Link>
          
          <button
            onClick={handleDownloadClick}
            className="flex items-center justify-center bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
            title="Baixar avaliação"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}

export default PublicAssessmentCard
