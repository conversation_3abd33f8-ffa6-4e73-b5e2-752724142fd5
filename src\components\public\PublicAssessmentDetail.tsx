import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  Download, 
  Eye, 
  Clock, 
  BookOpen, 
  GraduationCap,
  User,
  School,
  Calendar,
  Share2,
  ArrowLeft,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { motion } from 'framer-motion'
import { usePublicAssessmentDetail, useRelatedAssessments } from '../../hooks/usePublicAssessments'
import { useConversionTracking } from '../../hooks/useConversionTracking'
import { useAssessmentSEO } from '../../hooks/useSEOMetadata'
import { PublicAssessmentWithDetails } from '../../types/public'
import PublicBreadcrumbs from './PublicBreadcrumbs'
import SEOHead from './SEOHead'
import ConversionModal from './ConversionModal'
import PublicAssessmentCard from './PublicAssessmentCard'
import PublicAssessmentPreview from './PublicAssessmentPreview'
import PublicLayout from './PublicLayout'

/**
 * Página de detalhes de uma avaliação pública
 */
const PublicAssessmentDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>()
  const navigate = useNavigate()
  const [showPreview, setShowPreview] = useState(false)
  const [conversionModal, setConversionModal] = useState<{
    isOpen: boolean
    type: 'signup' | 'upgrade' | 'download'
  }>({
    isOpen: false,
    type: 'download'
  })

  // Hooks
  const { data: assessment, isLoading, error } = usePublicAssessmentDetail(slug!)
  const { data: relatedAssessments = [] } = useRelatedAssessments(
    assessment?.id || '',
    assessment?.disciplina || '',
    assessment?.serie || '',
    4
  )
  const { trackConversion, trackView } = useConversionTracking()
  const { seoData, schemaMarkup } = useAssessmentSEO(assessment)

  // Track view on mount
  useEffect(() => {
    if (assessment) {
      trackView(assessment.id)
    }
  }, [assessment, trackView])

  // Add global download prevention listener
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Check if there are any pending downloads
      const recentDownloads = JSON.parse(sessionStorage.getItem('recentDownloads') || '[]')
      const pendingDownload = recentDownloads.find((d: any) =>
        Date.now() - d.timestamp < 5000 // 5 seconds
      )

      if (pendingDownload) {
        console.log('PublicAssessmentDetail: Detected page unload during download')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  // Handle download
  const handleDownload = () => {
    console.log('PublicAssessmentDetail: Download button clicked', {
      assessmentId: assessment?.id,
      assessmentSlug: assessment?.slug,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    })

    setConversionModal({
      isOpen: true,
      type: 'download'
    })
  }

  // Handle conversion
  const handleConversion = async (data: any) => {
    await trackConversion(data)
    setConversionModal({ isOpen: false, type: 'download' })
  }

  // Handle share
  const handleShare = async () => {
    if (navigator.share && assessment) {
      try {
        await navigator.share({
          title: assessment.titulo,
          text: assessment.seo_description || `Avaliação de ${assessment.disciplina} para ${assessment.serie}`,
          url: window.location.href
        })
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href)
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  // Format duration
  const formatDuration = (minutes?: number) => {
    if (!minutes) return null
    if (minutes < 60) return `${minutes} minutos`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours} hora${hours > 1 ? 's' : ''}`
  }

  // Get difficulty color
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Fácil':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'Médio':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'Difícil':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  // Breadcrumbs
  const breadcrumbs = assessment ? [
    { label: 'Avaliações', href: '/avaliacoes', current: false },
    ...(assessment.public_category ? [{
      label: assessment.public_category,
      href: `/avaliacoes/categoria/${assessment.public_category}`,
      current: false
    }] : []),
    { label: assessment.titulo, href: `/avaliacoes/${assessment.slug}`, current: true }
  ] : []

  if (isLoading) {
    return (
      <PublicLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-300">Carregando avaliação...</p>
          </div>
        </div>
      </PublicLayout>
    )
  }

  if (error || !assessment) {
    return (
      <PublicLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Avaliação não encontrada
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              A avaliação que você está procurando não existe ou foi removida.
            </p>
            <button
              onClick={() => navigate('/avaliacoes')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Ver todas as avaliações
            </button>
          </div>
        </div>
      </PublicLayout>
    )
  }

  return (
    <PublicLayout>
      {/* SEO Head */}
      {seoData && (
        <SEOHead
          metadata={seoData}
          schemaMarkup={schemaMarkup}
          breadcrumbs={breadcrumbs}
        />
      )}

      <div className="container mx-auto px-4 py-8">
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors mb-6"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Voltar</span>
          </button>

          {/* Breadcrumbs */}
          <PublicBreadcrumbs items={breadcrumbs} className="mb-6" />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Header */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                {/* Featured Image */}
                {assessment.featured_image_url && (
                  <div className="mb-6">
                    <img
                      src={assessment.featured_image_url}
                      alt={assessment.titulo}
                      className="w-full h-64 object-cover rounded-lg"
                    />
                  </div>
                )}

                {/* Title and Meta */}
                <div className="mb-4">
                  <div className="flex flex-wrap items-center gap-2 mb-3">
                    {assessment.is_featured && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Destaque
                      </span>
                    )}
                    {assessment.difficulty_level && (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(assessment.difficulty_level)}`}>
                        {assessment.difficulty_level}
                      </span>
                    )}
                    {assessment.public_category && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {assessment.public_category}
                      </span>
                    )}
                  </div>

                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    {assessment.titulo}
                  </h1>

                  {assessment.seo_description && (
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-4">
                      {assessment.seo_description}
                    </p>
                  )}
                </div>

                {/* Meta Information */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
                    <BookOpen className="w-5 h-5" />
                    <span>{assessment.disciplina}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
                    <GraduationCap className="w-5 h-5" />
                    <span>{assessment.serie}</span>
                  </div>
                  {assessment.estimated_duration && (
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
                      <Clock className="w-5 h-5" />
                      <span>{formatDuration(assessment.estimated_duration)}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
                    <Eye className="w-5 h-5" />
                    <span>{assessment.view_count || 0} visualizações</span>
                  </div>
                </div>

                {/* Author */}
                {assessment.profiles && (
                  <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 mb-6">
                    <User className="w-5 h-5" />
                    <span>Por {assessment.profiles.nome}</span>
                    {assessment.profiles.escola && (
                      <>
                        <span>•</span>
                        <School className="w-4 h-4" />
                        <span>{assessment.profiles.escola}</span>
                      </>
                    )}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={handleDownload}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    <Download className="w-5 h-5" />
                    <span>Baixar Avaliação</span>
                  </button>
                  
                  <button
                    onClick={() => setShowPreview(true)}
                    className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    <Eye className="w-5 h-5" />
                    <span>Visualizar</span>
                  </button>
                  
                  <button
                    onClick={handleShare}
                    className="flex items-center space-x-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Share2 className="w-5 h-5" />
                    <span>Compartilhar</span>
                  </button>
                </div>
              </div>

              {/* Questions Preview */}
              {assessment.questions && assessment.questions.length > 0 && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Questões ({assessment.questions.length})
                  </h2>
                  
                  <div className="space-y-4">
                    {assessment.questions.slice(0, 3).map((question, index) => (
                      <div key={question.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            Questão {index + 1}
                          </h3>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(question.dificuldade)}`}>
                            {question.dificuldade}
                          </span>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 line-clamp-2">
                          {question.enunciado}
                        </p>
                      </div>
                    ))}
                    
                    {assessment.questions.length > 3 && (
                      <div className="text-center">
                        <button
                          onClick={() => setShowPreview(true)}
                          className="text-blue-600 hover:text-blue-700 font-medium"
                        >
                          Ver todas as {assessment.questions.length} questões
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Estatísticas
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Visualizações</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {assessment.view_count || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Downloads</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {assessment.download_count || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Criado em</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {new Date(assessment.created_at).toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Related Assessments */}
              {relatedAssessments.length > 0 && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Avaliações Relacionadas
                  </h3>
                  <div className="space-y-4">
                    {relatedAssessments.map((relatedAssessment) => (
                      <PublicAssessmentCard
                        key={relatedAssessment.id}
                        assessment={relatedAssessment}
                        showCategory={false}
                        showAuthor={false}
                        showStats={false}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

      {/* Preview Modal */}
      {showPreview && (
        <PublicAssessmentPreview
          assessment={assessment}
          onClose={() => setShowPreview(false)}
          onDownload={handleDownload}
        />
      )}

      {/* Conversion Modal */}
      <ConversionModal
        isOpen={conversionModal.isOpen}
        onClose={() => setConversionModal({ isOpen: false, type: 'download' })}
        assessment={assessment}
        conversionType={conversionModal.type}
        onConvert={handleConversion}
      />
    </PublicLayout>
  )
}

export default PublicAssessmentDetail
