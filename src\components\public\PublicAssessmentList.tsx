import React from 'react'
import { useAssessmentListSEO } from '../../hooks/useSEOMetadata'
import { PublicAssessmentFilters } from '../../types/public'
import PublicBreadcrumbs from './PublicBreadcrumbs'
import SEOHead from './SEOHead'
import PublicLayout from './PublicLayout'
import PublicAssessmentListContent from './PublicAssessmentListContent'

interface PublicAssessmentListProps {
  initialFilters?: Partial<PublicAssessmentFilters>
}

/**
 * Componente principal para listagem de avaliações públicas
 */
const PublicAssessmentList: React.FC<PublicAssessmentListProps> = ({
  initialFilters = {}
}) => {
  // SEO data
  const { seoData } = useAssessmentListSEO(initialFilters, 0)

  // Breadcrumbs
  const breadcrumbs = [
    { label: 'Avaliações', href: '/avaliacoes', current: true }
  ]

  return (
    <PublicLayout>
      {/* SEO Head */}
      {seoData && (
        <SEOHead
          metadata={seoData}
          breadcrumbs={breadcrumbs}
        />
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumbs */}
        <PublicBreadcrumbs items={breadcrumbs} className="mb-6" />

        {/* Assessment List Content */}
        <PublicAssessmentListContent
          initialFilters={initialFilters}
          showHeader={true}
          showBreadcrumbs={false}
        />
      </div>
    </PublicLayout>
  )
}

export default PublicAssessmentList
