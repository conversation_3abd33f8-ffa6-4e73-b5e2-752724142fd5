import React, { useState, useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { 
  Search, 
  Filter, 
  Grid, 
  List,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePublicAssessments, usePublicCategories } from '../../hooks/usePublicAssessments'
import { useConversionTracking } from '../../hooks/useConversionTracking'
import { PublicAssessmentFilters, PublicAssessmentWithDetails } from '../../types/public'
import PublicAssessmentCard from './PublicAssessmentCard'
import ConversionModal from './ConversionModal'

interface PublicAssessmentListContentProps {
  initialFilters?: Partial<PublicAssessmentFilters>
  showHeader?: boolean
  showBreadcrumbs?: boolean
  className?: string
}

/**
 * Conteúdo da listagem de avaliações públicas (sem layout wrapper)
 * Para ser usado dentro de outras páginas que já têm layout
 */
const PublicAssessmentListContent: React.FC<PublicAssessmentListContentProps> = ({
  initialFilters = {},
  showHeader = true,
  showBreadcrumbs = false,
  className = ''
}) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [conversionModal, setConversionModal] = useState<{
    isOpen: boolean
    assessment?: PublicAssessmentWithDetails
    type: 'signup' | 'upgrade' | 'download'
  }>({
    isOpen: false,
    type: 'download'
  })

  // Get filters from URL params and merge with initial filters
  const filters: PublicAssessmentFilters = useMemo(() => ({
    search: searchParams.get('search') || initialFilters.search || undefined,
    category: searchParams.get('category') || initialFilters.category || undefined,
    disciplina: searchParams.get('disciplina') || initialFilters.disciplina || undefined,
    serie: searchParams.get('serie') || initialFilters.serie || undefined,
    difficulty: searchParams.get('difficulty') || initialFilters.difficulty || undefined,
    sortBy: (searchParams.get('sortBy') as any) || initialFilters.sortBy || 'recent',
    sortOrder: (searchParams.get('sortOrder') as any) || initialFilters.sortOrder || 'desc',
    limit: initialFilters.limit || 12
  }), [searchParams, initialFilters])

  // Hooks
  const { data: categories = [] } = usePublicCategories()
  const { 
    assessments, 
    total, 
    isLoading, 
    error, 
    hasNextPage, 
    fetchNextPage, 
    refetch 
  } = usePublicAssessments(filters)
  
  const { trackConversion } = useConversionTracking()

  // Update URL params
  const updateFilters = useCallback((newFilters: Partial<PublicAssessmentFilters>) => {
    const params = new URLSearchParams(searchParams)
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        params.set(key, String(value))
      } else {
        params.delete(key)
      }
    })
    
    setSearchParams(params)
  }, [searchParams, setSearchParams])

  // Handle search
  const handleSearch = useCallback((searchTerm: string) => {
    updateFilters({ search: searchTerm })
  }, [updateFilters])

  // Handle assessment actions
  const handleView = useCallback((assessment: PublicAssessmentWithDetails) => {
    // Track view event
    trackConversion('view', assessment.id, 'organic')
  }, [trackConversion])

  const handleDownload = useCallback((assessment: PublicAssessmentWithDetails) => {
    setConversionModal({
      isOpen: true,
      assessment,
      type: 'download'
    })
  }, [])

  const handleConversion = useCallback(async (type: 'signup' | 'upgrade' | 'download') => {
    if (conversionModal.assessment) {
      await trackConversion(type, conversionModal.assessment.id, 'organic')
    }
    setConversionModal({ isOpen: false, type: 'download' })
  }, [conversionModal.assessment, trackConversion])

  // Loading state
  if (isLoading && !assessments.length) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-300">Carregando avaliações...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error && !assessments.length) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="max-w-md mx-auto">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Erro ao carregar avaliações
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {error.message}
          </p>
          <button
            onClick={() => refetch()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      {showHeader && (
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {filters.search ? `Resultados para "${filters.search}"` :
             filters.category ? `Avaliações de ${categories.find(c => c.slug === filters.category)?.name || filters.category}` :
             'Avaliações Educacionais'}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {total > 0 ? `${total} avaliações encontradas` : 'Nenhuma avaliação encontrada'}
          </p>
        </div>
      )}

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar avaliações..."
            defaultValue={filters.search || ''}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch(e.currentTarget.value)
              }
            }}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Filtros</span>
            </button>

            {/* Sort Controls */}
            <select
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-')
                updateFilters({ sortBy: sortBy as any, sortOrder: sortOrder as any })
              }}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="recent-desc">Mais recentes</option>
              <option value="popular-desc">Mais populares</option>
              <option value="title-asc">Título A-Z</option>
              <option value="title-desc">Título Z-A</option>
              <option value="views-desc">Mais visualizadas</option>
            </select>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Assessment Grid/List */}
      {assessments.length > 0 ? (
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          <AnimatePresence>
            {assessments.map((assessment) => (
              <PublicAssessmentCard
                key={assessment.id}
                assessment={assessment}
                onView={handleView}
                onDownload={handleDownload}
              />
            ))}
          </AnimatePresence>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Nenhuma avaliação encontrada com os filtros selecionados.
          </p>
          <button
            onClick={() => updateFilters({})}
            className="text-blue-600 hover:text-blue-700 transition-colors"
          >
            Limpar filtros
          </button>
        </div>
      )}

      {/* Load More Button */}
      {hasNextPage && (
        <div className="text-center mt-8">
          <button
            onClick={() => fetchNextPage()}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2 inline" />
                Carregando...
              </>
            ) : (
              'Carregar mais'
            )}
          </button>
        </div>
      )}

      {/* Conversion Modal */}
      <ConversionModal
        isOpen={conversionModal.isOpen}
        onClose={() => setConversionModal({ isOpen: false, type: 'download' })}
        assessment={conversionModal.assessment!}
        conversionType={conversionModal.type}
        onConvert={handleConversion}
      />
    </div>
  )
}

export default PublicAssessmentListContent
