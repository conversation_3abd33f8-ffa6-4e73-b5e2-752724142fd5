import React from 'react'
import { X, Download, Eye } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { PublicAssessmentWithDetails } from '../../types/public'

interface PublicAssessmentPreviewProps {
  assessment: PublicAssessmentWithDetails
  onClose: () => void
  onDownload: () => void
}

/**
 * Modal de preview completo da avaliação pública
 */
const PublicAssessmentPreview: React.FC<PublicAssessmentPreviewProps> = ({
  assessment,
  onClose,
  onDownload
}) => {
  // Get difficulty color
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Fácil':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'Médio':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'Difícil':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  // Render question based on type
  const renderQuestion = (question: any, index: number) => {
    return (
      <div key={question.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-6">
        {/* Question Header */}
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Questão {index + 1}
          </h3>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(question.dificuldade)}`}>
              {question.dificuldade}
            </span>
            <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {question.tipo.replace('_', ' ')}
            </span>
          </div>
        </div>

        {/* Question Text */}
        <div className="mb-4">
          <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
            {question.enunciado}
          </p>
        </div>

        {/* Question Options */}
        {question.tipo === 'multipla_escolha' && question.alternativas && (
          <div className="space-y-2">
            {question.alternativas.map((alternativa: string, altIndex: number) => (
              <div key={altIndex} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {String.fromCharCode(65 + altIndex)}
                  </span>
                </div>
                <p className="text-gray-700 dark:text-gray-300 flex-1">
                  {alternativa}
                </p>
              </div>
            ))}
          </div>
        )}

        {question.tipo === 'verdadeiro_falso' && (
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-6 h-6 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">V</span>
              </div>
              <p className="text-gray-700 dark:text-gray-300">Verdadeiro</p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-6 h-6 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">F</span>
              </div>
              <p className="text-gray-700 dark:text-gray-300">Falso</p>
            </div>
          </div>
        )}

        {question.tipo === 'dissertativa' && (
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 min-h-[100px]">
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Espaço para resposta dissertativa
            </p>
          </div>
        )}

        {/* Question Tags */}
        {question.tags && question.tags.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap gap-2">
              {question.tags.map((tag: string, tagIndex: number) => (
                <span
                  key={tagIndex}
                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Eye className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Preview da Avaliação
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {assessment.titulo}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onDownload}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Baixar</span>
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Assessment Header */}
            <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-center mb-4">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {assessment.titulo}
                </h1>
                <div className="flex items-center justify-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                  <span>{assessment.disciplina}</span>
                  <span>•</span>
                  <span>{assessment.serie}</span>
                  {assessment.estimated_duration && (
                    <>
                      <span>•</span>
                      <span>{assessment.estimated_duration} minutos</span>
                    </>
                  )}
                </div>
              </div>

              {/* Instructions */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Instruções:
                </h3>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• Leia atentamente cada questão antes de responder</li>
                  <li>• Marque apenas uma alternativa para questões de múltipla escolha</li>
                  <li>• Use caneta azul ou preta para respostas dissertativas</li>
                  <li>• Não é permitido o uso de calculadora</li>
                  {assessment.estimated_duration && (
                    <li>• Tempo estimado: {assessment.estimated_duration} minutos</li>
                  )}
                </ul>
              </div>
            </div>

            {/* Questions */}
            <div className="space-y-6">
              {assessment.questions && assessment.questions.length > 0 ? (
                assessment.questions.map((question, index) => renderQuestion(question, index))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    Nenhuma questão disponível para preview
                  </p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                <p>Avaliação criada com Atividade Pronta</p>
                <p>www.atvpronta.com.br</p>
              </div>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {assessment.questions?.length || 0} questões • {assessment.disciplina} • {assessment.serie}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              >
                Fechar
              </button>
              <button
                onClick={onDownload}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Baixar Avaliação</span>
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default PublicAssessmentPreview
