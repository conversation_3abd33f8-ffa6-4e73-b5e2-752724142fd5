import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { ChevronRight, Home } from 'lucide-react'
import { BreadcrumbItem } from '../../types/public'

interface PublicBreadcrumbsProps {
  items: BreadcrumbItem[]
  className?: string
}

/**
 * Componente de breadcrumbs para navegação em páginas públicas
 */
const PublicBreadcrumbs: React.FC<PublicBreadcrumbsProps> = ({
  items,
  className = ''
}) => {
  // Always include home as first item if not present
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Início', href: '/', current: false },
    ...items
  ]

  return (
    <nav 
      className={`flex ${className}`} 
      aria-label="Breadcrumb"
    >
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <ChevronRight 
                className="w-4 h-4 text-gray-400 mx-1" 
                aria-hidden="true" 
              />
            )}
            
            {item.current ? (
              <span 
                className="text-sm font-medium text-gray-500 dark:text-gray-400"
                aria-current="page"
              >
                {index === 0 && (
                  <Home className="w-4 h-4 mr-1 inline" aria-hidden="true" />
                )}
                {item.label}
              </span>
            ) : (
              <Link
                to={item.href}
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
              >
                {index === 0 && (
                  <Home className="w-4 h-4 mr-1" aria-hidden="true" />
                )}
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default PublicBreadcrumbs
