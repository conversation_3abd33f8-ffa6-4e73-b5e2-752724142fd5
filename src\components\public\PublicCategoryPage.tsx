import React, { useMemo } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { usePublicCategories } from '../../hooks/usePublicAssessments'
import { useCategorySEO } from '../../hooks/useSEOMetadata'
import PublicAssessmentListContent from './PublicAssessmentListContent'
import SEOHead from './SEOHead'
import PublicBreadcrumbs from './PublicBreadcrumbs'
import PublicLayout from './PublicLayout'

/**
 * Página de categoria pública de avaliações
 */
const PublicCategoryPage: React.FC = () => {
  const { categorySlug } = useParams<{ categorySlug: string }>()
  const { data: categories = [] } = usePublicCategories()
  
  // Find category by slug
  const category = useMemo(() => {
    return categories.find(cat => cat.slug === categorySlug)
  }, [categories, categorySlug])

  // SEO data
  const { seoData } = useCategorySEO(categorySlug!, category?.name)

  // Breadcrumbs
  const breadcrumbs = useMemo(() => [
    { label: 'Avaliações', href: '/avaliacoes', current: false },
    { 
      label: category?.name || categorySlug || 'Categoria', 
      href: `/avaliacoes/categoria/${categorySlug}`, 
      current: true 
    }
  ], [category, categorySlug])

  if (!categorySlug) {
    return <div>Categoria não encontrada</div>
  }

  return (
    <PublicLayout>
      {/* SEO Head */}
      {seoData && (
        <SEOHead
          metadata={seoData}
          breadcrumbs={breadcrumbs}
        />
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumbs */}
        <PublicBreadcrumbs items={breadcrumbs} className="mb-6" />

        {/* Category Header */}
        {category && (
          <div className="mb-8 text-center">
            <div className="flex items-center justify-center mb-4">
              {category.icon && (
                <span className="text-4xl mr-3">{category.icon}</span>
              )}
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Avaliações de {category.name}
              </h1>
            </div>
            {category.description && (
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                {category.description}
              </p>
            )}
          </div>
        )}

        {/* Assessment List with Category Filter */}
        <PublicAssessmentListContent
          initialFilters={{ category: categorySlug }}
          showHeader={false}
        />
      </div>
    </PublicLayout>
  )
}

export default PublicCategoryPage
