import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Mail, 
  MapPin, 
  BookOpen, 
  GraduationCap, 
  FileText, 
  Users,
  Star,
  Award,
  ExternalLink
} from 'lucide-react'

/**
 * Rodapé público para páginas de SEO
 * Inclui links importantes, informações da empresa e recursos educacionais
 */
const PublicFooter: React.FC = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Coluna 1 - Sobre a Atividade Pronta */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AP</span>
              </div>
              <span className="text-xl font-bold">Atividade Pronta</span>
            </div>
            <p className="text-gray-400 mb-6">
              Plataforma completa para professores criarem e gerenciarem avaliações educacionais com banco de questões inteligente e geração de PDFs otimizados.
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center text-sm text-gray-400">
                <Users className="w-4 h-4 mr-2" />
                <span>+10.000 professores</span>
              </div>
            </div>
          </div>
          
          {/* Coluna 2 - Recursos e Funcionalidades */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Recursos</h3>
            <ul className="space-y-4">
              <li>
                <Link 
                  to="/avaliacoes" 
                  className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <BookOpen className="w-4 h-4 mr-3" />
                  Banco de Avaliações
                </Link>
              </li>
              <li>
                <a
                  href="/#features"
                  className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <FileText className="w-4 h-4 mr-3" />
                  Editor de Avaliações
                </a>
              </li>
              <li>
                <a
                  href="/#features"
                  className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <GraduationCap className="w-4 h-4 mr-3" />
                  Banco de Questões
                </a>
              </li>
              <li>
                <a
                  href="/#features"
                  className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <Award className="w-4 h-4 mr-3" />
                  Modelos Prontos
                </a>
              </li>
              <li>
                <a
                  href="/#features"
                  className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <Star className="w-4 h-4 mr-3" />
                  Geração com IA
                </a>
              </li>
            </ul>
          </div>
          
          {/* Coluna 3 - Categorias Populares */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Categorias Populares</h3>
            <ul className="space-y-4">
              <li>
                <Link 
                  to="/avaliacoes?disciplina=Matemática" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Matemática
                </Link>
              </li>
              <li>
                <Link 
                  to="/avaliacoes?disciplina=Português" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Língua Portuguesa
                </Link>
              </li>
              <li>
                <Link 
                  to="/avaliacoes?disciplina=História" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  História
                </Link>
              </li>
              <li>
                <Link 
                  to="/avaliacoes?disciplina=Geografia" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Geografia
                </Link>
              </li>
              <li>
                <Link 
                  to="/avaliacoes?disciplina=Ciências" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Ciências
                </Link>
              </li>
              <li>
                <Link 
                  to="/avaliacoes?serie=Ensino Fundamental I" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Ensino Fundamental I
                </Link>
              </li>
              <li>
                <Link
                  to="/avaliacoes?serie=Ensino Fundamental II"
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Ensino Fundamental II
                </Link>
              </li>
              <li>
                <Link
                  to="/avaliacoes?serie=Ensino Médio"
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Ensino Médio
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Coluna 4 - Empresa e Contato */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Empresa</h3>
            <ul className="space-y-4">
              <li>
                <a
                  href="/#about"
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Sobre Nós
                </a>
              </li>
              <li>
                <a
                  href="/#contact"
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Contato
                </a>
              </li>
              <li>
                <a
                  href="/#pricing"
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Planos e Preços
                </a>
              </li>
              <li>
                <Link 
                  to="/terms" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link 
                  to="/privacy" 
                  className="text-gray-400 hover:text-gray-300 transition-colors"
                >
                  Política de Privacidade
                </Link>
              </li>
            </ul>
            
            <div className="mt-8">
              <h4 className="text-sm font-semibold mb-4">Contato</h4>
              <div className="space-y-3">
                <div className="flex items-start">
                  <Mail className="w-4 h-4 text-gray-400 mr-3 mt-0.5" />
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-gray-400 hover:text-gray-300 transition-colors text-sm"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-start">
                  <MapPin className="w-4 h-4 text-gray-400 mr-3 mt-0.5" />
                  <span className="text-gray-400 text-sm">
                    Brasil - Atendimento online
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Seção inferior - Copyright e Links Legais */}
        <div className="border-t border-gray-800 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm mb-4 md:mb-0">
              &copy; {currentYear} Atividade Pronta. Todos os direitos reservados.
            </p>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6">
              <Link 
                to="/terms" 
                className="text-gray-500 hover:text-gray-400 text-sm transition-colors"
              >
                Termos
              </Link>
              <Link 
                to="/privacy" 
                className="text-gray-500 hover:text-gray-400 text-sm transition-colors"
              >
                Privacidade
              </Link>
              <Link 
                to="/cookies" 
                className="text-gray-500 hover:text-gray-400 text-sm transition-colors"
              >
                Cookies
              </Link>
              <a 
                href="/sitemap.xml" 
                className="text-gray-500 hover:text-gray-400 text-sm transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                Sitemap
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default PublicFooter
