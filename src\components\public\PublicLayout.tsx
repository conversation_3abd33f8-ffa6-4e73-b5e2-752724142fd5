import React, { useEffect } from 'react'
import PublicHeader from './PublicHeader'
import PublicFooter from './PublicFooter'
import { useTheme } from '../../contexts/ThemeContext'

interface PublicLayoutProps {
  children: React.ReactNode
  className?: string
}

/**
 * Layout wrapper para páginas públicas de SEO
 * Combina PublicHeader + conteúdo + PublicFooter
 */
const PublicLayout: React.FC<PublicLayoutProps> = ({
  children,
  className = ''
}) => {
  const { theme } = useTheme()

  // Apply theme to document element
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else if (theme === 'light') {
      document.documentElement.classList.remove('dark')
    } else if (theme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }
  }, [theme])

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header público */}
      <PublicHeader />

      {/* Conteúdo principal */}
      <main className={`flex-1 ${className}`}>
        {children}
      </main>

      {/* Footer público */}
      <PublicFooter />
    </div>
  )
}

export default PublicLayout
