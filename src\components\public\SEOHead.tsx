import React from 'react'
import * as ReactHelmetAsync from 'react-helmet-async'
const { Helmet } = ReactHelmetAsync
import { SEOMetadata, BreadcrumbItem } from '../../types/public'

interface SEOHeadProps {
  metadata: SEOMetadata
  schemaMarkup?: Record<string, any>
  breadcrumbs?: BreadcrumbItem[]
  additionalMeta?: Array<{
    name?: string
    property?: string
    content: string
  }>
}

/**
 * Componente para gerenciar meta tags SEO de forma centralizada
 */
const SEOHead: React.FC<SEOHeadProps> = ({
  metadata,
  schemaMarkup,
  breadcrumbs,
  additionalMeta = []
}) => {
  // Generate breadcrumb schema markup
  const breadcrumbSchema = breadcrumbs ? {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.label,
      item: item.href.startsWith('http') ? item.href : `https://atvpronta.com.br${item.href}`
    }))
  } : null

  // Combine all schema markup
  const allSchemas = [schemaMarkup, breadcrumbSchema].filter(Boolean)

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{metadata.title}</title>
      <meta name="description" content={metadata.description} />
      <meta name="keywords" content={metadata.keywords.join(', ')} />
      <link rel="canonical" href={metadata.canonicalUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={metadata.title} />
      <meta property="og:description" content={metadata.description} />
      <meta property="og:type" content={metadata.ogType || 'website'} />
      <meta property="og:url" content={metadata.canonicalUrl} />
      <meta property="og:site_name" content="Atividade Pronta" />
      <meta property="og:locale" content="pt_BR" />
      
      {metadata.ogImage && (
        <>
          <meta property="og:image" content={metadata.ogImage} />
          <meta property="og:image:width" content="1200" />
          <meta property="og:image:height" content="630" />
          <meta property="og:image:alt" content={metadata.title} />
        </>
      )}

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={metadata.twitterCard || 'summary_large_image'} />
      <meta name="twitter:title" content={metadata.title} />
      <meta name="twitter:description" content={metadata.description} />
      <meta name="twitter:site" content="@atividadepronta" />
      <meta name="twitter:creator" content="@atividadepronta" />
      
      {metadata.ogImage && (
        <meta name="twitter:image" content={metadata.ogImage} />
      )}

      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Language and Region */}
      <meta httpEquiv="content-language" content="pt-BR" />
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#3B82F6" />
      <meta name="msapplication-TileColor" content="#3B82F6" />
      
      {/* Additional Custom Meta Tags */}
      {additionalMeta.map((meta, index) => (
        <meta
          key={index}
          {...(meta.name ? { name: meta.name } : {})}
          {...(meta.property ? { property: meta.property } : {})}
          content={meta.content}
        />
      ))}

      {/* Schema.org JSON-LD */}
      {allSchemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 2)
          }}
        />
      ))}

      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Alternative Languages (if applicable) */}
      <link rel="alternate" hrefLang="pt-BR" href={metadata.canonicalUrl} />
      <link rel="alternate" hrefLang="x-default" href={metadata.canonicalUrl} />
    </Helmet>
  )
}

export default SEOHead
