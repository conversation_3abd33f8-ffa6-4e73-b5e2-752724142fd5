import React, { useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Plus, Trash2, Save, Sparkles } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database.ts'
import { useSubscription } from '../../contexts/SubscriptionContext.tsx'
import { useAuth } from '../../contexts/AuthContext.tsx'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'

type QuestionInsert = Database['public']['Tables']['questions']['Insert']
type QuestionStatus = Database['public']['Tables']['questions']['Row']['status']

const questionSchema = z.object({
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  topico: z.string().min(1, 'Tópico é obrigatório'),
  subtopico: z.string().optional(),
  dificuldade: z.enum(['Fácil', 'Médio', 'Difícil']),
  tipo: z.enum(['multipla_escolha', 'dissertativa', 'verdadeiro_falso']),
  competencia_bncc: z.string().optional(),
  enunciado: z.string().min(10, 'Enunciado deve ter pelo menos 10 caracteres'),
  alternativas: z.array(z.string()).optional(),
  resposta_correta: z.string().min(1, 'Resposta correta é obrigatória'),
  explicacao: z.string().min(10, 'Explicação deve ter pelo menos 10 caracteres'),
  tags: z.array(z.string()).default([]),
  visibility: z.enum(['private', 'public', 'school']),
})

type QuestionFormData = z.infer<typeof questionSchema>

interface CreateQuestionModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: QuestionInsert) => void
  isLoading: boolean
}



const CreateQuestionModal: React.FC<CreateQuestionModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading
}) => {
  const { canAccess } = useSubscription()
  const { profile } = useAuth()
  const [newTag, setNewTag] = useState('')

  const {
    register,
    handleSubmit,
    watch,
    control,
    setValue,
    reset,
    formState: { errors }
  } = useForm<QuestionFormData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      alternativas: ['', '', '', ''],
      tags: [],
      visibility: 'private'
    }
  })

  const { fields, append, remove } = useFieldArray<string>({
    control,
    name: 'alternativas' as never
  })

  const watchedTipo = watch('tipo')
  const watchedTags = watch('tags')
  const watchedVisibility = watch('visibility')

  const handleClose = () => {
    reset()
    setNewTag('')
    onClose()
  }

  const handleFormSubmit = (data: QuestionFormData) => {
    let is_public_val: boolean = false
    let is_shared_with_school_val: boolean = false
    let status_val: QuestionStatus = 'approved'
    let school_id_val: string | null = null

    if (data.visibility === 'public') {
      is_public_val = true
      status_val = 'pending'
    } else if (data.visibility === 'school' && profile?.school_id) {
      is_shared_with_school_val = true
      school_id_val = profile.school_id
      status_val = 'approved'
    } else {
      is_public_val = false
      status_val = 'approved'
    }

    const questionData: QuestionInsert = {
      ...data,
      alternativas: data.tipo === 'multipla_escolha' ? data.alternativas?.filter(alt => alt.trim()) : null,
      tags: data.tags || [],
      uso_count: 0,
      rating: 0,
      rating_count: 0,
      is_verified: false,
      metadata: {},
      status: status_val,
      is_public: is_public_val,
      is_shared_with_school: is_shared_with_school_val,
      school_id: school_id_val,
    }

    onSubmit(questionData)
    handleClose()
  }

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      setValue('tags', [...watchedTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setValue('tags', watchedTags.filter((_, i) => i !== index))
  }

  const generateWithAI = () => {
    console.log('Generate with AI')
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-gray-800"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Nova Questão</h2>
              <div className="flex items-center space-x-2">
                {canAccess('ai_generation') && (
                  <button
                    type="button"
                    onClick={generateWithAI}
                    className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Sparkles className="w-4 h-4" />
                    <span>Gerar com IA</span>
                  </button>
                )}
                <button
                  onClick={handleClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors dark:hover:bg-gray-700"
                  title="Fechar modal"
                  aria-label="Fechar modal"
                >
                  <X className="w-5 h-5 dark:text-gray-400" />
                </button>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Disciplina *
                </label>
                <select
                  {...register('disciplina')}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  title="Selecionar disciplina"
                  aria-label="Selecionar disciplina"
                >
                  <option value="">Selecione...</option>
                  {DISCIPLINAS.map((disciplina) => (
                    <option key={disciplina} value={disciplina}>
                      {disciplina}
                    </option>
                  ))}
                </select>
                {errors.disciplina && (
                  <p className="mt-1 text-sm text-red-600">{errors.disciplina.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Série *
                </label>
                <select
                  {...register('serie')}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  title="Selecionar série"
                  aria-label="Selecionar série"
                >
                  <option value="">Selecione...</option>
                  {SERIES.map((serie) => (
                    <option key={serie} value={serie}>
                      {serie}
                    </option>
                  ))}
                </select>
                {errors.serie && (
                  <p className="mt-1 text-sm text-red-600">{errors.serie.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Dificuldade *
                </label>
                <select
                  {...register('dificuldade')}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                >
                  <option value="">Selecione...</option>
                  <option value="Fácil">Fácil</option>
                  <option value="Médio">Médio</option>
                  <option value="Difícil">Difícil</option>
                </select>
                {errors.dificuldade && (
                  <p className="mt-1 text-sm text-red-600">{errors.dificuldade.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Tópico *
                </label>
                <input
                  {...register('topico')}
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder="Ex: Frações"
                />
                {errors.topico && (
                  <p className="mt-1 text-sm text-red-600">{errors.topico.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Subtópico
                </label>
                <input
                  {...register('subtopico')}
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder="Ex: Operações com frações"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Tipo de Questão *
                </label>
                <select
                  {...register('tipo')}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                >
                  <option value="">Selecione...</option>
                  <option value="multipla_escolha">Múltipla Escolha</option>
                  <option value="dissertativa">Dissertativa</option>
                  <option value="verdadeiro_falso">Verdadeiro/Falso</option>
                </select>
                {errors.tipo && (
                  <p className="mt-1 text-sm text-red-600">{errors.tipo.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Competência BNCC
                </label>
                <input
                  {...register('competencia_bncc')}
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder="Ex: EF06MA07"
                />
              </div>
            </div>

            {/* Question Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                Enunciado *
              </label>
              <textarea
                {...register('enunciado')}
                rows={4}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Digite o enunciado da questão..."
              />
              {errors.enunciado && (
                <p className="mt-1 text-sm text-red-600">{errors.enunciado.message}</p>
              )}
            </div>

            {/* Alternatives for Multiple Choice */}
            {watchedTipo === 'multipla_escolha' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                  Alternativas
                </label>
                <div className="space-y-3">
                  {fields.map((field, index) => (
                    <div key={field.id} className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-600 w-6 dark:text-gray-400">
                        {String.fromCharCode(97 + index)})
                      </span>
                      <input
                        {...register(`alternativas.${index}`)}
                        type="text"
                        className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                        placeholder={`Alternativa ${String.fromCharCode(97 + index).toUpperCase()}`}
                      />
                      {fields.length > 2 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors dark:hover:bg-red-900/20"
                          title="Remover alternativa"
                          aria-label="Remover alternativa"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  {fields.length < 5 && (
                    <button
                      type="button"
                      onClick={() => append('')}
                      className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Adicionar alternativa</span>
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Correct Answer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                Resposta Correta *
              </label>
              {watchedTipo === 'verdadeiro_falso' ? (
                <div className="flex items-center gap-6">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      value="verdadeiro"
                      {...register('resposta_correta')}
                      checked={watch('resposta_correta') === 'verdadeiro'}
                    />
                    <span>Verdadeiro</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      value="falso"
                      {...register('resposta_correta')}
                      checked={watch('resposta_correta') === 'falso'}
                    />
                    <span>Falso</span>
                  </label>
                </div>
              ) : (
                <input
                  {...register('resposta_correta')}
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder={
                    watchedTipo === 'multipla_escolha' 
                      ? "Ex: a, b, c, d" 
                      : "Digite a resposta correta"
                  }
                />
              )}
              {errors.resposta_correta && (
                <p className="mt-1 text-sm text-red-600">{errors.resposta_correta.message}</p>
              )}
            </div>

            {/* Explanation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                Explicação *
              </label>
              <textarea
                {...register('explicacao')}
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Explique a resolução da questão..."
              />
              {errors.explicacao && (
                <p className="mt-1 text-sm text-red-600">{errors.explicacao.message}</p>
              )}
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {watchedTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm dark:bg-blue-900/30 dark:text-blue-400"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Remover tag"
                      aria-label="Remover tag"
                    >
                      <X className="w-3 h-3 dark:text-blue-400 dark:hover:text-blue-300" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag((e.target as HTMLInputElement).value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder="Digite uma tag e pressione Enter"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300"
                  title="Adicionar tag"
                  aria-label="Adicionar tag"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Visibility Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 dark:text-gray-300">
                Visibilidade *
              </label>
              <div className="mt-2 space-y-2">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    {...register('visibility')}
                    value="private"
                    className="form-radio text-blue-600 h-4 w-4"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Privada (somente eu)</span>
                </label>
                <label className="inline-flex items-center ml-6">
                  <input
                    type="radio"
                    {...register('visibility')}
                    value="public"
                    className="form-radio text-blue-600 h-4 w-4"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Pública (requer aprovação do admin)</span>
                </label>
                {profile?.is_school_admin && profile?.school_id && (
                  <label className="inline-flex items-center ml-6">
                    <input
                      type="radio"
                      {...register('visibility')}
                      value="school"
                      className="form-radio text-blue-600 h-4 w-4"
                    />
                    <span className="ml-2 text-gray-700 dark:text-gray-300">Compartilhar com minha escola</span>
                  </label>
                )}
              </div>
              {errors.visibility && (
                <p className="mt-1 text-sm text-red-600">{errors.visibility.message}</p>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors"
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isLoading ? 'Salvando...' : 'Salvar Questão'}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default CreateQuestionModal