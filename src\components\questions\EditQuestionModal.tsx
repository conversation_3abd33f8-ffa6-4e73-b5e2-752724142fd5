import React, { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Plus, Trash2, Save, CheckCircle, XCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Database } from '../../types/database.ts'
import toast from 'react-hot-toast'

type Question = Database['public']['Tables']['questions']['Row']
type QuestionUpdate = Database['public']['Tables']['questions']['Update']

const questionSchema = z.object({
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  topico: z.string().min(1, 'Tópico é obrigatório'),
  subtopico: z.string().optional(),
  dificuldade: z.enum(['<PERSON><PERSON>cil', 'Médio', 'Dif<PERSON>cil']),
  tipo: z.enum(['multipla_escolha', 'dissertativa', 'verdadeiro_falso']),
  competencia_bncc: z.string().optional(),
  enunciado: z.string().min(10, 'Enunciado deve ter pelo menos 10 caracteres'),
  alternativas: z.array(z.string()).optional(),
  resposta_correta: z.string().min(1, 'Resposta correta é obrigatória'),
  explicacao: z.string().min(10, 'Explicação deve ter pelo menos 10 caracteres'),
  tags: z.array(z.string()).default([]),
  is_public: z.boolean().default(true),
  is_verified: z.boolean().default(false)
})

type QuestionFormData = z.infer<typeof questionSchema>

interface EditQuestionModalProps {
  isOpen: boolean
  onClose: () => void
  question: Question | null
  onSave: (questionId: string, updates: QuestionUpdate) => Promise<void>
  isSaving: boolean
}

const DISCIPLINAS = [
  'Português', 'Matemática', 'História', 'Geografia', 'Ciências', 'Inglês', 'Espanhol', 'Artes', 'Educação Física', 'Sociologia', 'Filosofia'
]
const SERIES = [
  'Educação Infantil',
  '1º Ano EF', '2º Ano EF', '3º Ano EF', '4º Ano EF', '5º Ano EF', '6º Ano EF', '7º Ano EF', '8º Ano EF', '9º Ano EF',
  '1º Ano EM', '2º Ano EM', '3º Ano EM',
  'Ensino Superior', 'Pós-graduação', 'Cursos Técnicos'
]

const EditQuestionModal: React.FC<EditQuestionModalProps> = ({
  isOpen,
  onClose,
  question,
  onSave,
  isSaving
}) => {
  const [newTag, setNewTag] = useState('')

  const {
    register,
    handleSubmit,
    watch,
    control,
    reset,
    setValue,
    formState: { errors }
  } = useForm<QuestionFormData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      disciplina: '',
      serie: '',
      topico: '',
      subtopico: '',
      dificuldade: 'Médio',
      tipo: 'multipla_escolha',
      competencia_bncc: '',
      enunciado: '',
      alternativas: ['', '', '', ''],
      resposta_correta: '',
      explicacao: '',
      tags: [],
      is_public: true,
      is_verified: false
    }
  })

  const { fields, append, remove } = useFieldArray<string>({
    control,
    name: 'alternativas' as never
  })

  const watchedTipo = watch('tipo')
  const watchedTags = watch('tags')

  // Reset form when question changes
  useEffect(() => {
    if (question) {
      reset({
        disciplina: question.disciplina,
        serie: question.serie,
        topico: question.topico,
        subtopico: question.subtopico || '',
        dificuldade: question.dificuldade as 'Fácil' | 'Médio' | 'Difícil',
        tipo: question.tipo as 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso',
        competencia_bncc: question.competencia_bncc || '',
        enunciado: question.enunciado,
        alternativas: question.alternativas || ['', '', '', ''],
        resposta_correta: question.resposta_correta,
        explicacao: question.explicacao,
        tags: question.tags || [],
        is_public: question.is_public,
        is_verified: question.is_verified
      })
    }
  }, [question, reset])

  const onSubmit = async (data: QuestionFormData) => {
    if (!question) return

    try {
      const questionData: QuestionUpdate = {
        ...data,
        alternativas: data.tipo === 'multipla_escolha' ? data.alternativas?.filter(alt => alt.trim()) : null,
        subtopico: data.subtopico || null,
        competencia_bncc: data.competencia_bncc || null
      }

      await onSave(question.id, questionData)
      onClose()
    } catch (error) {
      console.error('Error saving question:', error)
      toast.error('Erro ao salvar questão')
    }
  }

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      setValue('tags', [...watchedTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setValue('tags', watchedTags.filter((_, i) => i !== index))
  }

  if (!isOpen || !question) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Editar Questão</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                title="Fechar modal"
                aria-label="Fechar modal"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Disciplina *
                </label>
                <select
                  {...register('disciplina')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  title="Selecionar disciplina"
                  aria-label="Selecionar disciplina"
                >
                  <option value="">Selecione...</option>
                  {DISCIPLINAS.map((disciplina) => (
                    <option key={disciplina} value={disciplina}>
                      {disciplina}
                    </option>
                  ))}
                </select>
                {errors.disciplina && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.disciplina.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Série *
                </label>
                <select
                  {...register('serie')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  title="Selecionar série"
                  aria-label="Selecionar série"
                >
                  <option value="">Selecione...</option>
                  {SERIES.map((serie) => (
                    <option key={serie} value={serie}>
                      {serie}
                    </option>
                  ))}
                </select>
                {errors.serie && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.serie.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Dificuldade *
                </label>
                <select
                  {...register('dificuldade')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="Fácil">Fácil</option>
                  <option value="Médio">Médio</option>
                  <option value="Difícil">Difícil</option>
                </select>
                {errors.dificuldade && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dificuldade.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tópico *
                </label>
                <input
                  {...register('topico')}
                  type="text"
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: Frações"
                />
                {errors.topico && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.topico.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subtópico
                </label>
                <input
                  {...register('subtopico')}
                  type="text"
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: Operações com frações"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Questão *
                </label>
                <select
                  {...register('tipo')}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="multipla_escolha">Múltipla Escolha</option>
                  <option value="dissertativa">Dissertativa</option>
                  <option value="verdadeiro_falso">Verdadeiro/Falso</option>
                </select>
                {errors.tipo && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.tipo.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Competência BNCC
                </label>
                <input
                  {...register('competencia_bncc')}
                  type="text"
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: EF06MA07"
                />
              </div>
            </div>

            {/* Question Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Enunciado *
              </label>
              <textarea
                {...register('enunciado')}
                rows={4}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Digite o enunciado da questão..."
              />
              {errors.enunciado && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.enunciado.message}</p>
              )}
            </div>

            {/* Alternatives for Multiple Choice */}
            {watchedTipo === 'multipla_escolha' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Alternativas
                </label>
                <div className="space-y-3">
                  {fields.map((field, index) => (
                    <div key={field.id} className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-6">
                        {String.fromCharCode(97 + index)})
                      </span>
                      <input
                        {...register(`alternativas.${index}`)}
                        type="text"
                        className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder={`Alternativa ${String.fromCharCode(97 + index).toUpperCase()}`}
                      />
                      {fields.length > 2 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="p-2 text-red-500 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                          title="Remover alternativa"
                          aria-label="Remover alternativa"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  {fields.length < 5 && (
                    <button
                      type="button"
                      onClick={() => append('')}
                      className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Adicionar alternativa</span>
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Correct Answer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Resposta Correta *
              </label>
              <input
                {...register('resposta_correta')}
                type="text"
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder={
                  watchedTipo === 'multipla_escolha' 
                    ? "Ex: a, b, c, d" 
                    : "Digite a resposta correta"
                }
              />
              {errors.resposta_correta && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.resposta_correta.message}</p>
              )}
            </div>

            {/* Explanation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Explicação *
              </label>
              <textarea
                {...register('explicacao')}
                rows={3}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Explique a resolução da questão..."
              />
              {errors.explicacao && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.explicacao.message}</p>
              )}
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {watchedTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full text-sm"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                      title="Remover tag"
                      aria-label="Remover tag"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag((e.target as HTMLInputElement).value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Digite uma tag e pressione Enter"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                  title="Adicionar tag"
                  aria-label="Adicionar tag"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Visibility and Verification */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('is_public')}
                    className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Questão Pública
                  </span>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                  Questões públicas são visíveis para todos os usuários.
                </p>
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('is_verified')}
                    className="rounded text-green-600 focus:ring-green-500 dark:bg-gray-600 dark:border-gray-500"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Questão Verificada
                  </span>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                  Questões verificadas passaram por revisão de qualidade.
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:disabled:bg-blue-800/50 text-white rounded-lg transition-colors"
              >
                {isSaving ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="w-5 h-5" />
                )}
                <span>Salvar Questão</span>
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default EditQuestionModal