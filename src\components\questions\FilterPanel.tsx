import React, { useState } from 'react'
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext.tsx'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected';
  status?: 'pending' | 'approved' | 'rejected' | 'all';
}

interface FilterPanelProps {
  filters: QuestionFilters
  onFilterChange: (key: keyof QuestionFilters, value: string | string[] | boolean) => void
  onClearFilters: () => void
  hasActiveFilters: boolean
  isAdmin: boolean;
  isSchoolAdmin: boolean;
}

// Criar estrutura de disciplinas usando as constantes centralizadas
const DISCIPLINAS_MAP = DISCIPLINAS.reduce((acc, disciplina) => {
  acc[disciplina] = {
    series: SERIES,
    topicos: {}
  }
  return acc
}, {} as Record<string, { series: readonly string[], topicos: {} }>)

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
  isAdmin,
  isSchoolAdmin,
}) => {
  const { user, profile } = useAuth()
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const selectedDisciplina = filters.disciplina
  const disciplinaData = selectedDisciplina ? DISCIPLINAS_MAP[selectedDisciplina as keyof typeof DISCIPLINAS_MAP] : null

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Filtros</h3>
          {hasActiveFilters && (
            <span className="bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs px-2 py-0.5 rounded-full">
              {Object.values(filters).filter(v => v && v !== '' && v !== 'all' && (!Array.isArray(v) || v.length > 0)).length}
            </span>
          )}
        </div>
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 flex items-center space-x-1 transition-colors px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <X className="w-3 h-3" />
            <span>Limpar</span>
          </button>
        )}
      </div>

      {/* Main Filters - Single Row Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        {/* Disciplina */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Disciplina
          </label>
          <select
            value={filters.disciplina || ''}
            onChange={(e) => {
              onFilterChange('disciplina', (e.target as HTMLSelectElement).value)
              onFilterChange('serie', '') // Clear série when disciplina changes
            }}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            title="Filtrar por disciplina"
            aria-label="Filtrar por disciplina"
          >
            <option value="">Todas</option>
            {Object.keys(DISCIPLINAS_MAP).map((disciplina) => (
              <option key={disciplina} value={disciplina}>
                {disciplina}
              </option>
            ))}
          </select>
        </div>

        {/* Série */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Série
          </label>
          <select
            value={filters.serie || ''}
            onChange={(e) => onFilterChange('serie', (e.target as HTMLSelectElement).value)}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            title="Filtrar por série"
            aria-label="Filtrar por série"
            disabled={!disciplinaData}
          >
            <option value="">Todas</option>
            {disciplinaData?.series.map((serie) => (
              <option key={serie} value={serie}>
                {serie}
              </option>
            ))}
          </select>
        </div>

        {/* Dificuldade */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Dificuldade
          </label>
          <select
            value={filters.dificuldade || ''}
            onChange={(e) => onFilterChange('dificuldade', (e.target as HTMLSelectElement).value)}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            title="Filtrar por dificuldade"
            aria-label="Filtrar por dificuldade"
          >
            <option value="">Todas</option>
            <option value="Fácil">Fácil</option>
            <option value="Médio">Médio</option>
            <option value="Difícil">Difícil</option>
          </select>
        </div>

        {/* Tipo */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tipo
          </label>
          <select
            value={filters.tipo || ''}
            onChange={(e) => onFilterChange('tipo', (e.target as HTMLSelectElement).value)}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            title="Filtrar por tipo"
            aria-label="Filtrar por tipo"
          >
            <option value="">Todos</option>
            <option value="multipla_escolha">Múltipla Escolha</option>
            <option value="dissertativa">Dissertativa</option>
            <option value="verdadeiro_falso">Verdadeiro/Falso</option>
          </select>
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center justify-between w-full text-left text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors py-2 px-3 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20"
          aria-expanded={showAdvancedFilters}
          aria-controls="advanced-filters"
        >
          <span className="text-sm font-medium">Filtros Avançados</span>
          {showAdvancedFilters ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>

        <AnimatePresence>
          {showAdvancedFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden mt-3"
              id="advanced-filters"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                {/* Visibilidade */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Visibilidade
                  </label>
                  <select
                    value={filters.visibility_filter || 'all'}
                    onChange={(e) => onFilterChange('visibility_filter', (e.target as HTMLSelectElement).value)}
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    title="Filtrar por visibilidade"
                    aria-label="Filtrar por visibilidade"
                  >
                    <option value="all">Todas</option>
                    <option value="private">Minhas</option>
                    <option value="public">Públicas</option>
                    {profile?.escola && (
                      <option value="school">Da Escola</option>
                    )}
                    {user && (
                      <option value="my_pending_or_rejected">Pendentes/Rejeitadas</option>
                    )}
                  </select>
                </div>

                {/* Status (Admin) */}
                {isAdmin && (
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Status (Admin)
                    </label>
                    <select
                      value={filters.status || 'all'}
                      onChange={(e) => onFilterChange('status', (e.target as HTMLSelectElement).value)}
                      className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                      title="Filtrar por status"
                      aria-label="Filtrar por status"
                    >
                      <option value="all">Todos</option>
                      <option value="pending">Pendente</option>
                      <option value="approved">Aprovada</option>
                      <option value="rejected">Rejeitada</option>
                    </select>
                  </div>
                )}

                {/* Competência BNCC */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Competência BNCC
                  </label>
                  <input
                    type="text"
                    value={filters.competencia_bncc || ''}
                    onChange={(e) => onFilterChange('competencia_bncc', (e.target as HTMLInputElement).value)}
                    placeholder="Ex: EF06MA07"
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    title="Filtrar por competência BNCC"
                    aria-label="Filtrar por competência BNCC"
                  />
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Tags
                  </label>
                  <input
                    type="text"
                    value={filters.tags?.join(', ') || ''}
                    onChange={(e) => onFilterChange('tags', (e.target as HTMLInputElement).value.split(',').map(tag => tag.trim()).filter(tag => tag !== ''))}
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    placeholder="Ex: matemática, álgebra"
                    title="Filtrar por tags"
                    aria-label="Filtrar por tags"
                  />
                </div>

                {/* Criado Após */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Criado Após
                  </label>
                  <input
                    type="date"
                    value={filters.created_after || ''}
                    onChange={(e) => onFilterChange('created_after', (e.target as HTMLInputElement).value)}
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    title="Filtrar por data inicial"
                    aria-label="Filtrar por data inicial"
                  />
                </div>

                {/* Criado Antes */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Criado Antes
                  </label>
                  <input
                    type="date"
                    value={filters.created_before || ''}
                    onChange={(e) => onFilterChange('created_before', (e.target as HTMLInputElement).value)}
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    title="Filtrar por data final"
                    aria-label="Filtrar por data final"
                  />
                </div>

                {/* Autor ID (Admin) */}
                {isAdmin && (
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      ID do Autor
                    </label>
                    <input
                      type="text"
                      value={filters.autor_id || ''}
                      onChange={(e) => onFilterChange('autor_id', (e.target as HTMLInputElement).value)}
                      className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                      placeholder="ID do usuário"
                      title="Filtrar por autor"
                      aria-label="Filtrar por autor"
                    />
                  </div>
                )}

                {/* Verificação */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Verificação
                  </label>
                  <select
                    value={filters.is_verified === true ? 'verified' : filters.is_verified === false ? 'not_verified' : 'all'}
                    onChange={(e) => {
                      const value = e.target.value
                      if (value === 'verified') onFilterChange('is_verified', true)
                      else if (value === 'not_verified') onFilterChange('is_verified', false)
                      else onFilterChange('is_verified', undefined)
                    }}
                    className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
                    title="Filtrar por verificação"
                    aria-label="Filtrar por verificação"
                  >
                    <option value="all">Todas</option>
                    <option value="verified">Verificadas</option>
                    <option value="not_verified">Não Verificadas</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default FilterPanel