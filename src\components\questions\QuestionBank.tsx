import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react'
import { Grid, List, Upload, Filter as FilterIcon, SlidersHorizontal, Search, Plus, Sparkles, X, AlertTriangle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuestions, useFavorites } from '../../hooks/useQuestions'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useAuth } from '../../contexts/AuthContext'
import QuestionCard from './QuestionCard'
import FilterPanel from './FilterPanel'
import CreateQuestionModal from './CreateQuestionModal'
import EditQuestionModal from './EditQuestionModal'
import QuestionDetailModal from '../editor/QuestionDetailModal'
import ImportExport from '../import/ImportExport'
import AIQuestionGenerator from '../ai/AIQuestionGenerator'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'
import { useInView } from 'react-intersection-observer'
import EmptyState from '../common/EmptyState'
import { useQuestionSelection } from '../../contexts/QuestionSelectionContext'
import { useNavigate } from 'react-router-dom'

type Question = Database['public']['Tables']['questions']['Row']
type QuestionUpdate = Database['public']['Tables']['questions']['Update']

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
}

type SortOption = 'newest' | 'oldest' | 'most_used' | 'highest_rated' | 'difficulty_asc' | 'difficulty_desc'

const QuestionBank: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [showImportExport, setShowImportExport] = useState(false)
  const [showAIGenerator, setShowAIGenerator] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [sortBy, setSortBy] = useState<SortOption>('newest')
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState<QuestionFilters>({
    disciplina: '',
    serie: '',
    dificuldade: '',
    tipo: '',
    competencia_bncc: '',
    search: '',
    tags: [],
    visibility_filter: 'all',
    status: 'all'
  })

  const ITEMS_PER_PAGE = 12
  const { canAccess } = useSubscription()
  const { profile, isAdmin, isSchoolAdmin, user, loading: authLoading } = useAuth()
  const currentUserId = profile?.id

  const searchInputRef = useRef<HTMLInputElement>(null)

  // Question selection context (mantido para compatibilidade futura)
  const { selectedIds, clearSelection } = useQuestionSelection()
  const navigate = useNavigate()

  const MAX_SELECTION = 50

  // Infinite scroll state management
  const [allQuestions, setAllQuestions] = useState<Question[]>([])
  const [hasNextPage, setHasNextPage] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  const [showOnboarding, setShowOnboarding] = useState(false)
  useEffect(() => {
    if (typeof window !== 'undefined' && !localStorage.getItem('questionBankOnboarding')) {
      setShowOnboarding(true)
    }
  }, [])
  const handleCloseOnboarding = () => {
    setShowOnboarding(false)
    localStorage.setItem('questionBankOnboarding', '1')
  }

  // Foco automático no input de busca ao abrir a página
  React.useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [])

  // Mapear filtros de visibilidade para as props do useQuestions
  const useQuestionFilters = useMemo(() => {
    const newFilters: Parameters<typeof useQuestions>[0] = {
      ...filters,
      limit: ITEMS_PER_PAGE,
      page,
      autor_id: undefined,
      is_public: undefined,
      is_shared_with_school: undefined,
      school_id: undefined,
      show_my_pending_or_rejected: undefined,
    };

    if (filters.visibility_filter === 'private' && currentUserId) {
      newFilters.autor_id = currentUserId;
    } else if (filters.visibility_filter === 'public') {
      newFilters.is_public = true;
      newFilters.status = 'approved';
    } else if (filters.visibility_filter === 'school' && profile?.escola) {
      newFilters.is_shared_with_school = true;
      newFilters.school_id = profile.escola;
    } else if (filters.visibility_filter === 'my_pending_or_rejected' && currentUserId) {
      newFilters.autor_id = currentUserId;
      newFilters.show_my_pending_or_rejected = true;
      newFilters.status = undefined;
    } else if (filters.visibility_filter === 'all') {
      // Para usuários não-admin, sempre mostrar apenas questões aprovadas
      if (!isAdmin) {
        newFilters.status = 'approved';
      } else {
        // Para admins, respeitar o filtro de status selecionado
        if (filters.status && filters.status !== 'all') {
          newFilters.status = filters.status;
        }
        // Se status for 'all', não definir filtro de status (mostrar todos)
      }
    }

    // Para admins, aplicar filtro de status apenas se não foi definido acima
    if (isAdmin && filters.status !== 'all' && filters.visibility_filter !== 'all') {
      newFilters.status = filters.status;
    }

    newFilters.search = filters.search;
    newFilters.disciplina = filters.disciplina;
    newFilters.serie = filters.serie;
    newFilters.dificuldade = filters.dificuldade;
    newFilters.tipo = filters.tipo;
    newFilters.competencia_bncc = filters.competencia_bncc;
    newFilters.tags = filters.tags;
    newFilters.is_verified = filters.is_verified;
    newFilters.created_after = filters.created_after;
    newFilters.created_before = filters.created_before;

    return newFilters;
  }, [filters, ITEMS_PER_PAGE, page, currentUserId, isAdmin, isSchoolAdmin, profile?.escola]);

  const { questions: currentPageQuestions, totalCount, isLoading, error, refetch, createQuestion, updateQuestion, deleteQuestion, isCreating, isUpdating, isDeleting } = useQuestions(useQuestionFilters)


  const { toggleFavorite, isFavorite } = useFavorites()

  // Accumulate questions for infinite scroll and update hasNextPage
  useEffect(() => {
    if (currentPageQuestions && Array.isArray(currentPageQuestions)) {
      let finalQuestionsLength = 0

      if (page === 1) {
        // First page or filter change - replace all questions
        setAllQuestions(currentPageQuestions)
        finalQuestionsLength = currentPageQuestions.length
      } else {
        // Subsequent pages - append to existing questions
        setAllQuestions(prev => {
          const existingIds = new Set(prev.map(q => q.id))
          const newQuestions = currentPageQuestions.filter(q => !existingIds.has(q.id))
          const updatedQuestions = [...prev, ...newQuestions]
          finalQuestionsLength = updatedQuestions.length
          return updatedQuestions
        })
      }

      // Update hasNextPage based on the current page results
      const hasMore = currentPageQuestions.length === ITEMS_PER_PAGE &&
                     finalQuestionsLength < (totalCount || 0)
      setHasNextPage(hasMore)
      setIsLoadingMore(false)
    }
  }, [currentPageQuestions, page, totalCount, ITEMS_PER_PAGE])

  // Reset questions when filters change
  useEffect(() => {
    setPage(1)
    setAllQuestions([])
    setHasNextPage(true)
    setIsLoadingMore(false)
  }, [filters.search, filters.disciplina, filters.serie, filters.dificuldade, filters.tipo, filters.competencia_bncc, filters.tags, filters.visibility_filter, filters.status])

  // Infinite scroll setup
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.5,
    triggerOnce: false
  })

  // Load more when bottom is reached - fixed infinite scroll logic
  React.useEffect(() => {
    if (inView && !isLoading && !isLoadingMore && hasNextPage &&
        filters.visibility_filter !== 'my_pending_or_rejected' &&
        allQuestions.length > 0) {
      setIsLoadingMore(true)
      setPage(prevPage => prevPage + 1)
    }
  }, [inView, isLoading, isLoadingMore, hasNextPage, filters.visibility_filter, allQuestions.length])

  // Debounce search input com cleanup otimizado
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [searchValue, setSearchValue] = useState(filters.search)

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value) // Update immediately for UI responsiveness

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: value }))
      setPage(1) // Reset to first page on new search
    }, 300)
  }, [])

  // Cleanup do timeout no unmount e sincronização do searchValue
  useEffect(() => {
    setSearchValue(filters.search)
  }, [filters.search])

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [])

  const handleFilterChange = useCallback((key: keyof QuestionFilters, value: string | string[] | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPage(1) // Reset to first page on filter change
  }, [])

  const clearFilters = useCallback(() => {
    const newFilters = {
      disciplina: '',
      serie: '',
      dificuldade: '',
      tipo: '',
      competencia_bncc: '',
      search: '',
      tags: [],
      visibility_filter: 'all' as const,
      status: isAdmin ? 'all' as const : 'approved' as const // Para não-admins, sempre approved
    }

    setFilters(newFilters)
    setSearchValue('') // Clear search input as well
    setPage(1)
    setAllQuestions([]) // Clear current questions to force reload
    setHasNextPage(true)
  }, [isAdmin])

  const handleEditQuestion = useCallback((question: Question) => {
    setSelectedQuestion(question)
    setShowEditModal(true)
  }, [])

  const handleViewQuestionDetail = useCallback((question: Question) => {
    setSelectedQuestion(question)
    setShowDetailModal(true)
  }, [])

  const handleDeleteQuestion = useCallback(async (questionId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta questão? Esta ação não pode ser desfeita.')) {
      try {
        await deleteQuestion(questionId)
      } catch (error) {
        console.error('Error deleting question:', error)
      }
    }
  }, [deleteQuestion])

  const handleUpdateQuestion = useCallback(async (questionId: string, updates: QuestionUpdate) => {
    try {
      await updateQuestion({ id: questionId, updates })
      setShowEditModal(false)
      setSelectedQuestion(null)
    } catch (error) {
      console.error('Error updating question:', error)
    }
  }, [updateQuestion])

  const hasActiveFilters = useMemo(() => {
    // Considerar apenas filtros que realmente filtram o conteúdo
    const activeFilters = {
      disciplina: filters.disciplina,
      serie: filters.serie,
      dificuldade: filters.dificuldade,
      tipo: filters.tipo,
      competencia_bncc: filters.competencia_bncc,
      search: filters.search,
      tags: filters.tags
    }

    return Object.values(activeFilters).some(filter =>
      (Array.isArray(filter) ? filter.length > 0 : filter !== '' && filter !== undefined)
    )
  }, [filters])

  // Sort questions based on selected option
  const sortedQuestions = useMemo(() => {
    if (!allQuestions || !Array.isArray(allQuestions) || !allQuestions.length) return []

    const sorted = [...allQuestions]
    
    switch (sortBy) {
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      case 'most_used':
        return sorted.sort((a, b) => (b.uso_count || 0) - (a.uso_count || 0))
      case 'highest_rated':
        return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
      case 'difficulty_asc':
        const difficultyOrder = { 'Fácil': 1, 'Médio': 2, 'Difícil': 3 }
        return sorted.sort((a, b) => 
          difficultyOrder[a.dificuldade as keyof typeof difficultyOrder] - 
          difficultyOrder[b.dificuldade as keyof typeof difficultyOrder]
        )
      case 'difficulty_desc':
        const difficultyOrderDesc = { 'Fácil': 1, 'Médio': 2, 'Difícil': 3 }
        return sorted.sort((a, b) => 
          difficultyOrderDesc[b.dificuldade as keyof typeof difficultyOrderDesc] - 
          difficultyOrderDesc[a.dificuldade as keyof typeof difficultyOrderDesc]
        )
      case 'newest':
      default:
        return sorted.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }
  }, [allQuestions, sortBy])



  if (error) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <EmptyState
          icon={AlertTriangle}
          title="Erro ao carregar questões"
          description="Ocorreu um erro ao buscar as questões. Verifique sua conexão ou tente novamente."
          action={{
            label: 'Tentar novamente',
            onClick: () => refetch()
          }}
        />
      </div>
    )
  }



  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900">
      <div className="mb-4 lg:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">Banco de Questões</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {isLoading && page === 1 ? 'Carregando questões...' : `${allQuestions.length} questões encontradas`}
            </p>
          </div>

          <div className="flex items-center space-x-2 lg:space-x-3">
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 lg:px-4 py-2 rounded-lg transition-colors whitespace-nowrap font-medium"
              title="Criar Nova Questão"
              aria-label="Criar Nova Questão"
            >
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">Nova Questão</span>
            </button>

            {canAccess('ai_generation') && (
              <button
                onClick={() => setShowAIGenerator(true)}
                className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-3 lg:px-4 py-2 rounded-lg transition-colors whitespace-nowrap font-medium"
                title="Gerar Questão com IA"
                aria-label="Gerar Questão com IA"
              >
                <Sparkles className="w-4 h-4" />
                <span className="hidden sm:inline">IA</span>
              </button>
            )}

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors whitespace-nowrap ${
                showFilters
                  ? 'bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              title={showFilters ? "Ocultar filtros" : "Mostrar filtros"}
              aria-label={showFilters ? "Ocultar filtros" : "Mostrar filtros"}
              aria-expanded={showFilters}
              aria-controls="filter-panel"
            >
              <SlidersHorizontal className="w-4 h-4" />
              <span className="hidden sm:inline">Filtros</span>
              {hasActiveFilters && (
                <span
                  className="w-2 h-2 bg-blue-500 rounded-full"
                  aria-label="Filtros ativos"
                  title="Há filtros aplicados"
                ></span>
              )}
            </button>

            <div
              className="flex items-center bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg"
              role="group"
              aria-label="Opções de visualização"
            >
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${
                  viewMode === 'grid'
                    ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                } rounded-l-lg transition-colors`}
                title="Visualizar em grade"
                aria-label="Visualizar em grade"
                aria-pressed={viewMode === 'grid'}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${
                  viewMode === 'list'
                    ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                } rounded-r-lg transition-colors`}
                title="Visualizar em lista"
                aria-label="Visualizar em lista"
                aria-pressed={viewMode === 'list'}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            <button 
              onClick={() => setShowImportExport(true)}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 lg:px-4 py-2 rounded-lg transition-colors whitespace-nowrap relative"
              title="Importar Questões"
              aria-label="Importar Questões"
            >
              <Upload className="w-4 h-4" />
              <span className="hidden lg:inline">Importar Questões</span>
            </button>
          </div>
        </div>

        {/* Search and Sort Bar */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Buscar questões por enunciado, tópico ou disciplina..."
              value={searchValue}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors"
              aria-label="Buscar questões"
            />
          </div>

          {/* Sort and Clear Filters */}
          <div className="flex items-center gap-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">Ordenar:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="p-2 border border-gray-300 dark:border-gray-700 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[140px]"
                title="Ordenar questões"
                aria-label="Ordenar questões"
              >
                <option value="newest">Mais Recentes</option>
                <option value="oldest">Mais Antigas</option>
                <option value="most_used">Mais Utilizadas</option>
                <option value="highest_rated">Melhor Avaliadas</option>
                <option value="difficulty_asc">Fácil → Difícil</option>
                <option value="difficulty_desc">Difícil → Fácil</option>
              </select>
            </div>

            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors whitespace-nowrap px-2 py-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
                aria-label="Limpar todos os filtros aplicados"
                title="Limpar Filtros"
              >
                Limpar Filtros
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Horizontal Filter Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="w-full mb-6 overflow-hidden"
            id="filter-panel"
            role="region"
            aria-label="Painel de filtros"
          >
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between lg:hidden">
                <h3 className="text-lg font-semibold dark:text-white">Filtros</h3>
                <button
                  onClick={() => setShowFilters(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  aria-label="Fechar filtros"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
              <div className="p-4">
                <FilterPanel
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onClearFilters={clearFilters}
                  hasActiveFilters={hasActiveFilters}
                  isAdmin={isAdmin}
                  isSchoolAdmin={isSchoolAdmin}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="flex-1 min-w-0">

        <div className="flex-1 min-w-0">
          {isLoading && page === 1 ? (
            // Show loading skeleton for initial load
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6 animate-pulse">
                  <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                  <div className="h-3 w-24 bg-gray-100 dark:bg-gray-800 rounded mb-2" />
                  <div className="h-4 w-full bg-gray-100 dark:bg-gray-800 rounded mb-4" />
                  <div className="h-3 w-3/4 bg-gray-100 dark:bg-gray-800 rounded mb-2" />
                  <div className="h-3 w-1/2 bg-gray-100 dark:bg-gray-800 rounded mb-2" />
                </div>
              ))}
            </div>
          ) : sortedQuestions.length === 0 ? (
            <>
              {filters.search || hasActiveFilters ? (
                <EmptyState
                  icon={FilterIcon}
                  title="Nenhuma questão encontrada"
                  description="Tente ajustar os filtros ou criar uma nova questão."
                  action={{
                    label: 'Criar Nova Questão',
                    onClick: () => setShowCreateModal(true)
                  }}
                />
              ) : (
                <EmptyState
                  icon={FilterIcon}
                  title="Nenhuma questão cadastrada ainda"
                  description="Nenhuma questão foi cadastrada até o momento. Que tal ser o primeiro?"
                  action={{
                    label: 'Criar Nova Questão',
                    onClick: () => setShowCreateModal(true)
                  }}
                />
              )}
            </>
          ) : (
            <motion.div
              layout
              className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6'
                  : 'space-y-3 lg:space-y-4'
              }
            >
              {sortedQuestions.map((question, index) => (
                <motion.div
                  key={question.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <QuestionCard
                    question={question}
                    variant={viewMode}
                    enableSelection={true}
                    isFavorite={isFavorite(question.id)}
                    onToggleFavorite={() => toggleFavorite(question.id)}
                    onViewDetail={() => handleViewQuestionDetail(question)}
                    onEdit={() => handleEditQuestion(question)}
                    onDelete={() => handleDeleteQuestion(question.id)}
                    showFeedback={viewMode === 'list'}
                    isDeleting={isDeleting}
                    isUpdating={isUpdating}
                    isTogglingFavorite={false}
                  />
                </motion.div>
              ))}
              
              {/* Load more trigger element */}
              {hasNextPage && (
                <div ref={loadMoreRef} className="h-10 w-full flex items-center justify-center">
                  {(isLoading || isLoadingMore) && (
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                      <div className="w-5 h-5 border-2 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-sm">Carregando mais questões...</span>
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreateQuestionModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={createQuestion}
        isLoading={isCreating}
      />

      <EditQuestionModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        question={selectedQuestion}
        onSave={handleUpdateQuestion}
        isSaving={isUpdating}
      />

      <QuestionDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        question={selectedQuestion}
        isAdmin={isAdmin}
      />

      {/* Import/Export Modal */}
      <AnimatePresence>
        {showImportExport && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            onClick={() => setShowImportExport(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Importar Questões
                  </h2>
                  <button
                    onClick={() => setShowImportExport(false)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </button>
                </div>
              </div>
              <div className="p-6">
                <ImportExport onClose={() => setShowImportExport(false)} />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Generator Modal */}
      <AnimatePresence>
        {showAIGenerator && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            onClick={() => setShowAIGenerator(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Gerador de Questões por IA
                  </h2>
                  <button
                    onClick={() => setShowAIGenerator(false)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </button>
                </div>
              </div>
              <div className="p-6">
                <AIQuestionGenerator />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Barra de ações flutuante para seleção múltipla */}
      {selectedIds.length > 0 && (
        <div className="fixed bottom-0 left-0 w-full z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg flex items-center justify-between px-6 py-3 animate-fade-in">
          <span className="font-medium text-gray-800 dark:text-white">
            {selectedIds.length} questão(ões) selecionada(s)
            {selectedIds.length >= MAX_SELECTION && (
              <span className="ml-2 text-xs text-red-600 dark:text-red-400">(Limite máximo)</span>
            )}
          </span>
          <div className="flex gap-2">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              onClick={() => {
                toast.success('Questões adicionadas à nova avaliação!')
                navigate('/app/editor')
              }}
              title="Criar avaliação com as questões selecionadas"
              aria-label="Criar avaliação com as questões selecionadas"
            >
              Criar Avaliação com Selecionadas
            </button>
            <button
              className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200"
              onClick={clearSelection}
              title="Limpar seleção de questões"
              aria-label="Limpar seleção de questões"
            >
              Limpar Seleção
            </button>
          </div>
        </div>
      )}

      {/* Mini tutorial de onboarding */}
      {showOnboarding && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl p-8 max-w-md w-full relative animate-fade-in">
            <button
              onClick={handleCloseOnboarding}
              className="absolute top-3 right-3 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Fechar tutorial"
              title="Fechar tutorial"
            >
              <span className="text-xl">×</span>
            </button>
            <h2 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">Novo fluxo de seleção de questões</h2>
            <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-200">
              <li>Marque as questões desejadas usando o <b>checkbox</b> no canto de cada card.</li>
              <li>Use a barra no rodapé para <b>criar uma avaliação</b> com as selecionadas ou limpar a seleção.</li>
              <li>Você pode selecionar até <b>50 questões</b> por vez.</li>
            </ol>
            <div className="mt-5 text-right">
              <button
                onClick={handleCloseOnboarding}
                className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded-lg font-medium transition-colors"
              >
                Entendi!
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default QuestionBank