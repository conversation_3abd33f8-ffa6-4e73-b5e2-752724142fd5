import React from 'react'
import { Heart, Plus, Eye, Tag, Clock, User, MoreVertical, Star, Edit, Trash2 } from 'lucide-react'
import { motion } from 'framer-motion'
import { Database } from '../../types/database.ts'
import FeedbackSystem from '../feedback/FeedbackSystem.tsx'
import LoadingSpinner from '../common/LoadingSpinner.tsx'
import { useQuestionSelection } from '../../contexts/QuestionSelectionContext.tsx'

type Question = Database['public']['Tables']['questions']['Row']

interface QuestionCardProps {
  question: Question
  variant?: 'grid' | 'list'
  isFavorite?: boolean
  onToggleFavorite?: () => void
  onAddToAssessment?: () => void
  onViewDetail?: () => void
  onEdit?: () => void
  onDelete?: () => void
  showFeedback?: boolean
  onRemoveFromAssessment?: () => void
  isDeleting?: boolean
  isUpdating?: boolean
  isApproving?: boolean
  isRejecting?: boolean
  isTogglingFavorite?: boolean
  enableSelection?: boolean
}

const difficultyColors = {
  'Fácil': 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
  'Médio': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
  'Difícil': 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
}

const typeLabels = {
  'multipla_escolha': 'Múltipla Escolha',
  'dissertativa': 'Dissertativa',
  'verdadeiro_falso': 'V/F'
}

const statusColors = {
  'pending': 'bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-300',
  'approved': 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
  'rejected': 'bg-gray-100 text-gray-800 dark:bg-gray-700/50 dark:text-gray-300',
  'draft': 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300',
};

const statusLabels = {
  'pending': 'Pendente',
  'approved': 'Aprovada',
  'rejected': 'Rejeitada',
  'draft': 'Rascunho',
};

const QuestionCard: React.FC<QuestionCardProps> = React.memo(({
  question,
  variant = 'grid',
  isFavorite = false,
  onToggleFavorite,
  onAddToAssessment,
  onViewDetail,
  onEdit,
  onDelete,
  showFeedback = false,
  onRemoveFromAssessment,
  isDeleting,
  isUpdating,
  isApproving,
  isRejecting,
  isTogglingFavorite,
  enableSelection = false
}) => {
  const [showMenu, setShowMenu] = React.useState(false)
  const [showFullFeedback, setShowFullFeedback] = React.useState(false)
  const { isSelected, toggleSelect } = useQuestionSelection()

  if (variant === 'list') {
    return (
      <motion.div
        whileHover={{ y: -2 }}
        className="relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 lg:p-6 hover:shadow-md transition-all duration-200"
      >

        {/* Topo: badges e botões */}
        <div className="flex flex-wrap items-center justify-between gap-2 mb-2 w-full">
          <div className="flex flex-wrap gap-2">
            <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap ${difficultyColors[question.dificuldade as keyof typeof difficultyColors]}`}>{question.dificuldade}</span>
            <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full whitespace-nowrap">{typeLabels[question.tipo as keyof typeof typeLabels]}</span>
            {question.status && (
              <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap ${statusColors[question.status as keyof typeof statusColors]}`}>{statusLabels[question.status as keyof typeof statusLabels]}</span>
            )}
          </div>
          <div className="flex items-center gap-1">
            {onToggleFavorite && (
              <button
                onClick={onToggleFavorite}
                title="Favoritar"
                className={`p-2 rounded-full transition-colors ${isFavorite ? 'text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30' : 'text-gray-400 dark:text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                aria-label="Favoritar"
                disabled={isTogglingFavorite}
              >
                {isTogglingFavorite ? (
                  <span className="w-4 h-4 flex items-center justify-center"><LoadingSpinner size="sm" /></span>
                ) : (
                  <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
                )}
              </button>
            )}
            {onViewDetail && (
              <button
                onClick={onViewDetail}
                title="Ver detalhes"
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-full transition-colors"
              >
                <Eye className="w-4 h-4" />
              </button>
            )}
            {!onRemoveFromAssessment && onAddToAssessment && (
              <button
                onClick={onAddToAssessment}
                title="Adicionar à avaliação"
                className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-full transition-colors"
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
        {/* Conteúdo central */}
        <div className="mb-4">
          <div className="flex flex-wrap items-center gap-2 mb-2">
            {enableSelection && (
              <label className="relative flex items-center cursor-pointer group mr-2">
                <input
                  type="checkbox"
                  checked={isSelected(question.id)}
                  onChange={() => toggleSelect(question.id)}
                  className="sr-only"
                  aria-label="Selecionar questão"
                />
                <div className={`
                  w-4 h-4 rounded border-2 transition-all duration-200 flex items-center justify-center
                  ${isSelected(question.id)
                    ? 'bg-blue-600 border-blue-600 text-white shadow-sm'
                    : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500'
                  }
                  group-hover:shadow-sm
                `}>
                  {isSelected(question.id) && (
                    <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </label>
            )}
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">{question.disciplina}</span>
            <span className="text-gray-300 dark:text-gray-600">•</span>
            <span className="text-sm text-gray-600 dark:text-gray-400">{question.serie}</span>
            <span className="text-gray-300 dark:text-gray-600 hidden sm:inline">•</span>
            <span className="text-sm text-gray-600 dark:text-gray-400 hidden sm:inline">{question.topico}</span>
            {question.competencia_bncc && (
              <>
                <span className="text-gray-300 dark:text-gray-600 hidden md:inline">•</span>
                <span className="text-xs text-gray-500 dark:text-gray-500 hidden md:inline">{question.competencia_bncc}</span>
              </>
            )}
          </div>
          <p className="text-gray-900 dark:text-white mb-2 leading-relaxed line-clamp-3">{question.enunciado}</p>
          {question.tipo === 'multipla_escolha' && question.alternativas && (
            <div className="space-y-1 mb-2">
              {question.alternativas.slice(0, 2).map((alt: string, index: number) => (
                <div key={index} className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {String.fromCharCode(97 + index)}) {alt}
                </div>
              ))}
              {question.alternativas.length > 2 && (
                <div className="text-sm text-gray-400 dark:text-gray-500">
                  ... e mais {question.alternativas.length - 2} alternativas
                </div>
              )}
            </div>
          )}
        </div>
        {/* Rodapé: metadados e botão adicionar/remover */}
        <div className="flex flex-wrap items-center justify-between gap-2 pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500 dark:text-gray-500">
            <div className="flex items-center space-x-1">
              <User className="w-4 h-4" />
              <span className="truncate max-w-32">{question.autor_id}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>{question.uso_count} usos</span>
            </div>
            {question.rating > 0 && (
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>{question.rating.toFixed(1)}</span>
              </div>
            )}
          </div>
          {onAddToAssessment && !onRemoveFromAssessment && (
            <button
              onClick={onAddToAssessment}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-lg transition-colors text-sm"
            >
              <Plus className="w-4 h-4" />
              <span>Adicionar</span>
            </button>
          )}
          {onRemoveFromAssessment && (
            <button
              onClick={onRemoveFromAssessment}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded-lg transition-colors text-sm"
            >
              <Trash2 className="w-4 h-4" />
              <span>Remover Questão</span>
            </button>
          )}
        </div>
        {showFeedback && (
          <div className="mt-6 pt-4 border-t border-gray-100 dark:border-gray-700">
            <FeedbackSystem 
              question={question}
              onFeedbackSubmit={(feedback: any) => {
                console.log('Feedback submitted:', feedback)
              }}
            />
          </div>
        )}
      </motion.div>
    )
  }

  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      className="relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 lg:p-6 hover:shadow-lg transition-all duration-200 group"
    >

      <div className="flex items-start justify-between mb-4">
        <div className="flex flex-wrap items-center gap-2">
          {enableSelection && (
            <label className="relative flex items-center cursor-pointer group mr-1">
              <input
                type="checkbox"
                checked={isSelected(question.id)}
                onChange={() => toggleSelect(question.id)}
                className="sr-only"
                aria-label="Selecionar questão"
              />
              <div className={`
                w-4 h-4 rounded border-2 transition-all duration-200 flex items-center justify-center
                ${isSelected(question.id)
                  ? 'bg-blue-600 border-blue-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500'
                }
                group-hover:shadow-sm
              `}>
                {isSelected(question.id) && (
                  <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </label>
          )}
          <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">{question.disciplina}</span>
          <span className="text-gray-300 dark:text-gray-600 hidden sm:inline">•</span>
          <span className="text-sm text-gray-600 dark:text-gray-400">{question.serie}</span>
          <span className="text-gray-300 dark:text-gray-600">•</span>
          <span className="text-sm text-gray-600 dark:text-gray-400">{question.topico}</span>
          {question.competencia_bncc && (
            <>
              <span className="text-gray-300 dark:text-gray-600">•</span>
              <span className="text-xs text-gray-500 dark:text-gray-500">{question.competencia_bncc}</span>
            </>
          )}
        </div>
        
        {onToggleFavorite && (
          <button
            onClick={onToggleFavorite}
            title="Favoritar"
            className={`p-2 rounded-full transition-colors ${
              isFavorite
                ? 'text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30'
                : 'text-gray-400 dark:text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            aria-label="Favoritar"
            disabled={isTogglingFavorite}
          >
            {isTogglingFavorite ? (
              <span className="w-4 h-4 flex items-center justify-center"><LoadingSpinner size="sm" /></span>
            ) : (
              <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
            )}
          </button>
        )}
      </div>

      <h3 className="text-sm text-gray-600 dark:text-gray-400 mb-2 truncate">{question.topico}</h3>
      
      <p className="text-gray-900 dark:text-white text-sm leading-relaxed mb-4 line-clamp-4">
        {question.enunciado}
      </p>

      {question.tipo === 'multipla_escolha' && question.alternativas && (
        <div className="space-y-1 mb-4">
          {question.alternativas.slice(0, 2).map((alt: string, index: number) => (
            <div key={index} className="text-xs text-gray-600 dark:text-gray-400 truncate">
              {String.fromCharCode(97 + index)}) {alt}
            </div>
          ))}
          {question.alternativas.length > 2 && (
            <div className="text-xs text-gray-400 dark:text-gray-500">
              ... e mais {question.alternativas.length - 2} alternativas
            </div>
          )}
        </div>
      )}

      <div className="flex flex-col gap-3">
        <div className="flex flex-wrap items-center gap-2">
          <span className={`text-xs px-2 py-1 rounded-full ${difficultyColors[question.dificuldade as keyof typeof difficultyColors]}`}>
            {question.dificuldade}
          </span>
          <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
            {typeLabels[question.tipo as keyof typeof typeLabels]}
          </span>
          {question.status && (
            <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap ${statusColors[question.status as keyof typeof statusColors]}`}>
              {statusLabels[question.status as keyof typeof statusLabels]}
            </span>
          )}
          {question.rating > 0 && (
            <div className="flex items-center space-x-1">
              <Star className="w-3 h-3 text-yellow-500" />
              <span className="text-xs text-gray-600 dark:text-gray-400">{question.rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between space-x-2">
          {onViewDetail && (
            <button
              onClick={onViewDetail}
              title="Ver detalhes"
              className="flex items-center justify-center space-x-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 px-2 py-1 rounded text-xs transition-colors"
            >
              <Eye className="w-3 h-3" />
              <span>Detalhes</span>
            </button>
          )}
          
          {onAddToAssessment && (
            <button
              onClick={onAddToAssessment}
              title="Adicionar à avaliação"
              className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-lg text-xs transition-colors opacity-0 group-hover:opacity-100 sm:opacity-100"
            >
              <Plus className="w-3 h-3" />
              <span>Adicionar</span>
            </button>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
        <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-500">
          <div className="flex items-center space-x-1">
            <User className="w-3 h-3" />
            <span className="truncate max-w-20">{question.autor_id}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye className="w-3 h-3" />
            <span>{question.uso_count}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-1">
          {question.tags && question.tags.length > 0 && question.tags.slice(0, 1).map((tag: string, index: number) => (
            <div key={index} className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-500">
              <Tag className="w-3 h-3" />
              <span className="truncate max-w-16">{tag}</span>
            </div>
          ))}
          {question.tags && question.tags.length > 1 && (
            <span className="text-xs text-gray-400 dark:text-gray-500">+{question.tags.length - 1}</span>
          )}
        </div>
      </div>

      {showFeedback && (
        <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <FeedbackSystem 
            question={question}
            onFeedbackSubmit={(feedback: any) => {
              console.log('Feedback submitted:', feedback)
            }}
          />
        </div>
      )}
    </motion.div>
  )
})

QuestionCard.displayName = 'QuestionCard'

export default QuestionCard