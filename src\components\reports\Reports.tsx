import React from 'react'
import { BarChart3, FileText, TrendingUp, Download } from 'lucide-react'
import FeatureComingSoon from '../common/FeatureComingSoon'

const Reports: React.FC = () => {
  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50">
      <div className="max-w-4xl mx-auto">
        <FeatureComingSoon
          title="Relatórios Avançados"
          description="Sistema completo de relatórios para acompanhar o desempenho das suas avaliações e questões com insights detalhados."
          expectedDate="Q3 2024"
          features={[
            'Relatórios de desempenho por turma',
            'Análise de dificuldade das questões',
            'Estatísticas de uso e engajamento',
            'Comparativos entre avaliações',
            'Exportação em múltiplos formatos',
            'Dashboards personalizáveis'
          ]}
        />
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">
                Analytics Detalhados
              </h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Acompanhe métricas detalhadas sobre o uso das suas questões e avaliações.
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <div>• Taxa de acerto por questão</div>
              <div>• Tempo médio de resolução</div>
              <div>• Questões mais utilizadas</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">
                Insights Inteligentes
              </h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Receba sugestões baseadas em dados para melhorar suas avaliações.
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <div>• Sugestões de melhoria</div>
              <div>• Identificação de padrões</div>
              <div>• Recomendações personalizadas</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Reports