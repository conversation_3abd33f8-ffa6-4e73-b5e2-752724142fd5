// Component to handle robots.txt route in React SPA
import React, { useEffect } from 'react'
import { handleRobotsRequest } from '../../lib/seo/dynamicSeoEndpoints'

/**
 * Component that generates and serves robots.txt content
 * This component should be rendered when the route is /robots.txt
 */
const RobotsRoute: React.FC = () => {
  useEffect(() => {
    const generateRobots = () => {
      try {
        const robots = handleRobotsRequest()
        
        // Set the document content type and body
        document.contentType = 'text/plain'
        document.body.innerHTML = `<pre>${robots}</pre>`
        
        // Also set proper headers if possible
        if (typeof window !== 'undefined' && window.history) {
          // Replace the current history entry to avoid back button issues
          window.history.replaceState(null, 'Robots', '/robots.txt')
        }
        
      } catch (error) {
        console.error('Error generating robots.txt:', error)
        document.body.innerHTML = '<pre>Error generating robots.txt</pre>'
      }
    }
    
    generateRobots()
  }, [])

  return (
    <div style={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
      Generating robots.txt...
    </div>
  )
}

export default RobotsRoute
