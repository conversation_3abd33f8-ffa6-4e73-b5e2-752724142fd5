// Component to handle sitemap.xml route in React SPA
import React, { useEffect } from 'react'
import { handleSitemapRequest } from '../../lib/seo/dynamicSeoEndpoints'

/**
 * Component that generates and serves sitemap.xml content
 * This component should be rendered when the route is /sitemap.xml
 */
const SitemapRoute: React.FC = () => {
  useEffect(() => {
    const generateSitemap = async () => {
      try {
        const sitemap = await handleSitemapRequest()
        
        // Set the document content type and body
        document.contentType = 'application/xml'
        document.body.innerHTML = `<pre>${sitemap}</pre>`
        
        // Also set proper headers if possible
        if (typeof window !== 'undefined' && window.history) {
          // Replace the current history entry to avoid back button issues
          window.history.replaceState(null, 'Sitemap', '/sitemap.xml')
        }
        
      } catch (error) {
        console.error('Error generating sitemap:', error)
        document.body.innerHTML = '<pre>Error generating sitemap</pre>'
      }
    }
    
    generateSitemap()
  }, [])

  return (
    <div style={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
      Generating sitemap...
    </div>
  )
}

export default SitemapRoute
