import React, { useState, useEffect } from 'react'
import { Bell, Volume2, VolumeX, Smartphone, Monitor, Settings } from 'lucide-react'
import pushNotificationService from '../../services/pushNotificationService'
import toast from 'react-hot-toast'

interface NotificationPreferences {
  pushEnabled: boolean
  audioEnabled: boolean
  browserEnabled: boolean
  types: {
    assessments: boolean
    questions: boolean
    system: boolean
    subscription: boolean
  }
}

const NotificationSettings: React.FC = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    pushEnabled: false,
    audioEnabled: true,
    browserEnabled: false,
    types: {
      assessments: true,
      questions: true,
      system: true,
      subscription: true
    }
  })

  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadPreferences()
    setPermission(pushNotificationService.getPermission())
  }, [])

  const loadPreferences = () => {
    const pushEnabled = localStorage.getItem('push_notifications_enabled') !== 'false'
    const audioEnabled = localStorage.getItem('notifications_audio') !== 'false'
    const browserEnabled = pushNotificationService.getPermission() === 'granted'
    
    const types = {
      assessments: localStorage.getItem('notifications_assessments') !== 'false',
      questions: localStorage.getItem('notifications_questions') !== 'false',
      system: localStorage.getItem('notifications_system') !== 'false',
      subscription: localStorage.getItem('notifications_subscription') !== 'false'
    }

    setPreferences({
      pushEnabled,
      audioEnabled,
      browserEnabled,
      types
    })
  }

  const handleRequestPermission = async () => {
    setLoading(true)
    try {
      const permission = await pushNotificationService.requestPermission()
      setPermission(permission)
      
      if (permission === 'granted') {
        toast.success('Permissão para notificações concedida!')
        setPreferences(prev => ({ ...prev, browserEnabled: true, pushEnabled: true }))
        pushNotificationService.setEnabled(true)
      } else {
        toast.error('Permissão para notificações negada')
      }
    } catch (error) {
      toast.error('Erro ao solicitar permissão')
    } finally {
      setLoading(false)
    }
  }

  const handleTogglePush = (enabled: boolean) => {
    pushNotificationService.setEnabled(enabled)
    setPreferences(prev => ({ ...prev, pushEnabled: enabled }))
    toast.success(enabled ? 'Notificações push ativadas' : 'Notificações push desativadas')
  }

  const handleToggleAudio = (enabled: boolean) => {
    pushNotificationService.setAudioEnabled(enabled)
    setPreferences(prev => ({ ...prev, audioEnabled: enabled }))
    toast.success(enabled ? 'Sons de notificação ativados' : 'Sons de notificação desativados')
  }

  const handleToggleType = (type: keyof NotificationPreferences['types'], enabled: boolean) => {
    localStorage.setItem(`notifications_${type}`, enabled.toString())
    setPreferences(prev => ({
      ...prev,
      types: { ...prev.types, [type]: enabled }
    }))
  }

  const testNotification = async () => {
    try {
      await pushNotificationService.sendNotification({
        title: '🔔 Teste de Notificação',
        body: 'Esta é uma notificação de teste do seu sistema!',
        tag: 'test',
        requireInteraction: false
      })
      toast.success('Notificação de teste enviada!')
    } catch (error) {
      toast.error('Erro ao enviar notificação de teste')
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Bell className="w-6 h-6 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Configurações de Notificação
        </h3>
      </div>

      {/* Status da Permissão do Navegador */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Monitor className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                Permissão do Navegador
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Status: {permission === 'granted' ? 'Concedida' : 
                        permission === 'denied' ? 'Negada' : 'Não solicitada'}
              </p>
            </div>
          </div>
          {permission !== 'granted' && (
            <button
              onClick={handleRequestPermission}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Solicitando...' : 'Solicitar Permissão'}
            </button>
          )}
        </div>
      </div>

      {/* Configurações Principais */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-white">Configurações Gerais</h4>
        
        {/* Push Notifications */}
        <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Smartphone className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white">
                Notificações Push
              </h5>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receber notificações mesmo quando a aba estiver inativa
              </p>
            </div>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={preferences.pushEnabled && permission === 'granted'}
              onChange={(e) => handleTogglePush(e.target.checked)}
              disabled={permission !== 'granted'}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Audio Notifications */}
        <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            {preferences.audioEnabled ? (
              <Volume2 className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            ) : (
              <VolumeX className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            )}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white">
                Sons de Notificação
              </h5>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Reproduzir sons quando receber notificações
              </p>
            </div>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={preferences.audioEnabled}
              onChange={(e) => handleToggleAudio(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      {/* Tipos de Notificação */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-white">Tipos de Notificação</h4>
        
        {Object.entries({
          assessments: { label: 'Avaliações', desc: 'Novas avaliações e resultados' },
          questions: { label: 'Questões', desc: 'Novas questões disponíveis' },
          system: { label: 'Sistema', desc: 'Atualizações e manutenções' },
          subscription: { label: 'Assinatura', desc: 'Informações sobre pagamentos e planos' }
        }).map(([key, { label, desc }]) => (
          <div key={key} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white">{label}</h5>
              <p className="text-sm text-gray-600 dark:text-gray-400">{desc}</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.types[key as keyof NotificationPreferences['types']]}
                onChange={(e) => handleToggleType(key as keyof NotificationPreferences['types'], e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        ))}
      </div>

      {/* Teste */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={testNotification}
          disabled={!preferences.pushEnabled || permission !== 'granted'}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Settings className="w-4 h-4" />
          <span>Testar Notificação</span>
        </button>
      </div>
    </div>
  )
}

export default NotificationSettings 