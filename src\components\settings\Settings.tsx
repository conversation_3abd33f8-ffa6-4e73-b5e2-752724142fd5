import React, { useState, useEffect } from 'react'
import { User, <PERSON>, Shield, Palette, Save, Check, X, Mail, Lock, School, BookOpen } from 'lucide-react'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { useTheme } from '../../contexts/ThemeContext'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import { Database } from '../../types/database'

const profileSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  escola: z.string().optional(),
  disciplinas: z.array(z.string()).default([]),
  series: z.array(z.string()).default([]),
})

type ProfileFormData = z.infer<typeof profileSchema>

const passwordSchema = z.object({
  currentPassword: z.string().min(6, 'Senha deve ter pelo menos 6 caracteres'),
  newPassword: z.string().min(6, 'Senha deve ter pelo menos 6 caracteres'),
  confirmPassword: z.string().min(6, 'Senha deve ter pelo menos 6 caracteres')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Senhas não coincidem",
  path: ["confirmPassword"]
})

type PasswordFormData = z.infer<typeof passwordSchema>

const notificationSchema = z.object({
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(false),
  newQuestionNotifications: z.boolean().default(true),
  systemUpdatesNotifications: z.boolean().default(true)
})

type NotificationFormData = z.infer<typeof notificationSchema>

const Settings: React.FC = () => {
  const { profile, updateProfile, refreshProfile } = useAuth()
  const { theme, fontSize, compactMode, setTheme, setFontSize, setCompactMode } = useTheme()
  const [activeTab, setActiveTab] = useState('profile')
  const [savingProfile, setSavingProfile] = useState(false)
  const [savingPassword, setSavingPassword] = useState(false)
  const [savingNotifications, setSavingNotifications] = useState(false)
  const [passwordChanged, setPasswordChanged] = useState(false)
  const [availableDisciplines, setAvailableDisciplines] = useState<string[]>([
    'Matemática', 'Português', 'Ciências', 'História', 'Geografia', 
    'Física', 'Química', 'Biologia', 'Inglês', 'Espanhol', 'Filosofia', 'Sociologia'
  ])
  const [availableSeries, setAvailableSeries] = useState<string[]>([
    '6º Ano', '7º Ano', '8º Ano', '9º Ano', '1º Médio', '2º Médio', '3º Médio'
  ])

  // NOVO: Query para buscar dados da escola
  const { data: schoolData, isLoading: isLoadingSchool, isError: isErrorSchool } = useQuery<Database['public']['Tables']['schools']['Row']>({
    queryKey: ['school', profile?.school_id],
    queryFn: async () => {
      if (!profile?.school_id) {
        return null;
      }
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('id', profile.school_id)
        .single();
      if (error) {
        throw error;
      }
      return data;
    },
    enabled: !!profile?.school_id, // Ativa a query apenas se houver um school_id no perfil
  });

  // Profile form
  const {
    register: registerProfile,
    handleSubmit: handleSubmitProfile,
    formState: { errors: profileErrors },
    reset: resetProfile,
    setValue: setProfileValue,
    watch: watchProfile
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      nome: profile?.nome || '',
      email: profile?.email || '',
      escola: profile?.escola || '',
      disciplinas: profile?.disciplinas || [],
      series: profile?.series || [],
    }
  })

  // Password form
  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors },
    reset: resetPassword
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema)
  })

  // Notification form
  const {
    register: registerNotification,
    handleSubmit: handleSubmitNotification,
    formState: { errors: notificationErrors },
    reset: resetNotification
  } = useForm<NotificationFormData>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      emailNotifications: true,
      pushNotifications: false,
      newQuestionNotifications: true,
      systemUpdatesNotifications: true
    }
  })

  // Reset forms when profile changes
  useEffect(() => {
    if (profile) {
      resetProfile({
        nome: profile.nome || '',
        email: profile.email || '',
        escola: schoolData?.name || profile.escola || '',
        disciplinas: profile.disciplinas || [],
        series: profile.series || [],
      })

      // Load notification settings from profile.configuracoes if available
      const notificationSettings = profile.configuracoes?.notifications || {}
      resetNotification({
        emailNotifications: notificationSettings.email ?? true,
        pushNotifications: notificationSettings.push ?? false,
        newQuestionNotifications: notificationSettings.newQuestions ?? true,
        systemUpdatesNotifications: notificationSettings.systemUpdates ?? true
      })
    }
  }, [profile, schoolData, resetProfile, resetNotification])

  const onSaveProfile = async (data: ProfileFormData) => {
    console.log('onSaveProfile: [START] Iniciando salvamento do perfil com dados:', data);
    setSavingProfile(true);
    console.log('onSaveProfile: savingProfile set to TRUE');
    try {
      const updates: Partial<Profile> = {
        nome: data.nome,
        email: data.email,
        disciplinas: data.disciplinas,
        series: data.series,
      };

      // Só inclui 'escola' se o perfil NÃO tiver um school_id vinculado
      if (!profile?.school_id) {
        updates.escola = data.escola || null;
      } else {
        // Se o usuário tem um school_id, garantir que 'escola' não seja enviado
        // ou seja enviado como o valor atual do schoolData.name para evitar conflitos de RLS
        // No entanto, para evitar RLS, é melhor não enviar se não for para alterar
        // e ele já está desabilitado no formulário.
        // A lógica principal é evitar enviar um valor que não pode ser alterado pelo usuário.
      }

      console.log('onSaveProfile: Chamando updateProfile com:', updates);
      await updateProfile(updates);
      console.log('onSaveProfile: updateProfile concluído.');

      toast.success('Perfil atualizado com sucesso!');
      console.log('onSaveProfile: Toast de sucesso exibido.');
    } catch (error) {
      console.error('onSaveProfile: [ERROR] Erro ao atualizar perfil no Settings.tsx:', error);
      toast.error('Erro ao atualizar perfil');
    } finally {
      console.log('onSaveProfile: [FINALLY] Finalizando salvamento do perfil, definindo savingProfile para FALSE.');
      setSavingProfile(false);
    }
  }

  const onChangePassword = async (data: PasswordFormData) => {
    setSavingPassword(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword
      });

      if (error) throw error;

      toast.success('Senha alterada com sucesso!');
      setPasswordChanged(true);
      resetPassword();
    } catch (error: any) {
      toast.error(`Erro ao alterar senha: ${error.message}`);
    } finally {
      setSavingPassword(false);
    }
  }

  const onSaveNotifications = async (data: NotificationFormData) => {
    setSavingNotifications(true);
    try {
      // Get current configuracoes or initialize empty object
      const currentConfig = profile?.configuracoes || {};

      // Update notifications settings
      const updatedConfig = {
        ...currentConfig,
        notifications: {
          email: data.emailNotifications,
          push: data.pushNotifications,
          newQuestions: data.newQuestionNotifications,
          systemUpdates: data.systemUpdatesNotifications
        }
      };

      await updateProfile({
        configuracoes: updatedConfig
      });

      toast.success('Configurações de notificações salvas!');
    } catch (error) {
      toast.error('Erro ao salvar configurações de notificações');
    } finally {
      setSavingNotifications(false);
    }
  }

  const tabs = [
    { id: 'profile', label: 'Perfil', icon: User },
    { id: 'security', label: 'Segurança', icon: Shield },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'appearance', label: 'Aparência', icon: Palette }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <form onSubmit={handleSubmitProfile(onSaveProfile)} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nome Completo
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  {...registerProfile('nome')}
                  type="text"
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white ${
                    profileErrors.nome ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Seu nome completo"
                />
              </div>
              {profileErrors.nome && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.nome.message}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  {...registerProfile('email')}
                  type="email"
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white ${
                    profileErrors.email ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="<EMAIL>"
                  disabled
                />
              </div>
              {profileErrors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.email.message}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Escola
              </label>
              <div className="relative">
                <School className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  {...registerProfile('escola')}
                  type="text"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  placeholder={isLoadingSchool ? "Carregando..." : schoolData?.name || "Nenhuma escola vinculada"}
                  readOnly
                  disabled={isLoadingSchool || !!profile?.school_id}
                />
              </div>
            </div>

            {/* NOVO: Se o usuário estiver vinculado a uma escola, mostre os detalhes */}
            {profile?.school_id && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mt-6">Informações da Escola</h3>
                {isLoadingSchool ? (
                  <p className="text-gray-600 dark:text-gray-400">Carregando informações da escola...</p>
                ) : isErrorSchool ? (
                  <p className="text-red-600 dark:text-red-400">Erro ao carregar informações da escola.</p>
                ) : schoolData ? (
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                    <p className="text-gray-800 dark:text-gray-200"><strong>Nome:</strong> {schoolData.name}</p>
                    {schoolData.contact_email && (
                      <p className="text-gray-800 dark:text-gray-200"><strong>Email de Contato:</strong> {schoolData.contact_email}</p>
                    )}
                    {schoolData.contact_phone && (
                      <p className="text-gray-800 dark:text-gray-200"><strong>Telefone:</strong> {schoolData.contact_phone}</p>
                    )}
                    {schoolData.address && (
                      <p className="text-gray-800 dark:text-gray-200"><strong>Endereço:</strong> {schoolData.address}</p>
                    )}
                    {schoolData.current_teachers_count !== null && (
                      <p className="text-gray-800 dark:text-gray-200"><strong>Professores Atuais:</strong> {schoolData.current_teachers_count}</p>
                    )}
                    {schoolData.max_teachers !== null && (
                      <p className="text-gray-800 dark:text-gray-200"><strong>Limite de Professores:</strong> {schoolData.max_teachers}</p>
                    )}
                    {/* Você pode adicionar mais informações da escola aqui, se necessário */}
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">Nenhuma informação de escola disponível.</p>
                )}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Disciplinas
              </label>
              <div className="relative">
                <BookOpen className="absolute left-3 top-3 text-gray-400 w-5 h-5" />
                <div className="pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-800">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {availableDisciplines.map((disciplina) => (
                      <label key={disciplina} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          value={disciplina}
                          {...registerProfile('disciplinas')}
                          className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{disciplina}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Séries/Anos
              </label>
              <div className="relative">
                <BookOpen className="absolute left-3 top-3 text-gray-400 w-5 h-5" />
                <div className="pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-800">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {availableSeries.map((serie) => (
                      <label key={serie} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          value={serie}
                          {...registerProfile('series')}
                          className="rounded text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{serie}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={savingProfile}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                {savingProfile ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <>
                    <Save className="w-5 h-5" />
                    <span>Salvar Alterações</span>
                  </>
                )}
              </button>
            </div>
          </form>
        )
      
      case 'security':
        return (
          <div className="space-y-6">
            {passwordChanged ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center"
              >
                <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-green-800 dark:text-green-400 mb-2">
                  Senha Alterada com Sucesso!
                </h3>
                <p className="text-green-700 dark:text-green-300 mb-4">
                  Sua senha foi atualizada. Na próxima vez que fizer login, use sua nova senha.
                </p>
                <button
                  onClick={() => setPasswordChanged(false)}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Voltar
                </button>
              </motion.div>
            ) : (
              <form onSubmit={handleSubmitPassword(onChangePassword)} className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Alterar Senha</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Senha Atual
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      {...registerPassword('currentPassword')}
                      type="password"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white ${
                        passwordErrors.currentPassword ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="Sua senha atual"
                    />
                  </div>
                  {passwordErrors.currentPassword && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{passwordErrors.currentPassword.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nova Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      {...registerPassword('newPassword')}
                      type="password"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white ${
                        passwordErrors.newPassword ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="Nova senha"
                    />
                  </div>
                  {passwordErrors.newPassword && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{passwordErrors.newPassword.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Confirmar Nova Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      {...registerPassword('confirmPassword')}
                      type="password"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white ${
                        passwordErrors.confirmPassword ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="Confirme a nova senha"
                    />
                  </div>
                  {passwordErrors.confirmPassword && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{passwordErrors.confirmPassword.message}</p>
                  )}
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={savingPassword}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors"
                  >
                    {savingPassword ? (
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save className="w-5 h-5" />
                    )}
                    <span>Alterar Senha</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        )
      
      case 'notifications':
        return (
          <form onSubmit={handleSubmitNotification(onSaveNotifications)} className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Preferências de Notificações</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Notificações por Email</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Receba atualizações importantes por email</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    {...registerNotification('emailNotifications')}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Notificações Push</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Receba notificações no navegador</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    {...registerNotification('pushNotifications')}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Novas Questões</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Notificações sobre novas questões adicionadas</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    {...registerNotification('newQuestionNotifications')}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Atualizações do Sistema</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Notificações sobre novos recursos e atualizações</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    {...registerNotification('systemUpdatesNotifications')}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={savingNotifications}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors"
              >
                {savingNotifications ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="w-5 h-5" />
                )}
                <span>Salvar Preferências</span>
              </button>
            </div>
          </form>
        )
      
      case 'appearance':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Preferências de Aparência</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Tema
              </label>
              <div className="grid grid-cols-3 gap-4">
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="theme"
                    value="light"
                    checked={theme === 'light'}
                    onChange={() => setTheme('light')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    theme === 'light' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <div className="w-full h-20 bg-white border rounded mb-2"></div>
                    <p className="text-sm text-center font-medium dark:text-gray-300">Claro</p>
                  </div>
                </label>
                
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="theme"
                    value="dark"
                    checked={theme === 'dark'}
                    onChange={() => setTheme('dark')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    theme === 'dark' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <div className="w-full h-20 bg-gray-800 border rounded mb-2"></div>
                    <p className="text-sm text-center font-medium dark:text-gray-300">Escuro</p>
                  </div>
                </label>
                
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="theme"
                    value="system"
                    checked={theme === 'system'}
                    onChange={() => setTheme('system')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    theme === 'system' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <div className="w-full h-20 bg-gradient-to-r from-white to-gray-800 border rounded mb-2"></div>
                    <p className="text-sm text-center font-medium dark:text-gray-300">Sistema</p>
                  </div>
                </label>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Tamanho da Fonte
              </label>
              <div className="grid grid-cols-3 gap-4">
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="fontSize"
                    value="small"
                    checked={fontSize === 'small'}
                    onChange={() => setFontSize('small')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    fontSize === 'small' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <p className="text-xs text-center dark:text-gray-300">Pequeno</p>
                  </div>
                </label>
                
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="fontSize"
                    value="medium"
                    checked={fontSize === 'medium'}
                    onChange={() => setFontSize('medium')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    fontSize === 'medium' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <p className="text-sm text-center dark:text-gray-300">Médio</p>
                  </div>
                </label>
                
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="fontSize"
                    value="large"
                    checked={fontSize === 'large'}
                    onChange={() => setFontSize('large')}
                    className="sr-only"
                  />
                  <div className={`border rounded-lg p-4 transition-colors ${
                    fontSize === 'large' 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                      : 'border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'
                  }`}>
                    <p className="text-base text-center dark:text-gray-300">Grande</p>
                  </div>
                </label>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Modo Compacto</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">Reduz o espaçamento e tamanho dos elementos</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={compactMode}
                  onChange={(e) => setCompactMode(e.target.checked)}
                  className="sr-only peer" 
                />
                <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-2">Configurações</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Gerencie suas preferências e configurações da conta
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="flex border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-400'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>

          <div className="p-6">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderTabContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings