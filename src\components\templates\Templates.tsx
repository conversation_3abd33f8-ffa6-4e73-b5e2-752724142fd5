import React, { useState } from 'react'
import { Layout, Search, Star, Eye } from 'lucide-react'
import { motion } from 'framer-motion'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useTemplates } from '../../hooks/useTemplates'
import { useNavigate } from 'react-router-dom'
import LoadingSpinner from '../common/LoadingSpinner'
import TemplatePreviewModal from '../admin/TemplatePreviewModal'

const Templates: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [selectedTemplateForPreview, setSelectedTemplateForPreview] = useState<any>(null)
  const { canAccess } = useSubscription()
  const { templates, isLoading } = useTemplates()
  const navigate = useNavigate()

  const handleOpenPreview = (template: any) => {
    setSelectedTemplateForPreview(template)
    setShowPreviewModal(true)
  }

  const handleClosePreview = () => {
    setShowPreviewModal(false)
    setSelectedTemplateForPreview(null)
  }

  const categories = ['all', 'Padrão', 'Premium', 'Design']

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.nome.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || template.categoria === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleUseTemplate = (templateId: string) => {
    navigate(`/app/editor?templateId=${templateId}`)
  }

  if (isLoading) {
    return (
      <div className="flex-1 p-4 lg:p-6 bg-gray-50 flex items-center justify-center dark:bg-gray-900">
        <LoadingSpinner size="lg" text="Carregando modelos prontos..." />
      </div>
    )
  }

  return (
    <div className="flex-1 p-4 lg:p-6 bg-gray-50 dark:bg-gray-900">
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">Modelos Prontos</h1>
            <p className="text-gray-600 mt-1 dark:text-gray-400">
              Escolha um modelo para suas avaliações
            </p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar modelos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-700 dark:text-white"
            />
          </div>
          
          <div className="flex space-x-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
                }`}
              >
                {category === 'all' ? 'Todos' : category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {filteredTemplates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <Layout className="w-16 h-16 text-gray-300 mx-auto mb-4 dark:text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2 dark:text-white">
            Nenhum modelo encontrado
          </h3>
          <p className="text-gray-600 mb-4 dark:text-gray-400">
            Tente ajustar sua busca ou filtros.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200 group dark:bg-gray-800 dark:border-gray-700"
            >
              <div className="relative">
                <div className="aspect-[3/4] bg-gray-100 flex items-center justify-center dark:bg-gray-700">
                  {template.preview_image ? (
                    <img src={template.preview_image} alt={template.nome} className="w-full h-full object-cover" />
                  ) : (
                    <Layout className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                  )}
                </div>
                

                
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                    <button
                      onClick={() => handleOpenPreview(template)}
                      className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
                      title="Preview do Template"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1 dark:text-white">{template.nome}</h3>
                <p className="text-sm text-gray-600 mb-3 dark:text-gray-400">{template.categoria}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-1 dark:text-gray-400">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>{template.rating?.toFixed(1) || '0.0'}</span>
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500">
                    {template.categoria}
                  </div>
                </div>
                
                <button
                  onClick={() => handleUseTemplate(template.id)}
                  className="w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Usar Modelo
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      <TemplatePreviewModal
        isOpen={showPreviewModal}
        onClose={handleClosePreview}
        template={selectedTemplateForPreview}
      />
    </div>
  )
}

export default Templates