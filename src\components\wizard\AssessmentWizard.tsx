import React, { Suspense, lazy } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>Lef<PERSON>, ArrowR<PERSON>, Check, Sparkles } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useAssessmentWizard } from '../../hooks/useAssessmentWizard'
import WizardProgress from './WizardProgress'
import { useIsMobile } from '../../hooks/useIsMobile'

// Lazy loading dos componentes das etapas
const BasicInfoStep = lazy(() => import('./steps/BasicInfoStep'))
const QuestionSelectionStep = lazy(() => import('./steps/QuestionSelectionStep'))
const ConfigurationStep = lazy(() => import('./steps/ConfigurationStep'))
const ReviewStep = lazy(() => import('./steps/ReviewStep'))

const AssessmentWizard: React.FC = () => {
  const navigate = useNavigate()
  const { isMobile } = useIsMobile()
  const {
    currentStep,
    steps,
    wizardData,
    updateBasicInfo,
    updateSelectedItems,
    updateConfig,
    nextStep,
    prevStep,
    goToStep,
    canProceedToNext,
    isFirstStep,
    isLastStep,
    handleConfigurationInteracted
  } = useAssessmentWizard()

  const handleBack = () => {
    if (isFirstStep) {
      navigate('/app/assessments')
    } else {
      prevStep()
    }
  }

  const handleNext = () => {
    if (canProceedToNext) {
      nextStep()
    }
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <BasicInfoStep
            data={wizardData.basicInfo}
            onUpdate={updateBasicInfo}
          />
        )
      case 1:
        return (
          <QuestionSelectionStep
            selectedItems={wizardData.selectedItems}
            onUpdate={updateSelectedItems}
            basicInfo={wizardData.basicInfo}
          />
        )
      case 2:
        return (
          <ConfigurationStep
            config={wizardData.config}
            onUpdate={updateConfig}
            onConfigurationInteracted={handleConfigurationInteracted}
          />
        )
      case 3:
        return (
          <ReviewStep
            wizardData={wizardData}
            onEdit={goToStep}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="hidden sm:inline">
                  {isFirstStep ? 'Voltar' : 'Anterior'}
                </span>
              </button>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    Modo Fácil
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Criação guiada de avaliação
                  </p>
                </div>
              </div>
            </div>

            {/* Progress indicator for mobile */}
            {isMobile && (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {currentStep + 1} de {steps.length}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Progress Sidebar - Hidden on mobile */}
          {!isMobile && (
            <div className="lg:col-span-1">
              <WizardProgress
                steps={steps}
                currentStep={currentStep}
                onStepClick={goToStep}
              />
            </div>
          )}

          {/* Main Content */}
          <div className={`${isMobile ? 'col-span-1' : 'lg:col-span-3'}`}>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Mobile Progress */}
              {isMobile && (
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <WizardProgress
                    steps={steps}
                    currentStep={currentStep}
                    onStepClick={goToStep}
                    compact
                  />
                </div>
              )}

              {/* Step Content */}
              <div className="p-6 lg:p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {steps[currentStep]?.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {steps[currentStep]?.description}
                  </p>
                </div>

                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentStep}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Suspense fallback={
                      <div className="flex items-center justify-center py-12">
                        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    }>
                      {renderCurrentStep()}
                    </Suspense>
                  </motion.div>
                </AnimatePresence>
              </div>

              {/* Navigation Footer */}
              <div className="px-6 lg:px-8 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <button
                    onClick={handleBack}
                    className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span>{isFirstStep ? 'Cancelar' : 'Anterior'}</span>
                  </button>

                  {!isLastStep && (
                    <button
                      onClick={handleNext}
                      disabled={!canProceedToNext}
                      className="flex items-center space-x-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                    >
                      <span>Próximo</span>
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AssessmentWizard
