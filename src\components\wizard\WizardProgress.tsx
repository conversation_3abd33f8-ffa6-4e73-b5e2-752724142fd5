import React from 'react'
import { motion } from 'framer-motion'
import { Check, FileText, Search, Settings, Eye } from 'lucide-react'
import { WizardStep } from '../../hooks/useAssessmentWizard'

interface WizardProgressProps {
  steps: WizardStep[]
  currentStep: number
  onStepClick: (stepIndex: number) => void
  compact?: boolean
}

const stepIcons = [
  FileText, // Basic Info
  Search,   // Questions
  Settings, // Configuration
  Eye       // Review
]

const WizardProgress: React.FC<WizardProgressProps> = React.memo(({
  steps,
  currentStep,
  onStepClick,
  compact = false
}) => {
  if (compact) {
    return (
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const Icon = stepIcons[index]
          const isActive = index === currentStep
          const isCompleted = step.isCompleted
          const isPast = index < currentStep
          const canClick = isPast || isActive

          return (
            <React.Fragment key={step.id}>
              <button
                onClick={() => canClick && onStepClick(index)}
                disabled={!canClick}
                className={`
                  flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200
                  ${isActive 
                    ? 'bg-blue-600 text-white shadow-lg' 
                    : isCompleted || isPast
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500'
                  }
                  ${canClick ? 'cursor-pointer hover:scale-105' : 'cursor-not-allowed'}
                `}
              >
                {isCompleted && !isActive ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <Icon className="w-5 h-5" />
                )}
              </button>
              
              {index < steps.length - 1 && (
                <div className={`
                  flex-1 h-0.5 mx-2 transition-colors duration-200
                  ${isPast ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-600'}
                `} />
              )}
            </React.Fragment>
          )
        })}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        Progresso
      </h3>
      
      {steps.map((step, index) => {
        const Icon = stepIcons[index]
        const isActive = index === currentStep
        const isCompleted = step.isCompleted
        const isPast = index < currentStep
        const canClick = isPast || isActive

        return (
          <div key={step.id} className="relative">
            {/* Connection Line */}
            {index < steps.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200 dark:bg-gray-600">
                <motion.div
                  className="w-full bg-green-500"
                  initial={{ height: 0 }}
                  animate={{ height: isPast ? '100%' : '0%' }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                />
              </div>
            )}

            <button
              onClick={() => canClick && onStepClick(index)}
              disabled={!canClick}
              className={`
                w-full flex items-start space-x-4 p-4 rounded-lg transition-all duration-200
                ${isActive 
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-2 border-blue-200 dark:border-blue-700' 
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }
                ${canClick ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'}
              `}
            >
              {/* Step Icon */}
              <div className={`
                flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200
                ${isActive 
                  ? 'bg-blue-600 text-white shadow-lg' 
                  : isCompleted
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500'
                }
              `}>
                {isCompleted && !isActive ? (
                  <Check className="w-6 h-6" />
                ) : (
                  <Icon className="w-6 h-6" />
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0 text-left">
                <h4 className={`
                  font-semibold transition-colors duration-200
                  ${isActive 
                    ? 'text-blue-900 dark:text-blue-100' 
                    : 'text-gray-900 dark:text-white'
                  }
                `}>
                  {step.title}
                </h4>
                <p className={`
                  text-sm mt-1 transition-colors duration-200
                  ${isActive 
                    ? 'text-blue-700 dark:text-blue-300' 
                    : 'text-gray-500 dark:text-gray-400'
                  }
                `}>
                  {step.description}
                </p>
                
                {/* Status Indicator */}
                <div className="flex items-center mt-2">
                  {isCompleted && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <Check className="w-3 h-3 mr-1" />
                      Concluído
                    </span>
                  )}
                  {isActive && !isCompleted && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      Em andamento
                    </span>
                  )}
                </div>
              </div>
            </button>
          </div>
        )
      })}
    </div>
  )
})

WizardProgress.displayName = 'WizardProgress'

export default WizardProgress
