import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>T<PERSON>t, BookOpen, GraduationCap, School } from 'lucide-react'
import { DISCIPLINAS, SERIES } from '../../../constants/educationOptions'
import SchoolNameSelect from '../../common/SchoolNameSelect'

interface BasicInfoStepProps {
  data: {
    titulo: string
    disciplina: string
    serie: string
    nomeEscola: string
  }
  onUpdate: (updates: Partial<BasicInfoStepProps['data']>) => void
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ data, onUpdate }) => {
  const handleInputChange = (field: keyof BasicInfoStepProps['data'], value: string) => {
    onUpdate({ [field]: value })
  }

  const isFieldComplete = (field: keyof BasicInfoStepProps['data']) => {
    return !!data[field]?.trim()
  }

  return (
    <div className="space-y-8">
      {/* Welcome Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-200 dark:border-blue-700"
      >
        <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-4">
          <FileText className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Vamos começar sua avaliação!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Preencha as informações básicas para personalizar sua avaliação
        </p>
      </motion.div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Título da Avaliação */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="md:col-span-2"
        >
          <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <FileText className="w-4 h-4" />
            <span>Título da Avaliação *</span>
            {isFieldComplete('titulo') && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </label>
          <input
            type="text"
            value={data.titulo}
            onChange={(e) => handleInputChange('titulo', e.target.value)}
            placeholder="Ex: Avaliação de Matemática - 1º Bimestre"
            className="w-full p-4 text-lg border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Este será o nome principal da sua avaliação
          </p>
        </motion.div>

        {/* Disciplina */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <BookOpen className="w-4 h-4" />
            <span>Disciplina *</span>
            {isFieldComplete('disciplina') && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </label>
          <select
            value={data.disciplina}
            onChange={(e) => handleInputChange('disciplina', e.target.value)}
            className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
          >
            <option value="">Selecione uma disciplina</option>
            {DISCIPLINAS.map((disciplina) => (
              <option key={disciplina} value={disciplina}>
                {disciplina}
              </option>
            ))}
          </select>
        </motion.div>

        {/* Série */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <GraduationCap className="w-4 h-4" />
            <span>Série/Ano *</span>
            {isFieldComplete('serie') && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </label>
          <select
            value={data.serie}
            onChange={(e) => handleInputChange('serie', e.target.value)}
            className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
          >
            <option value="">Selecione uma série</option>
            {SERIES.map((serie) => (
              <option key={serie} value={serie}>
                {serie}
              </option>
            ))}
          </select>
        </motion.div>

        {/* Nome da Escola */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="md:col-span-2"
        >
          <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <School className="w-4 h-4" />
            <span>Nome da Escola *</span>
            {isFieldComplete('nomeEscola') && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </label>
          <SchoolNameSelect
            value={data.nomeEscola}
            onChange={(value) => handleInputChange('nomeEscola', value)}
            placeholder="Selecione ou digite o nome da escola"
            className="text-lg"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            O nome da escola aparecerá no cabeçalho da avaliação
          </p>
        </motion.div>
      </div>

      {/* Progress Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mt-8"
      >
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
          <span>Progresso do formulário</span>
          <span>
            {Object.values(data).filter(value => value?.trim()).length} de 4 campos preenchidos
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <motion.div
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ 
              width: `${(Object.values(data).filter(value => value?.trim()).length / 4) * 100}%` 
            }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </motion.div>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4"
      >
        <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
          💡 Dica
        </h4>
        <p className="text-sm text-blue-700 dark:text-blue-300">
          Essas informações serão usadas para personalizar sua avaliação e ajudar na busca de questões adequadas. 
          Você poderá alterar esses dados posteriormente se necessário.
        </p>
      </motion.div>
    </div>
  )
}

export default BasicInfoStep
