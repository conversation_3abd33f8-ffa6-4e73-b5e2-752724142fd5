import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON>Text, <PERSON><PERSON>, Layout, Eye } from 'lucide-react'
import { AssessmentConfig } from '../../../types/assessment'
import { useSubscription } from '../../../contexts/SubscriptionContext'

interface ConfigurationStepProps {
  config: AssessmentConfig
  onUpdate: (config: AssessmentConfig) => void
  onConfigurationInteracted?: () => void
}

const ConfigurationStep: React.FC<ConfigurationStepProps> = ({
  config,
  onUpdate,
  onConfigurationInteracted
}) => {
  const { isPremium, isEscolar } = useSubscription()
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced'>('basic')
  const [hasInteracted, setHasInteracted] = useState(false)

  useEffect(() => {
    if (hasInteracted && onConfigurationInteracted) {
      onConfigurationInteracted()
    }
  }, [hasInteracted, onConfigurationInteracted])

  const handleConfigChange = (updates: Partial<AssessmentConfig>) => {
    setHasInteracted(true)
    onUpdate({ ...config, ...updates })
  }

  const handlePdfOptionChange = (field: string, value: any) => {
    setHasInteracted(true)
    onUpdate({
      ...config,
      pdfOptions: {
        ...config.pdfOptions,
        [field]: value
      }
    })
  }

  const handleHeaderChange = (field: string, value: string) => {
    setHasInteracted(true)
    onUpdate({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        [field]: value
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-700"
      >
        <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto mb-4">
          <Settings className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Personalize sua avaliação
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Configure o formato e aparência do PDF
        </p>
      </motion.div>

      {/* Configuration Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Tab Headers */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab('basic')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-4 font-medium transition-colors ${
              activeTab === 'basic'
                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-b-2 border-blue-600'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <Layout className="w-5 h-5" />
            <span>Configurações Básicas</span>
          </button>
          <button
            onClick={() => setActiveTab('advanced')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-4 font-medium transition-colors ${
              activeTab === 'advanced'
                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-b-2 border-blue-600'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <Palette className="w-5 h-5" />
            <span>Avançadas</span>
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'basic' ? (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              {/* PDF Format */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                  Formato do PDF
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tamanho do Papel
                    </label>
                    <select
                      value={config.pdfOptions.paperSize}
                      onChange={(e) => handlePdfOptionChange('paperSize', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="A4">A4</option>
                      <option value="Letter">Letter</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Orientação
                    </label>
                    <select
                      value={config.pdfOptions.orientation}
                      onChange={(e) => handlePdfOptionChange('orientation', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="portrait">Retrato</option>
                      <option value="landscape">Paisagem</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Text Formatting */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                  Formatação do Texto
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tamanho da Fonte
                    </label>
                    <select
                      value={config.pdfOptions.fontSize}
                      onChange={(e) => handlePdfOptionChange('fontSize', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="small">Pequena</option>
                      <option value="medium">Média</option>
                      <option value="large">Grande</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Família da Fonte
                    </label>
                    <select
                      value={config.pdfOptions.fontFamily}
                      onChange={(e) => handlePdfOptionChange('fontFamily', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="times">Times New Roman</option>
                      <option value="helvetica">Helvetica</option>
                      <option value="courier">Courier</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Espaçamento
                    </label>
                    <select
                      value={config.pdfOptions.lineSpacing}
                      onChange={(e) => handlePdfOptionChange('lineSpacing', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="compact">Compacto</option>
                      <option value="normal">Normal</option>
                      <option value="expanded">Expandido</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Additional Options */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                  Opções Adicionais
                </h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={config.pdfOptions.includeAnswerSheet}
                      onChange={(e) => handlePdfOptionChange('includeAnswerSheet', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Incluir folha de respostas
                    </span>
                  </label>

                  {(isPremium || isEscolar) && (
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={config.showFooter}
                        onChange={(e) => handleConfigChange({ showFooter: e.target.checked })}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Exibir rodapé da plataforma
                      </span>
                    </label>
                  )}
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              {/* Header Configuration */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                  Configuração do Cabeçalho
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Nome da Prova
                    </label>
                    <input
                      type="text"
                      value={config.headerConfig.nomeProva}
                      onChange={(e) => handleHeaderChange('nomeProva', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Instruções
                    </label>
                    <textarea
                      value={config.headerConfig.instrucoes}
                      onChange={(e) => handleHeaderChange('instrucoes', e.target.value)}
                      rows={3}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              {/* Premium Features Notice */}
              {!(isPremium || isEscolar) && (
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-700 rounded-xl p-4">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                    🎨 Recursos Premium
                  </h5>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                    Personalizações avançadas como logos, cabeçalhos personalizados e mais opções de formatação estão disponíveis nos planos Premium e Escolar.
                  </p>
                  <button className="text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Fazer Upgrade
                  </button>
                </div>
              )}
            </motion.div>
          )}
        </div>
      </div>

      {/* Preview Notice */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4"
      >
        <div className="flex items-start space-x-3">
          <Eye className="w-5 h-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              Visualização
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Na próxima etapa você poderá visualizar como sua avaliação ficará antes de finalizar.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default ConfigurationStep
