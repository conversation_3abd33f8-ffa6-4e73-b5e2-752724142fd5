import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Filter, Plus, Trash2, Eye, BookOpen, Target, Clock, ChevronDown, ChevronUp, Tag, User, Calendar, Shield } from 'lucide-react'
import { useQuestions } from '../../../hooks/useQuestions'
import { AssessmentItem } from '../../../types/assessment'
import { Database } from '../../../types/database'
import QuestionCard from '../../questions/QuestionCard'
import QuestionDetailModal from '../../editor/QuestionDetailModal'
import TextBlockModal from '../../editor/TextBlockModal'
import { useAuth } from '../../../contexts/AuthContext'

type Question = Database['public']['Tables']['questions']['Row']

interface QuestionSelectionStepProps {
  selectedItems: AssessmentItem[]
  onUpdate: (items: AssessmentItem[]) => void
  basicInfo: {
    titulo: string
    disciplina: string
    serie: string
    nomeEscola: string
  }
}

const QuestionSelectionStep: React.FC<QuestionSelectionStepProps> = ({
  selectedItems,
  onUpdate,
  basicInfo
}) => {
  const { user, isAdmin } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [filters, setFilters] = useState({
    dificuldade: '',
    tipo: '',
    topico: '',
    competencia_bncc: '',
    autor_id: '',
    is_public: undefined as boolean | undefined,
    source: '' as 'platform' | 'teacher' | 'all' | ''
  })
  const [showQuestionDetail, setShowQuestionDetail] = useState(false)
  const [showTextBlockModal, setShowTextBlockModal] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [visibleCount, setVisibleCount] = useState(20)

  // Função para contar filtros ativos (declarada antes de ser usada)
  const getActiveFiltersCount = () => {
    let count = 0
    if (searchTerm) count++
    if (filters.dificuldade) count++
    if (filters.tipo) count++
    if (filters.topico) count++
    if (filters.competencia_bncc) count++
    if (filters.autor_id) count++
    if (filters.is_public !== undefined) count++
    if (filters.source) count++
    return count
  }

  // Construir objeto de filtros limpo (sem valores vazios)
  const questionFilters = {
    ...(searchTerm && { search: searchTerm }),
    ...(basicInfo.disciplina && { disciplina: basicInfo.disciplina }),
    ...(basicInfo.serie && { serie: basicInfo.serie }),
    ...(filters.dificuldade && { dificuldade: filters.dificuldade }),
    ...(filters.tipo && { tipo: filters.tipo }),
    ...(filters.topico && { topico: filters.topico }),
    ...(filters.competencia_bncc && { competencia_bncc: filters.competencia_bncc }),
    ...(filters.autor_id && { autor_id: filters.autor_id }),
    ...(filters.is_public !== undefined && { is_public: filters.is_public }),
    ...(filters.source && { source: filters.source }),
    status: 'approved'
  }



  // Buscar questões com filtros baseados nas informações básicas e filtros avançados
  const { questions, isLoading } = useQuestions(questionFilters)

  const addQuestion = (question: Question) => {
    const isAlreadySelected = selectedItems.some(item => 
      'id' in item && item.id === question.id
    )
    
    if (!isAlreadySelected) {
      onUpdate([...selectedItems, question])
    }
  }

  const removeItem = (index: number) => {
    const newItems = selectedItems.filter((_, i) => i !== index)
    onUpdate(newItems)
  }

  const handleViewQuestionDetail = (question: Question) => {
    setSelectedQuestion(question)
    setShowQuestionDetail(true)
  }

  const handleAddTextBlock = (textBlock: any) => {
    onUpdate([...selectedItems, textBlock])
  }

  const loadMore = () => {
    setVisibleCount(prev => prev + 20) // Aumentado de 10 para 20
  }

  const filteredQuestions = questions.filter(question =>
    !selectedItems.some(item => 'id' in item && item.id === question.id)
  )



  // Fallback: Se não há questões e temos filtros de disciplina/série, tentar sem eles
  const shouldShowFallbackMessage = !isLoading && questions.length === 0 && (basicInfo.disciplina || basicInfo.serie)

  const selectedQuestions = selectedItems.filter(item => 'tipo' in item).length
  const selectedTextBlocks = selectedItems.filter(item => 'type' in item && item.type === 'text').length

  // Função para limpar todos os filtros
  const clearAllFilters = () => {
    setFilters({
      dificuldade: '',
      tipo: '',
      topico: '',
      competencia_bncc: '',
      autor_id: '',
      is_public: undefined,
      source: ''
    })
    setSearchTerm('')
  }

  // Obter lista única de tópicos das questões carregadas
  const availableTopics = [...new Set(questions.map(q => q.topico).filter(Boolean))].sort()

  // Obter lista única de competências BNCC das questões carregadas
  const availableCompetencias = [...new Set(questions.map(q => q.competencia_bncc).filter(Boolean))].sort()

  // Filtros pré-definidos para acesso rápido
  const quickFilters = [
    {
      name: 'Questões Fáceis',
      icon: '🟢',
      filters: { dificuldade: 'Fácil', tipo: '', topico: '', competencia_bncc: '', autor_id: '', is_public: undefined, source: '' }
    },
    {
      name: 'Múltipla Escolha',
      icon: '📝',
      filters: { dificuldade: '', tipo: 'multipla_escolha', topico: '', competencia_bncc: '', autor_id: '', is_public: undefined, source: '' }
    },
    {
      name: 'Dissertativas',
      icon: '✍️',
      filters: { dificuldade: '', tipo: 'dissertativa', topico: '', competencia_bncc: '', autor_id: '', is_public: undefined, source: '' }
    },
    {
      name: 'Da Plataforma',
      icon: '🏛️',
      filters: { dificuldade: '', tipo: '', topico: '', competencia_bncc: '', autor_id: '', is_public: undefined, source: 'platform' }
    }
  ]

  const applyQuickFilter = (quickFilter: typeof quickFilters[0]) => {
    setFilters(quickFilter.filters)
    setSearchTerm('')
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Seleção de Questões
          </h3>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-1 rounded-full">
              <BookOpen className="w-4 h-4 text-blue-500" />
              <span className="font-medium">{selectedQuestions} questões</span>
            </div>
            {selectedTextBlocks > 0 && (
              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-1 rounded-full">
                <Target className="w-4 h-4 text-purple-500" />
                <span className="font-medium">{selectedTextBlocks} textos</span>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400">
          {basicInfo.disciplina && basicInfo.serie ? (
            <>Buscando questões de <strong>{basicInfo.disciplina}</strong> para <strong>{basicInfo.serie}</strong></>
          ) : (
            'Selecione questões para sua avaliação'
          )}
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Question Bank */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search and Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            {/* Busca e filtros básicos */}
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Buscar questões..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <select
                value={filters.dificuldade}
                onChange={(e) => setFilters(prev => ({ ...prev, dificuldade: e.target.value }))}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Todas as dificuldades</option>
                <option value="Fácil">Fácil</option>
                <option value="Médio">Médio</option>
                <option value="Difícil">Difícil</option>
              </select>

              <select
                value={filters.tipo}
                onChange={(e) => setFilters(prev => ({ ...prev, tipo: e.target.value }))}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Todos os tipos</option>
                <option value="multipla_escolha">Múltipla Escolha</option>
                <option value="dissertativa">Dissertativa</option>
                <option value="verdadeiro_falso">Verdadeiro/Falso</option>
              </select>
            </div>

            {/* Filtros rápidos predefinidos */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filtros rápidos:</h4>
              <div className="flex flex-wrap gap-2">
                {quickFilters.map((quickFilter) => (
                  <button
                    key={quickFilter.name}
                    onClick={() => applyQuickFilter(quickFilter)}
                    className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <span>{quickFilter.icon}</span>
                    <span>{quickFilter.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Filtros rápidos em chips por tópicos */}
            {availableTopics.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tópicos populares:</h4>
                <div className="flex flex-wrap gap-2">
                  {availableTopics.slice(0, 6).map((topico) => (
                    <button
                      key={topico}
                      onClick={() => setFilters(prev => ({
                        ...prev,
                        topico: prev.topico === topico ? '' : topico
                      }))}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                        filters.topico === topico
                          ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {topico}
                    </button>
                  ))}
                  {availableTopics.length > 6 && (
                    <button
                      onClick={() => setShowAdvancedFilters(true)}
                      className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                      +{availableTopics.length - 6} mais
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Botão para mostrar/ocultar filtros avançados */}
            <div className="flex items-center justify-between">
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
              >
                <Filter className="w-4 h-4" />
                <span>Filtros Avançados</span>
                {getActiveFiltersCount() > 3 && (
                  <span className="bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
                    +{getActiveFiltersCount() - 3}
                  </span>
                )}
                {showAdvancedFilters ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>

              {getActiveFiltersCount() > 0 && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  Limpar filtros ({getActiveFiltersCount()})
                </button>
              )}
            </div>

            {/* Filtros avançados (colapsáveis) */}
            <AnimatePresence>
              {showAdvancedFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Filtro por Tópico */}
                    <div>
                      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <Tag className="w-4 h-4" />
                        <span>Tópico</span>
                      </label>
                      <select
                        value={filters.topico}
                        onChange={(e) => setFilters(prev => ({ ...prev, topico: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                      >
                        <option value="">Todos os tópicos</option>
                        {availableTopics.map((topico) => (
                          <option key={topico} value={topico}>
                            {topico}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Filtro por Competência BNCC */}
                    <div>
                      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <Shield className="w-4 h-4" />
                        <span>Competência BNCC</span>
                      </label>
                      <select
                        value={filters.competencia_bncc}
                        onChange={(e) => setFilters(prev => ({ ...prev, competencia_bncc: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                      >
                        <option value="">Todas as competências</option>
                        {availableCompetencias.map((competencia) => (
                          <option key={competencia} value={competencia}>
                            {competencia}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Filtro por Origem */}
                    <div>
                      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <User className="w-4 h-4" />
                        <span>Origem</span>
                      </label>
                      <select
                        value={filters.source}
                        onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                      >
                        <option value="">Todas as origens</option>
                        <option value="platform">Plataforma</option>
                        <option value="teacher">Professores</option>
                      </select>
                    </div>

                    {/* Filtro por Visibilidade */}
                    <div>
                      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <Eye className="w-4 h-4" />
                        <span>Visibilidade</span>
                      </label>
                      <select
                        value={filters.is_public === undefined ? '' : filters.is_public ? 'public' : 'private'}
                        onChange={(e) => setFilters(prev => ({
                          ...prev,
                          is_public: e.target.value === '' ? undefined : e.target.value === 'public'
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                      >
                        <option value="">Todas</option>
                        <option value="public">Públicas</option>
                        <option value="private">Privadas</option>
                      </select>
                    </div>

                    {/* Filtro por Autor (apenas para admins) */}
                    {isAdmin && (
                      <div>
                        <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          <User className="w-4 h-4" />
                          <span>Minhas Questões</span>
                        </label>
                        <select
                          value={filters.autor_id === user?.id ? 'mine' : ''}
                          onChange={(e) => setFilters(prev => ({
                            ...prev,
                            autor_id: e.target.value === 'mine' ? user?.id || '' : ''
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                        >
                          <option value="">Todos os autores</option>
                          <option value="mine">Minhas questões</option>
                        </select>
                      </div>
                    )}
                  </div>

                  {/* Resumo dos filtros ativos */}
                  {getActiveFiltersCount() > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-sm text-blue-800 dark:text-blue-300">
                          <Filter className="w-4 h-4" />
                          <span>
                            {getActiveFiltersCount()} filtro{getActiveFiltersCount() > 1 ? 's' : ''} ativo{getActiveFiltersCount() > 1 ? 's' : ''} •
                            {filteredQuestions.length} questão{filteredQuestions.length !== 1 ? 'ões' : ''} encontrada{filteredQuestions.length !== 1 ? 's' : ''}
                          </span>
                        </div>

                        {/* Tags dos filtros ativos */}
                        <div className="flex flex-wrap gap-1">
                          {searchTerm && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300">
                              Busca: "{searchTerm.substring(0, 10)}{searchTerm.length > 10 ? '...' : ''}"
                            </span>
                          )}
                          {filters.dificuldade && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300">
                              {filters.dificuldade}
                            </span>
                          )}
                          {filters.tipo && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300">
                              {filters.tipo.replace('_', ' ')}
                            </span>
                          )}
                          {filters.topico && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300">
                              {filters.topico}
                            </span>
                          )}
                          {filters.source && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-300">
                              {filters.source === 'platform' ? 'Plataforma' : 'Professores'}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Questions List */}
          <div className="space-y-3">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Carregando questões...</p>
              </div>
            ) : filteredQuestions.length === 0 ? (
              <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
                <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                {questions.length === 0 ? (
                  <div>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">
                      Nenhuma questão encontrada
                    </p>
                    {shouldShowFallbackMessage && (
                      <div className="text-sm text-gray-500 dark:text-gray-500 space-y-1">
                        <p>Filtros aplicados:</p>
                        {basicInfo.disciplina && <p>• Disciplina: {basicInfo.disciplina}</p>}
                        {basicInfo.serie && <p>• Série: {basicInfo.serie}</p>}
                        <button
                          onClick={clearAllFilters}
                          className="mt-2 text-blue-600 dark:text-blue-400 hover:underline"
                        >
                          Limpar filtros e ver todas as questões
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">
                    Todas as questões já foram selecionadas
                  </p>
                )}
              </div>
            ) : (
              <>
                {filteredQuestions.slice(0, visibleCount).map((question) => (
                  <motion.div
                    key={question.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden"
                  >
                    <QuestionCard
                      question={question}
                      variant="list"
                      onAddToAssessment={() => addQuestion(question)}
                      onViewDetail={() => handleViewQuestionDetail(question)}
                    />
                  </motion.div>
                ))}
                
                {visibleCount < filteredQuestions.length && (
                  <button
                    onClick={loadMore}
                    className="w-full py-3 text-blue-600 hover:text-blue-700 font-medium transition-colors"
                  >
                    Carregar mais questões ({filteredQuestions.length - visibleCount} restantes)
                  </button>
                )}
              </>
            )}
          </div>

          {/* Add Text Block Button */}
          <button
            onClick={() => setShowTextBlockModal(true)}
            className="w-full flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span>Adicionar Bloco de Texto</span>
          </button>
        </div>

        {/* Selected Items */}
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 sticky top-4">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
              Itens Selecionados ({selectedItems.length})
            </h4>
            
            {selectedItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Nenhum item selecionado</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                <AnimatePresence>
                  {selectedItems.map((item, index) => (
                    <motion.div
                      key={`${item.id || index}-${item.type || 'question'}`}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                            #{index + 1}
                          </span>
                          <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 rounded-full">
                            {'tipo' in item ? item.tipo?.replace('_', ' ') : 'Texto'}
                          </span>
                          {'dificuldade' in item && item.dificuldade && (
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              item.dificuldade === 'Fácil' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                              item.dificuldade === 'Médio' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300' :
                              'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
                            }`}>
                              {item.dificuldade}
                            </span>
                          )}
                        </div>
                        <button
                          onClick={() => removeItem(index)}
                          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>

                      <div className="mb-2">
                        <p className="text-sm text-gray-900 dark:text-white line-clamp-3">
                          {'enunciado' in item
                            ? item.enunciado
                            : item.content
                          }
                        </p>
                      </div>

                      {'topico' in item && item.topico && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                          <Tag className="w-3 h-3" />
                          <span>{item.topico}</span>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <QuestionDetailModal
        isOpen={showQuestionDetail}
        onClose={() => setShowQuestionDetail(false)}
        question={selectedQuestion}
        onAddToAssessment={selectedQuestion ? () => addQuestion(selectedQuestion) : undefined}
      />

      <TextBlockModal
        isOpen={showTextBlockModal}
        onClose={() => setShowTextBlockModal(false)}
        onSave={handleAddTextBlock}
      />
    </div>
  )
}

export default QuestionSelectionStep
