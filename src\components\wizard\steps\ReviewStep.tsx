import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Eye, Download, Save, Edit, Check, FileText, BookOpen, Settings, AlertCircle, Layout } from 'lucide-react'
import { WizardData } from '../../../hooks/useAssessmentWizard'
import { useAssessments } from '../../../hooks/useAssessments'
import { useAuth } from '../../../contexts/AuthContext'
import { useSubscription } from '../../../contexts/SubscriptionContext'
import { useUsageLimits } from '../../../hooks/useUsageLimits'
import { generatePDF, downloadPDF } from '../../../utils/pdfGenerator'
import { WATERMARK_CONFIG } from '../../../constants/usageLimits'
import { useAssets } from '../../../hooks/useAssets'
import { Database } from '../../../types/database'
import { CreateTemplateDTO } from '../../../types/templates'
import { TextBlock } from '../../../types/assessment'
import SaveAsTemplateModal from '../../editor/SaveAsTemplateModal'
import { supabase } from '../../../lib/supabase'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

type Question = Database['public']['Tables']['questions']['Row']

interface ReviewStepProps {
  wizardData: WizardData
  onEdit: (stepIndex: number) => void
}

const ReviewStep: React.FC<ReviewStepProps> = ({ wizardData, onEdit }) => {
  const navigate = useNavigate()
  const { isAdmin } = useAuth()
  const { isPremium, isEscolar } = useSubscription()
  const { getAssetUrl } = useAssets()
  const { createAssessment, isCreating } = useAssessments()
  const {
    checkCanPerformAction,
    trackUsage,
    isPaidUser,
    limitStatus
  } = useUsageLimits()
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false)
  const [showSaveModal, setShowSaveModal] = useState(false)
  const [showSaveAsTemplateModal, setShowSaveAsTemplateModal] = useState(false)
  const [assessmentTitle, setAssessmentTitle] = useState(wizardData.basicInfo.titulo)

  const selectedQuestions = wizardData.selectedItems.filter(item => 'tipo' in item).length
  const selectedTextBlocks = wizardData.selectedItems.filter(item => 'type' in item && item.type === 'text').length

  const handlePreviewPdf = async () => {
    if (wizardData.selectedItems.length === 0) {
      toast.error('Adicione itens à avaliação antes de gerar o PDF')
      return
    }

    // Check usage limits for free users
    const canDownload = await checkCanPerformAction('download_pdf')
    if (!canDownload) {
      return // Error message already shown by checkCanPerformAction
    }

    try {
      setIsGeneratingPdf(true)
      toast.loading('Gerando preview do PDF...', { id: 'pdf-preview' })

      // Preparar configuração do cabeçalho com URLs das imagens
      const customization = wizardData.config.headerConfig.customization
      const headerConfigWithUrls = {
        ...wizardData.config.headerConfig,
        customization: customization ? {
          ...customization,
          customHeader: customization.customHeader?.enabled ? {
            ...customization.customHeader,
            imageUrl: customization.customHeader.asset ? getAssetUrl(customization.customHeader.asset) : undefined
          } : undefined,
          schoolLogo: customization.schoolLogo?.enabled ? {
            ...customization.schoolLogo,
            imageUrl: customization.schoolLogo.asset ? getAssetUrl(customization.schoolLogo.asset) : undefined
          } : undefined
        } : undefined
      }

      const blob = await generatePDF(wizardData.selectedItems, {
        ...wizardData.config.pdfOptions,
        headerConfig: headerConfigWithUrls,
        showFooter: isPremium || isEscolar ? wizardData.config.showFooter : true,
        watermark: isPaidUser ? undefined : WATERMARK_CONFIG.FREE_PLAN_TEXT
      })

      downloadPDF(blob, `${wizardData.basicInfo.titulo || 'Avaliacao'}_Preview.pdf`)

      // Track PDF download with new system
      await trackUsage('pdf_downloaded', {
        assessment_title: wizardData.basicInfo.titulo,
        questions_count: selectedQuestions,
        text_blocks_count: selectedTextBlocks,
        createdWith: 'wizard'
      })

      toast.success('Preview gerado com sucesso!', { id: 'pdf-preview' })
    } catch (error) {
      console.error('Error generating PDF preview:', error)
      toast.error('Erro ao gerar preview do PDF', { id: 'pdf-preview' })
    } finally {
      setIsGeneratingPdf(false)
    }
  }

  const handleSaveAssessment = async () => {
    if (wizardData.selectedItems.length === 0) {
      toast.error('Adicione pelo menos um item à avaliação')
      return
    }

    // Check usage limits for free users - CRITICAL SECURITY FIX
    const canCreate = await checkCanPerformAction('create_assessment')
    if (!canCreate) {
      return // Error message already shown by checkCanPerformAction
    }

    try {
      const questionIds = wizardData.selectedItems
        .filter(item => 'tipo' in item && item.tipo !== undefined)
        .map(item => (item as Question).id)

      const textBlocks = wizardData.selectedItems
        .filter(item => 'type' in item && item.type === 'text')
        .map(item => item as TextBlock)

      await createAssessment({
        titulo: assessmentTitle,
        disciplina: wizardData.basicInfo.disciplina || 'Geral',
        serie: wizardData.basicInfo.serie || 'Geral',
        questoes_ids: questionIds,
        configuracao: {
          ...wizardData.config,
          titulo: assessmentTitle,
          textBlocks
        },
        is_public: isAdmin,
        versoes: 1,
        estatisticas: {},
        metadata: {
          createdWith: 'wizard',
          wizardVersion: '1.0'
        }
      })

      // Track usage with new system - CRITICAL: Track assessment creation
      await trackUsage('assessment_created', {
        titulo: assessmentTitle,
        disciplina: wizardData.basicInfo.disciplina,
        serie: wizardData.basicInfo.serie,
        questoes_count: questionIds.length,
        text_blocks_count: textBlocks.length,
        createdWith: 'wizard',
        wizardVersion: '1.0'
      })

      toast.success('Avaliação salva com sucesso!')
      navigate('/app/assessments')
    } catch (error) {
      console.error('Error saving assessment:', error)
      toast.error('Erro ao salvar avaliação')
    }
  }

  const handleSaveAsTemplate = async (templateData: CreateTemplateDTO) => {
    try {
      const { data: user } = await supabase.auth.getUser()

      const insertData = {
        nome: templateData.nome,
        categoria: templateData.categoria,
        descricao: templateData.descricao || null,
        content: templateData.content,
        metadata: {
          ...templateData.metadata,
          createdAt: new Date().toISOString()
        },
        layout_config: templateData.content.layout, // Backward compatibility
        is_premium: templateData.is_premium || false,
        is_system: templateData.is_system || false,
        autor_id: user.user?.id || null,
        tags: templateData.tags || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { error } = await supabase
        .from('templates')
        .insert(insertData)

      if (error) throw error

      toast.success('Template salvo com sucesso!')
      setShowSaveAsTemplateModal(false)
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Erro ao salvar template')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-700"
      >
        <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mx-auto mb-4">
          <Check className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Sua avaliação está pronta!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Revise as informações e finalize sua avaliação
        </p>
      </motion.div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white">Informações</h4>
              <button
                onClick={() => onEdit(0)}
                className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
              >
                <Edit className="w-3 h-3" />
                <span>Editar</span>
              </button>
            </div>
          </div>
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Título:</span>
              <p className="font-medium text-gray-900 dark:text-white">{wizardData.basicInfo.titulo}</p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Disciplina:</span>
              <p className="font-medium text-gray-900 dark:text-white">{wizardData.basicInfo.disciplina}</p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Série:</span>
              <p className="font-medium text-gray-900 dark:text-white">{wizardData.basicInfo.serie}</p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Escola:</span>
              <p className="font-medium text-gray-900 dark:text-white">{wizardData.basicInfo.nomeEscola}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <BookOpen className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white">Conteúdo</h4>
              <button
                onClick={() => onEdit(1)}
                className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
              >
                <Edit className="w-3 h-3" />
                <span>Editar</span>
              </button>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Questões:</span>
              <span className="font-semibold text-gray-900 dark:text-white">{selectedQuestions}</span>
            </div>
            {selectedTextBlocks > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">Blocos de texto:</span>
                <span className="font-semibold text-gray-900 dark:text-white">{selectedTextBlocks}</span>
              </div>
            )}
            <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Total de itens:</span>
              <span className="font-bold text-lg text-gray-900 dark:text-white">{wizardData.selectedItems.length}</span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Settings className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white">Configurações</h4>
              <button
                onClick={() => onEdit(2)}
                className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
              >
                <Edit className="w-3 h-3" />
                <span>Editar</span>
              </button>
            </div>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-500 dark:text-gray-400">Formato:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {wizardData.config.pdfOptions.paperSize} {wizardData.config.pdfOptions.orientation === 'portrait' ? 'Retrato' : 'Paisagem'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-500 dark:text-gray-400">Fonte:</span>
              <span className="font-medium text-gray-900 dark:text-white capitalize">
                {wizardData.config.pdfOptions.fontSize}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-500 dark:text-gray-400">Folha de respostas:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {wizardData.config.pdfOptions.includeAnswerSheet ? 'Sim' : 'Não'}
              </span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
      >
        <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
          Finalizar Avaliação
        </h4>
        
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={handlePreviewPdf}
            disabled={isGeneratingPdf || wizardData.selectedItems.length === 0 || (!isPaidUser && !limitStatus.canDownloadPDF)}
            className={`flex items-center justify-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
              !isPaidUser && !limitStatus.canDownloadPDF
                ? 'bg-gray-400 cursor-not-allowed text-white'
                : 'bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white'
            }`}
            title={!isPaidUser && !limitStatus.canDownloadPDF ? 'Limite de downloads atingido' : ''}
          >
            <Eye className="w-5 h-5" />
            <span>{isGeneratingPdf ? 'Gerando...' : 'Visualizar PDF'}</span>
            {!isPaidUser && !limitStatus.canDownloadPDF && (
              <AlertCircle className="w-4 h-4 text-yellow-400" />
            )}
          </button>

          <button
            onClick={() => setShowSaveModal(true)}
            disabled={isCreating || wizardData.selectedItems.length === 0 || (!isPaidUser && !limitStatus.canCreateAssessment)}
            className={`flex items-center justify-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
              !isPaidUser && !limitStatus.canCreateAssessment
                ? 'bg-gray-400 cursor-not-allowed text-white'
                : 'bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white'
            }`}
            title={!isPaidUser && !limitStatus.canCreateAssessment ? 'Limite de avaliações atingido' : ''}
          >
            <Save className="w-5 h-5" />
            <span>{isCreating ? 'Salvando...' : 'Salvar Avaliação'}</span>
            {!isPaidUser && !limitStatus.canCreateAssessment && (
              <AlertCircle className="w-4 h-4 text-yellow-400" />
            )}
          </button>

          <button
            onClick={() => setShowSaveAsTemplateModal(true)}
            disabled={wizardData.selectedItems.length === 0}
            className="flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            title="Salvar configuração como modelo reutilizável"
          >
            <Layout className="w-5 h-5" />
            <span>Salvar como Modelo</span>
          </button>
        </div>

        {wizardData.selectedItems.length === 0 && (
          <div className="flex items-center space-x-2 mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Adicione pelo menos um item à avaliação para continuar
            </p>
          </div>
        )}
      </motion.div>

      {/* Save Modal */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-xl w-full max-w-md"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Confirmar Título da Avaliação
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Título da Avaliação
              </label>
              <input
                type="text"
                value={assessmentTitle}
                onChange={(e) => setAssessmentTitle(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowSaveModal(false)}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleSaveAssessment}
                disabled={!assessmentTitle.trim() || isCreating}
                className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isCreating ? 'Salvando...' : 'Salvar'}
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Save As Template Modal */}
      <SaveAsTemplateModal
        isOpen={showSaveAsTemplateModal}
        onClose={() => setShowSaveAsTemplateModal(false)}
        onSave={handleSaveAsTemplate}
        config={wizardData.config}
        selectedItems={wizardData.selectedItems}
      />
    </div>
  )
}

export default ReviewStep
