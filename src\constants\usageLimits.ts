/**
 * Centralized Usage Limits Configuration
 * 
 * This file contains all usage limits and related constants for the EduAssess platform.
 * All hard-coded values throughout the codebase should reference these constants.
 */

// Usage limits for different plan types
export const USAGE_LIMITS = {
  FREE_PLAN: {
    ASSESSMENTS_PER_MONTH: 5,
    PDF_DOWNLOADS_PER_MONTH: 10,
    QUESTIONS_PER_MONTH: 50
  },
  PREMIUM_PLAN: {
    ASSESSMENTS_PER_MONTH: -1, // unlimited
    PDF_DOWNLOADS_PER_MONTH: -1, // unlimited
    QUESTIONS_PER_MONTH: -1 // unlimited
  },
  ESCOLAR_PLAN: {
    ASSESSMENTS_PER_MONTH: -1, // unlimited
    PDF_DOWNLOADS_PER_MONTH: -1, // unlimited
    QUESTIONS_PER_MONTH: -1 // unlimited
  }
} as const

// Watermark configuration
export const WATERMARK_CONFIG = {
  FREE_PLAN_TEXT: 'VERSÃO GRATUITA - EDUASSESS.COM',
  POSITION: 'center', // center, top, bottom
  OPACITY: 0.1,
  FONT_SIZE: 48,
  COLOR: '#666666'
} as const

// Thresholds for "near limit" warnings
export const NEAR_LIMIT_THRESHOLDS = {
  ASSESSMENTS: 1, // Show warning when 1 or fewer remaining
  PDF_DOWNLOADS: 2, // Show warning when 2 or fewer remaining
  QUESTIONS: 5 // Show warning when 5 or fewer remaining
} as const

// Rate limiting configuration (per minute)
export const RATE_LIMITS = {
  ASSESSMENT_CREATION: 10, // 10 assessments per minute max
  PDF_GENERATION: 5, // 5 PDFs per minute max
  QUESTION_CREATION: 20, // 20 questions per minute max
  AI_GENERATION: 3 // 3 AI generations per minute max
} as const

// Usage tracking action types
export const USAGE_ACTION_TYPES = {
  ASSESSMENT_CREATED: 'assessment_created',
  PDF_DOWNLOADED: 'pdf_downloaded',
  QUESTION_CREATED: 'question_created',
  AI_GENERATION_USED: 'ai_generation_used'
} as const

// Resource types for usage tracking
export const RESOURCE_TYPES = {
  ASSESSMENT: 'assessment',
  QUESTION: 'question',
  PDF: 'pdf',
  AI_CONTENT: 'ai_content'
} as const

// Plan types
export const PLAN_TYPES = {
  FREE: 'gratuito',
  PREMIUM: 'premium',
  ESCOLAR: 'escolar'
} as const

// Helper functions to get limits based on plan
export const getUsageLimits = (planType: string) => {
  switch (planType) {
    case PLAN_TYPES.PREMIUM:
      return USAGE_LIMITS.PREMIUM_PLAN
    case PLAN_TYPES.ESCOLAR:
      return USAGE_LIMITS.ESCOLAR_PLAN
    case PLAN_TYPES.FREE:
    default:
      return USAGE_LIMITS.FREE_PLAN
  }
}

// Helper function to check if plan is paid
export const isPaidPlan = (planType: string): boolean => {
  return planType === PLAN_TYPES.PREMIUM || planType === PLAN_TYPES.ESCOLAR
}

// Helper function to check if user is near limit
export const isNearLimit = (current: number, limit: number, type: keyof typeof NEAR_LIMIT_THRESHOLDS): boolean => {
  if (limit === -1) return false // unlimited
  const threshold = NEAR_LIMIT_THRESHOLDS[type]
  const remaining = Math.max(0, limit - current)
  return remaining <= threshold
}

// Helper function to calculate usage percentage
export const getUsagePercentage = (current: number, limit: number): number => {
  if (limit === -1) return 0 // unlimited
  return Math.min(100, (current / limit) * 100)
}

// Helper function to get remaining usage
export const getRemainingUsage = (current: number, limit: number): number => {
  if (limit === -1) return -1 // unlimited
  return Math.max(0, limit - current)
}

// Helper function to check if action is allowed
export const canPerformAction = (current: number, limit: number): boolean => {
  if (limit === -1) return true // unlimited
  return current < limit
}

// Error messages for different limit scenarios
export const LIMIT_ERROR_MESSAGES = {
  ASSESSMENT_LIMIT_REACHED: (limit: number) => 
    `Limite de ${limit} avaliações por mês atingido. Faça upgrade para continuar!`,
  PDF_LIMIT_REACHED: (limit: number) => 
    `Limite de ${limit} downloads de PDF por mês atingido. Faça upgrade para downloads ilimitados!`,
  QUESTION_LIMIT_REACHED: (limit: number) => 
    `Limite de ${limit} questões por mês atingido. Faça upgrade para continuar!`,
  NEAR_ASSESSMENT_LIMIT: (remaining: number) => 
    `Você tem apenas ${remaining} avaliação(ões) restante(s) este mês.`,
  NEAR_PDF_LIMIT: (remaining: number) => 
    `Você tem apenas ${remaining} download(s) de PDF restante(s) este mês.`,
  NEAR_QUESTION_LIMIT: (remaining: number) => 
    `Você tem apenas ${remaining} questão(ões) restante(s) este mês.`
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  ASSESSMENT_CREATED: 'Avaliação criada com sucesso!',
  PDF_DOWNLOADED: 'PDF gerado com sucesso!',
  QUESTION_CREATED: 'Questão criada com sucesso!',
  UPGRADE_SUCCESSFUL: 'Upgrade realizado com sucesso! Você agora tem acesso ilimitado.'
} as const

// Upgrade prompts
export const UPGRADE_PROMPTS = {
  ASSESSMENT_LIMIT: {
    title: 'Limite de Avaliações Atingido',
    description: 'Você atingiu o limite de avaliações do plano gratuito. Faça upgrade para criar avaliações ilimitadas!'
  },
  PDF_LIMIT: {
    title: 'Limite de Downloads Atingido',
    description: 'Você atingiu o limite de downloads do plano gratuito. Faça upgrade para downloads ilimitados!'
  },
  GENERAL: {
    title: 'Desbloqueie Todo o Potencial',
    description: 'Faça upgrade para o Premium e tenha acesso a recursos ilimitados e avançados.'
  }
} as const

// Premium features list for upgrade prompts
export const PREMIUM_FEATURES = [
  'Avaliações ilimitadas',
  'Downloads de PDF ilimitados',
  'Templates premium',
  'Geração por IA',
  'Colaboração em equipe',
  'Analytics avançados',
  'Suporte prioritário',
  'Sem marca d\'água nos PDFs'
] as const

// Query keys for React Query
export const QUERY_KEYS = {
  USAGE_LIMITS: (userId: string, period: string) => ['usage-limits', userId, period],
  MONTHLY_USAGE: (userId: string, year: number, month: number) => ['monthly-usage', userId, year, month],
  USER_SUBSCRIPTION: (userId: string) => ['user-subscription', userId]
} as const

// Cache configuration
export const CACHE_CONFIG = {
  USAGE_LIMITS_STALE_TIME: 1000 * 60 * 5, // 5 minutes
  SUBSCRIPTION_STALE_TIME: 1000 * 60 * 10, // 10 minutes
  REFETCH_ON_WINDOW_FOCUS: true,
  RETRY_ATTEMPTS: 3
} as const
