import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { User, Question, Assessment, FilterOptions } from '../types';
import { mockUser, generateMockQuestions } from '../data/mockData';

interface AppState {
  user: User | null;
  questions: Question[];
  currentAssessment: Assessment | null;
  filters: FilterOptions;
  selectedQuestions: Question[];
  sidebarCollapsed: boolean;
  loading: boolean;
}

type AppAction =
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_QUESTIONS'; payload: Question[] }
  | { type: 'ADD_QUESTION_TO_ASSESSMENT'; payload: Question }
  | { type: 'REMOVE_QUESTION_FROM_ASSESSMENT'; payload: string }
  | { type: 'SET_FILTERS'; payload: Partial<FilterOptions> }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'TOGGLE_FAVORITE'; payload: string }
  | { type: 'CREATE_NEW_ASSESSMENT' }
  | { type: 'REORDER_QUESTIONS'; payload: Question[] };

const initialState: AppState = {
  user: mockUser,
  questions: generateMockQuestions(),
  currentAssessment: null,
  filters: {
    disciplina: '',
    serie: '',
    topico: '',
    dificuldade: '',
    tipo: '',
    competenciaBncc: '',
    tags: []
  },
  selectedQuestions: [],
  sidebarCollapsed: false,
  loading: false
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_QUESTIONS':
      return { ...state, questions: action.payload };
    
    case 'ADD_QUESTION_TO_ASSESSMENT':
      return {
        ...state,
        selectedQuestions: [...state.selectedQuestions, action.payload]
      };
    
    case 'REMOVE_QUESTION_FROM_ASSESSMENT':
      return {
        ...state,
        selectedQuestions: state.selectedQuestions.filter(q => q.id !== action.payload)
      };
    
    case 'SET_FILTERS':
      return {
        ...state,
        filters: { ...state.filters, ...action.payload }
      };
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'TOGGLE_FAVORITE':
      return {
        ...state,
        questions: state.questions.map(q =>
          q.id === action.payload ? { ...q, favorita: !q.favorita } : q
        )
      };
    
    case 'CREATE_NEW_ASSESSMENT':
      return {
        ...state,
        selectedQuestions: [],
        currentAssessment: null
      };
    
    case 'REORDER_QUESTIONS':
      return {
        ...state,
        selectedQuestions: action.payload
      };
    
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};