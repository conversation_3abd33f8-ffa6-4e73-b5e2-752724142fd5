import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  isAdmin: boolean
  isSchoolAdmin: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: { nome: string; escola?: string }) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)
  const [initializing, setInitializing] = useState(false)

  const createProfile = async (userId: string, userData: { nome: string; escola?: string }) => {
    try {
      console.log('Creating profile manually for user:', userId)
      
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          nome: userData.nome,
          email: user?.email || '',
          escola: userData.escola || null,
          disciplinas: [],
          plano: 'gratuito',
          is_admin: false,
          is_school_admin: false,
          configuracoes: {},
          estatisticas: {
            questoesCriadas: 0,
            provasGeradas: 0,
            ultimoAcesso: new Date().toISOString()
          },
          onboarding_completed: false
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating profile manually:', error)
        throw error
      }

      console.log('Profile created manually:', data)
      return data
    } catch (error) {
      console.error('Failed to create profile manually:', error)
      throw error
    }
  }

  const fetchProfile = async (userId: string, retryCount = 0): Promise<Profile | null> => {
    try {
      console.log(`Fetching profile for user: ${userId}, attempt: ${retryCount + 1}`)
      
      // Use standard client-side query with RLS
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle()
      
      if (profileError) {
        console.log('Profile fetch error:', profileError.code, profileError.message)
        
        // If profile not found and not the last attempt, retry
        if (retryCount < 3) {
          console.log('Profile not found, waiting for trigger...')
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))
          return fetchProfile(userId, retryCount + 1)
        }
        
        throw profileError
      }

      console.log('Profile fetched successfully:', profileData?.nome || 'No profile found')
      console.log('Is admin:', profileData?.is_admin)
      console.log('Is school admin:', profileData?.is_school_admin)
      console.log('School ID:', profileData?.school_id)
      return profileData
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  const refreshProfile = async (): Promise<void> => {
    if (!user) {
      console.log('refreshProfile: Não é possível atualizar o perfil: Nenhum usuário logado.');
      return;
    }

    try {
      console.log('refreshProfile: [START] Atualizando perfil para o usuário:', user.id);
      const profileData = await fetchProfile(user.id, 0);

      if (profileData) {
        setProfile(profileData);
        console.log('refreshProfile: Perfil atualizado com sucesso no estado local:', profileData.nome);
        console.log('refreshProfile: Status de administrador:', profileData.is_admin);
        console.log('refreshProfile: Status de admin escola:', profileData.is_school_admin);
        console.log('refreshProfile: School ID:', profileData.school_id);
      } else {
        console.log('refreshProfile: Falha ao atualizar perfil: Nenhum dado de perfil retornado.');
      }
      console.log('refreshProfile: [END] Função refreshProfile concluída.');
    } catch (error) {
      console.error('refreshProfile: [ERROR] Erro ao atualizar dados do perfil:', error);
      toast.error('Erro ao atualizar dados do perfil');
    }
  };

  useEffect(() => {
    let mounted = true
    let authSubscription: any = null

    const initializeAuth = async () => {
      // Prevent multiple initialization attempts
      if (initializing || initialized) {
        console.log('Auth already initializing or initialized, skipping...', { initializing, initialized })
        return
      }

      setInitializing(true)

      try {
        console.log('Initializing auth...', { tabId: Date.now() })

        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          if (mounted) {
            setLoading(false)
            setInitialized(true)
            setInitializing(false)
          }
          return
        }

        console.log('Initial session:', session?.user?.id || 'No session')

        // Set session and user state
        if (mounted) {
          setSession(session)
          setUser(session?.user ?? null)

          // Fetch profile if user exists
          if (session?.user) {
            const profileData = await fetchProfile(session.user.id)
            if (mounted) {
              setProfile(profileData)
              console.log('Initial profile loaded, is_admin:', profileData?.is_admin)
              console.log('Initial profile loaded, is_school_admin:', profileData?.is_school_admin)
              console.log('Initial profile loaded, school_id:', profileData?.school_id)
            }
          } else {
            setProfile(null)
          }
        }

        // Set up auth state listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            if (!mounted) return

            console.log('Auth state changed:', event, session?.user?.id || 'No session')

            setSession(session)
            setUser(session?.user ?? null)

            if (session?.user) {
              const profileData = await fetchProfile(session.user.id)
              if (mounted) {
                setProfile(profileData)
                console.log('Profile updated on auth change, is_admin:', profileData?.is_admin)
                console.log('Profile updated on auth change, is_school_admin:', profileData?.is_school_admin)
                console.log('Profile updated on auth change, school_id:', profileData?.school_id)
              }
            } else {
              setProfile(null)
            }
          }
        )

        authSubscription = subscription

        // Complete initialization after setting up listener
        if (mounted) {
          setLoading(false)
          setInitialized(true)
          setInitializing(false)
        }

      } catch (error) {
        console.error('Error in initializeAuth:', error)
        if (mounted) {
          setLoading(false)
          setInitialized(true)
          setInitializing(false)
        }
      }
    }

    // Initialize auth on mount
    initializeAuth()

    return () => {
      mounted = false
      if (authSubscription) {
        authSubscription.unsubscribe()
      }
    }
  }, []) // Empty dependency array to run only once on mount

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signInWithPassword({ email, password })
      if (error) throw error
      toast.success('Login realizado com sucesso!')
    } catch (error) {
      const authError = error as AuthError
      toast.error(authError.message || 'Erro ao fazer login')
      throw error
    } finally {
      setLoading(false);
    }
  }

  const signUp = async (email: string, password: string, userData: { nome: string; escola?: string }) => {
    try {
      setLoading(true)
      console.log('Starting signup process...')
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            nome: userData.nome,
            escola: userData.escola
          }
        }
      })

      if (error) throw error

      console.log('Auth signup successful:', data.user?.id)

      if (data.user && !data.user.email_confirmed_at) {
        console.log('User created, waiting for profile trigger or manual creation...')
        
        const profileData = await fetchProfile(data.user.id, 0)
        
        if (!profileData) {
          console.log('Profile not created by trigger, creating manually...')
          try {
            await createProfile(data.user.id, userData)
            toast.success('Conta criada com sucesso!')
          } catch (profileError) {
            console.error('Failed to create profile manually:', profileError)
            toast.error('Conta criada, mas houve um problema ao configurar o perfil. Tente fazer login.')
          }
        } else {
          console.log('Profile created successfully by trigger')
          toast.success('Conta criada com sucesso!')
        }
      } else {
        toast.success('Conta criada com sucesso! Verifique seu email.')
      }
    } catch (error) {
      const authError = error as AuthError
      console.error('Signup error:', authError)
      toast.error(authError.message || 'Erro ao criar conta')
      throw error
    }
  }

  const signOut = async () => {
    console.log('[SIGNOUT - START] Iniciando processo de logout...');
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[SIGNOUT - ERROR] Erro ao fazer logout:', error);
        throw error;
      }
      setUser(null);
      setProfile(null);
      setSession(null);
      toast.success('Logout realizado com sucesso!');
      console.log('[SIGNOUT - SUCCESS] Logout concluído com sucesso.');
    } catch (error) {
      console.error('[SIGNOUT - CATCH ERROR] Erro inesperado durante o logout:', error);
      toast.error('Erro ao fazer logout.');
    } finally {
      setLoading(false);
      console.log('[SIGNOUT - FINALLY] setLoading(false) acionado.');
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })
      if (error) throw error
      toast.success('Email de recuperação enviado!')
    } catch (error) {
      const authError = error as AuthError
      toast.error(authError.message || 'Erro ao enviar email de recuperação')
      throw error
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) {
      console.error('updateProfile: Usuário não autenticado.');
      throw new Error('User not authenticated');
    }

    try {
      console.log('updateProfile: [START] Iniciando atualização de perfil com dados:', updates);
      // Lida com a atualização do email separadamente, pois ele é do auth.users
      const profileUpdates: Partial<Profile> = { ...updates };

      if (updates.email && updates.email !== user.email) {
        console.log('updateProfile: Tentando atualizar email do usuário na tabela auth.users para:', updates.email);
        const { error: authError } = await supabase.auth.updateUser({ email: updates.email });
        if (authError) {
          console.error('updateProfile: Erro ao atualizar email do usuário:', authError);
          throw authError; // Relança o erro para ser pego pelo catch externo
        }
        console.log('updateProfile: Email do usuário atualizado com sucesso no auth.users.');
        // Remove o email de profileUpdates, pois já foi tratado
        delete profileUpdates.email;
      }

      console.log('updateProfile: Tentando atualizar a tabela profiles com:', profileUpdates);
      // Atualiza a tabela profiles
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', user.id);

      if (profileError) {
        console.error('updateProfile: Erro ao atualizar a tabela profiles:', profileError);
        throw profileError; // Relança o erro para ser pego pelo catch externo
      }
      console.log('updateProfile: Tabela profiles atualizada com sucesso.');

      // Atualiza o estado local
      setProfile(prev => {
        if (prev) {
          console.log('updateProfile: Atualizando estado local do perfil...');
          return { ...prev, ...updates };
        }
        console.log('updateProfile: Prev profile é nulo, não atualizando estado local.');
        return null;
      });

      console.log('updateProfile: Chamando refreshProfile para garantir consistência...');
      await refreshProfile();
      console.log('updateProfile: [END] Atualização do perfil e refreshProfile concluídos com sucesso.');

    } catch (error) {
      console.error('updateProfile: [ERRO FATAL] Erro capturado na função updateProfile:', error);
      toast.error('Erro ao atualizar perfil: ' + (error instanceof Error ? error.message : String(error)));
      throw error; // Relança o erro para ser pego pelo componente chamador (Settings.tsx)
    }
  };

  // Explicitly compute isAdmin from profile
  const isAdmin = !!profile?.is_admin
  const isSchoolAdmin = !!profile?.is_school_admin

  return (
    <AuthContext.Provider value={{
      user,
      profile,
      session,
      loading,
      isAdmin,
      isSchoolAdmin,
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,
      refreshProfile
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}