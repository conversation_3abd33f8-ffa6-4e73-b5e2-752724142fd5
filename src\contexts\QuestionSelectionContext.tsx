import React, { createContext, useContext, useState, useEffect } from 'react'

interface QuestionSelectionContextType {
  selectedIds: string[]
  select: (id: string) => void
  deselect: (id: string) => void
  toggleSelect: (id: string) => void
  clearSelection: () => void
  isSelected: (id: string) => boolean
  setSelection: (ids: string[]) => void
}

const QuestionSelectionContext = createContext<QuestionSelectionContextType | undefined>(undefined)

export const QuestionSelectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedIds, setSelectedIds] = useState<string[]>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('selectedQuestionIds')
      return stored ? JSON.parse(stored) : []
    }
    return []
  })

  useEffect(() => {
    localStorage.setItem('selectedQuestionIds', JSON.stringify(selectedIds))
  }, [selectedIds])

  const select = (id: string) => setSelectedIds((prev) => prev.includes(id) ? prev : [...prev, id])
  const deselect = (id: string) => setSelectedIds((prev) => prev.filter(qid => qid !== id))
  const toggleSelect = (id: string) => setSelectedIds((prev) => prev.includes(id) ? prev.filter(qid => qid !== id) : [...prev, id])
  const clearSelection = () => setSelectedIds([])
  const isSelected = (id: string) => selectedIds.includes(id)
  const setSelection = (ids: string[]) => setSelectedIds(ids)

  return (
    <QuestionSelectionContext.Provider value={{ selectedIds, select, deselect, toggleSelect, clearSelection, isSelected, setSelection }}>
      {children}
    </QuestionSelectionContext.Provider>
  )
}

export const useQuestionSelection = () => {
  const ctx = useContext(QuestionSelectionContext)
  if (!ctx) throw new Error('useQuestionSelection must be used within a QuestionSelectionProvider')
  return ctx
} 