import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'

type Subscription = Database['public']['Tables']['subscriptions']['Row']

interface SubscriptionContextType {
  subscription: Subscription | null
  loading: boolean
  isActive: boolean
  isTrialing: boolean
  isPremium: boolean
  isEscolar: boolean
  trialEndDate: Date | null
  daysLeftInTrial: number | null
  canAccess: (feature: string) => boolean
  refreshSubscription: () => Promise<void>
  getUsageLimits: () => UsageLimits
}

interface UsageLimits {
  questionsPerMonth: number
  assessmentsPerMonth: number
  templatesAccess: boolean
  aiGeneration: boolean
  collaboration: boolean
  analytics: boolean
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined)

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading: authLoading } = useAuth()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  const fetchSubscription = async () => {
    if (!user) {
      setSubscription(null)
      setLoading(false)
      setInitialized(true)
      return
    }

    try {
      console.log('Fetching subscription for user:', user.id)
      
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .in('status', ['active', 'trialing']) // Include both active and trialing subscriptions
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle() // Use maybeSingle instead of single to avoid error when no rows

      if (error) {
        console.error('Error fetching subscription:', error)
        setSubscription(null)
      } else {
        console.log('Subscription fetched:', data ? 'Found' : 'None')
        setSubscription(data)
      }
    } catch (error) {
      console.error('Error in fetchSubscription:', error)
      setSubscription(null)
    } finally {
      setLoading(false)
      setInitialized(true)
    }
  }

  useEffect(() => {
    // Only fetch subscription when auth is not loading and we have a user state (even if null)
    if (!authLoading && !initialized) {
      fetchSubscription()
    }
  }, [user, authLoading, initialized])

  // Reset when user changes
  useEffect(() => {
    if (!authLoading) {
      setInitialized(false)
      setLoading(true)
    }
  }, [user?.id])

  // Calculate subscription status
  const isActive = subscription?.status === 'active'
  const isTrialing = subscription?.status === 'trialing'
  const isPremium = (isActive || isTrialing) && subscription?.plano === 'premium'
  const isEscolar = (isActive || isTrialing) && subscription?.plano === 'escolar'

  // Calculate trial information
  const trialEndDate = subscription?.trial_end ? new Date(subscription.trial_end) : null
  const daysLeftInTrial = trialEndDate && isTrialing
    ? Math.max(0, Math.ceil((trialEndDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
    : null

  const getUsageLimits = (): UsageLimits => {
    if (isEscolar) {
      return {
        questionsPerMonth: -1, // unlimited
        assessmentsPerMonth: -1, // unlimited
        templatesAccess: true,
        aiGeneration: true,
        collaboration: true,
        analytics: true
      }
    }

    if (isPremium) {
      return {
        questionsPerMonth: -1, // unlimited
        assessmentsPerMonth: -1, // unlimited
        templatesAccess: true,
        aiGeneration: true,
        collaboration: true,
        analytics: true
      }
    }

    // Free plan
    return {
      questionsPerMonth: 50,
      assessmentsPerMonth: 5,
      templatesAccess: false,
      aiGeneration: false,
      collaboration: false,
      analytics: false
    }
  }

  const canAccess = (feature: string): boolean => {
    const limits = getUsageLimits()
    
    const features = {
      'unlimited_questions': limits.questionsPerMonth === -1,
      'unlimited_assessments': limits.assessmentsPerMonth === -1,
      'premium_templates': limits.templatesAccess,
      'ai_generation': limits.aiGeneration,
      'collaboration': limits.collaboration,
      'analytics': limits.analytics
    }

    return features[feature as keyof typeof features] || false
  }

  return (
    <SubscriptionContext.Provider value={{
      subscription,
      loading: loading || authLoading,
      isActive,
      isTrialing,
      isPremium,
      isEscolar,
      trialEndDate,
      daysLeftInTrial,
      canAccess,
      refreshSubscription: fetchSubscription,
      getUsageLimits
    }}>
      {children}
    </SubscriptionContext.Provider>
  )
}

export const useSubscription = () => {
  const context = useContext(SubscriptionContext)
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider')
  }
  return context
}