import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { supabase } from '../lib/supabase'

type ThemeMode = 'light' | 'dark' | 'system'
type FontSize = 'small' | 'medium' | 'large'

interface ThemeContextType {
  theme: ThemeMode
  fontSize: FontSize
  compactMode: boolean
  setTheme: (theme: ThemeMode) => Promise<void>
  setFontSize: (size: FontSize) => Promise<void>
  setCompactMode: (compact: boolean) => Promise<void>
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { profile, updateProfile } = useAuth()
  const [theme, setThemeState] = useState<ThemeMode>('light')
  const [fontSize, setFontSizeState] = useState<FontSize>('medium')
  const [compactMode, setCompactModeState] = useState<boolean>(false)

  // Load theme settings from profile on mount
  useEffect(() => {
    if (profile?.configuracoes?.appearance) {
      const { theme: savedTheme, fontSize: savedFontSize, compactMode: savedCompactMode } = profile.configuracoes.appearance
      
      if (savedTheme) {
        setThemeState(savedTheme as ThemeMode)
        applyTheme(savedTheme as ThemeMode)
      }
      
      if (savedFontSize) {
        setFontSizeState(savedFontSize as FontSize)
        applyFontSize(savedFontSize as FontSize)
      }
      
      if (savedCompactMode !== undefined) {
        setCompactModeState(savedCompactMode)
        applyCompactMode(savedCompactMode)
      }
    } else {
      // Default to system preference if no saved theme
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const systemTheme = prefersDark ? 'dark' : 'light'
      applyTheme(systemTheme)
    }
  }, [profile])

  // Listen for system theme changes if using system theme
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e: MediaQueryListEvent) => {
        applyTheme('system')
      }
      
      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme])

  const applyTheme = (mode: ThemeMode) => {
    if (mode === 'dark' || (mode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  const applyFontSize = (size: FontSize) => {
    document.documentElement.dataset.fontSize = size
    
    // Apply font size classes
    const fontSizeClasses = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg'
    }
    
    document.body.classList.remove('text-sm', 'text-base', 'text-lg')
    document.body.classList.add(fontSizeClasses[size])
  }

  const applyCompactMode = (compact: boolean) => {
    if (compact) {
      document.documentElement.classList.add('compact-mode')
    } else {
      document.documentElement.classList.remove('compact-mode')
    }
  }

  const setTheme = async (newTheme: ThemeMode) => {
    try {
      setThemeState(newTheme)
      applyTheme(newTheme)
      
      if (profile) {
        // Get current configuracoes or initialize empty object
        const currentConfig = profile.configuracoes || {}
        
        // Update appearance settings
        const updatedConfig = {
          ...currentConfig,
          appearance: {
            ...(currentConfig.appearance || {}),
            theme: newTheme
          }
        }
        
        await updateProfile({
          configuracoes: updatedConfig
        })
      }
    } catch (error) {
      console.error('Error saving theme setting:', error)
    }
  }

  const setFontSize = async (newSize: FontSize) => {
    try {
      setFontSizeState(newSize)
      applyFontSize(newSize)
      
      if (profile) {
        // Get current configuracoes or initialize empty object
        const currentConfig = profile.configuracoes || {}
        
        // Update appearance settings
        const updatedConfig = {
          ...currentConfig,
          appearance: {
            ...(currentConfig.appearance || {}),
            fontSize: newSize
          }
        }
        
        await updateProfile({
          configuracoes: updatedConfig
        })
      }
    } catch (error) {
      console.error('Error saving font size setting:', error)
    }
  }

  const setCompactMode = async (compact: boolean) => {
    try {
      setCompactModeState(compact)
      applyCompactMode(compact)
      
      if (profile) {
        // Get current configuracoes or initialize empty object
        const currentConfig = profile.configuracoes || {}
        
        // Update appearance settings
        const updatedConfig = {
          ...currentConfig,
          appearance: {
            ...(currentConfig.appearance || {}),
            compactMode: compact
          }
        }
        
        await updateProfile({
          configuracoes: updatedConfig
        })
      }
    } catch (error) {
      console.error('Error saving compact mode setting:', error)
    }
  }

  return (
    <ThemeContext.Provider value={{
      theme,
      fontSize,
      compactMode,
      setTheme,
      setFontSize,
      setCompactMode
    }}>
      {children}
    </ThemeContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}