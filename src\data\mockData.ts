import { Question, User, Template } from '../types';

export const DISCIPLINAS = {
  matematica: {
    nome: 'Matemática',
    series: ['6º Ano', '7º Ano', '8º Ano', '9º Ano', '1º Médio', '2º Médio', '3º Médio'],
    topicos: {
      '6º Ano': ['Números Naturais', 'Frações', 'Geometria Básica', 'Medidas', 'Tratamento da Informação'],
      '7º Ano': ['Números Inteiros', 'Números Racionais', 'Equações', 'Proporcionalidade', 'Geometria Plana'],
      '8º Ano': ['Números Reais', 'Expressões Algébricas', 'Sistemas de Equações', 'Geometria', 'Estatística'],
      '9º Ano': ['Números Reais', 'Equações do 2º Grau', 'Funções', 'Geometria Espacial', 'Probabilidade']
    }
  },
  portugues: {
    nome: 'Português',
    series: ['6º Ano', '7º Ano', '8º Ano', '9º Ano', '1º Médio', '2º Médio', '3º Médio'],
    topicos: {
      '6º Ano': ['Ortografia', 'Classes Gramaticais', 'Interpretação de Texto', 'Produção Textual'],
      '7º Ano': ['Sintaxe', 'Morfologia', 'Literatura', 'Gêneros Textuais'],
      '8º Ano': ['Análise Sintática', 'Figuras de Linguagem', 'Literatura Brasileira', 'Redação'],
      '9º Ano': ['Período Composto', 'Estilística', 'Literatura Contemporânea', 'Argumentação']
    }
  },
  ciencias: {
    nome: 'Ciências',
    series: ['6º Ano', '7º Ano', '8º Ano', '9º Ano'],
    topicos: {
      '6º Ano': ['Ambiente', 'Seres Vivos', 'Corpo Humano', 'Matéria e Energia'],
      '7º Ano': ['Diversidade da Vida', 'Ecologia', 'Evolução', 'Astronomia'],
      '8º Ano': ['Sistemas do Corpo Humano', 'Sexualidade', 'Química Básica', 'Física Básica'],
      '9º Ano': ['Química Geral', 'Física Geral', 'Biotecnologia', 'Sustentabilidade']
    }
  }
};

export const mockUser: User = {
  id: 'user-001',
  nome: 'Prof. Maria Silva',
  email: '<EMAIL>',
  escola: 'Escola Estadual Dom Pedro II',
  disciplinas: ['Matemática', 'Ciências'],
  plano: 'premium',
  dataCadastro: '2024-01-15',
  configuracoes: {
    tema: 'claro',
    layoutPreferido: 'template-classico',
    notificacoes: true
  },
  estatisticas: {
    questoesCriadas: 45,
    provasGeradas: 28,
    ultimoAcesso: '2024-01-20'
  }
};

export const mockQuestions: Question[] = [
  {
    id: 'q001',
    disciplina: 'Matemática',
    serie: '6º Ano',
    topico: 'Frações',
    subtopico: 'Operações com frações',
    dificuldade: 'Médio',
    tipo: 'multipla_escolha',
    competenciaBncc: 'EF06MA07',
    enunciado: 'Qual é o resultado de 2/3 + 1/4?',
    alternativas: ['5/12', '11/12', '3/7', '2/12'],
    respostaCorreta: '11/12',
    explicacao: 'Para somar frações com denominadores diferentes, devemos encontrar o MMC dos denominadores (3 e 4 = 12), depois convertemos as frações: 2/3 = 8/12 e 1/4 = 3/12. Somando: 8/12 + 3/12 = 11/12.',
    tags: ['soma', 'frações', 'denominadores diferentes'],
    autor: 'Sistema',
    dataCriacao: '2024-01-15',
    usoCount: 45,
    favorita: false
  },
  {
    id: 'q002',
    disciplina: 'Matemática',
    serie: '7º Ano',
    topico: 'Números Inteiros',
    subtopico: 'Operações com números inteiros',
    dificuldade: 'Fácil',
    tipo: 'multipla_escolha',
    competenciaBncc: 'EF07MA03',
    enunciado: 'Calcule: (-5) + (+3) - (-2) =',
    alternativas: ['-10', '0', '6', '-4'],
    respostaCorreta: '0',
    explicacao: 'Seguindo a regra dos sinais: (-5) + (+3) - (-2) = -5 + 3 + 2 = 0',
    tags: ['números inteiros', 'operações', 'regra dos sinais'],
    autor: 'Prof. João Santos',
    dataCriacao: '2024-01-16',
    usoCount: 32,
    favorita: true
  },
  {
    id: 'q003',
    disciplina: 'Português',
    serie: '8º Ano',
    topico: 'Interpretação de Texto',
    subtopico: 'Inferência',
    dificuldade: 'Médio',
    tipo: 'dissertativa',
    competenciaBncc: 'EF08LP04',
    enunciado: 'Leia o texto abaixo e explique qual é a crítica social presente na narrativa: "João acordou cedo para trabalhar na fábrica. Seus filhos foram para a escola sem café da manhã, pois não havia dinheiro para comprar comida. Enquanto isso, o dono da fábrica comprava seu terceiro carro do ano."',
    respostaCorreta: 'O texto critica a desigualdade social e econômica, contrastando a situação de pobreza do trabalhador com a riqueza excessiva do patrão.',
    explicacao: 'A crítica social evidencia o contraste entre as classes sociais, mostrando como o trabalhador vive em condições precárias enquanto o empregador ostenta riqueza.',
    tags: ['interpretação', 'crítica social', 'desigualdade'],
    autor: 'Profa. Ana Costa',
    dataCriacao: '2024-01-17',
    usoCount: 28,
    favorita: false
  },
  {
    id: 'q004',
    disciplina: 'Ciências',
    serie: '7º Ano',
    topico: 'Ecologia',
    subtopico: 'Cadeia alimentar',
    dificuldade: 'Fácil',
    tipo: 'multipla_escolha',
    competenciaBncc: 'EF07CI07',
    enunciado: 'Em uma cadeia alimentar, os organismos que produzem seu próprio alimento são chamados de:',
    alternativas: ['Consumidores primários', 'Produtores', 'Consumidores secundários', 'Decompositores'],
    respostaCorreta: 'Produtores',
    explicacao: 'Os produtores são organismos autótrofos, principalmente plantas, que produzem seu próprio alimento através da fotossíntese.',
    tags: ['ecologia', 'cadeia alimentar', 'produtores'],
    autor: 'Prof. Carlos Lima',
    dataCriacao: '2024-01-18',
    usoCount: 41,
    favorita: true
  },
  {
    id: 'q005',
    disciplina: 'Matemática',
    serie: '9º Ano',
    topico: 'Equações do 2º Grau',
    subtopico: 'Resolução por fórmula de Bhaskara',
    dificuldade: 'Difícil',
    tipo: 'dissertativa',
    competenciaBncc: 'EF09MA09',
    enunciado: 'Resolva a equação x² - 5x + 6 = 0 usando a fórmula de Bhaskara e verifique as raízes encontradas.',
    respostaCorreta: 'x₁ = 2 e x₂ = 3',
    explicacao: 'Aplicando a fórmula de Bhaskara: Δ = b² - 4ac = 25 - 24 = 1; x = (5 ± 1)/2, resultando em x₁ = 2 e x₂ = 3. Verificação: 2² - 5(2) + 6 = 0 ✓ e 3² - 5(3) + 6 = 0 ✓',
    tags: ['equação segundo grau', 'bhaskara', 'raízes'],
    autor: 'Profa. Sandra Oliveira',
    dataCriacao: '2024-01-19',
    usoCount: 22,
    favorita: false
  }
];

// Gerar mais questões automaticamente
export const generateMockQuestions = (): Question[] => {
  const allQuestions = [...mockQuestions];
  const disciplinas = Object.keys(DISCIPLINAS);
  
  // Adicionar mais questões de cada disciplina
  for (let i = 6; i <= 50; i++) {
    const disciplina = disciplinas[Math.floor(Math.random() * disciplinas.length)];
    const disciplinaData = DISCIPLINAS[disciplina as keyof typeof DISCIPLINAS];
    const serie = disciplinaData.series[Math.floor(Math.random() * disciplinaData.series.length)];
    const topicos = disciplinaData.topicos[serie as keyof typeof disciplinaData.topicos] || [];
    const topico = topicos.length > 0 ? topicos[Math.floor(Math.random() * topicos.length)] : 'Tópico Geral';
    
    allQuestions.push({
      id: `q${String(i).padStart(3, '0')}`,
      disciplina: disciplinaData.nome,
      serie,
      topico,
      subtopico: `Subtópico ${i}`,
      dificuldade: ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)] as 'Fácil' | 'Médio' | 'Difícil',
      tipo: ['multipla_escolha', 'dissertativa', 'verdadeiro_falso'][Math.floor(Math.random() * 3)] as 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso',
      competenciaBncc: `EF${String(Math.floor(Math.random() * 9) + 1).padStart(2, '0')}MA${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
      enunciado: `Esta é uma questão exemplo de ${disciplinaData.nome} para ${serie} sobre ${topico}. Questão número ${i}.`,
      alternativas: ['Alternativa A', 'Alternativa B', 'Alternativa C', 'Alternativa D'],
      respostaCorreta: 'Alternativa A',
      explicacao: `Explicação detalhada da questão ${i}.`,
      tags: [topico.toLowerCase().replace(' ', '-'), 'exemplo'],
      autor: 'Sistema',
      dataCriacao: '2024-01-20',
      usoCount: Math.floor(Math.random() * 100),
      favorita: Math.random() > 0.8
    });
  }
  
  return allQuestions;
};

export const mockTemplates: Template[] = [
  {
    id: 'template-001',
    nome: 'Clássico Escolar',
    categoria: 'Padrão',
    layoutConfig: {
      cabecalho: {
        nomeEscola: 'Nome da Escola',
        nomeProva: 'Avaliação',
        serie: '6º Ano',
        data: new Date().toLocaleDateString('pt-BR'),
        instrucoes: 'Leia atentamente cada questão antes de responder.'
      },
      espacamento: 'normal',
      numeracao: 'automatica',
      quebrasPagina: true,
      folhaRespostas: false
    },
    previewImage: '/templates/classico.jpg',
    premium: false,
    rating: 4.5,
    downloads: 1250
  },
  {
    id: 'template-002',
    nome: 'Moderno Premium',
    categoria: 'Premium',
    layoutConfig: {
      cabecalho: {
        nomeEscola: 'Nome da Escola',
        nomeProva: 'Avaliação Premium',
        serie: '7º Ano',
        data: new Date().toLocaleDateString('pt-BR'),
        instrucoes: 'Responda com atenção e boa sorte!'
      },
      espacamento: 'expandido',
      numeracao: 'automatica',
      quebrasPagina: true,
      folhaRespostas: true
    },
    previewImage: '/templates/moderno.jpg',
    premium: true,
    rating: 4.8,
    downloads: 890
  }
];