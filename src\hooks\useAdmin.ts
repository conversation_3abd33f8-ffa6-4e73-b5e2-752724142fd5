import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

export const useAdminUsers = () => {
  const { isAdmin } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: users = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['admin-users'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },
    enabled: isAdmin
  })

  const updateUserMutation = useMutation({
    mutationFn: async ({ userId, updates }: { userId: string; updates: any }) => {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success('Usuário atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating user:', error)
      toast.error('Erro ao atualizar usuário')
    }
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success('Usuário excluído com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting user:', error)
      toast.error('Erro ao excluir usuário')
    }
  })

  return {
    users,
    isLoading,
    error,
    updateUser: updateUserMutation.mutate,
    deleteUser: deleteUserMutation.mutate,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending
  }
}

export const useAdminQuestions = () => {
  const { isAdmin } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: questions = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['admin-questions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('questions')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },
    enabled: isAdmin
  })

  const updateQuestionMutation = useMutation({
    mutationFn: async ({ questionId, updates }: { questionId: string; updates: any }) => {
      const { data, error } = await supabase
        .from('questions')
        .update(updates)
        .eq('id', questionId)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-questions'] })
      toast.success('Questão atualizada com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating question:', error)
      toast.error('Erro ao atualizar questão')
    }
  })

  const deleteQuestionMutation = useMutation({
    mutationFn: async (questionId: string) => {
      const { error } = await supabase
        .from('questions')
        .delete()
        .eq('id', questionId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-questions'] })
      toast.success('Questão excluída com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting question:', error)
      toast.error('Erro ao excluir questão')
    }
  })

  return {
    questions,
    isLoading,
    error,
    updateQuestion: updateQuestionMutation.mutate,
    deleteQuestion: deleteQuestionMutation.mutate,
    isUpdating: updateQuestionMutation.isPending,
    isDeleting: deleteQuestionMutation.isPending
  }
}

export const useAdminStats = () => {
  const { isAdmin } = useAuth()

  const {
    data: stats,
    isLoading,
    error
  } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: async () => {
      const [usersResult, questionsResult, assessmentsResult, subscriptionsResult] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('questions').select('*', { count: 'exact', head: true }),
        supabase.from('assessments').select('*', { count: 'exact', head: true }),
        supabase.from('subscriptions').select('*').eq('status', 'active')
      ])

      const monthlyRevenue = subscriptionsResult.data?.reduce((acc, sub) => {
        const amount = sub.plano === 'premium' ? 29.90 : sub.plano === 'escolar' ? 199.90 : 0
        return acc + amount
      }, 0) || 0

      return {
        totalUsers: usersResult.count || 0,
        totalQuestions: questionsResult.count || 0,
        totalAssessments: assessmentsResult.count || 0,
        activeSubscriptions: subscriptionsResult.data?.length || 0,
        monthlyRevenue
      }
    },
    enabled: isAdmin,
    refetchInterval: 5 * 60 * 1000 // Refetch every 5 minutes
  })

  return {
    stats,
    isLoading,
    error
  }
}