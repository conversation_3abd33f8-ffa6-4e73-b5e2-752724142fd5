import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

type Subscription = Database['public']['Tables']['subscriptions']['Row']
type SubscriptionUpdate = Database['public']['Tables']['subscriptions']['Update']

interface SubscriptionWithProfile extends Subscription {
  profiles?: {
    nome: string
    email: string
  }
}

export const useAdminSubscriptions = () => {
  const queryClient = useQueryClient()
  const { loading: authLoading } = useAuth()

  const {
    data: subscriptions = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['admin-subscriptions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          profiles (
            nome,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as SubscriptionWithProfile[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    enabled: !authLoading
  })

  const updateSubscriptionMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: SubscriptionUpdate }) => {
      const { data, error } = await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-subscriptions'] })
      toast.success('Assinatura atualizada com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating subscription:', error)
      toast.error('Erro ao atualizar assinatura')
    }
  })

  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await supabase
        .from('subscriptions')
        .update({ status: 'canceled' })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-subscriptions'] })
      toast.success('Assinatura cancelada com sucesso!')
    },
    onError: (error) => {
      console.error('Error canceling subscription:', error)
      toast.error('Erro ao cancelar assinatura')
    }
  })

  const getSubscriptionStats = () => {
    const total = subscriptions.length
    const active = subscriptions.filter(s => s.status === 'active').length
    const canceled = subscriptions.filter(s => s.status === 'canceled').length
    const revenue = subscriptions
      .filter(s => s.status === 'active')
      .reduce((acc, s) => {
        const amount = s.plano === 'premium' ? 29.90 : s.plano === 'escolar' ? 199.90 : 0
        return acc + amount
      }, 0)

    return {
      total,
      active,
      canceled,
      revenue
    }
  }

  return {
    subscriptions,
    isLoading,
    error,
    updateSubscription: updateSubscriptionMutation.mutate,
    cancelSubscription: cancelSubscriptionMutation.mutate,
    isUpdating: updateSubscriptionMutation.isPending,
    isCanceling: cancelSubscriptionMutation.isPending,
    stats: getSubscriptionStats()
  }
}