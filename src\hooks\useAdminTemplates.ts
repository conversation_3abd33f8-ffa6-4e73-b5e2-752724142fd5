import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Template = Database['public']['Tables']['templates']['Row']
type TemplateInsert = Database['public']['Tables']['templates']['Insert']
type TemplateUpdate = Database['public']['Tables']['templates']['Update']

export const useAdminTemplates = () => {
  const queryClient = useQueryClient()

  const {
    data: templates = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['admin-templates'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('templates')
        .select(`
          *,
          profiles (
            nome,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as any[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })

  const createTemplateMutation = useMutation({
    mutationFn: async (templateData: TemplateInsert) => {
      const { data, error } = await supabase
        .from('templates')
        .insert(templateData)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] })
      toast.success('Template criado com sucesso!')
    },
    onError: (error) => {
      console.error('Error creating template:', error)
      toast.error('Erro ao criar template')
    }
  })

  const updateTemplateMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: TemplateUpdate }) => {
      const { data, error } = await supabase
        .from('templates')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] })
      toast.success('Template atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating template:', error)
      toast.error('Erro ao atualizar template')
    }
  })

  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('templates')
        .delete()
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] })
      toast.success('Template excluído com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting template:', error)
      toast.error('Erro ao excluir template')
    }
  })

  return {
    templates,
    isLoading,
    error,
    createTemplate: createTemplateMutation.mutate,
    updateTemplate: updateTemplateMutation.mutate,
    deleteTemplate: deleteTemplateMutation.mutate,
    isCreating: createTemplateMutation.isPending,
    isUpdating: updateTemplateMutation.isPending,
    isDeleting: deleteTemplateMutation.isPending
  }
}