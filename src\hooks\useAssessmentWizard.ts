import { useState, useCallback, useMemo } from 'react'
import { AssessmentConfig, AssessmentItem } from '../types/assessment'
import { Database } from '../types/database'

type Question = Database['public']['Tables']['questions']['Row']

export interface WizardStep {
  id: string
  title: string
  description: string
  isCompleted: boolean
  isActive: boolean
}

export interface WizardData {
  // Step 1 - Basic Info
  basicInfo: {
    titulo: string
    disciplina: string
    serie: string
    nomeEscola: string
  }
  
  // Step 2 - Question Selection
  selectedItems: AssessmentItem[]
  
  // Step 3 - Configuration
  config: AssessmentConfig
  
  // Step 4 - Review (computed)
}

const initialConfig: AssessmentConfig = {
  titulo: '',
  disciplina: '',
  serie: '',
  headerConfig: {
    nomeEscola: '',
    nomeProva: '',
    serie: '',
    data: new Date().toLocaleDateString('pt-BR'),
    instrucoes: 'Leia atentamente cada questão antes de responder.'
  },
  pdfOptions: {
    paperSize: 'A4',
    orientation: 'portrait',
    fontSize: 'medium',
    fontFamily: 'times',
    lineSpacing: 'normal',
    includeAnswerSheet: false,
    generateVersions: 1
  },
  showFooter: true
}

export const useAssessmentWizard = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [configurationInteracted, setConfigurationInteracted] = useState(false)
  const [wizardData, setWizardData] = useState<WizardData>({
    basicInfo: {
      titulo: '',
      disciplina: '',
      serie: '',
      nomeEscola: ''
    },
    selectedItems: [],
    config: initialConfig
  })

  // Declarar funções de validação primeiro
  const isBasicInfoComplete = useCallback((): boolean => {
    const { titulo, disciplina, serie, nomeEscola } = wizardData.basicInfo
    return !!(titulo.trim() && disciplina && serie && nomeEscola.trim())
  }, [wizardData.basicInfo])

  const isConfigurationComplete = useCallback((): boolean => {
    // Considera completo se o usuário interagiu com as configurações
    // ou se passou pela etapa (indicado por ter dados básicos completos e estar em etapa posterior)
    return configurationInteracted || (isBasicInfoComplete() && currentStep > 2)
  }, [configurationInteracted, isBasicInfoComplete, currentStep])

  // Agora declarar steps usando as funções já definidas
  const steps = useMemo((): WizardStep[] => [
    {
      id: 'basic-info',
      title: 'Informações Básicas',
      description: 'Título, disciplina e série da avaliação',
      isCompleted: isBasicInfoComplete(),
      isActive: currentStep === 0
    },
    {
      id: 'questions',
      title: 'Seleção de Questões',
      description: 'Escolha as questões para sua avaliação',
      isCompleted: wizardData.selectedItems.length > 0,
      isActive: currentStep === 1
    },
    {
      id: 'configuration',
      title: 'Configurações',
      description: 'Personalize o formato e aparência',
      isCompleted: isConfigurationComplete(),
      isActive: currentStep === 2
    },
    {
      id: 'review',
      title: 'Revisão',
      description: 'Revise e finalize sua avaliação',
      isCompleted: false,
      isActive: currentStep === 3
    }
  ], [currentStep, isBasicInfoComplete, wizardData.selectedItems.length, isConfigurationComplete])

  const updateBasicInfo = useCallback((updates: Partial<WizardData['basicInfo']>) => {
    setWizardData(prev => {
      const newBasicInfo = { ...prev.basicInfo, ...updates }
      return {
        ...prev,
        basicInfo: newBasicInfo,
        config: {
          ...prev.config,
          titulo: updates.titulo ?? prev.basicInfo.titulo,
          disciplina: updates.disciplina ?? prev.basicInfo.disciplina,
          serie: updates.serie ?? prev.basicInfo.serie,
          headerConfig: {
            ...prev.config.headerConfig,
            nomeEscola: updates.nomeEscola ?? prev.basicInfo.nomeEscola,
            nomeProva: updates.titulo ?? prev.basicInfo.titulo,
            serie: updates.serie ?? prev.basicInfo.serie
          }
        }
      }
    })
  }, []) // Removida dependência circular

  const updateSelectedItems = useCallback((items: AssessmentItem[]) => {
    setWizardData(prev => ({
      ...prev,
      selectedItems: items
    }))
  }, [])

  const updateConfig = useCallback((config: AssessmentConfig) => {
    setWizardData(prev => ({
      ...prev,
      config
    }))
  }, [])

  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1)
    }
  }, [currentStep, steps.length])

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }, [currentStep])

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex)
    }
  }, [steps.length])

  const resetWizard = useCallback(() => {
    setCurrentStep(0)
    setConfigurationInteracted(false)
    setWizardData({
      basicInfo: {
        titulo: '',
        disciplina: '',
        serie: '',
        nomeEscola: ''
      },
      selectedItems: [],
      config: initialConfig
    })
  }, [])

  const handleConfigurationInteracted = useCallback(() => {
    setConfigurationInteracted(true)
  }, [])

  const canProceedToNext = useMemo(() => {
    switch (currentStep) {
      case 0: // Basic Info
        return isBasicInfoComplete()
      case 1: // Questions
        return wizardData.selectedItems.length > 0
      case 2: // Configuration
        return true // Always can proceed from config
      case 3: // Review
        return false // Last step
      default:
        return false
    }
  }, [currentStep, isBasicInfoComplete, wizardData.selectedItems.length])

  const processedSteps = useMemo(() =>
    steps.map((step, index) => ({
      ...step,
      isActive: index === currentStep,
      isCompleted: step.isCompleted
    })), [steps, currentStep]
  )

  return {
    currentStep,
    steps: processedSteps,
    wizardData,
    updateBasicInfo,
    updateSelectedItems,
    updateConfig,
    nextStep,
    prevStep,
    goToStep,
    resetWizard,
    canProceedToNext,
    isFirstStep: currentStep === 0,
    isLastStep: currentStep === steps.length - 1,
    handleConfigurationInteracted
  }
}
