import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Assessment = Database['public']['Tables']['assessments']['Row']
type AssessmentInsert = Database['public']['Tables']['assessments']['Insert']
type AssessmentUpdate = Database['public']['Tables']['assessments']['Update']

export const useAssessments = () => {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: assessments = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['assessments', user?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('assessments')
        .select('*')
        .eq('autor_id', user!.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as Assessment[]
    },
    enabled: !!user?.id && !authLoading
  })

  const createAssessmentMutation = useMutation({
    mutationFn: async (assessmentData: AssessmentInsert) => {
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('assessments')
        .insert({ ...assessmentData, autor_id: user.id })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      toast.success('Avaliação criada com sucesso!')
    },
    onError: (error) => {
      console.error('Error creating assessment:', error)
      toast.error('Erro ao criar avaliação')
    }
  })

  const updateAssessmentMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: AssessmentUpdate }) => {
      const { data, error } = await supabase
        .from('assessments')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      toast.success('Avaliação atualizada com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating assessment:', error)
      toast.error('Erro ao atualizar avaliação')
    }
  })

  const deleteAssessmentMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('assessments')
        .delete()
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      toast.success('Avaliação excluída com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting assessment:', error)
      toast.error('Erro ao excluir avaliação')
    }
  })

  return {
    assessments,
    isLoading,
    error,
    createAssessment: createAssessmentMutation.mutateAsync,
    updateAssessment: updateAssessmentMutation.mutate,
    deleteAssessment: deleteAssessmentMutation.mutate,
    isCreating: createAssessmentMutation.isPending,
    isUpdating: updateAssessmentMutation.isPending,
    isDeleting: deleteAssessmentMutation.isPending
  }
}

export const useAssessment = (id: string) => {
  const {
    data: assessment,
    isLoading,
    error
  } = useQuery({
    queryKey: ['assessment', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('assessments')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data as Assessment
    },
    enabled: !!id
  })

  return {
    assessment,
    isLoading,
    error
  }
}