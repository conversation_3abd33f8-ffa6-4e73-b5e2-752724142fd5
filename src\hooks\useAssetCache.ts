import { useState, useEffect, useCallback, useRef } from 'react'
import { assetCache, AssetType, ASSET_CACHE_CONFIG } from '../lib/cache/assetCache'

/**
 * Hook para gerenciar cache de assets com lazy loading e preloading
 */
export const useAssetCache = () => {
  const [stats, setStats] = useState(assetCache.getStats())
  const [loading, setLoading] = useState<Record<string, boolean>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  /**
   * Carregar asset com cache
   */
  const loadAsset = useCallback(async (
    url: string, 
    type: AssetType, 
    options?: {
      quality?: number
      size?: number
      priority?: 'high' | 'low'
    }
  ) => {
    const key = `${url}_${JSON.stringify(options || {})}`
    
    setLoading(prev => ({ ...prev, [key]: true }))
    setErrors(prev => ({ ...prev, [key]: '' }))
    
    try {
      const asset = await assetCache.getAsset(url, type, options)
      return asset
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load asset'
      setErrors(prev => ({ ...prev, [key]: errorMessage }))
      throw error
    } finally {
      setLoading(prev => ({ ...prev, [key]: false }))
    }
  }, [])
  
  /**
   * Preload múltiplos assets
   */
  const preloadAssets = useCallback(async (
    assets: Array<{ url: string; type: AssetType; priority?: 'high' | 'low' }>
  ) => {
    const loadedCount = await assetCache.preloadAssets(assets)
    setStats(assetCache.getStats())
    return loadedCount
  }, [])
  
  /**
   * Configurar lazy loading para um elemento
   */
  const setupLazyLoading = useCallback((
    element: HTMLElement,
    url: string,
    type: AssetType,
    callback: (asset: any) => void
  ) => {
    assetCache.setupLazyLoading(element, url, type, (asset) => {
      callback(asset)
      setStats(assetCache.getStats())
    })
  }, [])
  
  /**
   * Limpar cache
   */
  const clearCache = useCallback(() => {
    assetCache.cleanup()
    setStats(assetCache.getStats())
  }, [])
  
  /**
   * Atualizar estatísticas
   */
  const updateStats = useCallback(() => {
    setStats(assetCache.getStats())
  }, [])
  
  // Atualizar estatísticas periodicamente
  useEffect(() => {
    const interval = setInterval(updateStats, 30000) // 30 segundos
    return () => clearInterval(interval)
  }, [updateStats])
  
  return {
    loadAsset,
    preloadAssets,
    setupLazyLoading,
    clearCache,
    updateStats,
    stats,
    loading,
    errors,
    config: ASSET_CACHE_CONFIG,
  }
}

/**
 * Hook específico para imagens com lazy loading
 */
export const useImageCache = (
  src: string,
  options?: {
    lazy?: boolean
    quality?: number
    size?: number
    priority?: 'high' | 'low'
  }
) => {
  const [imageUrl, setImageUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [dimensions, setDimensions] = useState<{ width: number; height: number }>()
  const elementRef = useRef<HTMLImageElement>(null)
  const { loadAsset, setupLazyLoading } = useAssetCache()
  
  const loadImage = useCallback(async () => {
    if (!src) return
    
    setIsLoading(true)
    setError('')
    
    try {
      const asset = await loadAsset(src, 'image', options)
      const url = URL.createObjectURL(asset.data as Blob)
      setImageUrl(url)
      setDimensions(asset.metadata.dimensions)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load image')
    } finally {
      setIsLoading(false)
    }
  }, [src, loadAsset, options])
  
  useEffect(() => {
    if (!src) return
    
    if (options?.lazy && elementRef.current) {
      // Configurar lazy loading
      setupLazyLoading(elementRef.current, src, 'image', (asset) => {
        const url = URL.createObjectURL(asset.data as Blob)
        setImageUrl(url)
        setDimensions(asset.metadata.dimensions)
      })
    } else {
      // Carregar imediatamente
      loadImage()
    }
    
    // Cleanup
    return () => {
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl)
      }
    }
  }, [src, options?.lazy, setupLazyLoading, loadImage])
  
  return {
    imageUrl,
    isLoading,
    error,
    dimensions,
    elementRef,
  }
}

/**
 * Hook para cache de PDFs
 */
export const usePDFCache = (src: string) => {
  const [pdfUrl, setPdfUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const { loadAsset } = useAssetCache()
  
  useEffect(() => {
    if (!src) return
    
    const loadPDF = async () => {
      setIsLoading(true)
      setError('')
      
      try {
        const asset = await loadAsset(src, 'pdf')
        const url = URL.createObjectURL(asset.data as Blob)
        setPdfUrl(url)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load PDF')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadPDF()
    
    // Cleanup
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [src, loadAsset])
  
  return {
    pdfUrl,
    isLoading,
    error,
  }
}

/**
 * Hook para preloading inteligente baseado na navegação
 */
export const useIntelligentPreloading = () => {
  const { preloadAssets } = useAssetCache()
  
  /**
   * Preload assets críticos para a página atual
   */
  const preloadForPage = useCallback(async (
    page: string,
    userContext?: {
      isLoggedIn: boolean
      subscription?: string
      preferences?: any
    }
  ) => {
    const assetsToPreload: Array<{ url: string; type: AssetType; priority?: 'high' | 'low' }> = []
    
    switch (page) {
      case 'dashboard':
        // Preload imagens do dashboard
        assetsToPreload.push(
          { url: '/images/dashboard-hero.webp', type: 'image', priority: 'high' },
          { url: '/images/icons/questions.svg', type: 'image', priority: 'high' },
          { url: '/images/icons/assessments.svg', type: 'image', priority: 'high' }
        )
        break
        
      case 'questions':
        // Preload assets relacionados a questões
        assetsToPreload.push(
          { url: '/images/question-placeholder.webp', type: 'image', priority: 'high' },
          { url: '/fonts/math-symbols.woff2', type: 'font', priority: 'low' }
        )
        break
        
      case 'assessments':
        // Preload templates e imagens de avaliações
        assetsToPreload.push(
          { url: '/images/assessment-template.webp', type: 'image', priority: 'high' },
          { url: '/templates/default-template.pdf', type: 'pdf', priority: 'low' }
        )
        break
        
      case 'editor':
        // Preload assets do editor
        assetsToPreload.push(
          { url: '/images/editor-toolbar.webp', type: 'image', priority: 'high' },
          { url: '/fonts/editor-font.woff2', type: 'font', priority: 'high' }
        )
        break
    }
    
    if (assetsToPreload.length > 0) {
      return await preloadAssets(assetsToPreload)
    }
    
    return 0
  }, [preloadAssets])
  
  /**
   * Preload baseado no comportamento do usuário
   */
  const preloadOnHover = useCallback(async (
    targetPage: string,
    assets: Array<{ url: string; type: AssetType }>
  ) => {
    // Preload com prioridade baixa quando usuário faz hover
    const assetsWithPriority = assets.map(asset => ({
      ...asset,
      priority: 'low' as const
    }))
    
    return await preloadAssets(assetsWithPriority)
  }, [preloadAssets])
  
  return {
    preloadForPage,
    preloadOnHover,
  }
}

/**
 * Hook para otimização de imagens responsivas
 */
export const useResponsiveImages = (
  src: string,
  sizes: number[] = [320, 640, 1024, 1920]
) => {
  const [srcSet, setSrcSet] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const { loadAsset } = useAssetCache()
  
  useEffect(() => {
    if (!src) return
    
    const generateSrcSet = async () => {
      setIsLoading(true)
      
      try {
        const promises = sizes.map(async (size) => {
          const asset = await loadAsset(src, 'image', { size })
          const url = URL.createObjectURL(asset.data as Blob)
          return `${url} ${size}w`
        })
        
        const srcSetArray = await Promise.all(promises)
        setSrcSet(srcSetArray.join(', '))
      } catch (error) {
        console.error('Failed to generate responsive images:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    generateSrcSet()
  }, [src, sizes, loadAsset])
  
  return {
    srcSet,
    isLoading,
  }
}
