import { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { validateImageFile, generateUniqueFileName } from '../utils/imageUtils'
import toast from 'react-hot-toast'

export interface AssessmentAsset {
  id: string
  user_id: string
  file_name: string
  file_path: string
  file_size: number
  mime_type: string
  asset_type: 'custom_header' | 'school_logo'
  description?: string
  created_at: string
  updated_at: string
}

export interface UploadProgress {
  progress: number
  isUploading: boolean
}

export const useAssets = (assetType?: 'custom_header' | 'school_logo') => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    progress: 0,
    isUploading: false
  })

  // Buscar assets do usuário
  const {
    data: assets = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['assets', user?.id, assetType],
    queryFn: async () => {
      if (!user) return []

      let query = supabase
        .from('assessment_assets')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (assetType) {
        query = query.eq('asset_type', assetType)
      }

      const { data, error } = await query

      if (error) throw error
      return data as AssessmentAsset[]
    },
    enabled: !!user
  })

  // Upload de asset
  const uploadAsset = useMutation({
    mutationFn: async ({
      file,
      type,
      description,
      onAssetCreated
    }: {
      file: File
      type: 'custom_header' | 'school_logo'
      description?: string
      onAssetCreated?: (asset: AssessmentAsset) => void
    }) => {
      if (!user) throw new Error('Usuário não autenticado')

      setUploadProgress({ progress: 0, isUploading: true })

      try {
        // Validar arquivo
        const validation = await validateImageFile(file)
        if (!validation.isValid) {
          throw new Error(validation.error)
        }

        // Gerar nome único
        const fileName = generateUniqueFileName(file.name)
        const filePath = `${user.id}/${type}/${fileName}`

        setUploadProgress({ progress: 25, isUploading: true })

        // Upload para storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('assessment-assets')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false
          })

        if (uploadError) throw uploadError

        setUploadProgress({ progress: 50, isUploading: true })

        // Salvar metadados no banco
        const { data: assetData, error: dbError } = await supabase
          .from('assessment_assets')
          .insert({
            user_id: user.id,
            file_name: file.name,
            file_path: uploadData.path,
            file_size: file.size,
            mime_type: file.type,
            asset_type: type,
            description
          })
          .select()
          .single()

        if (dbError) {
          // Limpar arquivo do storage se falhou no banco
          await supabase.storage
            .from('assessment-assets')
            .remove([uploadData.path])
          throw dbError
        }

        setUploadProgress({ progress: 100, isUploading: false })

        const asset = assetData as AssessmentAsset

        // Auto-selecionar o asset recém-criado
        if (onAssetCreated) {
          onAssetCreated(asset)
        }

        return asset

      } catch (error) {
        setUploadProgress({ progress: 0, isUploading: false })
        throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assets'] })
      toast.success('Imagem enviada e selecionada com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao enviar imagem')
    }
  })

  // Deletar asset
  const deleteAsset = useMutation({
    mutationFn: async (assetId: string) => {
      if (!user) throw new Error('Usuário não autenticado')

      // Buscar dados do asset
      const { data: asset, error: fetchError } = await supabase
        .from('assessment_assets')
        .select('file_path')
        .eq('id', assetId)
        .eq('user_id', user.id)
        .single()

      if (fetchError) throw fetchError

      // Deletar do storage
      const { error: storageError } = await supabase.storage
        .from('assessment-assets')
        .remove([asset.file_path])

      if (storageError) throw storageError

      // Deletar do banco
      const { error: dbError } = await supabase
        .from('assessment_assets')
        .delete()
        .eq('id', assetId)
        .eq('user_id', user.id)

      if (dbError) throw dbError

      return assetId
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assets'] })
      toast.success('Imagem removida com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao remover imagem')
    }
  })

  // Obter URL pública do asset
  const getAssetUrl = useCallback((filePath: string): string => {
    const { data } = supabase.storage
      .from('assessment-assets')
      .getPublicUrl(filePath)

    return data.publicUrl
  }, [])

  // Obter URL com token de acesso (para assets privados)
  const getAssetSignedUrl = useCallback(async (filePath: string): Promise<string> => {
    const { data, error } = await supabase.storage
      .from('assessment-assets')
      .createSignedUrl(filePath, 3600) // 1 hora

    if (error) throw error
    return data.signedUrl
  }, [])

  return {
    assets,
    isLoading,
    error,
    uploadProgress,
    uploadAsset: uploadAsset.mutate,
    isUploading: uploadAsset.isPending,
    deleteAsset: deleteAsset.mutate,
    isDeleting: deleteAsset.isPending,
    getAssetUrl,
    getAssetSignedUrl
  }
}
