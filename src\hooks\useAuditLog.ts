import { useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

export interface AuditLogEntry {
  action_type: string
  resource_type: string
  resource_id?: string
  details: Record<string, any>
  ip_address?: string
  user_agent?: string
  timestamp: string
  user_id: string
}

export interface AuditLogFilter {
  action_type?: string
  resource_type?: string
  user_id?: string
  start_date?: string
  end_date?: string
  limit?: number
}

export const useAuditLog = () => {
  const { user } = useAuth()

  // Função para registrar uma ação no log de auditoria
  const logAction = useCallback(async (
    actionType: string,
    resourceType: string,
    details: Record<string, any>,
    resourceId?: string
  ): Promise<boolean> => {
    if (!user) {
      console.warn('Cannot log action: user not authenticated')
      return false
    }

    try {
      // Obter informações do navegador
      const userAgent = navigator.userAgent
      const timestamp = new Date().toISOString()

      // Tentar obter IP (limitado no browser, mas podemos tentar)
      let ipAddress = 'unknown'
      try {
        // Em produção, isso seria obtido do servidor
        ipAddress = 'client-side'
      } catch (error) {
        // IP não disponível no client-side
      }

      const logEntry: Omit<AuditLogEntry, 'id'> = {
        action_type: actionType,
        resource_type: resourceType,
        resource_id: resourceId,
        details: {
          ...details,
          timestamp: timestamp,
          session_id: user.id // Usar user ID como session identifier
        },
        ip_address: ipAddress,
        user_agent: userAgent,
        timestamp: timestamp,
        user_id: user.id
      }

      // Inserir no banco de dados
      const { error } = await supabase
        .from('admin_audit_log')
        .insert([logEntry])

      if (error) {
        console.error('Failed to log audit action:', error)
        return false
      }

      console.log(`Audit log recorded: ${actionType} on ${resourceType}`, details)
      return true
    } catch (error) {
      console.error('Error logging audit action:', error)
      return false
    }
  }, [user])

  // Função para registrar geração de questões
  const logQuestionGeneration = useCallback(async (
    params: Record<string, any>,
    result: { success: boolean; count?: number; error?: string; duration_ms?: number; method?: string }
  ) => {
    return await logAction(
      'GENERATE_QUESTIONS',
      'bulk_question_generator',
      {
        generation_params: {
          disciplina: params.disciplina,
          serie: params.serie,
          topico: params.topico,
          subtopico: params.subtopico,
          dificuldade: params.dificuldade,
          tipo: params.tipo,
          quantidade: params.quantidade,
          competencia_bncc: params.competencia_bncc
        },
        result: {
          success: result.success,
          count: result.count || 0,
          method: result.method || 'edge_function',
          error: result.error,
          duration_ms: result.duration_ms
        },
        performance: {
          generation_time: result.duration_ms,
          questions_per_second: result.count && result.duration_ms
            ? (result.count / (result.duration_ms / 1000)).toFixed(2)
            : null,
          efficiency_score: result.count && result.duration_ms
            ? Math.min(100, (result.count / (result.duration_ms / 1000)) * 10)
            : null
        },
        questions_generated: result.count || 0,
        success: result.success,
        session_info: {
          user_agent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          session_id: sessionStorage.getItem('session_id') || crypto.randomUUID()
        }
      }
    )
  }, [logAction])

  // Função para registrar aprovação/rejeição de questões
  const logQuestionApproval = useCallback(async (
    questionId: string,
    action: 'APPROVE' | 'REJECT',
    questionData?: Record<string, any>
  ) => {
    return await logAction(
      `QUESTION_${action}`,
      'generated_question',
      {
        question_data: questionData,
        approval_action: action
      },
      questionId
    )
  }, [logAction])

  // Função para registrar regeneração de questões
  const logQuestionRegeneration = useCallback(async (
    questionId: string,
    originalData: Record<string, any>,
    newData: Record<string, any>
  ) => {
    return await logAction(
      'REGENERATE_QUESTION',
      'generated_question',
      {
        original_question: originalData,
        new_question: newData,
        regeneration_reason: 'admin_request'
      },
      questionId
    )
  }, [logAction])

  // Função para registrar exportação de questões
  const logQuestionExport = useCallback(async (
    exportFormat: string,
    questionCount: number,
    filters?: Record<string, any>
  ) => {
    return await logAction(
      'EXPORT_QUESTIONS',
      'bulk_question_generator',
      {
        export_format: exportFormat,
        question_count: questionCount,
        export_filters: filters,
        export_timestamp: new Date().toISOString()
      }
    )
  }, [logAction])

  // Função para registrar acesso ao painel administrativo
  const logAdminAccess = useCallback(async (
    panel: string,
    accessType: 'VIEW' | 'EDIT' | 'DELETE' = 'VIEW'
  ) => {
    return await logAction(
      `ADMIN_${accessType}`,
      'admin_panel',
      {
        panel_accessed: panel,
        access_type: accessType,
        access_timestamp: new Date().toISOString()
      }
    )
  }, [logAction])

  // Função para buscar logs de auditoria (apenas para admins)
  const getAuditLogs = useCallback(async (filters: AuditLogFilter = {}) => {
    try {
      let query = supabase
        .from('admin_audit_log')
        .select(`
          *,
          profiles:user_id (
            nome,
            email
          )
        `)
        .order('timestamp', { ascending: false })

      // Aplicar filtros
      if (filters.action_type) {
        query = query.eq('action_type', filters.action_type)
      }
      if (filters.resource_type) {
        query = query.eq('resource_type', filters.resource_type)
      }
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id)
      }
      if (filters.start_date) {
        query = query.gte('timestamp', filters.start_date)
      }
      if (filters.end_date) {
        query = query.lte('timestamp', filters.end_date)
      }

      // Limitar resultados
      const limit = filters.limit || 100
      query = query.limit(limit)

      const { data, error } = await query

      if (error) {
        console.error('Failed to fetch audit logs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      return []
    }
  }, [])

  return {
    logAction,
    logQuestionGeneration,
    logQuestionApproval,
    logQuestionRegeneration,
    logQuestionExport,
    logAdminAccess,
    getAuditLogs
  }
}
