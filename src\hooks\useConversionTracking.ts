import { useState, useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import {
  ConversionTrackingData,
  UseConversionTrackingReturn,
  PublicError
} from '../types/public'

/**
 * Hook para tracking de conversões em avaliações públicas
 */
export const useConversionTracking = (): UseConversionTrackingReturn => {
  const [isTracking, setIsTracking] = useState(false)
  const [error, setError] = useState<PublicError | null>(null)
  const { user } = useAuth()

  /**
   * Ra<PERSON><PERSON>ia uma conversão (signup, upgrade, download)
   */
  const trackConversion = useCallback(async (data: ConversionTrackingData) => {
    setIsTracking(true)
    setError(null)

    try {
      // Get user agent
      const userAgent = navigator.userAgent

      // Extract UTM parameters from current URL
      const urlParams = new URLSearchParams(window.location.search)
      const utmParams = {
        source: urlParams.get('utm_source') || data.utmParams?.source,
        medium: urlParams.get('utm_medium') || data.utmParams?.medium,
        campaign: urlParams.get('utm_campaign') || data.utmParams?.campaign,
        content: urlParams.get('utm_content') || data.utmParams?.content,
        term: urlParams.get('utm_term') || data.utmParams?.term
      }

      // Call the track_conversion function
      const { data: conversionId, error } = await supabase.rpc('track_conversion', {
        p_assessment_id: data.assessmentId,
        p_user_id: data.userId || user?.id || null,
        p_conversion_type: data.conversionType,
        p_source_page: data.sourcePage,
        p_user_agent: userAgent,
        p_ip_address: null, // Will be handled server-side if needed
        p_utm_source: utmParams.source,
        p_utm_medium: utmParams.medium,
        p_utm_campaign: utmParams.campaign,
        p_utm_content: utmParams.content,
        p_utm_term: utmParams.term,
        p_metadata: {
          ...data.metadata,
          timestamp: new Date().toISOString(),
          referrer: document.referrer,
          screenResolution: `${screen.width}x${screen.height}`,
          language: navigator.language
        }
      })

      if (error) throw error

      // Store conversion in localStorage for analytics
      const conversions = JSON.parse(localStorage.getItem('publicConversions') || '[]')
      conversions.push({
        id: conversionId,
        assessmentId: data.assessmentId,
        type: data.conversionType,
        timestamp: new Date().toISOString()
      })
      localStorage.setItem('publicConversions', JSON.stringify(conversions))

      return conversionId
    } catch (err) {
      const publicError: PublicError = {
        code: 'CONVERSION_TRACKING_ERROR',
        message: err instanceof Error ? err.message : 'Erro ao rastrear conversão',
        details: { data },
        timestamp: new Date().toISOString()
      }
      setError(publicError)
      throw publicError
    } finally {
      setIsTracking(false)
    }
  }, [user])

  /**
   * Rastreia uma visualização de avaliação
   */
  const trackView = useCallback(async (assessmentId: string) => {
    try {
      // Check if view was already tracked in this session
      const viewedAssessments = JSON.parse(sessionStorage.getItem('viewedAssessments') || '[]')
      if (viewedAssessments.includes(assessmentId)) {
        return // Already tracked in this session
      }

      // Increment view count
      await supabase.rpc('increment_view_count', { assessment_id: assessmentId })

      // Mark as viewed in session
      viewedAssessments.push(assessmentId)
      sessionStorage.setItem('viewedAssessments', JSON.stringify(viewedAssessments))

      // Track as analytics event (optional)
      if (typeof window !== 'undefined' && 'gtag' in window) {
        (window as any).gtag('event', 'view_assessment', {
          assessment_id: assessmentId,
          event_category: 'engagement',
          event_label: 'public_assessment_view'
        })
      }
    } catch (err) {
      // Don't throw error for view tracking failures
      console.warn('Failed to track view:', err)
    }
  }, [])

  return {
    trackConversion,
    trackView,
    isTracking,
    error
  }
}

/**
 * Hook para tracking de eventos de analytics
 */
export const useAnalyticsTracking = () => {
  const trackEvent = useCallback((
    eventName: string,
    parameters: Record<string, any> = {}
  ) => {
    try {
      // Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', eventName, {
          ...parameters,
          timestamp: new Date().toISOString()
        })
      }

      // Custom analytics (if needed)
      const customEvents = JSON.parse(localStorage.getItem('customAnalytics') || '[]')
      customEvents.push({
        event: eventName,
        parameters,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        referrer: document.referrer
      })
      
      // Keep only last 100 events
      if (customEvents.length > 100) {
        customEvents.splice(0, customEvents.length - 100)
      }
      
      localStorage.setItem('customAnalytics', JSON.stringify(customEvents))
    } catch (err) {
      console.warn('Failed to track analytics event:', err)
    }
  }, [])

  const trackPageView = useCallback((pagePath: string, pageTitle: string) => {
    try {
      if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
          page_path: pagePath,
          page_title: pageTitle
        })
      }
    } catch (err) {
      console.warn('Failed to track page view:', err)
    }
  }, [])

  const trackConversionGoal = useCallback((
    goalName: string,
    value?: number,
    currency?: string
  ) => {
    try {
      if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
          send_to: `GA_MEASUREMENT_ID/${goalName}`,
          value: value,
          currency: currency || 'BRL'
        })
      }
    } catch (err) {
      console.warn('Failed to track conversion goal:', err)
    }
  }, [])

  return {
    trackEvent,
    trackPageView,
    trackConversionGoal
  }
}

/**
 * Hook para performance tracking
 */
export const usePerformanceTracking = () => {
  const trackPerformance = useCallback(() => {
    try {
      // Web Vitals tracking
      if ('PerformanceObserver' in window) {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          
          if (typeof gtag !== 'undefined') {
            gtag('event', 'web_vitals', {
              metric_name: 'LCP',
              metric_value: Math.round(lastEntry.startTime),
              metric_rating: lastEntry.startTime < 2500 ? 'good' : lastEntry.startTime < 4000 ? 'needs-improvement' : 'poor'
            })
          }
        }).observe({ entryTypes: ['largest-contentful-paint'] })

        // First Input Delay (FID)
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (typeof gtag !== 'undefined') {
              gtag('event', 'web_vitals', {
                metric_name: 'FID',
                metric_value: Math.round(entry.processingStart - entry.startTime),
                metric_rating: entry.processingStart - entry.startTime < 100 ? 'good' : entry.processingStart - entry.startTime < 300 ? 'needs-improvement' : 'poor'
              })
            }
          })
        }).observe({ entryTypes: ['first-input'] })

        // Cumulative Layout Shift (CLS)
        let clsValue = 0
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          
          if (typeof gtag !== 'undefined') {
            gtag('event', 'web_vitals', {
              metric_name: 'CLS',
              metric_value: Math.round(clsValue * 1000) / 1000,
              metric_rating: clsValue < 0.1 ? 'good' : clsValue < 0.25 ? 'needs-improvement' : 'poor'
            })
          }
        }).observe({ entryTypes: ['layout-shift'] })
      }
    } catch (err) {
      console.warn('Failed to track performance:', err)
    }
  }, [])

  return { trackPerformance }
}
