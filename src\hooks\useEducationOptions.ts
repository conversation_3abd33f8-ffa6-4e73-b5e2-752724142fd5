import { useQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { DISCIPLINAS, SERIES } from '../constants/educationOptions'

interface EducationOption {
  value: string
  label: string
}

/**
 * Hook para buscar tópicos únicos baseados na disciplina selecionada
 */
export const useTopicos = (disciplina?: string) => {
  return useQuery({
    queryKey: ['topicos', disciplina],
    queryFn: async () => {
      if (!disciplina) return []

      const { data, error } = await supabase
        .from('questions')
        .select('topico')
        .eq('disciplina', disciplina)
        .not('topico', 'is', null)
        .neq('topico', '')

      if (error) throw error

      // Extrair tópicos únicos e ordenar
      const uniqueTopicos = [...new Set(data.map(item => item.topico))]
        .filter(Boolean)
        .filter(topico => topico.trim() !== '')
        .sort()

      return uniqueTopicos.map(topico => ({
        value: topico,
        label: topico
      })) as EducationOption[]
    },
    enabled: !!disciplina,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

/**
 * Hook para buscar subtópicos únicos baseados na disciplina e tópico selecionados
 */
export const useSubtopicos = (disciplina?: string, topico?: string) => {
  return useQuery({
    queryKey: ['subtopicos', disciplina, topico],
    queryFn: async () => {
      if (!disciplina || !topico) return []

      const { data, error } = await supabase
        .from('questions')
        .select('subtopico')
        .eq('disciplina', disciplina)
        .eq('topico', topico)
        .not('subtopico', 'is', null)
        .neq('subtopico', '')

      if (error) throw error

      // Extrair subtópicos únicos e ordenar
      const uniqueSubtopicos = [...new Set(data.map(item => item.subtopico))]
        .filter(Boolean)
        .filter(subtopico => subtopico.trim() !== '')
        .sort()

      return uniqueSubtopicos.map(subtopico => ({
        value: subtopico,
        label: subtopico
      })) as EducationOption[]
    },
    enabled: !!disciplina && !!topico,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

/**
 * Hook para buscar séries únicas baseadas na disciplina selecionada
 */
export const useSeriesByDisciplina = (disciplina?: string) => {
  return useQuery({
    queryKey: ['series-by-disciplina', disciplina],
    queryFn: async () => {
      if (!disciplina) {
        // Busca todas as séries disponíveis no banco se não há disciplina selecionada
        const { data, error } = await supabase
          .from('questions')
          .select('serie')
          .not('serie', 'is', null)

        if (error) throw error

        const uniqueSeries = [...new Set(data.map(item => item.serie))]
          .filter(Boolean)
          .sort()

        return uniqueSeries.map(serie => ({
          value: serie,
          label: serie
        })) as EducationOption[]
      }

      const { data, error } = await supabase
        .from('questions')
        .select('serie')
        .eq('disciplina', disciplina)
        .not('serie', 'is', null)

      if (error) throw error

      // Extrair séries únicas e ordenar alfabeticamente
      const uniqueSeries = [...new Set(data.map(item => item.serie))]
        .filter(Boolean)
        .sort()

      return uniqueSeries.map(serie => ({
        value: serie,
        label: serie
      })) as EducationOption[]
    },
    enabled: true,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

/**
 * Hook para obter opções de disciplinas (buscando do banco)
 */
export const useDisciplinas = () => {
  return useQuery({
    queryKey: ['disciplinas'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('questions')
        .select('disciplina')
        .not('disciplina', 'is', null)

      if (error) throw error

      // Extrair disciplinas únicas e ordenar alfabeticamente
      const uniqueDisciplinas = [...new Set(data.map(item => item.disciplina))]
        .filter(Boolean)
        .sort()

      return uniqueDisciplinas.map(disciplina => ({
        value: disciplina,
        label: disciplina
      })) as EducationOption[]
    },
    staleTime: 30 * 60 * 1000, // 30 minutes (disciplinas mudam raramente)
    gcTime: 60 * 60 * 1000 // 1 hour
  })
}

/**
 * Hook para obter opções de séries (usando constantes)
 */
export const useSeries = () => {
  return {
    data: SERIES.map(serie => ({
      value: serie,
      label: serie
    })) as EducationOption[],
    isLoading: false,
    error: null
  }
}

export type { EducationOption }
