import { useState, useCallback, useEffect, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { CacheConfig, DEFAULT_CACHE_CONFIG } from '../types/public'
import {
  CACHE_KEYS,
  CACHE_TIMES,
  INVALIDATION_STRATEGIES,
  generate<PERSON><PERSON><PERSON><PERSON>,
  invalidateMultipleKeys,
  prefetchData,
  setupBackgroundRefetch,
  cleanupOldCache,
  BACKGROUND_REFETCH,
  PERSISTENT_CACHE_CONFIG
} from '../lib/cache/cacheConfig'

/**
 * Hook para gerenciamento inteligente de cache
 * Fornece funcionalidades avançadas de cache, invalidação e prefetch
 */
export const useIntelligentCache = (config: Partial<CacheConfig> = {}) => {
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
    totalQueries: 0,
    activeQueries: 0,
    staleQueries: 0,
    errorQueries: 0,
    loadingQueries: 0,
    cacheSize: 0,
    oldestQuery: 0,
    newestQuery: 0,
  })

  const queryClient = useQueryClient()
  const cacheConfig = { ...DEFAULT_CACHE_CONFIG, ...config }
  const backgroundIntervals = useRef<NodeJS.Timeout[]>([])

  /**
   * Invalidar cache por estratégia
   */
  const invalidateByStrategy = useCallback(async (
    strategy: keyof typeof INVALIDATION_STRATEGIES
  ) => {
    const keys = INVALIDATION_STRATEGIES[strategy]
    await invalidateMultipleKeys(queryClient, keys)
  }, [queryClient])

  /**
   * Invalidar cache específico com parâmetros
   */
  const invalidateCache = useCallback(async (
    baseKey: string,
    params?: Record<string, any>
  ) => {
    const cacheKey = generateCacheKey(baseKey, params)
    await queryClient.invalidateQueries({ queryKey: cacheKey })
  }, [queryClient])

  /**
   * Invalidar múltiplas chaves (compatibilidade com versão anterior)
   */
  const invalidateMultiple = useCallback(async (keys: string[]) => {
    for (const key of keys) {
      await queryClient.invalidateQueries({ queryKey: [key] })
    }
  }, [queryClient])

  /**
   * Limpar todo o cache
   */
  const clearAllCache = useCallback(async () => {
    queryClient.clear()
    localStorage.removeItem('publicAssessmentCache')
    sessionStorage.clear()
  }, [queryClient])

  /**
   * Prefetch dados com configuração inteligente
   */
  const prefetchWithConfig = useCallback(async (
    key: string,
    fetchFn: () => Promise<any>,
    cacheType: keyof typeof CACHE_TIMES = 'DYNAMIC',
    params?: Record<string, any>
  ) => {
    const cacheKey = generateCacheKey(key, params)
    const config = CACHE_TIMES[cacheType]

    await queryClient.prefetchQuery({
      queryKey: cacheKey,
      queryFn: fetchFn,
      staleTime: config.staleTime,
      gcTime: config.gcTime,
    })
  }, [queryClient])

  /**
   * Configurar background refetch para dados em tempo real
   */
  const setupRealtimeRefetch = useCallback((
    keys: string[],
    interval: number = BACKGROUND_REFETCH.REALTIME_INTERVAL
  ) => {
    keys.forEach(key => {
      const intervalId = setupBackgroundRefetch(queryClient, key, interval)
      backgroundIntervals.current.push(intervalId)
    })
  }, [queryClient])

  /**
   * Obter dados do cache sem fazer nova requisição
   */
  const getCachedData = useCallback(<T>(
    key: string,
    params?: Record<string, any>
  ): T | undefined => {
    const cacheKey = generateCacheKey(key, params)
    return queryClient.getQueryData<T>(cacheKey)
  }, [queryClient])

  /**
   * Definir dados no cache manualmente
   */
  const setCachedData = useCallback(<T>(
    key: string,
    data: T,
    params?: Record<string, any>
  ) => {
    const cacheKey = generateCacheKey(key, params)
    queryClient.setQueryData(cacheKey, data)
  }, [queryClient])

  /**
   * Pré-carregar dados importantes (versão original)
   */
  const preloadCriticalData = useCallback(async () => {
    try {
      // Pré-carregar categorias públicas
      await queryClient.prefetchQuery({
        queryKey: ['publicCategories'],
        staleTime: cacheConfig.categories * 1000
      })

      // Pré-carregar avaliações em destaque
      await queryClient.prefetchQuery({
        queryKey: ['publicAssessments', 'featured'],
        staleTime: cacheConfig.assessmentList * 1000
      })
    } catch (error) {
      console.warn('Failed to preload critical data:', error)
    }
  }, [queryClient, cacheConfig])

  /**
   * Prefetch dados críticos baseado no contexto do usuário
   */
  const prefetchCriticalData = useCallback(async (
    userContext: {
      isLoggedIn: boolean
      isAdmin?: boolean
      isSchoolAdmin?: boolean
      currentPage?: string
    }
  ) => {
    const fetchFunctions: Record<string, () => Promise<any>> = {}

    // Definir funções de fetch baseado no contexto
    if (userContext.isLoggedIn) {
      // Prefetch dados do usuário logado
      if (userContext.currentPage === 'questions') {
        await prefetchData(queryClient, 'ON_QUESTIONS_PAGE', fetchFunctions)
      } else if (userContext.currentPage === 'assessments') {
        await prefetchData(queryClient, 'ON_ASSESSMENTS_PAGE', fetchFunctions)
      }
    }
  }, [queryClient])

  /**
   * Salvar dados importantes no localStorage
   */
  const persistCriticalData = useCallback(() => {
    PERSISTENT_CACHE_CONFIG.PERSISTENT_KEYS.forEach(key => {
      const data = queryClient.getQueryData([key])
      if (data) {
        const persistentData = {
          data,
          timestamp: Date.now(),
          ttl: PERSISTENT_CACHE_CONFIG.PERSISTENT_TTL
        }
        localStorage.setItem(`cache_${key}`, JSON.stringify(persistentData))
      }
    })
  }, [queryClient])

  /**
   * Restaurar dados do localStorage
   */
  const restorePersistedData = useCallback(() => {
    PERSISTENT_CACHE_CONFIG.PERSISTENT_KEYS.forEach(key => {
      try {
        const stored = localStorage.getItem(`cache_${key}`)
        if (stored) {
          const { data, timestamp, ttl } = JSON.parse(stored)

          // Verificar se não expirou
          if (Date.now() - timestamp < ttl) {
            queryClient.setQueryData([key], data)
          } else {
            localStorage.removeItem(`cache_${key}`)
          }
        }
      } catch (error) {
        console.warn(`Failed to restore cached data for ${key}:`, error)
        localStorage.removeItem(`cache_${key}`)
      }
    })
  }, [queryClient])

  /**
   * Configurar cache inteligente baseado no tipo de conexão
   */
  const adaptToConnection = useCallback(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection

      if (connection) {
        const isSlowConnection = connection.effectiveType === '2g' ||
                               connection.effectiveType === 'slow-2g'

        if (isSlowConnection) {
          // Aumentar tempo de cache para conexões lentas
          queryClient.setDefaultOptions({
            queries: {
              staleTime: CACHE_TIMES.STATIC.staleTime,
              gcTime: CACHE_TIMES.STATIC.gcTime,
            }
          })
        }
      }
    }
  }, [queryClient])

  /**
   * Otimizar cache baseado no uso (versão melhorada)
   */
  const optimizeCache = useCallback(() => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()

    // Remover queries não utilizadas há mais de 30 minutos
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000)

    queries.forEach(query => {
      const lastUsed = query.state.dataUpdatedAt
      const hasObservers = query.getObserversCount() > 0

      if (!hasObservers && lastUsed < thirtyMinutesAgo) {
        queryClient.removeQueries({ queryKey: query.queryKey })
      }
    })

    // Atualizar estatísticas
    updateCacheStats()
  }, [queryClient])

  /**
   * Limpar cache antigo automaticamente
   */
  const performCacheCleanup = useCallback(() => {
    cleanupOldCache(queryClient)
    updateCacheStats()
  }, [queryClient])

  /**
   * Obter estatísticas avançadas do cache
   */
  const getCacheStats = useCallback(() => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()

    const stats = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
      cacheSize: queries.reduce((size, query) => {
        const dataSize = JSON.stringify(query.state.data || {}).length
        return size + dataSize
      }, 0),
      oldestQuery: Math.min(...queries.map(q => q.state.dataUpdatedAt)),
      newestQuery: Math.max(...queries.map(q => q.state.dataUpdatedAt)),
    }

    return stats
  }, [queryClient])

  /**
   * Atualizar estatísticas do cache (versão melhorada)
   */
  const updateCacheStats = useCallback(() => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()

    let hits = 0
    let misses = 0

    queries.forEach(query => {
      const state = query.state
      if (state.status === 'success' && state.isFetching === false) {
        hits++
      } else if (state.status === 'error' || state.isFetching) {
        misses++
      }
    })

    const total = hits + misses
    const hitRate = total > 0 ? (hits / total) * 100 : 0
    const advancedStats = getCacheStats()

    setCacheStats({
      hits,
      misses,
      size: queries.length,
      hitRate: Math.round(hitRate * 100) / 100,
      ...advancedStats
    })
  }, [queryClient, getCacheStats])

  /**
   * Cache de imagens com lazy loading
   */
  const cacheImage = useCallback((src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  }, [])

  /**
   * Pré-carregar imagens críticas
   */
  const preloadImages = useCallback(async (imageUrls: string[]) => {
    const promises = imageUrls.map(url => cacheImage(url))
    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.warn('Some images failed to preload:', error)
    }
  }, [cacheImage])

  /**
   * Cache de dados no localStorage com TTL
   */
  const setLocalCache = useCallback((key: string, data: any, ttlSeconds: number) => {
    const item = {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    }
    localStorage.setItem(`cache_${key}`, JSON.stringify(item))
  }, [])

  /**
   * Recuperar dados do localStorage com verificação de TTL
   */
  const getLocalCache = useCallback((key: string) => {
    try {
      const item = localStorage.getItem(`cache_${key}`)
      if (!item) return null

      const parsed = JSON.parse(item)
      const now = Date.now()
      
      if (now - parsed.timestamp > parsed.ttl) {
        localStorage.removeItem(`cache_${key}`)
        return null
      }
      
      return parsed.data
    } catch (error) {
      console.warn('Failed to get local cache:', error)
      return null
    }
  }, [])

  /**
   * Limpar cache local expirado
   */
  const cleanExpiredLocalCache = useCallback(() => {
    const keys = Object.keys(localStorage)
    const cacheKeys = keys.filter(key => key.startsWith('cache_'))
    
    cacheKeys.forEach(key => {
      try {
        const item = localStorage.getItem(key)
        if (item) {
          const parsed = JSON.parse(item)
          const now = Date.now()
          
          if (now - parsed.timestamp > parsed.ttl) {
            localStorage.removeItem(key)
          }
        }
      } catch (error) {
        // Remove invalid cache entries
        localStorage.removeItem(key)
      }
    })
  }, [])

  /**
   * Configurar cache headers para requests
   */
  const getCacheHeaders = useCallback((type: keyof CacheConfig) => {
    const maxAge = cacheConfig[type]
    return {
      'Cache-Control': `public, max-age=${maxAge}, s-maxage=${maxAge}`,
      'Expires': new Date(Date.now() + maxAge * 1000).toUTCString(),
      'ETag': `"${Date.now()}"`,
      'Last-Modified': new Date().toUTCString()
    }
  }, [cacheConfig])

  /**
   * Verificar se o cache está funcionando bem
   */
  const getCacheHealth = useCallback(() => {
    const { hitRate, size } = cacheStats
    
    let health: 'excellent' | 'good' | 'fair' | 'poor'
    let recommendations: string[] = []
    
    if (hitRate >= 80) {
      health = 'excellent'
    } else if (hitRate >= 60) {
      health = 'good'
    } else if (hitRate >= 40) {
      health = 'fair'
      recommendations.push('Considere aumentar o tempo de cache')
    } else {
      health = 'poor'
      recommendations.push('Cache não está sendo efetivo')
      recommendations.push('Verifique as configurações de TTL')
    }
    
    if (size > 100) {
      recommendations.push('Cache muito grande, considere limpeza automática')
    }
    
    return {
      health,
      recommendations,
      stats: cacheStats
    }
  }, [cacheStats])

  // Cleanup dos intervalos ao desmontar
  useEffect(() => {
    return () => {
      backgroundIntervals.current.forEach(interval => {
        clearInterval(interval)
      })
      backgroundIntervals.current = []
    }
  }, [])

  // Configurar limpeza automática do cache
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      performCacheCleanup()
      optimizeCache()
    }, 10 * 60 * 1000) // A cada 10 minutos

    return () => clearInterval(cleanupInterval)
  }, [performCacheCleanup, optimizeCache])

  // Salvar dados críticos periodicamente
  useEffect(() => {
    const persistInterval = setInterval(() => {
      persistCriticalData()
    }, 5 * 60 * 1000) // A cada 5 minutos

    return () => clearInterval(persistInterval)
  }, [persistCriticalData])

  // Restaurar dados na inicialização
  useEffect(() => {
    restorePersistedData()
    adaptToConnection()
  }, [restorePersistedData, adaptToConnection])

  // Limpeza automática do cache a cada 5 minutos (versão original)
  useEffect(() => {
    const interval = setInterval(() => {
      optimizeCache()
      cleanExpiredLocalCache()
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [optimizeCache, cleanExpiredLocalCache])

  // Atualizar estatísticas periodicamente
  useEffect(() => {
    const interval = setInterval(updateCacheStats, 30 * 1000)
    updateCacheStats() // Initial update
    
    return () => clearInterval(interval)
  }, [updateCacheStats])

  return {
    // Invalidação
    invalidateByStrategy,
    invalidateCache,
    invalidateMultiple,

    // Prefetch
    prefetchWithConfig,
    prefetchCriticalData,
    preloadCriticalData, // Compatibilidade
    preloadImages,

    // Background refetch
    setupRealtimeRefetch,

    // Manipulação direta do cache
    getCachedData,
    setCachedData,

    // Cache management
    clearAllCache,
    optimizeCache,
    performCacheCleanup,

    // Local cache
    setLocalCache,
    getLocalCache,
    cleanExpiredLocalCache,

    // Persistência
    persistCriticalData,
    restorePersistedData,

    // Adaptação
    adaptToConnection,

    // Estatísticas e otimização
    getCacheStats,
    getCacheHeaders,
    getCacheHealth,

    // Stats (compatibilidade)
    cacheStats,

    // Chaves de cache
    CACHE_KEYS,

    // Config
    cacheConfig
  }
}
