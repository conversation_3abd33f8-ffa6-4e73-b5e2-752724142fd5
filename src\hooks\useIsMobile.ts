import { useState, useEffect } from 'react'

/**
 * Hook para detectar se o dispositivo é móvel
 * Considera móvel quando a largura da tela é menor que 768px
 */
export const useIsMobile = (breakpoint: number = 768) => {
  const [isMobile, setIsMobile] = useState(false)
  const [screenWidth, setScreenWidth] = useState(0)

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      setScreenWidth(width)
      setIsMobile(width < breakpoint)
    }

    // Verificação inicial
    checkDevice()

    // Listener para mudanças de tamanho
    window.addEventListener('resize', checkDevice)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkDevice)
  }, [breakpoint])

  return {
    isMobile,
    screenWidth,
    isTablet: screenWidth >= 768 && screenWidth < 1024,
    isDesktop: screenWidth >= 1024
  }
} 