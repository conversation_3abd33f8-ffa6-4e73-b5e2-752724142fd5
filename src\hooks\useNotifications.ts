import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'
import { useEffect } from 'react'
import pushNotificationService from '../services/pushNotificationService'

interface Notification {
  id: string
  user_id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  read: boolean
  action_url?: string | null
  action_label?: string | null
  created_at: string
}

export const useNotifications = () => {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: notifications = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['notifications', user?.id],
    queryFn: async () => {
      if (!user) return []

      try {
        const { data, error } = await supabase
          .from('user_notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(20)

        if (error) {
          // If the table doesn't exist or there's another error, return empty array
          console.error('Error fetching notifications:', error)
          return []
        }

        return data as Notification[]
      } catch (error) {
        console.error('Error in fetchNotifications:', error)
        return []
      }
    },
    enabled: !!user,
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000 // Refetch every minute
  })

  // Configurar Realtime para notificações em tempo real
  useEffect(() => {
    if (!user) return

    const channel = supabase
      .channel('user_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'user_notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          console.log('Nova notificação recebida:', payload)
          
          // Atualizar cache imediatamente
          queryClient.invalidateQueries({ queryKey: ['notifications'] })
          
          // Mostrar toast para notificação nova
          const newNotification = payload.new as Notification
          toast(newNotification.message, {
            icon: getNotificationIcon(newNotification.type),
            duration: 5000,
            position: 'top-right'
          })

          // Enviar push notification
          pushNotificationService.sendNotification({
            title: newNotification.title,
            body: newNotification.message,
            tag: newNotification.type,
            requireInteraction: newNotification.type === 'error'
          })

          // Reproduzir som de notificação
          playNotificationSound(newNotification.type)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'user_notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          // Atualizar cache quando notificação for marcada como lida
          queryClient.invalidateQueries({ queryKey: ['notifications'] })
        }
      )
      .subscribe()

    // Cleanup subscription
    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, queryClient])

  // Função para obter ícone da notificação
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return '✅'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      default: return 'ℹ️'
    }
  }

  // Função para reproduzir som de notificação
  const playNotificationSound = (type: string) => {
    try {
      // Verificar se o áudio está habilitado
      const audioEnabled = localStorage.getItem('notifications_audio') !== 'false'
      if (!audioEnabled) return

      // Criar áudio baseado no tipo
      const audio = new Audio()
      switch (type) {
        case 'success': {
          // Som de sucesso simples
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()
          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)
          oscillator.frequency.value = 520
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.3)
          break
        }
        case 'warning': {
          // Som de warning simples
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()
          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)
          oscillator.frequency.value = 440
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.3)
          break
        }
        case 'error': {
          // Som de erro simples
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()
          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)
          oscillator.frequency.value = 220
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.3)
          break
        }
        default: {
          // Som padrão
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()
          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)
          oscillator.frequency.value = 660
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.3)
        }
      }
    } catch (error) {
      console.error('Error playing notification sound:', error)
    }
  }

  return { notifications, isLoading, error }
}