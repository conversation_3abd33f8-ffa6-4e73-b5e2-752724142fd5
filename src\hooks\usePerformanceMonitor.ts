import { useState, useEffect, useCallback } from 'react'
import { PerformanceMetrics, PERFORMANCE_THRESHOLDS } from '../types/public'

/**
 * Hook para monitorar performance das páginas públicas
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: 0,
    fid: 0,
    cls: 0,
    ttfb: 0,
    fcp: 0
  })
  
  const [isSupported, setIsSupported] = useState(false)
  const [recommendations, setRecommendations] = useState<string[]>([])

  /**
   * Verificar suporte a Performance Observer
   */
  useEffect(() => {
    setIsSupported('PerformanceObserver' in window && 'performance' in window)
  }, [])

  /**
   * Medir Largest Contentful Paint (LCP)
   */
  const measureLCP = useCallback(() => {
    if (!isSupported) return

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: Math.round(lastEntry.startTime) }))
        }
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      
      return () => observer.disconnect()
    } catch (error) {
      console.warn('Failed to measure LCP:', error)
    }
  }, [isSupported])

  /**
   * Medir First Input Delay (FID)
   */
  const measureFID = useCallback(() => {
    if (!isSupported) return

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          const fid = entry.processingStart - entry.startTime
          setMetrics(prev => ({ ...prev, fid: Math.round(fid) }))
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
      
      return () => observer.disconnect()
    } catch (error) {
      console.warn('Failed to measure FID:', error)
    }
  }, [isSupported])

  /**
   * Medir Cumulative Layout Shift (CLS)
   */
  const measureCLS = useCallback(() => {
    if (!isSupported) return

    try {
      let clsValue = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        
        setMetrics(prev => ({ ...prev, cls: Math.round(clsValue * 1000) / 1000 }))
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      
      return () => observer.disconnect()
    } catch (error) {
      console.warn('Failed to measure CLS:', error)
    }
  }, [isSupported])

  /**
   * Medir Time to First Byte (TTFB)
   */
  const measureTTFB = useCallback(() => {
    if (!('performance' in window) || !performance.timing) return

    try {
      const { responseStart, requestStart } = performance.timing
      const ttfb = responseStart - requestStart
      setMetrics(prev => ({ ...prev, ttfb: Math.round(ttfb) }))
    } catch (error) {
      console.warn('Failed to measure TTFB:', error)
    }
  }, [])

  /**
   * Medir First Contentful Paint (FCP)
   */
  const measureFCP = useCallback(() => {
    if (!isSupported) return

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-contentful-paint') {
            setMetrics(prev => ({ ...prev, fcp: Math.round(entry.startTime) }))
          }
        })
      })
      
      observer.observe({ entryTypes: ['paint'] })
      
      return () => observer.disconnect()
    } catch (error) {
      console.warn('Failed to measure FCP:', error)
    }
  }, [isSupported])

  /**
   * Avaliar performance e gerar recomendações
   */
  const analyzePerformance = useCallback(() => {
    const newRecommendations: string[] = []
    
    // Analisar LCP
    if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp) {
      newRecommendations.push('LCP alto: Otimize imagens e recursos críticos')
    }
    
    // Analisar FID
    if (metrics.fid > PERFORMANCE_THRESHOLDS.fid) {
      newRecommendations.push('FID alto: Reduza JavaScript bloqueante')
    }
    
    // Analisar CLS
    if (metrics.cls > PERFORMANCE_THRESHOLDS.cls) {
      newRecommendations.push('CLS alto: Defina dimensões para imagens e elementos')
    }
    
    // Analisar TTFB
    if (metrics.ttfb > PERFORMANCE_THRESHOLDS.ttfb) {
      newRecommendations.push('TTFB alto: Otimize servidor e cache')
    }
    
    // Analisar FCP
    if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp) {
      newRecommendations.push('FCP alto: Otimize CSS crítico e fontes')
    }
    
    setRecommendations(newRecommendations)
  }, [metrics])

  /**
   * Obter score geral de performance
   */
  const getPerformanceScore = useCallback(() => {
    let score = 100
    
    // Penalizar métricas ruins
    if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp) score -= 20
    if (metrics.fid > PERFORMANCE_THRESHOLDS.fid) score -= 20
    if (metrics.cls > PERFORMANCE_THRESHOLDS.cls) score -= 20
    if (metrics.ttfb > PERFORMANCE_THRESHOLDS.ttfb) score -= 20
    if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp) score -= 20
    
    return Math.max(0, score)
  }, [metrics])

  /**
   * Obter classificação de performance
   */
  const getPerformanceGrade = useCallback(() => {
    const score = getPerformanceScore()
    
    if (score >= 90) return { grade: 'A', color: 'green', label: 'Excelente' }
    if (score >= 80) return { grade: 'B', color: 'blue', label: 'Bom' }
    if (score >= 70) return { grade: 'C', color: 'yellow', label: 'Regular' }
    if (score >= 60) return { grade: 'D', color: 'orange', label: 'Ruim' }
    return { grade: 'F', color: 'red', label: 'Muito Ruim' }
  }, [getPerformanceScore])

  /**
   * Medir performance de carregamento de recursos
   */
  const measureResourceTiming = useCallback(() => {
    if (!('performance' in window)) return []

    try {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      
      return resources.map(resource => ({
        name: resource.name,
        duration: Math.round(resource.duration),
        size: resource.transferSize || 0,
        type: resource.initiatorType,
        startTime: Math.round(resource.startTime)
      })).sort((a, b) => b.duration - a.duration)
    } catch (error) {
      console.warn('Failed to measure resource timing:', error)
      return []
    }
  }, [])

  /**
   * Detectar recursos lentos
   */
  const getSlowResources = useCallback(() => {
    const resources = measureResourceTiming()
    return resources.filter(resource => resource.duration > 1000) // > 1s
  }, [measureResourceTiming])

  /**
   * Medir uso de memória
   */
  const measureMemoryUsage = useCallback(() => {
    if (!('memory' in performance)) return null

    try {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
      }
    } catch (error) {
      console.warn('Failed to measure memory usage:', error)
      return null
    }
  }, [])

  /**
   * Inicializar monitoramento
   */
  useEffect(() => {
    if (!isSupported) return

    const cleanupFunctions: (() => void)[] = []
    
    // Aguardar carregamento completo
    const initializeMetrics = () => {
      cleanupFunctions.push(measureLCP() || (() => {}))
      cleanupFunctions.push(measureFID() || (() => {}))
      cleanupFunctions.push(measureCLS() || (() => {}))
      cleanupFunctions.push(measureFCP() || (() => {}))
      measureTTFB()
    }

    if (document.readyState === 'complete') {
      initializeMetrics()
    } else {
      window.addEventListener('load', initializeMetrics)
      cleanupFunctions.push(() => window.removeEventListener('load', initializeMetrics))
    }

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup())
    }
  }, [isSupported, measureLCP, measureFID, measureCLS, measureFCP, measureTTFB])

  /**
   * Analisar performance quando métricas mudarem
   */
  useEffect(() => {
    if (metrics.lcp > 0 || metrics.fid > 0 || metrics.cls > 0) {
      analyzePerformance()
    }
  }, [metrics, analyzePerformance])

  return {
    metrics,
    recommendations,
    isSupported,
    getPerformanceScore,
    getPerformanceGrade,
    measureResourceTiming,
    getSlowResources,
    measureMemoryUsage
  }
}
