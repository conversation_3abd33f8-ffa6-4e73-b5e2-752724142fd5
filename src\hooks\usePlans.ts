import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

// Tipos para a tabela plans
type Plan = Database['public']['Tables']['plans']['Row']
type PlanInsert = Database['public']['Tables']['plans']['Insert']
type PlanUpdate = Database['public']['Tables']['plans']['Update']

// Interface para os filtros de planos (se for necessário no futuro)
interface PlanFilters {
  searchTerm?: string
  isActive?: boolean
}

export const usePlans = (filters?: PlanFilters) => {
  const queryClient = useQueryClient()

  // 1. Hook para buscar planos
  const {
    data: plans,
    isLoading,
    error,
    refetch,
  } = useQuery<Plan[], Error>({
    queryKey: ['plans', filters], // Adiciona filtros à chave da query para recarregar quando mudarem
    queryFn: async () => {
      let query = supabase.from('plans').select('*')

      if (filters?.searchTerm) {
        query = query.or(`name.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`)
      }
      if (filters?.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive)
      }

      const { data, error } = await query.order('name', { ascending: true })
      if (error) throw error
      return data || [] // MODIFICAÇÃO: Garante que sempre retorna um array
    },
    staleTime: 1000 * 60 * 5, // 5 minutos de cache para os planos
    onError: (err) => {
      console.error('Erro ao carregar planos:', err)
      toast.error('Erro ao carregar planos: ' + err.message)
    },
  })

  // 2. Hook para criar um novo plano
  const createPlanMutation = useMutation<Plan, Error, Omit<PlanInsert, 'id' | 'created_at' | 'updated_at'>>({
    mutationFn: async (newPlanData) => {
      const { data, error } = await supabase
        .from('plans')
        .insert(newPlanData)
        .select()
        .single()
      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] }) // Invalida o cache para recarregar a lista de planos
      toast.success('Plano criado com sucesso!')
    },
    onError: (err) => {
      console.error('Erro ao criar plano:', err)
      toast.error('Erro ao criar plano: ' + err.message)
    },
  })

  // 3. Hook para atualizar um plano existente
  const updatePlanMutation = useMutation<Plan, Error, { id: string; updates: PlanUpdate }>({
    mutationFn: async ({ id, updates }) => {
      const { data, error } = await supabase
        .from('plans')
        .update({ ...updates, updated_at: new Date().toISOString() }) // Atualiza timestamp
        .eq('id', id)
        .select()
        .single()
      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] }) // Invalida o cache
      toast.success('Plano atualizado com sucesso!')
    },
    onError: (err) => {
      console.error('Erro ao atualizar plano:', err)
      toast.error('Erro ao atualizar plano: ' + err.message)
    },
  })

  // 4. Hook para deletar um plano
  const deletePlanMutation = useMutation<void, Error, string>({
    mutationFn: async (planId) => {
      const { error } = await supabase
        .from('plans')
        .delete()
        .eq('id', planId)
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] }) // Invalida o cache
      toast.success('Plano excluído com sucesso!')
    },
    onError: (err) => {
      console.error('Erro ao excluir plano:', err)
      toast.error('Erro ao excluir plano: ' + err.message)
    },
  })

  return {
    plans,
    isLoading,
    error,
    refetch,
    createPlan: createPlanMutation.mutateAsync,
    updatePlan: updatePlanMutation.mutateAsync,
    deletePlan: deletePlanMutation.mutateAsync,
    isCreating: createPlanMutation.isPending,
    isUpdating: updatePlanMutation.isPending,
    isDeleting: deletePlanMutation.isPending,
  }
} 