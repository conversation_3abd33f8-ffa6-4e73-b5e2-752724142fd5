import { useState, useEffect, useCallback } from 'react'
import { useQuery, useInfiniteQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { 
  PublicAssessmentWithDetails, 
  PublicAssessmentFilters, 
  PublicAssessmentSearchResult,
  UsePublicAssessmentsReturn,
  PublicError
} from '../types/public'

const QUERY_KEYS = {
  publicAssessments: 'publicAssessments',
  publicAssessmentDetail: 'publicAssessmentDetail',
  publicCategories: 'publicCategories'
} as const

/**
 * Hook para buscar avaliações públicas com filtros e paginação
 */
export const usePublicAssessments = (
  filters: PublicAssessmentFilters = {}
): UsePublicAssessmentsReturn => {
  const [error, setError] = useState<PublicError | null>(null)

  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    refetch
  } = useInfiniteQuery({
    queryKey: [QUERY_KEYS.publicAssessments, filters],
    queryFn: async ({ pageParam = 0 }) => {
      try {
        const limit = filters.limit || 12
        const offset = pageParam * limit

        let query = supabase
          .from('assessments')
          .select(`
            *,
            profiles!assessments_autor_id_fkey(nome, escola)
          `)
          .eq('is_public', true)
          .range(offset, offset + limit - 1)

        // Apply filters
        if (filters.category) {
          query = query.eq('public_category', filters.category)
        }

        if (filters.disciplina) {
          query = query.eq('disciplina', filters.disciplina)
        }

        if (filters.serie) {
          query = query.eq('serie', filters.serie)
        }

        if (filters.difficulty) {
          query = query.eq('difficulty_level', filters.difficulty)
        }

        if (filters.featured) {
          query = query.eq('is_featured', true)
        }

        if (filters.search) {
          query = query.or(`titulo.ilike.%${filters.search}%,seo_description.ilike.%${filters.search}%`)
        }

        // Apply sorting
        const sortBy = filters.sortBy || 'recent'
        const sortOrder = filters.sortOrder || 'desc'

        switch (sortBy) {
          case 'recent':
            query = query.order('created_at', { ascending: sortOrder === 'asc' })
            break
          case 'popular':
            query = query.order('view_count', { ascending: sortOrder === 'asc' })
            break
          case 'title':
            query = query.order('titulo', { ascending: sortOrder === 'asc' })
            break
          case 'views':
            query = query.order('view_count', { ascending: sortOrder === 'asc' })
            break
          default:
            query = query.order('created_at', { ascending: false })
        }

        const { data: assessments, error, count } = await query

        if (error) throw error

        return {
          assessments: assessments || [],
          total: count || 0,
          page: pageParam,
          limit,
          hasMore: (assessments?.length || 0) === limit
        }
      } catch (err) {
        const publicError: PublicError = {
          code: 'FETCH_ERROR',
          message: err instanceof Error ? err.message : 'Erro ao buscar avaliações',
          timestamp: new Date().toISOString()
        }
        setError(publicError)
        throw publicError
      }
    },
    getNextPageParam: (lastPage, pages) => {
      return lastPage.hasMore ? pages.length : undefined
    },
    initialPageParam: 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })

  // Flatten all pages into a single array
  const assessments = data?.pages.flatMap(page => page.assessments) || []
  const total = data?.pages[0]?.total || 0

  const loadMore = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage()
    }
  }, [hasNextPage, isLoading, fetchNextPage])

  useEffect(() => {
    if (isError) {
      setError({
        code: 'QUERY_ERROR',
        message: 'Erro ao carregar avaliações públicas',
        timestamp: new Date().toISOString()
      })
    }
  }, [isError])

  return {
    assessments: assessments as PublicAssessmentWithDetails[],
    loading: isLoading,
    error,
    total,
    hasMore: hasNextPage || false,
    loadMore,
    refetch
  }
}

/**
 * Hook para buscar uma avaliação pública específica por slug
 */
export const usePublicAssessmentDetail = (slug: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.publicAssessmentDetail, slug],
    queryFn: async () => {
      if (!slug) throw new Error('Slug é obrigatório')

      const { data: assessment, error } = await supabase
        .from('assessments')
        .select(`
          *,
          profiles!assessments_autor_id_fkey(nome, escola)
        `)
        .eq('slug', slug)
        .eq('is_public', true)
        .single()

      if (error) throw error
      if (!assessment) throw new Error('Avaliação não encontrada')

      // Increment view count
      await supabase.rpc('increment_view_count', { assessment_id: assessment.id })

      // Fetch questions if needed
      if (assessment.questoes_ids && assessment.questoes_ids.length > 0) {
        const { data: questions } = await supabase
          .from('questions')
          .select('id, enunciado, tipo, alternativas, disciplina, serie, dificuldade, tags')
          .in('id', assessment.questoes_ids)
          .eq('is_public', true)

        assessment.questions = questions || []
      }

      return assessment as PublicAssessmentWithDetails
    },
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

/**
 * Hook para buscar categorias públicas
 */
export const usePublicCategories = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.publicCategories],
    queryFn: async () => {
      const { data: categories, error } = await supabase
        .from('public_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order')

      if (error) throw error
      return categories || []
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000 // 2 hours
  })
}

/**
 * Hook para buscar avaliações em destaque
 */
export const useFeaturedAssessments = (limit: number = 6) => {
  return useQuery({
    queryKey: [QUERY_KEYS.publicAssessments, 'featured', limit],
    queryFn: async () => {
      const { data: assessments, error } = await supabase
        .from('assessments')
        .select(`
          *,
          profiles!assessments_autor_id_fkey(nome, escola)
        `)
        .eq('is_public', true)
        .eq('is_featured', true)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return assessments as PublicAssessmentWithDetails[]
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

/**
 * Hook para buscar avaliações relacionadas
 */
export const useRelatedAssessments = (
  currentAssessmentId: string,
  disciplina: string,
  serie: string,
  limit: number = 4
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.publicAssessments, 'related', currentAssessmentId, disciplina, serie, limit],
    queryFn: async () => {
      const { data: assessments, error } = await supabase
        .from('assessments')
        .select(`
          *,
          profiles!assessments_autor_id_fkey(nome, escola)
        `)
        .eq('is_public', true)
        .neq('id', currentAssessmentId)
        .or(`disciplina.eq.${disciplina},serie.eq.${serie}`)
        .order('view_count', { ascending: false })
        .limit(limit)

      if (error) throw error
      return assessments as PublicAssessmentWithDetails[]
    },
    enabled: !!currentAssessmentId && !!disciplina && !!serie,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000 // 1 hour
  })
}
