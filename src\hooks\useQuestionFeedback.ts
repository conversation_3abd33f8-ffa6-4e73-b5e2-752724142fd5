import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type QuestionFeedback = Database['public']['Tables']['question_feedback']['Row']
type QuestionFeedbackInsert = Database['public']['Tables']['question_feedback']['Insert']
type QuestionFeedbackUpdate = Database['public']['Tables']['question_feedback']['Update']
type FeedbackAction = Database['public']['Tables']['feedback_actions']['Row']

interface FeedbackStats {
  total_feedback: number
  approved_feedback: number
  pending_feedback: number
  rejected_feedback: number
  avg_rating: number
  feedback_by_type: Record<string, number>
  top_issues: Array<{ suggestion: string; count: number }>
}

interface FeedbackFilters {
  question_id?: string
  user_id?: string
  is_approved?: boolean
  is_reviewed?: boolean
  feedback_type?: string
  rating?: number
  limit?: number
  page?: number
}

export const useQuestionFeedback = (filters: FeedbackFilters = {}) => {
  const { user, isAdmin } = useAuth()
  const queryClient = useQueryClient()

  // Query to fetch feedback based on filters
  const {
    data: feedback = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['question-feedback', filters],
    queryFn: async () => {
      let query = supabase
        .from('question_feedback')
        .select(`
          *,
          profiles!question_feedback_user_id_fkey (
            nome,
            email
          ),
          questions (
            enunciado,
            disciplina,
            serie,
            topico
          )
        `)

      // Apply filters
      if (filters.question_id) {
        query = query.eq('question_id', filters.question_id)
      }
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id)
      }
      if (filters.is_approved !== undefined) {
        query = query.eq('is_approved', filters.is_approved)
      }
      if (filters.is_reviewed !== undefined) {
        query = query.eq('is_reviewed', filters.is_reviewed)
      }
      if (filters.feedback_type) {
        query = query.eq('feedback_type', filters.feedback_type)
      }
      if (filters.rating) {
        query = query.eq('rating', filters.rating)
      }

      // Pagination
      const limit = filters.limit || 20
      const page = filters.page || 1
      const offset = (page - 1) * limit

      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!user,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1
  })

  // Mutation to create feedback
  const createFeedbackMutation = useMutation({
    mutationFn: async (feedbackData: QuestionFeedbackInsert) => {
      if (!user) throw new Error('User not authenticated')

      // Get user's IP and user agent for security tracking
      const userAgent = navigator.userAgent
      
      const { data, error } = await supabase
        .from('question_feedback')
        .insert({
          ...feedbackData,
          user_id: user.id,
          user_agent: userAgent,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['question-feedback'] })
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Feedback enviado com sucesso! Será analisado por nossa equipe.')
    },
    onError: (error: any) => {
      console.error('Error creating feedback:', error)
      if (error.code === '23505') {
        toast.error('Você já enviou feedback para esta questão')
      } else {
        toast.error('Erro ao enviar feedback. Tente novamente.')
      }
    }
  })

  // Mutation to update feedback (for users to edit their own feedback)
  const updateFeedbackMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: QuestionFeedbackUpdate }) => {
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('question_feedback')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['question-feedback'] })
      toast.success('Feedback atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating feedback:', error)
      toast.error('Erro ao atualizar feedback')
    }
  })

  // Admin mutation to moderate feedback
  const moderateFeedbackMutation = useMutation({
    mutationFn: async ({ 
      id, 
      is_approved, 
      admin_response 
    }: { 
      id: string; 
      is_approved: boolean; 
      admin_response?: string 
    }) => {
      if (!user || !isAdmin) throw new Error('Admin access required')

      const { data, error } = await supabase
        .from('question_feedback')
        .update({
          is_approved,
          is_reviewed: true,
          admin_response,
          reviewed_by: user.id,
          reviewed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['question-feedback'] })
      queryClient.invalidateQueries({ queryKey: ['feedback-stats'] })
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      
      const action = data.is_approved ? 'aprovado' : 'rejeitado'
      toast.success(`Feedback ${action} com sucesso!`)
    },
    onError: (error) => {
      console.error('Error moderating feedback:', error)
      toast.error('Erro ao moderar feedback')
    }
  })

  // Delete feedback mutation (admin only)
  const deleteFeedbackMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user || !isAdmin) throw new Error('Admin access required')

      const { error } = await supabase
        .from('question_feedback')
        .delete()
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['question-feedback'] })
      queryClient.invalidateQueries({ queryKey: ['feedback-stats'] })
      toast.success('Feedback excluído com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting feedback:', error)
      toast.error('Erro ao excluir feedback')
    }
  })

  return {
    feedback,
    isLoading,
    error,
    createFeedback: createFeedbackMutation.mutate,
    updateFeedback: updateFeedbackMutation.mutate,
    moderateFeedback: moderateFeedbackMutation.mutate,
    deleteFeedback: deleteFeedbackMutation.mutate,
    isCreating: createFeedbackMutation.isPending,
    isUpdating: updateFeedbackMutation.isPending,
    isModerating: moderateFeedbackMutation.isPending,
    isDeleting: deleteFeedbackMutation.isPending
  }
}

// Hook for feedback statistics (admin only)
export const useFeedbackStats = (startDate?: string, endDate?: string) => {
  const { user, isAdmin } = useAuth()

  return useQuery({
    queryKey: ['feedback-stats', startDate, endDate],
    queryFn: async (): Promise<FeedbackStats> => {
      const { data, error } = await supabase.rpc('get_feedback_stats', {
        start_date: startDate || null,
        end_date: endDate || null
      })

      if (error) throw error
      
      // Transform the response to match our interface
      const result = data[0] || {}
      return {
        total_feedback: Number(result.total_feedback) || 0,
        approved_feedback: Number(result.approved_feedback) || 0,
        pending_feedback: Number(result.pending_feedback) || 0,
        rejected_feedback: Number(result.rejected_feedback) || 0,
        avg_rating: Number(result.avg_rating) || 0,
        feedback_by_type: result.feedback_by_type || {},
        top_issues: result.top_issues || []
      }
    },
    enabled: !!user && isAdmin,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })
}

// Hook for feedback actions (admin audit trail)
export const useFeedbackActions = (feedbackId?: string) => {
  const { user, isAdmin } = useAuth()

  return useQuery({
    queryKey: ['feedback-actions', feedbackId],
    queryFn: async () => {
      let query = supabase
        .from('feedback_actions')
        .select(`
          *,
          profiles!feedback_actions_admin_id_fkey (
            nome,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (feedbackId) {
        query = query.eq('feedback_id', feedbackId)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!user && isAdmin,
    staleTime: 2 * 60 * 1000,
    retry: 1
  })
}

// Utility function to check if user can edit feedback
export const canEditFeedback = (feedback: QuestionFeedback, currentUserId?: string): boolean => {
  if (!currentUserId) return false
  return feedback.user_id === currentUserId && !feedback.is_reviewed
}

// Utility function to get feedback type label
export const getFeedbackTypeLabel = (type: string): string => {
  const labels = {
    rating: 'Avaliação',
    improvement: 'Sugestão de Melhoria',
    error: 'Erro Encontrado',
    general: 'Comentário Geral'
  }
  return labels[type as keyof typeof labels] || type
}

// Utility function to get feedback status
export const getFeedbackStatus = (feedback: QuestionFeedback): {
  status: 'pending' | 'approved' | 'rejected'
  label: string
  color: string
} => {
  if (!feedback.is_reviewed) {
    return { status: 'pending', label: 'Pendente', color: 'yellow' }
  }
  if (feedback.is_approved) {
    return { status: 'approved', label: 'Aprovado', color: 'green' }
  }
  return { status: 'rejected', label: 'Rejeitado', color: 'red' }
}
