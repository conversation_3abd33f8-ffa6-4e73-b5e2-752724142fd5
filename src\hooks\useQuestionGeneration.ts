import { useState, useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useQuestionGenerationRateLimit } from './useRateLimit'
import { useAuditLog } from './useAuditLog'
import toast from 'react-hot-toast'

export interface GenerationParams {
  disciplina: string
  serie: string
  topico: string
  subtopico?: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  quantidade: number
  competencia_bncc?: string
  contexto?: string
}

export interface GeneratedQuestion {
  id: string
  disciplina: string
  serie: string
  topico: string
  subtopico?: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  competencia_bncc?: string
  enunciado: string
  alternativas?: string[]
  resposta_correta: string
  explicacao: string
  confidence?: number
  status: 'pending' | 'accepted' | 'rejected'
}

export interface GenerationResponse {
  questions: GeneratedQuestion[]
  provider: string
  tokens_used?: number
  cost?: number
  cached: boolean
  generated_at: string
}

export interface GenerationError {
  error: string
  timestamp: string
  request_id: string
}

export const useQuestionGeneration = () => {
  const { user } = useAuth()
  const rateLimit = useQuestionGenerationRateLimit(user?.id)
  const auditLog = useAuditLog()
  
  const [generating, setGenerating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')
  const [performanceMetrics, setPerformanceMetrics] = useState<{
    startTime?: number
    endTime?: number
    duration?: number
    questionsPerSecond?: number
    provider?: string
    tokensUsed?: number
    cost?: number
    cached?: boolean
  }>({})

  const [lastGenerationInfo, setLastGenerationInfo] = useState<{
    provider?: string
    tokensUsed?: number
    cost?: number
    cached?: boolean
  }>({})

  // Função para sanitizar inputs e prevenir XSS/injection
  const sanitizeInput = useCallback((input: string): string => {
    if (typeof input !== 'string') return ''
    
    return input
      .trim()
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .slice(0, 500) // Limitar tamanho
  }, [])

  // Função para validar parâmetros de geração
  const validateParams = useCallback((params: GenerationParams): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    // Validações obrigatórias
    if (!params.disciplina?.trim()) {
      errors.push('Disciplina é obrigatória')
    } else if (params.disciplina.length < 2 || params.disciplina.length > 100) {
      errors.push('Disciplina deve ter entre 2 e 100 caracteres')
    }

    if (!params.serie?.trim()) {
      errors.push('Série é obrigatória')
    } else if (params.serie.length < 1 || params.serie.length > 50) {
      errors.push('Série deve ter entre 1 e 50 caracteres')
    }

    if (!params.topico?.trim()) {
      errors.push('Tópico é obrigatório')
    } else if (params.topico.length < 3 || params.topico.length > 200) {
      errors.push('Tópico deve ter entre 3 e 200 caracteres')
    }

    // Validações de formato
    if (!['Fácil', 'Médio', 'Difícil'].includes(params.dificuldade)) {
      errors.push('Dificuldade deve ser Fácil, Médio ou Difícil')
    }

    if (!['multipla_escolha', 'dissertativa', 'verdadeiro_falso'].includes(params.tipo)) {
      errors.push('Tipo de questão inválido')
    }

    if (!Number.isInteger(params.quantidade) || params.quantidade < 1 || params.quantidade > 50) {
      errors.push('Quantidade deve ser um número inteiro entre 1 e 50')
    }

    // Validações opcionais
    if (params.subtopico && (params.subtopico.length > 200)) {
      errors.push('Subtópico deve ter no máximo 200 caracteres')
    }

    if (params.competencia_bncc && (params.competencia_bncc.length > 300)) {
      errors.push('Competência BNCC deve ter no máximo 300 caracteres')
    }

    if (params.contexto && (params.contexto.length > 1000)) {
      errors.push('Contexto deve ter no máximo 1000 caracteres')
    }

    return { isValid: errors.length === 0, errors }
  }, [])

  // Função para sanitizar todos os parâmetros
  const sanitizeParams = useCallback((params: GenerationParams): GenerationParams => {
    return {
      disciplina: sanitizeInput(params.disciplina),
      serie: sanitizeInput(params.serie),
      topico: sanitizeInput(params.topico),
      subtopico: params.subtopico ? sanitizeInput(params.subtopico) : undefined,
      dificuldade: params.dificuldade, // Enum controlado
      tipo: params.tipo, // Enum controlado
      quantidade: Math.max(1, Math.min(50, Math.floor(params.quantidade))), // Garantir range válido
      competencia_bncc: params.competencia_bncc ? sanitizeInput(params.competencia_bncc) : undefined,
      contexto: params.contexto ? sanitizeInput(params.contexto) : undefined
    }
  }, [sanitizeInput])

  // Função principal para gerar questões
  const generateQuestions = useCallback(async (params: GenerationParams): Promise<GeneratedQuestion[]> => {
    // Verificar rate limiting
    if (!rateLimit.canMakeRequest) {
      const error = `Rate limit excedido. Você pode fazer ${rateLimit.remainingRequests} requisições. Tente novamente em ${rateLimit.formatTimeUntilReset()}.`
      toast.error(error)
      throw new Error(error)
    }

    // Sanitizar parâmetros antes da validação
    const sanitizedParams = sanitizeParams(params)
    
    // Validar parâmetros sanitizados
    const validation = validateParams(sanitizedParams)
    if (!validation.isValid) {
      const error = `Erro de validação: ${validation.errors.join(', ')}`
      toast.error(error)
      throw new Error(error)
    }

    // Registrar requisição no rate limiter
    if (!rateLimit.recordRequest()) {
      const error = 'Não foi possível processar a requisição. Rate limit excedido.'
      toast.error(error)
      throw new Error(error)
    }

    setGenerating(true)
    setProgress(0)
    setCurrentStep('Iniciando geração...')

    // Iniciar métricas de performance
    const startTime = performance.now()
    setPerformanceMetrics({ startTime })

    try {
      // Tentar chamar a Edge Function
      setProgress(25)
      setCurrentStep('Conectando com IA...')

      const { data, error } = await supabase.functions.invoke('admin-generate-questions', {
        body: sanitizedParams
      })

      if (error) throw error

      // Handle the new response format
      const response = data as GenerationResponse

      setProgress(75)
      setCurrentStep(`Processando questões (${response.provider})...`)

      // Adicionar status a cada questão
      const questionsWithStatus: GeneratedQuestion[] = response.questions.map((q: any) => ({
        ...q,
        status: 'pending' as const
      }))

      setProgress(100)
      setCurrentStep('Concluído!')

      // Calcular métricas de performance
      const endTime = performance.now()
      const duration = endTime - startTime
      const questionsPerSecond = questionsWithStatus.length / (duration / 1000)

      // Store generation info for display
      setLastGenerationInfo({
        provider: response.provider,
        tokensUsed: response.tokens_used,
        cost: response.cost,
        cached: response.cached
      })

      setPerformanceMetrics({
        startTime,
        endTime,
        duration,
        questionsPerSecond,
        provider: response.provider,
        tokensUsed: response.tokens_used,
        cost: response.cost,
        cached: response.cached
      })

      // Log de auditoria para geração bem-sucedida
      await auditLog.logQuestionGeneration(sanitizedParams, {
        success: true,
        count: questionsWithStatus.length,
        duration_ms: duration,
        method: 'multi_provider',
        provider: response.provider,
        tokens_used: response.tokens_used,
        cost: response.cost,
        cached: response.cached
      })

      const successMessage = response.cached
        ? `${questionsWithStatus.length} questões recuperadas do cache em ${(duration / 1000).toFixed(2)}s!`
        : `${questionsWithStatus.length} questões geradas com ${response.provider} em ${(duration / 1000).toFixed(2)}s!`

      toast.success(successMessage)
      return questionsWithStatus
    } catch (error: any) {
      console.error('Error generating questions:', error)

      // Parse error response for better user feedback
      let errorMessage = error.message
      let userFriendlyMessage = 'Erro ao gerar questões. Tente novamente.'

      try {
        // Try to parse structured error response
        if (error.message.includes('All AI providers failed')) {
          userFriendlyMessage = 'Todos os provedores de IA estão indisponíveis no momento. Tente novamente em alguns minutos.'
        } else if (error.message.includes('Rate limit exceeded')) {
          userFriendlyMessage = 'Limite de requisições excedido. Aguarde alguns minutos antes de tentar novamente.'
        } else if (error.message.includes('API key not configured')) {
          userFriendlyMessage = 'Serviço de IA temporariamente indisponível. Entre em contato com o suporte.'
        } else if (error.message.includes('Invalid quantidade')) {
          userFriendlyMessage = 'Quantidade inválida. Deve ser entre 1 e 20 questões.'
        }
      } catch (parseError) {
        console.error('Error parsing error response:', parseError)
      }

      // Log de auditoria para erro
      await auditLog.logQuestionGeneration(sanitizedParams, {
        success: false,
        error: errorMessage,
        method: 'multi_provider'
      })

      toast.error(userFriendlyMessage, { duration: 5000 })
      throw new Error(userFriendlyMessage)
    } finally {
      setGenerating(false)
      setTimeout(() => {
        setProgress(0)
        setCurrentStep('')
      }, 2000)
    }
  }, [rateLimit, sanitizeParams, validateParams, auditLog])

  return {
    generateQuestions,
    generating,
    progress,
    currentStep,
    performanceMetrics,
    rateLimit,
    lastGenerationInfo
  }
}
