import { useState, useCallback } from 'react'
import { useQuestions } from './useQuestions'
import { useAuditLog } from './useAuditLog'
import { GeneratedQuestion } from './useQuestionGeneration'
import toast from 'react-hot-toast'

export const useQuestionManagement = () => {
  const { createQuestion } = useQuestions()
  const auditLog = useAuditLog()
  
  const [questions, setQuestions] = useState<GeneratedQuestion[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')

  // Função para aceitar uma questão individual
  const acceptQuestion = useCallback(async (question: GeneratedQuestion) => {
    try {
      await createQuestion({
        disciplina: question.disciplina,
        serie: question.serie,
        topico: question.topico,
        subtopico: question.subtopico || null,
        dificuldade: question.dificuldade,
        tipo: question.tipo,
        competencia_bncc: question.competencia_bncc || null,
        enunciado: question.enunciado,
        alternativas: question.alternativas || null,
        resposta_correta: question.resposta_correta,
        explicacao: question.explicacao,
        imagem_url: null,
        tags: [],
        is_public: false,
        is_verified: false
      })

      // Atualizar status no estado local
      setQuestions(prev => 
        prev.map(q => q.id === question.id ? { ...q, status: 'accepted' as const } : q)
      )
      
      // Log de auditoria para aprovação
      await auditLog.logQuestionApproval(question.id, 'APPROVE', {
        disciplina: question.disciplina,
        serie: question.serie,
        topico: question.topico,
        tipo: question.tipo
      })
      
      toast.success('Questão salva com sucesso!')
    } catch (error: any) {
      console.error('Error saving question:', error)
      toast.error(`Erro ao salvar questão: ${error.message}`)
      
      // Log de auditoria para erro na aprovação
      await auditLog.logAction('QUESTION_APPROVE_ERROR', 'generated_question', {
        error: error.message,
        question_id: question.id
      }, question.id)
    }
  }, [createQuestion, auditLog])

  // Função para rejeitar uma questão individual
  const rejectQuestion = useCallback(async (questionId: string) => {
    const question = questions.find(q => q.id === questionId)
    
    setQuestions(prev => 
      prev.map(q => q.id === questionId ? { ...q, status: 'rejected' as const } : q)
    )
    
    // Log de auditoria para rejeição
    if (question) {
      await auditLog.logQuestionApproval(questionId, 'REJECT', {
        disciplina: question.disciplina,
        serie: question.serie,
        topico: question.topico,
        tipo: question.tipo
      })
    }
    
    toast.success('Questão rejeitada')
  }, [questions, auditLog])

  // Função para aceitar todas as questões pendentes
  const acceptAllQuestions = useCallback(async () => {
    const pendingQuestions = questions.filter(q => q.status === 'pending')
    if (pendingQuestions.length === 0) {
      toast.error('Não há questões pendentes para aceitar')
      return
    }

    const confirmResult = confirm(`Deseja aceitar todas as ${pendingQuestions.length} questões pendentes?`)
    if (!confirmResult) return

    let successCount = 0
    let errorCount = 0

    for (const question of pendingQuestions) {
      try {
        await createQuestion({
          disciplina: question.disciplina,
          serie: question.serie,
          topico: question.topico,
          subtopico: question.subtopico || null,
          dificuldade: question.dificuldade,
          tipo: question.tipo,
          competencia_bncc: question.competencia_bncc || null,
          enunciado: question.enunciado,
          alternativas: question.alternativas || null,
          resposta_correta: question.resposta_correta,
          explicacao: question.explicacao,
          imagem_url: null,
          tags: [],
          is_public: false,
          is_verified: false
        })

        // Log de auditoria para aprovação
        await auditLog.logQuestionApproval(question.id, 'APPROVE', {
          disciplina: question.disciplina,
          serie: question.serie,
          topico: question.topico,
          tipo: question.tipo
        })

        successCount++
      } catch (error: any) {
        console.error('Error saving question:', error)
        
        // Log de auditoria para erro
        await auditLog.logAction('QUESTION_APPROVE_ERROR', 'generated_question', {
          error: error.message,
          question_id: question.id
        }, question.id)
        
        errorCount++
      }
    }

    // Atualizar status das questões aceitas
    setQuestions(prev => 
      prev.map(q => 
        pendingQuestions.some(pq => pq.id === q.id) && successCount > 0
          ? { ...q, status: 'accepted' as const }
          : q
      )
    )

    if (successCount > 0) {
      toast.success(`${successCount} questões aceitas com sucesso!`)
    }
    if (errorCount > 0) {
      toast.error(`Erro ao salvar ${errorCount} questões`)
    }
  }, [questions, createQuestion, auditLog])

  // Função para rejeitar todas as questões pendentes
  const rejectAllQuestions = useCallback(async () => {
    const pendingQuestions = questions.filter(q => q.status === 'pending')
    if (pendingQuestions.length === 0) {
      toast.error('Não há questões pendentes para rejeitar')
      return
    }

    const confirmResult = confirm(`Deseja rejeitar todas as ${pendingQuestions.length} questões pendentes?`)
    if (!confirmResult) return

    setQuestions(prev => 
      prev.map(q => 
        q.status === 'pending' ? { ...q, status: 'rejected' as const } : q
      )
    )

    // Log de auditoria para rejeição em massa
    for (const question of pendingQuestions) {
      await auditLog.logQuestionApproval(question.id, 'REJECT', {
        disciplina: question.disciplina,
        serie: question.serie,
        topico: question.topico,
        tipo: question.tipo,
        bulk_action: true
      })
    }
    
    toast.success(`${pendingQuestions.length} questões rejeitadas`)
  }, [questions, auditLog])

  // Função para regenerar uma questão específica
  const regenerateQuestion = useCallback(async (questionId: string) => {
    const question = questions.find(q => q.id === questionId)
    if (!question) return

    // Por enquanto, apenas simular regeneração
    // Em uma implementação real, chamaria a API novamente
    const regeneratedQuestion: GeneratedQuestion = {
      ...question,
      id: `regenerated-${Date.now()}`,
      enunciado: `${question.enunciado} (Regenerada)`,
      confidence: Math.random() * 0.4 + 0.6, // 60-100%
      status: 'pending'
    }

    // Substituir a questão original pela regenerada
    setQuestions(prev => 
      prev.map(q => q.id === questionId ? regeneratedQuestion : q)
    )

    // Log de auditoria para regeneração
    await auditLog.logQuestionRegeneration(questionId, question, regeneratedQuestion)

    toast.success('Questão regenerada com sucesso!')
  }, [questions, auditLog])

  // Função para exportar questões aceitas
  const exportQuestions = useCallback(async () => {
    const acceptedQuestions = questions.filter(q => q.status === 'accepted')
    if (acceptedQuestions.length === 0) {
      toast.error('Não há questões aceitas para exportar')
      return
    }

    try {
      const dataStr = JSON.stringify(acceptedQuestions, null, 2)
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
      
      const exportFileDefaultName = `questoes-aceitas-${new Date().toISOString().split('T')[0]}.json`
      
      const linkElement = document.createElement('a')
      linkElement.setAttribute('href', dataUri)
      linkElement.setAttribute('download', exportFileDefaultName)
      linkElement.click()

      // Log de auditoria para exportação
      await auditLog.logQuestionExport('json', acceptedQuestions.length, {
        filter_status: 'accepted'
      })

      toast.success(`${acceptedQuestions.length} questões exportadas com sucesso!`)
    } catch (error: any) {
      console.error('Error exporting questions:', error)
      toast.error(`Erro ao exportar questões: ${error.message}`)
    }
  }, [questions, auditLog])

  // Função para filtrar questões
  const getFilteredQuestions = useCallback(() => {
    return questions.filter(question => {
      const matchesSearch = question.enunciado.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           question.disciplina.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           question.topico.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesFilter = filterBy === 'all' || question.status === filterBy
      
      return matchesSearch && matchesFilter
    })
  }, [questions, searchTerm, filterBy])

  // Função para obter estatísticas das questões
  const getQuestionStats = useCallback(() => {
    const total = questions.length
    const pending = questions.filter(q => q.status === 'pending').length
    const accepted = questions.filter(q => q.status === 'accepted').length
    const rejected = questions.filter(q => q.status === 'rejected').length

    return { total, pending, accepted, rejected }
  }, [questions])

  return {
    questions,
    setQuestions,
    searchTerm,
    setSearchTerm,
    filterBy,
    setFilterBy,
    acceptQuestion,
    rejectQuestion,
    acceptAllQuestions,
    rejectAllQuestions,
    regenerateQuestion,
    exportQuestions,
    getFilteredQuestions,
    getQuestionStats
  }
}
