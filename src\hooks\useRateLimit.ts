import { useState, useCallback, useEffect } from 'react'

interface RateLimitConfig {
  maxRequests: number
  windowMs: number
  storageKey: string
}

interface RateLimitState {
  canMakeRequest: boolean
  remainingRequests: number
  resetTime: number | null
  isBlocked: boolean
}

interface RequestRecord {
  timestamp: number
  userId: string
}

export const useRateLimit = (config: RateLimitConfig, userId?: string) => {
  const [state, setState] = useState<RateLimitState>({
    canMakeRequest: true,
    remainingRequests: config.maxRequests,
    resetTime: null,
    isBlocked: false
  })

  // Função para obter registros do localStorage
  const getStoredRequests = useCallback((): RequestRecord[] => {
    try {
      const stored = localStorage.getItem(config.storageKey)
      if (!stored) return []
      
      const records: RequestRecord[] = JSON.parse(stored)
      const now = Date.now()
      
      // Filtrar apenas registros dentro da janela de tempo
      return records.filter(record => 
        now - record.timestamp < config.windowMs &&
        record.userId === userId
      )
    } catch (error) {
      console.error('Error reading rate limit data:', error)
      return []
    }
  }, [config.storageKey, config.windowMs, userId])

  // Função para salvar registros no localStorage
  const saveRequests = useCallback((records: RequestRecord[]) => {
    try {
      localStorage.setItem(config.storageKey, JSON.stringify(records))
    } catch (error) {
      console.error('Error saving rate limit data:', error)
    }
  }, [config.storageKey])

  // Função para verificar se pode fazer requisição
  const checkRateLimit = useCallback(() => {
    if (!userId) {
      setState(prev => ({ ...prev, canMakeRequest: false, isBlocked: true }))
      return false
    }

    const now = Date.now()
    const validRequests = getStoredRequests()
    
    const canMake = validRequests.length < config.maxRequests
    const remaining = Math.max(0, config.maxRequests - validRequests.length)
    
    // Calcular quando o rate limit será resetado
    let resetTime: number | null = null
    if (validRequests.length > 0) {
      const oldestRequest = Math.min(...validRequests.map(r => r.timestamp))
      resetTime = oldestRequest + config.windowMs
    }

    setState({
      canMakeRequest: canMake,
      remainingRequests: remaining,
      resetTime,
      isBlocked: !canMake
    })

    return canMake
  }, [userId, getStoredRequests, config.maxRequests, config.windowMs])

  // Função para registrar uma nova requisição
  const recordRequest = useCallback(() => {
    if (!userId) return false

    const now = Date.now()
    const validRequests = getStoredRequests()
    
    if (validRequests.length >= config.maxRequests) {
      return false
    }

    const newRecord: RequestRecord = {
      timestamp: now,
      userId
    }

    const updatedRequests = [...validRequests, newRecord]
    saveRequests(updatedRequests)
    
    // Atualizar estado
    checkRateLimit()
    
    return true
  }, [userId, getStoredRequests, saveRequests, config.maxRequests, checkRateLimit])

  // Função para obter tempo restante até reset (em segundos)
  const getTimeUntilReset = useCallback((): number => {
    if (!state.resetTime) return 0
    return Math.max(0, Math.ceil((state.resetTime - Date.now()) / 1000))
  }, [state.resetTime])

  // Função para formatar tempo restante
  const formatTimeUntilReset = useCallback((): string => {
    const seconds = getTimeUntilReset()
    if (seconds <= 0) return '0s'
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    }
    return `${remainingSeconds}s`
  }, [getTimeUntilReset])

  // Verificar rate limit quando o componente monta ou userId muda
  useEffect(() => {
    checkRateLimit()
  }, [checkRateLimit])

  // Atualizar estado periodicamente
  useEffect(() => {
    const interval = setInterval(() => {
      checkRateLimit()
    }, 1000) // Verificar a cada segundo

    return () => clearInterval(interval)
  }, [checkRateLimit])

  return {
    ...state,
    recordRequest,
    checkRateLimit,
    getTimeUntilReset,
    formatTimeUntilReset,
    reset: () => {
      localStorage.removeItem(config.storageKey)
      setState({
        canMakeRequest: true,
        remainingRequests: config.maxRequests,
        resetTime: null,
        isBlocked: false
      })
    }
  }
}

// Hook específico para geração de questões
export const useQuestionGenerationRateLimit = (userId?: string) => {
  return useRateLimit({
    maxRequests: 10, // 10 requisições
    windowMs: 60 * 1000, // por minuto
    storageKey: 'question_generation_rate_limit'
  }, userId)
}
