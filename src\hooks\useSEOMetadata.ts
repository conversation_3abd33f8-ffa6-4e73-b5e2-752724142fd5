import { useState, useCallback, useMemo } from 'react'
import { SEOMetadata, UseSEOMetadataReturn, PublicAssessmentWithDetails } from '../types/public'

/**
 * Hook para gerenciar metadados SEO de páginas públicas
 */
export const useSEOMetadata = (
  initialMetadata?: Partial<SEOMetadata>
): UseSEOMetadataReturn => {
  const [metadata, setMetadata] = useState<SEOMetadata>({
    title: 'Atividade Pronta | Crie avaliações em minutos',
    description: 'A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados.',
    keywords: ['avaliação', 'educação', 'professores', 'questões', 'ensino'],
    canonicalUrl: window.location.href,
    ogImage: 'https://atvpronta.com.br/og-image.png',
    ogType: 'website',
    twitterCard: 'summary_large_image',
    ...initialMetadata
  })

  const updateMetadata = useCallback((updates: Partial<SEOMetadata>) => {
    setMetadata(prev => ({ ...prev, ...updates }))
  }, [])

  const generateSchemaMarkup = useCallback((
    type: string, 
    data: Record<string, any>
  ): Record<string, any> => {
    const baseSchema = {
      '@context': 'https://schema.org',
      '@type': type
    }

    switch (type) {
      case 'EducationalResource':
        return {
          ...baseSchema,
          name: data.title,
          description: data.description,
          educationalLevel: data.serie,
          subject: data.disciplina,
          learningResourceType: 'Assessment',
          author: {
            '@type': 'Organization',
            name: 'Atividade Pronta',
            url: 'https://atvpronta.com.br'
          },
          datePublished: data.datePublished,
          dateModified: data.dateModified,
          inLanguage: 'pt-BR',
          isAccessibleForFree: data.isFree || false,
          url: data.url,
          image: data.image,
          keywords: data.keywords?.join(', '),
          educationalUse: 'assessment',
          interactivityType: 'mixed',
          typicalAgeRange: data.ageRange
        }

      case 'WebPage':
        return {
          ...baseSchema,
          name: data.title,
          description: data.description,
          url: data.url,
          mainEntity: data.mainEntity,
          breadcrumb: data.breadcrumb,
          isPartOf: {
            '@type': 'WebSite',
            name: 'Atividade Pronta',
            url: 'https://atvpronta.com.br'
          }
        }

      case 'BreadcrumbList':
        return {
          ...baseSchema,
          itemListElement: data.items?.map((item: any, index: number) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: item.label,
            item: item.href
          }))
        }

      case 'Organization':
        return {
          ...baseSchema,
          name: 'Atividade Pronta',
          url: 'https://atvpronta.com.br',
          logo: 'https://atvpronta.com.br/logo.png',
          description: 'Plataforma completa para criação de avaliações educacionais',
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: '+55-11-99999-9999',
            contactType: 'customer service',
            availableLanguage: 'Portuguese'
          },
          sameAs: [
            'https://facebook.com/atividadepronta',
            'https://instagram.com/atividadepronta',
            'https://twitter.com/atividadepronta'
          ]
        }

      default:
        return { ...baseSchema, ...data }
    }
  }, [])

  return {
    metadata,
    updateMetadata,
    generateSchemaMarkup
  }
}

/**
 * Hook para gerar metadados SEO específicos para avaliações
 */
export const useAssessmentSEO = (assessment?: PublicAssessmentWithDetails) => {
  const seoData = useMemo(() => {
    if (!assessment) return null

    const title = assessment.seo_title || 
      `${assessment.titulo} - ${assessment.disciplina} ${assessment.serie} | Atividade Pronta`
    
    const description = assessment.seo_description || 
      `Avaliação de ${assessment.disciplina} para ${assessment.serie}. ${assessment.titulo}. Download grátis com gabarito e instruções detalhadas.`
    
    const keywords = assessment.seo_keywords?.length ? assessment.seo_keywords : [
      'avaliação',
      assessment.disciplina.toLowerCase(),
      assessment.serie.toLowerCase(),
      'ensino fundamental',
      'educação',
      'professores',
      'questões',
      'atividade'
    ]

    const canonicalUrl = `https://atvpronta.com.br/avaliacoes/${assessment.slug}`
    
    const ogImage = assessment.featured_image_url || 
      `https://atvpronta.com.br/og-assessment.png?title=${encodeURIComponent(assessment.titulo)}&subject=${encodeURIComponent(assessment.disciplina)}`

    return {
      title,
      description,
      keywords,
      canonicalUrl,
      ogImage,
      ogType: 'article',
      twitterCard: 'summary_large_image'
    }
  }, [assessment])

  const schemaMarkup = useMemo(() => {
    if (!assessment || !seoData) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'EducationalResource',
      name: assessment.titulo,
      description: seoData.description,
      educationalLevel: assessment.serie,
      subject: assessment.disciplina,
      learningResourceType: 'Assessment',
      author: {
        '@type': 'Person',
        name: assessment.profiles?.nome || 'Atividade Pronta',
        affiliation: assessment.profiles?.escola
      },
      publisher: {
        '@type': 'Organization',
        name: 'Atividade Pronta',
        url: 'https://atvpronta.com.br'
      },
      datePublished: assessment.created_at,
      dateModified: assessment.updated_at,
      inLanguage: 'pt-BR',
      isAccessibleForFree: true,
      url: seoData.canonicalUrl,
      image: seoData.ogImage,
      keywords: seoData.keywords.join(', '),
      educationalUse: 'assessment',
      interactivityType: 'mixed',
      typicalAgeRange: getAgeRangeFromSerie(assessment.serie),
      difficulty: assessment.difficulty_level,
      timeRequired: assessment.estimated_duration ? `PT${assessment.estimated_duration}M` : undefined,
      aggregateRating: assessment.view_count > 0 ? {
        '@type': 'AggregateRating',
        ratingValue: Math.min(5, Math.max(3, assessment.view_count / 100)),
        reviewCount: Math.floor(assessment.view_count / 10),
        bestRating: 5,
        worstRating: 1
      } : undefined
    }
  }, [assessment, seoData])

  return {
    seoData,
    schemaMarkup
  }
}

/**
 * Hook para gerar metadados SEO para páginas de categoria
 */
export const useCategorySEO = (categorySlug: string, categoryName?: string) => {
  const seoData = useMemo(() => {
    if (!categorySlug) return null

    const title = `Avaliações de ${categoryName || categorySlug} | Atividade Pronta`
    const description = `Encontre as melhores avaliações de ${categoryName || categorySlug} para todos os níveis de ensino. Download grátis com gabarito e instruções detalhadas.`
    const keywords = [
      'avaliações',
      categoryName?.toLowerCase() || categorySlug,
      'ensino fundamental',
      'ensino médio',
      'educação',
      'professores',
      'atividades'
    ]
    const canonicalUrl = `https://atvpronta.com.br/avaliacoes/categoria/${categorySlug}`

    return {
      title,
      description,
      keywords,
      canonicalUrl,
      ogImage: `https://atvpronta.com.br/og-category.png?category=${encodeURIComponent(categoryName || categorySlug)}`,
      ogType: 'website',
      twitterCard: 'summary_large_image'
    }
  }, [categorySlug, categoryName])

  return { seoData }
}

/**
 * Hook para gerar metadados SEO para página de listagem
 */
export const useAssessmentListSEO = (filters?: { search?: string; category?: string }) => {
  const seoData = useMemo(() => {
    let title = 'Avaliações Educacionais Gratuitas | Atividade Pronta'
    let description = 'Encontre milhares de avaliações educacionais gratuitas para todos os níveis de ensino. Download imediato com gabarito e instruções detalhadas.'
    
    if (filters?.search) {
      title = `Avaliações: ${filters.search} | Atividade Pronta`
      description = `Resultados de busca para "${filters.search}". Encontre avaliações educacionais relacionadas ao seu tema de interesse.`
    }
    
    if (filters?.category) {
      title = `Avaliações de ${filters.category} | Atividade Pronta`
      description = `Avaliações de ${filters.category} para todos os níveis de ensino. Material educacional gratuito e de qualidade.`
    }

    const keywords = [
      'avaliações educacionais',
      'atividades escolares',
      'ensino fundamental',
      'ensino médio',
      'educação',
      'professores',
      'material didático',
      filters?.search,
      filters?.category
    ].filter(Boolean) as string[]

    return {
      title,
      description,
      keywords,
      canonicalUrl: 'https://atvpronta.com.br/avaliacoes',
      ogImage: 'https://atvpronta.com.br/og-assessments.png',
      ogType: 'website',
      twitterCard: 'summary_large_image'
    }
  }, [filters])

  return { seoData }
}

// Utility function to get age range from serie
function getAgeRangeFromSerie(serie: string): string {
  const serieMap: Record<string, string> = {
    '1º Ano': '6-7',
    '2º Ano': '7-8',
    '3º Ano': '8-9',
    '4º Ano': '9-10',
    '5º Ano': '10-11',
    '6º Ano': '11-12',
    '7º Ano': '12-13',
    '8º Ano': '13-14',
    '9º Ano': '14-15',
    '1ª Série': '15-16',
    '2ª Série': '16-17',
    '3ª Série': '17-18'
  }
  
  return serieMap[serie] || '6-18'
}
