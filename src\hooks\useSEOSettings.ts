/**
 * Hook para gerenciamento de configurações SEO
 */

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { toast } from 'react-hot-toast'
import type { 
  SEOGlobalSettings, 
  SEOPageSettings, 
  SEOPageMetrics,
  SEORecommendation,
  SEOKeywordTracking,
  SEOValidationResult
} from '../types/seo'
import { SEO_VALIDATION_RULES } from '../types/seo'

// Hook principal para configurações SEO
export const useSEOSettings = () => {
  const queryClient = useQueryClient()

  // Buscar configurações globais
  const {
    data: globalSettings,
    isLoading: isLoadingGlobal,
    error: globalError
  } = useQuery({
    queryKey: ['seo-global-settings'],
    queryFn: async (): Promise<SEOGlobalSettings> => {
      const { data, error } = await supabase
        .from('seo_global_settings')
        .select('*')
        .single()

      if (error) {
        // Se não existir, criar configuração padrão
        if (error.code === 'PGRST116') {
          const { data: newData, error: createError } = await supabase
            .from('seo_global_settings')
            .insert({})
            .select()
            .single()

          if (createError) throw createError
          return newData
        }
        throw error
      }

      return data
    }
  })

  // Buscar configurações de páginas
  const {
    data: pageSettings,
    isLoading: isLoadingPages,
    error: pagesError
  } = useQuery({
    queryKey: ['seo-page-settings'],
    queryFn: async (): Promise<SEOPageSettings[]> => {
      const { data, error } = await supabase
        .from('seo_page_settings')
        .select('*')
        .order('page_path')

      if (error) throw error
      return data || []
    }
  })

  // Salvar configurações globais
  const saveGlobalSettings = useMutation({
    mutationFn: async (settings: Partial<SEOGlobalSettings>) => {
      const { data, error } = await supabase
        .from('seo_global_settings')
        .update(settings)
        .eq('id', globalSettings?.id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Configurações globais de SEO salvas com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['seo-global-settings'] })
    },
    onError: (error) => {
      console.error('Erro ao salvar configurações globais:', error)
      toast.error('Erro ao salvar configurações globais de SEO')
    }
  })

  // Salvar configurações de página
  const savePageSettings = useMutation({
    mutationFn: async (settings: SEOPageSettings) => {
      const { data, error } = await supabase
        .from('seo_page_settings')
        .upsert(settings, { onConflict: 'page_path' })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Configurações da página salvas com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['seo-page-settings'] })
    },
    onError: (error) => {
      console.error('Erro ao salvar configurações da página:', error)
      toast.error('Erro ao salvar configurações da página')
    }
  })

  // Deletar configuração de página
  const deletePageSettings = useMutation({
    mutationFn: async (pageId: string) => {
      const { error } = await supabase
        .from('seo_page_settings')
        .delete()
        .eq('id', pageId)

      if (error) throw error
    },
    onSuccess: () => {
      toast.success('Configuração da página removida com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['seo-page-settings'] })
    },
    onError: (error) => {
      console.error('Erro ao remover configuração da página:', error)
      toast.error('Erro ao remover configuração da página')
    }
  })

  return {
    // Dados
    globalSettings,
    pageSettings,
    
    // Estados de carregamento
    isLoadingGlobal,
    isLoadingPages,
    isLoading: isLoadingGlobal || isLoadingPages,
    
    // Erros
    globalError,
    pagesError,
    
    // Mutações
    saveGlobalSettings,
    savePageSettings,
    deletePageSettings,
    
    // Estados das mutações
    isSavingGlobal: saveGlobalSettings.isPending,
    isSavingPage: savePageSettings.isPending,
    isDeletingPage: deletePageSettings.isPending
  }
}

// Hook para métricas de performance
export const useSEOMetrics = () => {
  const queryClient = useQueryClient()

  // Buscar métricas de páginas
  const {
    data: metrics,
    isLoading,
    error
  } = useQuery({
    queryKey: ['seo-page-metrics'],
    queryFn: async (): Promise<SEOPageMetrics[]> => {
      const { data, error } = await supabase
        .from('seo_page_metrics')
        .select('*')
        .order('last_checked', { ascending: false })

      if (error) throw error
      return data || []
    }
  })

  // Salvar métricas
  const saveMetrics = useMutation({
    mutationFn: async (metrics: Omit<SEOPageMetrics, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('seo_page_metrics')
        .insert(metrics)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['seo-page-metrics'] })
    }
  })

  // Buscar métricas por URL
  const getMetricsByUrl = (url: string) => {
    return metrics?.filter(metric => metric.page_url === url) || []
  }

  // Calcular métricas médias
  const getAverageMetrics = () => {
    if (!metrics || metrics.length === 0) return null

    const totals = metrics.reduce((acc, metric) => ({
      performance: acc.performance + (metric.performance_score || 0),
      seo: acc.seo + (metric.seo_score || 0),
      accessibility: acc.accessibility + (metric.accessibility_score || 0),
      bestPractices: acc.bestPractices + (metric.best_practices_score || 0)
    }), { performance: 0, seo: 0, accessibility: 0, bestPractices: 0 })

    const count = metrics.length

    return {
      performance: Math.round(totals.performance / count),
      seo: Math.round(totals.seo / count),
      accessibility: Math.round(totals.accessibility / count),
      bestPractices: Math.round(totals.bestPractices / count)
    }
  }

  return {
    metrics,
    isLoading,
    error,
    saveMetrics,
    getMetricsByUrl,
    getAverageMetrics,
    isSaving: saveMetrics.isPending
  }
}

// Hook para recomendações SEO
export const useSEORecommendations = () => {
  const queryClient = useQueryClient()

  // Buscar recomendações
  const {
    data: recommendations,
    isLoading,
    error
  } = useQuery({
    queryKey: ['seo-recommendations'],
    queryFn: async (): Promise<SEORecommendation[]> => {
      const { data, error } = await supabase
        .from('seo_recommendations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    }
  })

  // Marcar recomendação como resolvida
  const resolveRecommendation = useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await supabase
        .from('seo_recommendations')
        .update({ 
          is_resolved: true, 
          resolved_at: new Date().toISOString() 
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Recomendação marcada como resolvida!')
      queryClient.invalidateQueries({ queryKey: ['seo-recommendations'] })
    }
  })

  // Adicionar nova recomendação
  const addRecommendation = useMutation({
    mutationFn: async (recommendation: Omit<SEORecommendation, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('seo_recommendations')
        .insert(recommendation)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['seo-recommendations'] })
    }
  })

  // Filtrar recomendações por prioridade
  const getRecommendationsByPriority = (priority: SEORecommendation['priority']) => {
    return recommendations?.filter(rec => rec.priority === priority && !rec.is_resolved) || []
  }

  // Contar recomendações não resolvidas
  const getUnresolvedCount = () => {
    return recommendations?.filter(rec => !rec.is_resolved).length || 0
  }

  return {
    recommendations,
    isLoading,
    error,
    resolveRecommendation,
    addRecommendation,
    getRecommendationsByPriority,
    getUnresolvedCount,
    isResolving: resolveRecommendation.isPending,
    isAdding: addRecommendation.isPending
  }
}

// Hook para validação de SEO
export const useSEOValidation = () => {
  const [validationResults, setValidationResults] = useState<SEOValidationResult[]>([])

  // Validar título
  const validateTitle = (title: string): SEOValidationResult => {
    const rules = SEO_VALIDATION_RULES.title
    
    if (!title && rules.required) {
      return {
        field: 'title',
        isValid: false,
        message: 'Título é obrigatório',
        severity: 'error'
      }
    }

    if (title.length < rules.minLength) {
      return {
        field: 'title',
        isValid: false,
        message: `Título muito curto. Mínimo: ${rules.minLength} caracteres`,
        severity: 'warning'
      }
    }

    if (title.length > rules.maxLength) {
      return {
        field: 'title',
        isValid: false,
        message: `Título muito longo. Máximo: ${rules.maxLength} caracteres`,
        severity: 'error'
      }
    }

    return {
      field: 'title',
      isValid: true,
      message: 'Título otimizado',
      severity: 'info'
    }
  }

  // Validar descrição
  const validateDescription = (description: string): SEOValidationResult => {
    const rules = SEO_VALIDATION_RULES.description
    
    if (!description && rules.required) {
      return {
        field: 'description',
        isValid: false,
        message: 'Descrição é obrigatória',
        severity: 'error'
      }
    }

    if (description.length < rules.minLength) {
      return {
        field: 'description',
        isValid: false,
        message: `Descrição muito curta. Mínimo: ${rules.minLength} caracteres`,
        severity: 'warning'
      }
    }

    if (description.length > rules.maxLength) {
      return {
        field: 'description',
        isValid: false,
        message: `Descrição muito longa. Máximo: ${rules.maxLength} caracteres`,
        severity: 'error'
      }
    }

    return {
      field: 'description',
      isValid: true,
      message: 'Descrição otimizada',
      severity: 'info'
    }
  }

  // Validar keywords
  const validateKeywords = (keywords: string[]): SEOValidationResult => {
    const rules = SEO_VALIDATION_RULES.keywords
    
    if (keywords.length < rules.minCount) {
      return {
        field: 'keywords',
        isValid: false,
        message: `Poucas palavras-chave. Mínimo: ${rules.minCount}`,
        severity: 'warning'
      }
    }

    if (keywords.length > rules.maxCount) {
      return {
        field: 'keywords',
        isValid: false,
        message: `Muitas palavras-chave. Máximo: ${rules.maxCount}`,
        severity: 'warning'
      }
    }

    const longKeywords = keywords.filter(k => k.length > rules.maxLength)
    if (longKeywords.length > 0) {
      return {
        field: 'keywords',
        isValid: false,
        message: `Palavras-chave muito longas: ${longKeywords.join(', ')}`,
        severity: 'warning'
      }
    }

    return {
      field: 'keywords',
      isValid: true,
      message: 'Palavras-chave otimizadas',
      severity: 'info'
    }
  }

  // Validar configuração completa
  const validatePageSettings = (settings: Partial<SEOPageSettings>) => {
    const results: SEOValidationResult[] = []

    if (settings.title) {
      results.push(validateTitle(settings.title))
    }

    if (settings.description) {
      results.push(validateDescription(settings.description))
    }

    if (settings.keywords) {
      results.push(validateKeywords(settings.keywords))
    }

    setValidationResults(results)
    return results
  }

  // Verificar se há erros
  const hasErrors = validationResults.some(result => !result.isValid && result.severity === 'error')
  const hasWarnings = validationResults.some(result => !result.isValid && result.severity === 'warning')

  return {
    validationResults,
    validateTitle,
    validateDescription,
    validateKeywords,
    validatePageSettings,
    hasErrors,
    hasWarnings
  }
}
