import { useState, useEffect, useRef } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

export interface SchoolName {
  id: string
  name: string
  created_at: string
}

// Cache global para evitar recarregamentos desnecessários
let globalSchoolNamesCache: SchoolName[] = []
let globalCacheUserId: string | null = null
let globalCacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutos

// Função para limpar o cache (útil para debugging ou logout)
export const clearSchoolNamesCache = () => {
  globalSchoolNamesCache = []
  globalCacheUserId = null
  globalCacheTimestamp = 0
}

export const useSchoolNames = () => {
  const { user } = useAuth()
  const [schoolNames, setSchoolNames] = useState<SchoolName[]>([])
  const [isLoading, setIsLoading] = useState(false) // Mudança: iniciar como false
  const [isCreating, setIsCreating] = useState(false)
  const hasLoadedRef = useRef(false)

  // Carregar nomes de escola do usuário
  const loadSchoolNames = async (forceRefresh = false) => {
    if (!user) {
      setIsLoading(false)
      return
    }

    // Verificar cache se não for refresh forçado
    const now = Date.now()
    const isCacheValid = globalCacheUserId === user.id &&
                        (now - globalCacheTimestamp) < CACHE_DURATION &&
                        globalSchoolNamesCache.length >= 0

    if (!forceRefresh && isCacheValid) {
      setSchoolNames(globalSchoolNamesCache)
      setIsLoading(false)
      hasLoadedRef.current = true
      return
    }

    // Só mostrar loading se não temos dados em cache
    if (!isCacheValid) {
      setIsLoading(true)
    }

    try {
      const { data, error } = await supabase
        .from('user_school_names')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Erro ao carregar nomes de escola:', error)
        return
      }

      const schoolNamesData = data || []

      // Atualizar cache global
      globalSchoolNamesCache = schoolNamesData
      globalCacheUserId = user.id
      globalCacheTimestamp = now

      setSchoolNames(schoolNamesData)
      hasLoadedRef.current = true
    } catch (error) {
      console.error('Erro ao carregar nomes de escola:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Criar novo nome de escola
  const createSchoolName = async (name: string): Promise<boolean> => {
    if (!user || !name.trim()) {
      return false
    }

    // Verificar se já existe
    const existingName = schoolNames.find(
      school => school.name.toLowerCase() === name.toLowerCase()
    )
    
    if (existingName) {
      return true // Já existe, não precisa criar
    }

    setIsCreating(true)

    try {
      const { data, error } = await supabase
        .from('user_school_names')
        .insert([
          {
            user_id: user.id,
            name: name.trim()
          }
        ])
        .select()
        .single()

      if (error) {
        console.error('Erro ao criar nome de escola:', error)
        toast.error('Erro ao salvar nome da escola')
        return false
      }

      // Adicionar à lista local e ao cache global
      const newSchoolNames = [data, ...schoolNames]
      setSchoolNames(newSchoolNames)

      // Atualizar cache global
      if (globalCacheUserId === user.id) {
        globalSchoolNamesCache = newSchoolNames
        globalCacheTimestamp = Date.now()
      }

      return true

    } catch (error) {
      console.error('Erro ao criar nome de escola:', error)
      toast.error('Erro ao salvar nome da escola')
      return false
    } finally {
      setIsCreating(false)
    }
  }

  // Deletar nome de escola (opcional, para futuras funcionalidades)
  const deleteSchoolName = async (id: string): Promise<boolean> => {
    if (!user) return false

    try {
      const { error } = await supabase
        .from('user_school_names')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        console.error('Erro ao deletar nome de escola:', error)
        return false
      }

      // Remover da lista local e do cache global
      const filteredSchoolNames = schoolNames.filter(school => school.id !== id)
      setSchoolNames(filteredSchoolNames)

      // Atualizar cache global
      if (globalCacheUserId === user.id) {
        globalSchoolNamesCache = filteredSchoolNames
        globalCacheTimestamp = Date.now()
      }

      return true

    } catch (error) {
      console.error('Erro ao deletar nome de escola:', error)
      return false
    }
  }

  useEffect(() => {
    if (user && !hasLoadedRef.current) {
      loadSchoolNames()
    } else if (!user) {
      setSchoolNames([])
      setIsLoading(false)
      hasLoadedRef.current = false
      // Limpar cache quando usuário faz logout
      clearSchoolNamesCache()
    }
  }, [user])

  return {
    schoolNames,
    isLoading,
    isCreating,
    createSchoolName,
    deleteSchoolName,
    refreshSchoolNames: () => loadSchoolNames(true), // Force refresh
    hasLoaded: hasLoadedRef.current
  }
}
