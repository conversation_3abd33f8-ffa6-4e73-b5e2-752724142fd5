import { useState, useEffect, useCallback } from 'react'

interface ServiceWorkerState {
  isSupported: boolean
  isRegistered: boolean
  isInstalling: boolean
  isWaiting: boolean
  isUpdateAvailable: boolean
  registration: ServiceWorkerRegistration | null
  error: string | null
}

interface CacheStats {
  [cacheName: string]: {
    count: number
    size: number
  }
}

/**
 * Hook para gerenciar Service Worker e cache offline
 */
export const useServiceWorker = () => {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: 'serviceWorker' in navigator,
    isRegistered: false,
    isInstalling: false,
    isWaiting: false,
    isUpdateAvailable: false,
    registration: null,
    error: null,
  })
  
  const [cacheStats, setCacheStats] = useState<CacheStats>({})
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  
  /**
   * Registrar Service Worker
   */
  const register = useCallback(async () => {
    if (!state.isSupported) {
      setState(prev => ({ 
        ...prev, 
        error: 'Service Worker não é suportado neste navegador' 
      }))
      return
    }
    
    try {
      setState(prev => ({ ...prev, isInstalling: true, error: null }))
      
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      })
      
      console.log('Service Worker registrado:', registration)
      
      setState(prev => ({
        ...prev,
        isRegistered: true,
        isInstalling: false,
        registration,
      }))
      
      // Configurar listeners
      setupServiceWorkerListeners(registration)
      
    } catch (error) {
      console.error('Erro ao registrar Service Worker:', error)
      setState(prev => ({
        ...prev,
        isInstalling: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      }))
    }
  }, [state.isSupported])
  
  /**
   * Configurar listeners do Service Worker
   */
  const setupServiceWorkerListeners = useCallback((registration: ServiceWorkerRegistration) => {
    // Listener para instalação
    if (registration.installing) {
      registration.installing.addEventListener('statechange', (event) => {
        const sw = event.target as ServiceWorker
        if (sw.state === 'installed') {
          setState(prev => ({ ...prev, isWaiting: true }))
        }
      })
    }
    
    // Listener para atualizações
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            setState(prev => ({ ...prev, isUpdateAvailable: true }))
          }
        })
      }
    })
    
    // Listener para mensagens do Service Worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, payload } = event.data
      
      switch (type) {
        case 'CACHE_UPDATED':
          updateCacheStats()
          break
        case 'OFFLINE_READY':
          console.log('Aplicação pronta para uso offline')
          break
      }
    })
  }, [])
  
  /**
   * Ativar Service Worker em espera
   */
  const skipWaiting = useCallback(() => {
    if (state.registration?.waiting) {
      state.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      setState(prev => ({ ...prev, isWaiting: false }))
    }
  }, [state.registration])
  
  /**
   * Atualizar Service Worker
   */
  const update = useCallback(async () => {
    if (state.registration) {
      try {
        await state.registration.update()
      } catch (error) {
        console.error('Erro ao atualizar Service Worker:', error)
      }
    }
  }, [state.registration])
  
  /**
   * Desregistrar Service Worker
   */
  const unregister = useCallback(async () => {
    if (state.registration) {
      try {
        await state.registration.unregister()
        setState(prev => ({
          ...prev,
          isRegistered: false,
          registration: null,
        }))
      } catch (error) {
        console.error('Erro ao desregistrar Service Worker:', error)
      }
    }
  }, [state.registration])
  
  /**
   * Cache URLs específicas
   */
  const cacheUrls = useCallback((urls: string[]) => {
    if (state.registration?.active) {
      state.registration.active.postMessage({
        type: 'CACHE_URLS',
        payload: { urls }
      })
    }
  }, [state.registration])
  
  /**
   * Limpar cache
   */
  const clearCache = useCallback((cacheName?: string) => {
    if (state.registration?.active) {
      state.registration.active.postMessage({
        type: 'CLEAR_CACHE',
        payload: { cacheName }
      })
    }
  }, [state.registration])
  
  /**
   * Obter estatísticas do cache
   */
  const updateCacheStats = useCallback(async () => {
    if (state.registration?.active) {
      const messageChannel = new MessageChannel()
      
      messageChannel.port1.onmessage = (event) => {
        setCacheStats(event.data)
      }
      
      state.registration.active.postMessage(
        { type: 'GET_CACHE_STATS' },
        [messageChannel.port2]
      )
    }
  }, [state.registration])
  
  /**
   * Verificar se está offline
   */
  const checkOnlineStatus = useCallback(() => {
    setIsOnline(navigator.onLine)
  }, [])
  
  /**
   * Preload recursos críticos
   */
  const preloadCriticalResources = useCallback((urls: string[]) => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      cacheUrls(urls)
    } else {
      // Fallback: preload usando link tags
      urls.forEach(url => {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = url
        document.head.appendChild(link)
      })
    }
  }, [cacheUrls])
  
  /**
   * Verificar se recurso está em cache
   */
  const isResourceCached = useCallback(async (url: string): Promise<boolean> => {
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const response = await cache.match(url)
        if (response) {
          return true
        }
      }
    }
    
    return false
  }, [])
  
  /**
   * Obter tamanho total do cache
   */
  const getCacheSize = useCallback(async (): Promise<number> => {
    if ('caches' in window && 'storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        return estimate.usage || 0
      } catch (error) {
        console.warn('Não foi possível obter tamanho do cache:', error)
      }
    }
    
    return 0
  }, [])
  
  // Efeitos
  useEffect(() => {
    // Registrar automaticamente se suportado
    if (state.isSupported && !state.isRegistered) {
      register()
    }
  }, [state.isSupported, state.isRegistered, register])
  
  useEffect(() => {
    // Listeners para status online/offline
    window.addEventListener('online', checkOnlineStatus)
    window.addEventListener('offline', checkOnlineStatus)
    
    return () => {
      window.removeEventListener('online', checkOnlineStatus)
      window.removeEventListener('offline', checkOnlineStatus)
    }
  }, [checkOnlineStatus])
  
  useEffect(() => {
    // Atualizar estatísticas do cache periodicamente
    if (state.isRegistered) {
      updateCacheStats()
      const interval = setInterval(updateCacheStats, 60000) // A cada minuto
      
      return () => clearInterval(interval)
    }
  }, [state.isRegistered, updateCacheStats])
  
  useEffect(() => {
    // Verificar atualizações periodicamente
    if (state.registration) {
      const interval = setInterval(() => {
        update()
      }, 60000) // A cada minuto
      
      return () => clearInterval(interval)
    }
  }, [state.registration, update])
  
  return {
    // Estado
    ...state,
    isOnline,
    cacheStats,
    
    // Ações
    register,
    unregister,
    update,
    skipWaiting,
    
    // Cache
    cacheUrls,
    clearCache,
    updateCacheStats,
    preloadCriticalResources,
    isResourceCached,
    getCacheSize,
    
    // Utilitários
    checkOnlineStatus,
  }
}

/**
 * Hook para detectar quando a aplicação está offline
 */
export const useOfflineDetection = () => {
  const [isOffline, setIsOffline] = useState(!navigator.onLine)
  const [wasOffline, setWasOffline] = useState(false)
  
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false)
      if (wasOffline) {
        // Aplicação voltou online
        console.log('Aplicação voltou online')
        setWasOffline(false)
      }
    }
    
    const handleOffline = () => {
      setIsOffline(true)
      setWasOffline(true)
      console.log('Aplicação está offline')
    }
    
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [wasOffline])
  
  return {
    isOffline,
    wasOffline,
    isOnline: !isOffline,
  }
}

/**
 * Hook para notificações de atualização
 */
export const useUpdateNotification = () => {
  const { isUpdateAvailable, skipWaiting } = useServiceWorker()
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false)
  
  useEffect(() => {
    if (isUpdateAvailable) {
      setShowUpdatePrompt(true)
    }
  }, [isUpdateAvailable])
  
  const acceptUpdate = useCallback(() => {
    skipWaiting()
    setShowUpdatePrompt(false)
    // Recarregar página após atualização
    window.location.reload()
  }, [skipWaiting])
  
  const dismissUpdate = useCallback(() => {
    setShowUpdatePrompt(false)
  }, [])
  
  return {
    showUpdatePrompt,
    acceptUpdate,
    dismissUpdate,
  }
}
