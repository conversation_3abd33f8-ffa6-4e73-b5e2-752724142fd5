import { useState, useCallback } from 'react'
import { SitemapGenerator } from '../lib/seo/sitemapGenerator'

/**
 * Hook para gerenciar sitemap dinâmico
 */
export const useSitemap = () => {
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generator = new SitemapGenerator({
    baseUrl: 'https://atvpronta.com.br'
  })

  /**
   * Gerar sitemap completo
   */
  const generateSitemap = useCallback(async (): Promise<string | null> => {
    setIsGenerating(true)
    setError(null)

    try {
      const sitemap = await generator.generateSitemap()
      return sitemap
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao gerar sitemap'
      setError(errorMessage)
      return null
    } finally {
      setIsGenerating(false)
    }
  }, [generator])

  /**
   * Atualizar cache do sitemap
   */
  const updateSitemapCache = useCallback(async (): Promise<boolean> => {
    setIsGenerating(true)
    setError(null)

    try {
      await generator.updateSitemapCache()
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar cache do sitemap'
      setError(errorMessage)
      return false
    } finally {
      setIsGenerating(false)
    }
  }, [generator])

  /**
   * Obter sitemap do cache
   */
  const getSitemapFromCache = useCallback(async (): Promise<string | null> => {
    setIsGenerating(true)
    setError(null)

    try {
      const sitemap = await generator.getSitemapFromCache()
      return sitemap
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao obter sitemap do cache'
      setError(errorMessage)
      return null
    } finally {
      setIsGenerating(false)
    }
  }, [generator])

  return {
    generateSitemap,
    updateSitemapCache,
    getSitemapFromCache,
    isGenerating,
    error
  }
}
