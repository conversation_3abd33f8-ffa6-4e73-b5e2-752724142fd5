import { useQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'

type Template = Database['public']['Tables']['templates']['Row']

export const useTemplates = () => {
  const {
    data: templates = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['templates'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as Template[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })

  return {
    templates,
    isLoading,
    error
  }
}