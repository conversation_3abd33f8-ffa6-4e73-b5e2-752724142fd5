import { useState, useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface TrialValidationResult {
  allowed: boolean
  reason: string
  message: string
  cooldownEnds?: string
}

interface DeviceFingerprint {
  userAgent: string
  language: string
  platform: string
  screenResolution: string
  timezone: string
  cookieEnabled: boolean
  doNotTrack: string | null
}

export const useTrialValidation = () => {
  const { user } = useAuth()
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<TrialValidationResult | null>(null)

  // Generate device fingerprint for abuse prevention
  const generateDeviceFingerprint = useCallback((): string => {
    const fingerprint: DeviceFingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack
    }

    // Create a hash of the fingerprint data
    const fingerprintString = JSON.stringify(fingerprint)
    let hash = 0
    for (let i = 0; i < fingerprintString.length; i++) {
      const char = fingerprintString.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    return `fp_${Math.abs(hash).toString(36)}`
  }, [])

  // Get user's IP address (simplified - in production use a proper service)
  const getUserIP = useCallback(async (): Promise<string | null> => {
    try {
      // In production, you might want to use a service like ipify or get IP from server
      // For now, we'll return null and handle IP detection server-side
      return null
    } catch (error) {
      console.warn('Could not get user IP:', error)
      return null
    }
  }, [])

  // Validate if user can start a trial
  const validateTrialEligibility = useCallback(async (
    planType: 'premium' | 'escolar',
    paymentMethodFingerprint?: string
  ): Promise<TrialValidationResult> => {
    if (!user) {
      return {
        allowed: false,
        reason: 'not_authenticated',
        message: 'Você precisa estar logado para iniciar um teste gratuito.'
      }
    }

    setIsValidating(true)
    
    try {
      const deviceFingerprint = generateDeviceFingerprint()
      const ipAddress = await getUserIP()

      // Call the database function to check eligibility
      const { data, error } = await supabase.rpc('can_user_start_trial', {
        p_user_id: user.id,
        p_email: user.email,
        p_plan_type: planType,
        p_ip_address: ipAddress,
        p_payment_method_fingerprint: paymentMethodFingerprint || null,
        p_device_fingerprint: deviceFingerprint
      })

      if (error) {
        console.error('Error validating trial eligibility:', error)
        return {
          allowed: false,
          reason: 'validation_error',
          message: 'Erro ao validar elegibilidade para teste. Tente novamente.'
        }
      }

      const result = data as TrialValidationResult
      setValidationResult(result)
      return result

    } catch (error) {
      console.error('Error in trial validation:', error)
      return {
        allowed: false,
        reason: 'unexpected_error',
        message: 'Erro inesperado. Tente novamente mais tarde.'
      }
    } finally {
      setIsValidating(false)
    }
  }, [user, generateDeviceFingerprint, getUserIP])

  // Record trial start (called after successful subscription creation)
  const recordTrialStart = useCallback(async (
    planType: 'premium' | 'escolar',
    trialStart: Date,
    trialEnd: Date,
    stripeCustomerId: string,
    paymentMethodFingerprint?: string
  ): Promise<boolean> => {
    if (!user) return false

    try {
      const deviceFingerprint = generateDeviceFingerprint()
      const ipAddress = await getUserIP()

      const { data, error } = await supabase.rpc('record_trial_start', {
        p_user_id: user.id,
        p_email: user.email,
        p_plan_type: planType,
        p_trial_start: trialStart.toISOString(),
        p_trial_end: trialEnd.toISOString(),
        p_stripe_customer_id: stripeCustomerId,
        p_ip_address: ipAddress,
        p_user_agent: navigator.userAgent,
        p_payment_method_fingerprint: paymentMethodFingerprint || null,
        p_device_fingerprint: deviceFingerprint
      })

      if (error) {
        console.error('Error recording trial start:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in recordTrialStart:', error)
      return false
    }
  }, [user, generateDeviceFingerprint, getUserIP])

  // Get user's trial history
  const getTrialHistory = useCallback(async () => {
    if (!user) return []

    try {
      const { data, error } = await supabase
        .from('trial_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching trial history:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getTrialHistory:', error)
      return []
    }
  }, [user])

  // Check if user has any previous trials
  const hasPreviousTrials = useCallback(async (): Promise<boolean> => {
    const history = await getTrialHistory()
    return history.length > 0
  }, [getTrialHistory])

  // Format cooldown end date for display
  const formatCooldownDate = useCallback((cooldownEnds?: string): string => {
    if (!cooldownEnds) return ''
    
    const date = new Date(cooldownEnds)
    return date.toLocaleDateString('pt-BR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }, [])

  // Get user-friendly error message
  const getErrorMessage = useCallback((result: TrialValidationResult): string => {
    if (result.allowed) return ''

    switch (result.reason) {
      case 'email_limit_exceeded':
        return `Este email já foi usado para um teste gratuito. ${result.cooldownEnds ? `Você poderá tentar novamente em ${formatCooldownDate(result.cooldownEnds)}.` : ''}`
      
      case 'user_limit_exceeded':
        return `Você já utilizou seu teste gratuito. ${result.cooldownEnds ? `Você poderá tentar novamente em ${formatCooldownDate(result.cooldownEnds)}.` : ''}`
      
      case 'ip_limit_exceeded':
        return 'Muitos testes foram iniciados deste local. Tente novamente mais tarde ou use uma conexão diferente.'
      
      case 'payment_method_limit_exceeded':
        return 'Este método de pagamento já foi usado para um teste gratuito. Tente com um cartão diferente.'
      
      case 'device_limit_exceeded':
        return 'Este dispositivo já foi usado para testes gratuitos. Limite atingido.'
      
      default:
        return result.message || 'Não foi possível iniciar o teste gratuito.'
    }
  }, [formatCooldownDate])

  return {
    isValidating,
    validationResult,
    validateTrialEligibility,
    recordTrialStart,
    getTrialHistory,
    hasPreviousTrials,
    getErrorMessage,
    formatCooldownDate
  }
}
