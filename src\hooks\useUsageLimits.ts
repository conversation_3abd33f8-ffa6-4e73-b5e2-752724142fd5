import { useState, useEffect, useCallback } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useSubscription } from '../contexts/SubscriptionContext'
import { toast } from 'react-hot-toast'
import {
  USAGE_LIMITS,
  NEAR_LIMIT_THRESHOLDS,
  USAGE_ACTION_TYPES,
  RESOURCE_TYPES,
  PLAN_TYPES,
  LIMIT_ERROR_MESSAGES,
  QUERY_KEYS,
  CACHE_CONFIG,
  getUsageLimits,
  isPaidPlan,
  isNearLimit,
  getUsagePercentage,
  getRemainingUsage,
  canPerformAction
} from '../constants/usageLimits'

export interface UsageData {
  assessmentsCreated: number
  pdfDownloads: number
  questionsCreated: number
  currentPeriodStart: Date
  currentPeriodEnd: Date
}

export interface UsageLimitStatus {
  canCreateAssessment: boolean
  canDownloadPDF: boolean
  assessmentsRemaining: number
  pdfDownloadsRemaining: number
  isNearLimit: boolean
  shouldShowUpgrade: boolean
}

export const useUsageLimits = () => {
  const { user } = useAuth()
  const { getUsageLimits, isPremium, isEscolar } = useSubscription()
  const queryClient = useQueryClient()
  const [isCheckingLimits, setIsCheckingLimits] = useState(false)

  const limits = getUsageLimits()
  const isPaidUser = isPremium || isEscolar

  // Get current month boundaries
  const getCurrentPeriod = useCallback(() => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), 1)
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
    return { start, end }
  }, [])

  // Fetch current usage data
  const {
    data: usageData,
    isLoading,
    error,
    refetch: refetchUsage
  } = useQuery<UsageData>({
    queryKey: QUERY_KEYS.USAGE_LIMITS(user?.id || '', getCurrentPeriod().start.toISOString()),
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated')

      const { start, end } = getCurrentPeriod()

      const { data: stats, error } = await supabase
        .from('usage_stats')
        .select('action_type, created_at, metadata')
        .eq('user_id', user.id)
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString())

      if (error) throw error

      // Use centralized action types and optimize with single reduce
      const counts = stats?.reduce((acc, stat) => {
        acc[stat.action_type] = (acc[stat.action_type] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      const assessmentsCreated = counts[USAGE_ACTION_TYPES.ASSESSMENT_CREATED] || 0
      const pdfDownloads = counts[USAGE_ACTION_TYPES.PDF_DOWNLOADED] || 0
      const questionsCreated = counts[USAGE_ACTION_TYPES.QUESTION_CREATED] || 0

      return {
        assessmentsCreated,
        pdfDownloads,
        questionsCreated,
        currentPeriodStart: start,
        currentPeriodEnd: end
      }
    },
    enabled: !!user,
    staleTime: CACHE_CONFIG.USAGE_LIMITS_STALE_TIME,
    refetchOnWindowFocus: CACHE_CONFIG.REFETCH_ON_WINDOW_FOCUS,
    retry: CACHE_CONFIG.RETRY_ATTEMPTS
  })

  // Calculate limit status using centralized functions
  const getLimitStatus = useCallback((): UsageLimitStatus => {
    if (isPaidUser) {
      return {
        canCreateAssessment: true,
        canDownloadPDF: true,
        assessmentsRemaining: -1, // unlimited
        pdfDownloadsRemaining: -1, // unlimited
        isNearLimit: false,
        shouldShowUpgrade: false
      }
    }

    if (!usageData) {
      return {
        canCreateAssessment: false,
        canDownloadPDF: false,
        assessmentsRemaining: 0,
        pdfDownloadsRemaining: 0,
        isNearLimit: false,
        shouldShowUpgrade: true
      }
    }

    const currentLimits = getUsageLimits(PLAN_TYPES.FREE)
    const assessmentsRemaining = getRemainingUsage(usageData.assessmentsCreated, currentLimits.ASSESSMENTS_PER_MONTH)
    const pdfDownloadsRemaining = getRemainingUsage(usageData.pdfDownloads, currentLimits.PDF_DOWNLOADS_PER_MONTH)

    const canCreateAssessment = canPerformAction(usageData.assessmentsCreated, currentLimits.ASSESSMENTS_PER_MONTH)
    const canDownloadPDF = canPerformAction(usageData.pdfDownloads, currentLimits.PDF_DOWNLOADS_PER_MONTH)

    const isNearAssessmentLimit = isNearLimit(usageData.assessmentsCreated, currentLimits.ASSESSMENTS_PER_MONTH, 'ASSESSMENTS')
    const isNearPdfLimit = isNearLimit(usageData.pdfDownloads, currentLimits.PDF_DOWNLOADS_PER_MONTH, 'PDF_DOWNLOADS')
    const isNearLimitOverall = isNearAssessmentLimit || isNearPdfLimit

    const shouldShowUpgrade = !canCreateAssessment || !canDownloadPDF || isNearLimitOverall

    return {
      canCreateAssessment,
      canDownloadPDF,
      assessmentsRemaining,
      pdfDownloadsRemaining,
      isNearLimit: isNearLimitOverall,
      shouldShowUpgrade
    }
  }, [usageData, isPaidUser])

  // Check if user can perform action
  const checkCanPerformAction = useCallback(async (
    action: 'create_assessment' | 'download_pdf'
  ): Promise<boolean> => {
    if (isPaidUser) return true

    setIsCheckingLimits(true)
    
    try {
      // Refresh usage data to get latest counts
      await refetchUsage()
      
      const status = getLimitStatus()
      
      if (action === 'create_assessment' && !status.canCreateAssessment) {
        const currentLimits = getUsageLimits(PLAN_TYPES.FREE)
        toast.error(
          LIMIT_ERROR_MESSAGES.ASSESSMENT_LIMIT_REACHED(currentLimits.ASSESSMENTS_PER_MONTH),
          { duration: 5000 }
        )
        return false
      }

      if (action === 'download_pdf' && !status.canDownloadPDF) {
        const currentLimits = getUsageLimits(PLAN_TYPES.FREE)
        toast.error(
          LIMIT_ERROR_MESSAGES.PDF_LIMIT_REACHED(currentLimits.PDF_DOWNLOADS_PER_MONTH),
          { duration: 5000 }
        )
        return false
      }

      return true
    } finally {
      setIsCheckingLimits(false)
    }
  }, [isPaidUser, refetchUsage, getLimitStatus, limits])

  // Track usage action with centralized types
  const trackUsage = useCallback(async (
    actionType: keyof typeof USAGE_ACTION_TYPES,
    metadata: Record<string, any> = {}
  ) => {
    if (!user) return

    try {
      // Determine resource type based on action
      let resourceType: string
      switch (actionType) {
        case 'assessment_created':
          resourceType = RESOURCE_TYPES.ASSESSMENT
          break
        case 'pdf_downloaded':
          resourceType = RESOURCE_TYPES.PDF
          break
        case 'question_created':
          resourceType = RESOURCE_TYPES.QUESTION
          break
        default:
          resourceType = RESOURCE_TYPES.ASSESSMENT
      }

      await supabase.from('usage_stats').insert({
        user_id: user.id,
        action_type: USAGE_ACTION_TYPES[actionType],
        resource_type: resourceType,
        metadata
      })

      // Invalidate and refetch usage data with proper query key
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.USAGE_LIMITS(user.id, '')
      })
    } catch (error) {
      console.error('Error tracking usage:', error)
    }
  }, [user, queryClient])

  // Get usage percentage for progress bars using centralized function
  const getUsagePercentageForType = useCallback((type: 'assessments' | 'pdfs') => {
    if (isPaidUser || !usageData) return 0

    const currentLimits = getUsageLimits(PLAN_TYPES.FREE)

    if (type === 'assessments') {
      return getUsagePercentage(usageData.assessmentsCreated, currentLimits.ASSESSMENTS_PER_MONTH)
    } else {
      return getUsagePercentage(usageData.pdfDownloads, currentLimits.PDF_DOWNLOADS_PER_MONTH)
    }
  }, [usageData, isPaidUser])

  return {
    usageData,
    limitStatus: getLimitStatus(),
    isLoading: isLoading || isCheckingLimits,
    error,
    checkCanPerformAction,
    trackUsage,
    refetchUsage,
    getUsagePercentage: getUsagePercentageForType,
    isPaidUser
  }
}
