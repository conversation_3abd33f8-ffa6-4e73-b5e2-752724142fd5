import { useQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface UserAnalyticsData {
  questionsCreated: number
  assessmentsGenerated: number
  pdfDownloads: number
  mostUsedTopics: Array<{ topic: string; count: number }>
  weeklyActivity: Array<{ date: string; count: number }>
}

export const useUserAnalytics = () => {
  const { user } = useAuth()

  const {
    data: analytics,
    isLoading,
    error
  } = useQuery<UserAnalyticsData | null>({
    queryKey: ['userAnalytics', user?.id],
    queryFn: async () => {
      if (!user) return null

      try {
        const { data: stats, error } = await supabase
          .from('usage_stats')
          .select('action_type, created_at, metadata')
          .eq('user_id', user.id)
          .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

        if (error) throw error

        const questionsCreated = stats?.filter(s => s.action_type === 'question_created').length || 0
        const assessmentsGenerated = stats?.filter(s => s.action_type === 'assessment_created').length || 0
        const pdfDownloads = stats?.filter(s => s.action_type === 'pdf_downloaded').length || 0

        const topicCounts: Record<string, number> = {}
        stats?.forEach(stat => {
          if (stat.metadata?.topico) {
            topicCounts[stat.metadata.topico] = (topicCounts[stat.metadata.topico] || 0) + 1
          }
        })

        const mostUsedTopics = Object.entries(topicCounts)
          .map(([topic, count]) => ({ topic, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5)

        const weeklyActivity = Array.from({ length: 7 }, (_, i) => {
          const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
          const dayStats = stats?.filter(s =>
            new Date(s.created_at).toDateString() === date.toDateString()
          ).length || 0

          return {
            date: date.toLocaleDateString('pt-BR'),
            count: dayStats
          }
        }).reverse()

        return {
          questionsCreated,
          assessmentsGenerated,
          pdfDownloads,
          mostUsedTopics,
          weeklyActivity
        }
      } catch (err) {
        console.error('Error fetching user analytics:', err)
        return null
      }
    },
    enabled: !!user, // Only run query if user is available
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })

  return {
    analytics,
    isLoading,
    error
  }
}