@tailwind base;
@tailwind components;
@tailwind utilities;

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark mode styles */
.dark {
  color-scheme: dark;
}

/* Font size utilities */
html[data-font-size="small"] {
  font-size: 14px;
}

html[data-font-size="medium"] {
  font-size: 16px;
}

html[data-font-size="large"] {
  font-size: 18px;
}

/* Compact mode utilities */
.compact-mode .p-4 {
  padding: 0.75rem;
}

.compact-mode .p-6 {
  padding: 1rem;
}

.compact-mode .space-y-4 > * + * {
  margin-top: 0.75rem;
}

.compact-mode .space-y-6 > * + * {
  margin-top: 1rem;
}

.compact-mode .gap-4 {
  gap: 0.75rem;
}

.compact-mode .gap-6 {
  gap: 1rem;
}