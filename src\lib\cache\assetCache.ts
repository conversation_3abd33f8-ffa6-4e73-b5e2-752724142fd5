/**
 * Sistema avançado de cache para imagens, PDFs e outros assets
 * Inclui lazy loading, preloading inteligente e otimizações de performance
 */

// Tipos de assets suportados
export type AssetType = 'image' | 'pdf' | 'video' | 'audio' | 'document' | 'font'

// Configurações de cache por tipo de asset
export const ASSET_CACHE_CONFIG = {
  image: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 dias
    compressionQuality: 0.8,
    formats: ['webp', 'avif', 'jpg', 'png'],
    sizes: [320, 640, 1024, 1920], // Responsive sizes
  },
  pdf: {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 dias
    previewPages: 3,
  },
  video: {
    maxSize: 500 * 1024 * 1024, // 500MB
    maxAge: 3 * 24 * 60 * 60 * 1000, // 3 dias
    qualities: ['360p', '720p', '1080p'],
  },
  audio: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 dias
    bitrates: [128, 256, 320], // kbps
  },
  document: {
    maxSize: 20 * 1024 * 1024, // 20MB
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 dias
  },
  font: {
    maxSize: 5 * 1024 * 1024, // 5MB
    maxAge: 90 * 24 * 60 * 60 * 1000, // 90 dias
    formats: ['woff2', 'woff', 'ttf'],
  }
} as const

// Interface para metadados de cache
interface CachedAsset {
  url: string
  type: AssetType
  data: Blob | string
  metadata: {
    size: number
    mimeType: string
    lastModified: number
    etag?: string
    expires?: number
    quality?: number
    dimensions?: { width: number; height: number }
  }
  timestamp: number
  accessCount: number
  lastAccessed: number
}

// Cache em memória para assets
class AssetCacheManager {
  private cache = new Map<string, CachedAsset>()
  private loadingPromises = new Map<string, Promise<CachedAsset>>()
  private observers = new Map<string, IntersectionObserver>()
  
  /**
   * Obter asset do cache ou carregar se necessário
   */
  async getAsset(url: string, type: AssetType, options?: {
    quality?: number
    size?: number
    priority?: 'high' | 'low'
  }): Promise<CachedAsset> {
    const cacheKey = this.generateCacheKey(url, options)
    
    // Verificar cache em memória
    const cached = this.cache.get(cacheKey)
    if (cached && !this.isExpired(cached)) {
      cached.accessCount++
      cached.lastAccessed = Date.now()
      return cached
    }
    
    // Verificar se já está carregando
    const loadingPromise = this.loadingPromises.get(cacheKey)
    if (loadingPromise) {
      return loadingPromise
    }
    
    // Carregar asset
    const promise = this.loadAsset(url, type, options)
    this.loadingPromises.set(cacheKey, promise)
    
    try {
      const asset = await promise
      this.cache.set(cacheKey, asset)
      return asset
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }
  
  /**
   * Carregar asset da rede
   */
  private async loadAsset(
    url: string, 
    type: AssetType, 
    options?: { quality?: number; size?: number; priority?: 'high' | 'low' }
  ): Promise<CachedAsset> {
    const config = ASSET_CACHE_CONFIG[type]
    
    // Configurar fetch com prioridade
    const fetchOptions: RequestInit = {
      cache: 'force-cache',
      priority: options?.priority || 'auto',
    } as RequestInit
    
    const response = await fetch(url, fetchOptions)
    
    if (!response.ok) {
      throw new Error(`Failed to load asset: ${response.status}`)
    }
    
    const blob = await response.blob()
    
    // Verificar tamanho
    if (blob.size > config.maxSize) {
      console.warn(`Asset ${url} exceeds max size for type ${type}`)
    }
    
    // Obter dimensões para imagens
    let dimensions: { width: number; height: number } | undefined
    if (type === 'image') {
      dimensions = await this.getImageDimensions(blob)
    }
    
    const asset: CachedAsset = {
      url,
      type,
      data: blob,
      metadata: {
        size: blob.size,
        mimeType: blob.type,
        lastModified: Date.now(),
        etag: response.headers.get('etag') || undefined,
        expires: this.parseExpires(response.headers.get('expires')),
        quality: options?.quality,
        dimensions,
      },
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
    }
    
    return asset
  }
  
  /**
   * Preload múltiplos assets
   */
  async preloadAssets(assets: Array<{ url: string; type: AssetType; priority?: 'high' | 'low' }>) {
    const promises = assets.map(({ url, type, priority }) => 
      this.getAsset(url, type, { priority }).catch(error => {
        console.warn(`Failed to preload asset ${url}:`, error)
        return null
      })
    )
    
    const results = await Promise.allSettled(promises)
    return results.filter(result => result.status === 'fulfilled').length
  }
  
  /**
   * Lazy loading com Intersection Observer
   */
  setupLazyLoading(
    element: HTMLElement, 
    url: string, 
    type: AssetType,
    callback: (asset: CachedAsset) => void
  ) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.getAsset(url, type, { priority: 'high' })
              .then(callback)
              .catch(error => console.error('Lazy loading failed:', error))
            
            observer.unobserve(element)
            this.observers.delete(url)
          }
        })
      },
      {
        rootMargin: '50px', // Carregar 50px antes de aparecer
        threshold: 0.1,
      }
    )
    
    observer.observe(element)
    this.observers.set(url, observer)
  }
  
  /**
   * Otimizar imagem para diferentes tamanhos
   */
  async optimizeImage(
    blob: Blob, 
    targetWidth: number, 
    quality: number = 0.8
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        const aspectRatio = img.height / img.width
        canvas.width = targetWidth
        canvas.height = targetWidth * aspectRatio
        
        ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        canvas.toBlob(
          (optimizedBlob) => {
            if (optimizedBlob) {
              resolve(optimizedBlob)
            } else {
              reject(new Error('Failed to optimize image'))
            }
          },
          'image/webp',
          quality
        )
      }
      
      img.onerror = reject
      img.src = URL.createObjectURL(blob)
    })
  }
  
  /**
   * Limpar cache expirado
   */
  cleanup() {
    const now = Date.now()
    
    for (const [key, asset] of this.cache.entries()) {
      if (this.isExpired(asset) || this.shouldEvict(asset, now)) {
        this.cache.delete(key)
        
        // Revogar URL se for blob
        if (asset.data instanceof Blob) {
          URL.revokeObjectURL(asset.url)
        }
      }
    }
  }
  
  /**
   * Obter estatísticas do cache
   */
  getStats() {
    const assets = Array.from(this.cache.values())
    const totalSize = assets.reduce((size, asset) => size + asset.metadata.size, 0)
    const typeStats = assets.reduce((stats, asset) => {
      stats[asset.type] = (stats[asset.type] || 0) + 1
      return stats
    }, {} as Record<AssetType, number>)
    
    return {
      totalAssets: assets.length,
      totalSize,
      typeStats,
      hitRate: this.calculateHitRate(),
      oldestAsset: Math.min(...assets.map(a => a.timestamp)),
      newestAsset: Math.max(...assets.map(a => a.timestamp)),
    }
  }
  
  // Métodos auxiliares
  private generateCacheKey(url: string, options?: any): string {
    const params = options ? JSON.stringify(options) : ''
    return `${url}${params}`
  }
  
  private isExpired(asset: CachedAsset): boolean {
    const config = ASSET_CACHE_CONFIG[asset.type]
    return Date.now() - asset.timestamp > config.maxAge
  }
  
  private shouldEvict(asset: CachedAsset, now: number): boolean {
    // Evict assets não acessados há mais de 1 hora
    const oneHour = 60 * 60 * 1000
    return now - asset.lastAccessed > oneHour && asset.accessCount < 3
  }
  
  private async getImageDimensions(blob: Blob): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve({ width: img.width, height: img.height })
      img.onerror = reject
      img.src = URL.createObjectURL(blob)
    })
  }
  
  private parseExpires(expiresHeader: string | null): number | undefined {
    if (!expiresHeader) return undefined
    const date = new Date(expiresHeader)
    return isNaN(date.getTime()) ? undefined : date.getTime()
  }
  
  private calculateHitRate(): number {
    const assets = Array.from(this.cache.values())
    if (assets.length === 0) return 0
    
    const totalAccesses = assets.reduce((sum, asset) => sum + asset.accessCount, 0)
    const cacheHits = assets.length // Cada asset no cache representa pelo menos um hit
    
    return totalAccesses > 0 ? (cacheHits / totalAccesses) * 100 : 0
  }
}

// Instância global do cache manager
export const assetCache = new AssetCacheManager()

// Configurar limpeza automática
setInterval(() => {
  assetCache.cleanup()
}, 10 * 60 * 1000) // A cada 10 minutos
