/**
 * Configurações avançadas de cache para React Query
 * Sistema inteligente com TTL dinâmico e estratégias de invalidação
 */

import { QueryClient, QueryClientConfig } from '@tanstack/react-query'

// Configurações de TTL por tipo de dados
export const CACHE_TIMES = {
  // Dados estáticos (raramente mudam)
  STATIC: {
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
  },
  
  // Dados do usuário (perfil, configurações)
  USER: {
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
  },
  
  // Listas dinâmicas (questões, avaliações)
  DYNAMIC: {
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
  },
  
  // Dados em tempo real (notificações, estatísticas)
  REALTIME: {
    staleTime: 30 * 1000, // 30 segundos
    gcTime: 5 * 60 * 1000, // 5 minutos
  },
  
  // Dados públicos (avaliações públicas, categorias)
  PUBLIC: {
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  }
} as const

// Chaves de cache organizadas por contexto
export const CACHE_KEYS = {
  // Autenticação e usuário
  AUTH: {
    USER: 'user',
    PROFILE: 'profile',
    SESSION: 'session',
    SUBSCRIPTION: 'subscription',
  },
  
  // Questões
  QUESTIONS: {
    LIST: 'questions',
    DETAIL: 'question',
    SEARCH: 'questions-search',
    FILTERS: 'questions-filters',
    CATEGORIES: 'question-categories',
  },
  
  // Avaliações
  ASSESSMENTS: {
    LIST: 'assessments',
    DETAIL: 'assessment',
    TEMPLATES: 'assessment-templates',
    HISTORY: 'assessment-history',
  },
  
  // Dados públicos
  PUBLIC: {
    ASSESSMENTS: 'public-assessments',
    CATEGORIES: 'public-categories',
    FEATURED: 'featured-assessments',
  },
  
  // Sistema
  SYSTEM: {
    SETTINGS: 'system-settings',
    ANALYTICS: 'analytics',
    NOTIFICATIONS: 'notifications',
  },
  
  // Escola/Admin
  SCHOOL: {
    USERS: 'school-users',
    STATS: 'school-stats',
    SETTINGS: 'school-settings',
  }
} as const

// Configuração inteligente do QueryClient
export const createIntelligentQueryClient = (): QueryClient => {
  const config: QueryClientConfig = {
    defaultOptions: {
      queries: {
        // Configurações padrão
        staleTime: CACHE_TIMES.DYNAMIC.staleTime,
        gcTime: CACHE_TIMES.DYNAMIC.gcTime,
        retry: (failureCount, error: any) => {
          // Não retry para erros 4xx (client errors)
          if (error?.status >= 400 && error?.status < 500) {
            return false
          }
          // Retry até 3 vezes para outros erros
          return failureCount < 3
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        
        // Network mode inteligente
        networkMode: 'online',
        
        // Refetch em foco da janela apenas para dados em tempo real
        refetchOnWindowFocus: false,
        
        // Refetch na reconexão
        refetchOnReconnect: 'always',
      },
      
      mutations: {
        retry: 1,
        networkMode: 'online',
      }
    }
  }
  
  return new QueryClient(config)
}

// Função para obter configurações específicas por tipo de query
export const getCacheConfig = (type: keyof typeof CACHE_TIMES) => {
  return CACHE_TIMES[type]
}

// Função para gerar chave de cache consistente
export const generateCacheKey = (
  baseKey: string, 
  params?: Record<string, any>
): string[] => {
  if (!params) return [baseKey]
  
  // Ordenar parâmetros para consistência
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {} as Record<string, any>)
  
  return [baseKey, sortedParams]
}

// Estratégias de invalidação
export const INVALIDATION_STRATEGIES = {
  // Invalidar dados relacionados ao usuário
  USER_DATA: [
    CACHE_KEYS.AUTH.PROFILE,
    CACHE_KEYS.AUTH.SUBSCRIPTION,
    CACHE_KEYS.ASSESSMENTS.LIST,
    CACHE_KEYS.ASSESSMENTS.HISTORY,
  ],
  
  // Invalidar após criar/editar questão
  QUESTION_MUTATION: [
    CACHE_KEYS.QUESTIONS.LIST,
    CACHE_KEYS.QUESTIONS.SEARCH,
    CACHE_KEYS.QUESTIONS.CATEGORIES,
  ],
  
  // Invalidar após criar/editar avaliação
  ASSESSMENT_MUTATION: [
    CACHE_KEYS.ASSESSMENTS.LIST,
    CACHE_KEYS.ASSESSMENTS.HISTORY,
    CACHE_KEYS.PUBLIC.ASSESSMENTS,
  ],
  
  // Invalidar dados da escola
  SCHOOL_DATA: [
    CACHE_KEYS.SCHOOL.USERS,
    CACHE_KEYS.SCHOOL.STATS,
    CACHE_KEYS.SCHOOL.SETTINGS,
  ],
} as const

// Função para invalidar múltiplas chaves
export const invalidateMultipleKeys = async (
  queryClient: QueryClient,
  keys: string[]
) => {
  const promises = keys.map(key => 
    queryClient.invalidateQueries({ queryKey: [key] })
  )
  
  await Promise.all(promises)
}

// Configurações de prefetch inteligente
export const PREFETCH_CONFIG = {
  // Prefetch dados críticos no login
  ON_LOGIN: [
    { key: CACHE_KEYS.AUTH.PROFILE, config: CACHE_TIMES.USER },
    { key: CACHE_KEYS.AUTH.SUBSCRIPTION, config: CACHE_TIMES.USER },
    { key: CACHE_KEYS.QUESTIONS.CATEGORIES, config: CACHE_TIMES.STATIC },
  ],
  
  // Prefetch ao navegar para questões
  ON_QUESTIONS_PAGE: [
    { key: CACHE_KEYS.QUESTIONS.FILTERS, config: CACHE_TIMES.STATIC },
    { key: CACHE_KEYS.QUESTIONS.CATEGORIES, config: CACHE_TIMES.STATIC },
  ],
  
  // Prefetch ao navegar para avaliações
  ON_ASSESSMENTS_PAGE: [
    { key: CACHE_KEYS.ASSESSMENTS.TEMPLATES, config: CACHE_TIMES.STATIC },
  ],
} as const

// Função para prefetch inteligente
export const prefetchData = async (
  queryClient: QueryClient,
  scenario: keyof typeof PREFETCH_CONFIG,
  fetchFunctions: Record<string, () => Promise<any>>
) => {
  const configs = PREFETCH_CONFIG[scenario]
  
  const promises = configs.map(({ key, config }) => {
    const fetchFn = fetchFunctions[key]
    if (!fetchFn) return Promise.resolve()
    
    return queryClient.prefetchQuery({
      queryKey: [key],
      queryFn: fetchFn,
      staleTime: config.staleTime,
      gcTime: config.gcTime,
    })
  })
  
  await Promise.allSettled(promises)
}

// Configurações de background refetch
export const BACKGROUND_REFETCH = {
  // Intervalo para dados em tempo real
  REALTIME_INTERVAL: 30 * 1000, // 30 segundos
  
  // Intervalo para dados dinâmicos
  DYNAMIC_INTERVAL: 5 * 60 * 1000, // 5 minutos
  
  // Intervalo para dados do usuário
  USER_INTERVAL: 15 * 60 * 1000, // 15 minutos
} as const

// Função para configurar background refetch
export const setupBackgroundRefetch = (
  queryClient: QueryClient,
  key: string,
  interval: number
) => {
  return setInterval(() => {
    queryClient.invalidateQueries({ 
      queryKey: [key],
      refetchType: 'active' // Apenas refetch queries ativas
    })
  }, interval)
}

// Função para limpar cache antigo
export const cleanupOldCache = (queryClient: QueryClient) => {
  // Remove queries não utilizadas há mais de 1 hora
  queryClient.getQueryCache().getAll().forEach(query => {
    const lastUsed = query.state.dataUpdatedAt
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    
    if (lastUsed < oneHourAgo && query.getObserversCount() === 0) {
      queryClient.removeQueries({ queryKey: query.queryKey })
    }
  })
}

// Configuração de cache persistente (localStorage)
export const PERSISTENT_CACHE_CONFIG = {
  // Dados que devem persistir entre sessões
  PERSISTENT_KEYS: [
    CACHE_KEYS.AUTH.USER,
    CACHE_KEYS.AUTH.PROFILE,
    CACHE_KEYS.QUESTIONS.CATEGORIES,
    CACHE_KEYS.PUBLIC.CATEGORIES,
  ],
  
  // TTL para cache persistente (7 dias)
  PERSISTENT_TTL: 7 * 24 * 60 * 60 * 1000,
} as const
