/**
 * Sistema de cache de assets para otimização de performance
 * Gerencia cache inteligente de imagens, fontes e outros recursos
 */

// Tipos para o sistema de cache
export interface AssetCacheOptions {
  priority?: 'high' | 'medium' | 'low'
  maxAge?: number // em milissegundos
  quality?: number // para imagens (0-1)
  format?: 'webp' | 'avif' | 'jpeg' | 'png'
}

export interface CachedAsset {
  url: string
  type: 'image' | 'font' | 'script' | 'style'
  data: string | ArrayBuffer
  timestamp: number
  size: number
  options: AssetCacheOptions
}

// Configurações padrão
const DEFAULT_OPTIONS: AssetCacheOptions = {
  priority: 'medium',
  maxAge: 24 * 60 * 60 * 1000, // 24 horas
  quality: 0.8,
}

// Cache em memória
const memoryCache = new Map<string, CachedAsset>()

// Cache no IndexedDB para persistência
let dbCache: IDBDatabase | null = null

/**
 * Inicializar IndexedDB para cache persistente
 */
async function initIndexedDB(): Promise<IDBDatabase> {
  if (dbCache) return dbCache

  return new Promise((resolve, reject) => {
    const request = indexedDB.open('AssetCache', 1)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      dbCache = request.result
      resolve(dbCache)
    }
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result
      
      if (!db.objectStoreNames.contains('assets')) {
        const store = db.createObjectStore('assets', { keyPath: 'url' })
        store.createIndex('timestamp', 'timestamp', { unique: false })
        store.createIndex('type', 'type', { unique: false })
      }
    }
  })
}

/**
 * Gerar chave de cache baseada na URL e opções
 */
function generateCacheKey(url: string, options: AssetCacheOptions): string {
  const optionsStr = JSON.stringify(options)
  return `${url}:${btoa(optionsStr)}`
}

/**
 * Verificar se um asset está no cache e ainda é válido
 */
function isAssetValid(asset: CachedAsset): boolean {
  const now = Date.now()
  const maxAge = asset.options.maxAge || DEFAULT_OPTIONS.maxAge!
  return (now - asset.timestamp) < maxAge
}

/**
 * Obter asset do cache em memória
 */
function getFromMemoryCache(cacheKey: string): CachedAsset | null {
  const asset = memoryCache.get(cacheKey)
  if (asset && isAssetValid(asset)) {
    return asset
  }
  
  // Remover asset expirado
  if (asset) {
    memoryCache.delete(cacheKey)
  }
  
  return null
}

/**
 * Obter asset do IndexedDB
 */
async function getFromIndexedDB(cacheKey: string): Promise<CachedAsset | null> {
  try {
    const db = await initIndexedDB()
    const transaction = db.transaction(['assets'], 'readonly')
    const store = transaction.objectStore('assets')
    
    return new Promise((resolve) => {
      const request = store.get(cacheKey)
      
      request.onsuccess = () => {
        const asset = request.result as CachedAsset
        if (asset && isAssetValid(asset)) {
          // Adicionar ao cache em memória para acesso rápido
          memoryCache.set(cacheKey, asset)
          resolve(asset)
        } else {
          // Remover asset expirado
          if (asset) {
            const deleteTransaction = db.transaction(['assets'], 'readwrite')
            const deleteStore = deleteTransaction.objectStore('assets')
            deleteStore.delete(cacheKey)
          }
          resolve(null)
        }
      }
      
      request.onerror = () => resolve(null)
    })
  } catch (error) {
    console.warn('Failed to get asset from IndexedDB:', error)
    return null
  }
}

/**
 * Armazenar asset no cache
 */
async function storeAsset(cacheKey: string, asset: CachedAsset): Promise<void> {
  // Armazenar em memória
  memoryCache.set(cacheKey, asset)
  
  // Armazenar no IndexedDB para persistência
  try {
    const db = await initIndexedDB()
    const transaction = db.transaction(['assets'], 'readwrite')
    const store = transaction.objectStore('assets')
    
    // Usar a URL original como chave no IndexedDB
    const dbAsset = { ...asset, url: cacheKey }
    store.put(dbAsset)
  } catch (error) {
    console.warn('Failed to store asset in IndexedDB:', error)
  }
}

/**
 * Fazer download de um asset
 */
async function downloadAsset(url: string, type: CachedAsset['type']): Promise<string | ArrayBuffer> {
  const response = await fetch(url)
  
  if (!response.ok) {
    throw new Error(`Failed to fetch asset: ${response.status} ${response.statusText}`)
  }
  
  // Para imagens e fontes, usar ArrayBuffer
  if (type === 'image' || type === 'font') {
    return await response.arrayBuffer()
  }
  
  // Para scripts e estilos, usar texto
  return await response.text()
}

/**
 * Converter ArrayBuffer para Data URL (para imagens)
 */
function arrayBufferToDataUrl(buffer: ArrayBuffer, mimeType: string): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  const base64 = btoa(binary)
  return `data:${mimeType};base64,${base64}`
}

/**
 * Detectar tipo MIME baseado na extensão do arquivo
 */
function getMimeType(url: string): string {
  const extension = url.split('.').pop()?.toLowerCase()
  
  switch (extension) {
    case 'webp': return 'image/webp'
    case 'avif': return 'image/avif'
    case 'jpg':
    case 'jpeg': return 'image/jpeg'
    case 'png': return 'image/png'
    case 'gif': return 'image/gif'
    case 'svg': return 'image/svg+xml'
    case 'woff': return 'font/woff'
    case 'woff2': return 'font/woff2'
    case 'ttf': return 'font/ttf'
    case 'otf': return 'font/otf'
    case 'js': return 'application/javascript'
    case 'css': return 'text/css'
    default: return 'application/octet-stream'
  }
}

/**
 * Classe principal do sistema de cache de assets
 */
class AssetCache {
  /**
   * Obter um asset do cache ou fazer download se necessário
   */
  async getAsset(
    url: string, 
    type: CachedAsset['type'], 
    options: AssetCacheOptions = {}
  ): Promise<string> {
    const finalOptions = { ...DEFAULT_OPTIONS, ...options }
    const cacheKey = generateCacheKey(url, finalOptions)
    
    // Tentar obter do cache em memória primeiro
    let cachedAsset = getFromMemoryCache(cacheKey)
    
    // Se não estiver em memória, tentar IndexedDB
    if (!cachedAsset) {
      cachedAsset = await getFromIndexedDB(cacheKey)
    }
    
    // Se encontrou no cache, retornar
    if (cachedAsset) {
      if (typeof cachedAsset.data === 'string') {
        return cachedAsset.data
      } else {
        // Converter ArrayBuffer para Data URL
        const mimeType = getMimeType(url)
        return arrayBufferToDataUrl(cachedAsset.data, mimeType)
      }
    }
    
    // Asset não está no cache, fazer download
    try {
      const data = await downloadAsset(url, type)
      const size = typeof data === 'string' ? data.length : data.byteLength
      
      const asset: CachedAsset = {
        url,
        type,
        data,
        timestamp: Date.now(),
        size,
        options: finalOptions,
      }
      
      // Armazenar no cache
      await storeAsset(cacheKey, asset)
      
      // Retornar dados
      if (typeof data === 'string') {
        return data
      } else {
        const mimeType = getMimeType(url)
        return arrayBufferToDataUrl(data, mimeType)
      }
    } catch (error) {
      console.error('Failed to download and cache asset:', url, error)
      throw error
    }
  }
  
  /**
   * Preload de múltiplos assets
   */
  async preloadAssets(assets: Array<{ url: string; type: CachedAsset['type']; options?: AssetCacheOptions }>): Promise<void> {
    const promises = assets.map(asset => 
      this.getAsset(asset.url, asset.type, asset.options)
        .catch(error => {
          console.warn(`Failed to preload asset ${asset.url}:`, error)
        })
    )
    
    await Promise.allSettled(promises)
  }
  
  /**
   * Limpar cache expirado
   */
  async cleanup(): Promise<void> {
    const now = Date.now()
    
    // Limpar cache em memória
    for (const [key, asset] of memoryCache.entries()) {
      if (!isAssetValid(asset)) {
        memoryCache.delete(key)
      }
    }
    
    // Limpar IndexedDB
    try {
      const db = await initIndexedDB()
      const transaction = db.transaction(['assets'], 'readwrite')
      const store = transaction.objectStore('assets')
      const index = store.index('timestamp')
      
      // Obter todos os assets
      const request = index.openCursor()
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const asset = cursor.value as CachedAsset
          if (!isAssetValid(asset)) {
            cursor.delete()
          }
          cursor.continue()
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup IndexedDB cache:', error)
    }
  }
  
  /**
   * Obter estatísticas do cache
   */
  async getStats(): Promise<{
    memorySize: number
    memoryCount: number
    totalSize: number
    totalCount: number
  }> {
    let memorySize = 0
    let totalSize = 0
    let totalCount = 0
    
    // Calcular tamanho do cache em memória
    for (const asset of memoryCache.values()) {
      memorySize += asset.size
    }
    
    // Calcular tamanho total no IndexedDB
    try {
      const db = await initIndexedDB()
      const transaction = db.transaction(['assets'], 'readonly')
      const store = transaction.objectStore('assets')
      
      const request = store.openCursor()
      
      return new Promise((resolve) => {
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result
          if (cursor) {
            const asset = cursor.value as CachedAsset
            totalSize += asset.size
            totalCount++
            cursor.continue()
          } else {
            resolve({
              memorySize,
              memoryCount: memoryCache.size,
              totalSize,
              totalCount,
            })
          }
        }
        
        request.onerror = () => {
          resolve({
            memorySize,
            memoryCount: memoryCache.size,
            totalSize: memorySize,
            totalCount: memoryCache.size,
          })
        }
      })
    } catch (error) {
      return {
        memorySize,
        memoryCount: memoryCache.size,
        totalSize: memorySize,
        totalCount: memoryCache.size,
      }
    }
  }
  
  /**
   * Limpar todo o cache
   */
  async clear(): Promise<void> {
    // Limpar cache em memória
    memoryCache.clear()
    
    // Limpar IndexedDB
    try {
      const db = await initIndexedDB()
      const transaction = db.transaction(['assets'], 'readwrite')
      const store = transaction.objectStore('assets')
      store.clear()
    } catch (error) {
      console.warn('Failed to clear IndexedDB cache:', error)
    }
  }
}

// Instância singleton do cache
export const assetCache = new AssetCache()

// Exportar tipos e utilitários
export { AssetCache }
export default assetCache
