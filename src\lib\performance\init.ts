/**
 * Inicialização do sistema de cache e performance
 * Configura todos os sistemas de otimização na inicialização da aplicação
 */

import { initWebVitals, monitorResourcePerformance, monitorLongTasks } from './webVitals'
import { assetCache } from './assetCache'

/**
 * Inicializar todos os sistemas de performance
 */
export async function initPerformanceSystems() {
  console.log('[Performance] Initializing performance systems...')
  
  try {
    // 1. Inicializar monitoramento de Web Vitals
    await initWebVitals()
    console.log('[Performance] ✓ Web Vitals monitoring initialized')
    
    // 2. Configurar monitoramento de recursos
    const resourceCleanup = monitorResourcePerformance()
    console.log('[Performance] ✓ Resource performance monitoring initialized')
    
    // 3. Configurar monitoramento de long tasks
    const longTaskCleanup = monitorLongTasks()
    console.log('[Performance] ✓ Long task monitoring initialized')
    
    // 4. Configurar preload de recursos críticos
    await preloadCriticalResources()
    console.log('[Performance] ✓ Critical resources preloaded')
    
    // 5. Configurar otimizações baseadas na conexão
    adaptToNetworkConditions()
    console.log('[Performance] ✓ Network adaptations configured')
    
    // 6. Configurar limpeza automática
    setupAutomaticCleanup()
    console.log('[Performance] ✓ Automatic cleanup configured')
    
    console.log('[Performance] All systems initialized successfully!')
    
    // Retornar função de cleanup
    return () => {
      resourceCleanup?.()
      longTaskCleanup?.()
    }
    
  } catch (error) {
    console.error('[Performance] Failed to initialize performance systems:', error)
  }
}

/**
 * Preload de recursos críticos
 */
async function preloadCriticalResources() {
  const criticalResources = [
    // Fontes críticas
    { url: '/fonts/inter-var.woff2', type: 'font' as const },
    
    // Imagens críticas
    { url: '/images/logo.webp', type: 'image' as const },
    { url: '/images/hero-bg.webp', type: 'image' as const },
    
    // Icons críticos
    { url: '/images/icons/dashboard.svg', type: 'image' as const },
    { url: '/images/icons/questions.svg', type: 'image' as const },
    { url: '/images/icons/assessments.svg', type: 'image' as const },
  ]
  
  // Preload com prioridade alta
  const preloadPromises = criticalResources.map(resource => 
    assetCache.getAsset(resource.url, resource.type, { priority: 'high' })
      .catch(error => {
        console.warn(`Failed to preload ${resource.url}:`, error)
      })
  )
  
  await Promise.allSettled(preloadPromises)
}

/**
 * Adaptações baseadas nas condições da rede
 */
function adaptToNetworkConditions() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    
    if (connection) {
      const isSlowConnection = connection.effectiveType === '2g' || 
                             connection.effectiveType === 'slow-2g'
      
      if (isSlowConnection) {
        console.log('[Performance] Slow connection detected, applying optimizations')
        
        // Reduzir qualidade de imagens
        document.documentElement.style.setProperty('--image-quality', '0.6')
        
        // Desabilitar animações desnecessárias
        document.documentElement.style.setProperty('--animation-duration', '0s')
        
        // Aumentar tempos de cache
        localStorage.setItem('performance-mode', 'low-bandwidth')
      }
      
      // Listener para mudanças na conexão
      connection.addEventListener('change', () => {
        console.log('[Performance] Connection changed:', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        })
        
        // Reconfigurar baseado na nova conexão
        adaptToNetworkConditions()
      })
    }
  }
}

/**
 * Configurar limpeza automática de recursos
 */
function setupAutomaticCleanup() {
  // Limpeza a cada 10 minutos
  setInterval(() => {
    // Limpar cache de assets
    assetCache.cleanup()
    
    // Limpar dados antigos do localStorage
    cleanupLocalStorage()
    
    // Forçar garbage collection se disponível
    if ('gc' in window && typeof (window as any).gc === 'function') {
      try {
        (window as any).gc()
      } catch (error) {
        // Ignorar erros de GC
      }
    }
    
    console.log('[Performance] Automatic cleanup completed')
  }, 10 * 60 * 1000)
  
  // Limpeza ao sair da página
  window.addEventListener('beforeunload', () => {
    assetCache.cleanup()
    cleanupLocalStorage()
  })
}

/**
 * Limpar dados antigos do localStorage
 */
function cleanupLocalStorage() {
  const keysToCheck = Object.keys(localStorage)
  const now = Date.now()
  const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 dias
  
  keysToCheck.forEach(key => {
    if (key.startsWith('cache_') || key.startsWith('perf_')) {
      try {
        const data = JSON.parse(localStorage.getItem(key) || '{}')
        if (data.timestamp && (now - data.timestamp) > maxAge) {
          localStorage.removeItem(key)
        }
      } catch (error) {
        // Remover dados corrompidos
        localStorage.removeItem(key)
      }
    }
  })
}

/**
 * Configurar observadores de performance
 */
export function setupPerformanceObservers() {
  // Observer para mudanças no DOM
  if ('MutationObserver' in window) {
    const observer = new MutationObserver((mutations) => {
      let significantChanges = 0
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          significantChanges++
        }
      })
      
      // Log mudanças significativas no DOM
      if (significantChanges > 10) {
        console.warn('[Performance] Significant DOM changes detected:', significantChanges)
      }
    })
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    })
  }
  
  // Observer para mudanças de visibilidade
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      // Página ficou oculta - pausar operações não críticas
      console.log('[Performance] Page hidden, pausing non-critical operations')
    } else {
      // Página ficou visível - retomar operações
      console.log('[Performance] Page visible, resuming operations')
    }
  })
}

/**
 * Configurar hints de recursos
 */
export function setupResourceHints() {
  // DNS prefetch para domínios externos
  const externalDomains = [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    'cdn.jsdelivr.net',
  ]
  
  externalDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = `//${domain}`
    document.head.appendChild(link)
  })
  
  // Preconnect para recursos críticos
  const criticalDomains = [
    'fonts.googleapis.com',
  ]
  
  criticalDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = `https://${domain}`
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

/**
 * Configurar métricas customizadas
 */
export function setupCustomMetrics() {
  // Métrica: Tempo até interatividade
  let timeToInteractive = 0
  
  const measureTTI = () => {
    if (document.readyState === 'complete') {
      timeToInteractive = performance.now()
      console.log('[Performance] Time to Interactive:', timeToInteractive)
      
      // Enviar métrica customizada
      if ('gtag' in window) {
        (window as any).gtag('event', 'timing_complete', {
          name: 'time_to_interactive',
          value: Math.round(timeToInteractive)
        })
      }
    }
  }
  
  if (document.readyState === 'complete') {
    measureTTI()
  } else {
    window.addEventListener('load', measureTTI)
  }
  
  // Métrica: Tempo de resposta de cliques
  let clickStartTime = 0
  
  document.addEventListener('mousedown', () => {
    clickStartTime = performance.now()
  })
  
  document.addEventListener('click', () => {
    if (clickStartTime > 0) {
      const clickResponseTime = performance.now() - clickStartTime
      
      if (clickResponseTime > 100) {
        console.warn('[Performance] Slow click response:', clickResponseTime)
      }
      
      clickStartTime = 0
    }
  })
}

/**
 * Função principal de inicialização
 */
export async function initializePerformanceSystem() {
  // Aguardar DOM estar pronto
  if (document.readyState === 'loading') {
    await new Promise(resolve => {
      document.addEventListener('DOMContentLoaded', resolve)
    })
  }
  
  // Inicializar todos os sistemas
  const cleanup = await initPerformanceSystems()
  
  // Configurar observadores
  setupPerformanceObservers()
  
  // Configurar hints de recursos
  setupResourceHints()
  
  // Configurar métricas customizadas
  setupCustomMetrics()
  
  console.log('[Performance] Complete performance system initialized!')
  
  return cleanup
}
