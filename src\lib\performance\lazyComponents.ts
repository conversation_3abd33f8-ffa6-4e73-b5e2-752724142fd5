/**
 * Sistema de lazy loading para componentes React
 * Otimiza o carregamento inicial da aplicação
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react'

// Configurações de preload
interface LazyLoadConfig {
  preload?: boolean
  priority?: 'high' | 'low'
  chunkName?: string
}

// Cache para componentes já carregados
const componentCache = new Map<string, ComponentType<any>>()

/**
 * Função utilitária para criar componentes lazy com configurações avançadas
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
): LazyExoticComponent<T> {
  const { preload = false, chunkName } = config
  
  // Criar componente lazy
  const LazyComponent = lazy(() => {
    // Adicionar nome do chunk se especificado
    if (chunkName) {
      return importFn().then(module => {
        // Cache do componente
        componentCache.set(chunkName, module.default)
        return module
      })
    }
    
    return importFn()
  })
  
  // Preload se configurado
  if (preload) {
    // Preload após um pequeno delay para não bloquear o carregamento inicial
    setTimeout(() => {
      importFn().catch(error => {
        console.warn('Failed to preload component:', error)
      })
    }, 100)
  }
  
  return LazyComponent
}

/**
 * Componentes lazy-loaded organizados por categoria
 */

// Componentes principais
export const LazyDashboard = createLazyComponent(
  () => import('../../components/dashboard/Dashboard'),
  { chunkName: 'dashboard', preload: true, priority: 'high' }
)

export const LazyQuestions = createLazyComponent(
  () => import('../../components/questions/QuestionBank'),
  { chunkName: 'questions', priority: 'high' }
)

export const LazyAssessments = createLazyComponent(
  () => import('../../components/assessments/MyAssessments'),
  { chunkName: 'assessments', priority: 'high' }
)

export const LazyEditor = createLazyComponent(
  () => import('../../components/editor/AssessmentEditor'),
  { chunkName: 'editor', priority: 'high' }
)

// Componentes administrativos
export const LazyAdminDashboard = createLazyComponent(
  () => import('../../components/admin/AdminOverview'),
  { chunkName: 'admin-dashboard' }
)

export const LazyAdminUsers = createLazyComponent(
  () => import('../../components/admin/UserManagement'),
  { chunkName: 'admin-users' }
)

export const LazyAdminAnalytics = createLazyComponent(
  () => import('../../components/admin/AdminAnalytics'),
  { chunkName: 'admin-analytics' }
)

export const LazySystemSettings = createLazyComponent(
  () => import('../../components/admin/SystemSettings'),
  { chunkName: 'system-settings' }
)

// Componentes de editor
export const LazyAssessmentEditor = createLazyComponent(
  () => import('../../components/editor/AssessmentEditor'),
  { chunkName: 'assessment-editor' }
)

export const LazyMobileAssessmentEditor = createLazyComponent(
  () => import('../../components/editor/MobileAssessmentEditor'),
  { chunkName: 'mobile-editor' }
)

export const LazyBulkQuestionGenerator = createLazyComponent(
  () => import('../../components/admin/BulkQuestionGenerator'),
  { chunkName: 'bulk-generator' }
)

// Componentes de relatórios
export const LazyReportsPage = createLazyComponent(
  () => import('../../components/reports/Reports'),
  { chunkName: 'reports' }
)

export const LazyAnalyticsCharts = createLazyComponent(
  () => import('../../components/analytics/Analytics'),
  { chunkName: 'analytics-charts' }
)

// Componentes de billing
export const LazyBillingPage = createLazyComponent(
  () => import('../../components/billing/Billing'),
  { chunkName: 'billing' }
)

export const LazyPricingPlans = createLazyComponent(
  () => import('../../components/billing/PricingPlans'),
  { chunkName: 'pricing-plans' }
)

// Componentes de templates
export const LazyTemplatesPage = createLazyComponent(
  () => import('../../components/templates/Templates'),
  { chunkName: 'templates' }
)

// Componentes de colaboração
export const LazyCollaborationPage = createLazyComponent(
  () => import('../../components/collaboration/Collaboration'),
  { chunkName: 'collaboration' }
)

// Componentes de help
export const LazyHelpCenter = createLazyComponent(
  () => import('../../components/help/HelpCenter'),
  { chunkName: 'help-center' }
)

// Componentes públicos
export const LazyPublicAssessments = createLazyComponent(
  () => import('../../components/public/PublicAssessmentList'),
  { chunkName: 'public-assessments', preload: true }
)

export const LazyPublicAssessmentDetail = createLazyComponent(
  () => import('../../components/public/PublicAssessmentDetail'),
  { chunkName: 'public-assessment-detail' }
)

// Componentes de configurações
export const LazySettingsPage = createLazyComponent(
  () => import('../../components/settings/Settings'),
  { chunkName: 'settings' }
)

export const LazyNotificationSettings = createLazyComponent(
  () => import('../../components/settings/NotificationSettings'),
  { chunkName: 'notification-settings' }
)

// Componentes de wizard
export const LazyAssessmentWizard = createLazyComponent(
  () => import('../../components/wizard/AssessmentWizard'),
  { chunkName: 'assessment-wizard' }
)

// Componentes de importação
export const LazyImportExport = createLazyComponent(
  () => import('../../components/import/ImportExport'),
  { chunkName: 'import-export' }
)

// Landing page
export const LazyLandingPage = createLazyComponent(
  () => import('../../components/landing/LandingPage'),
  { chunkName: 'landing-page', preload: true }
)

/**
 * Função para preload de componentes baseado na rota atual
 */
export const preloadComponentsForRoute = (route: string) => {
  const preloadMap: Record<string, Array<() => Promise<any>>> = {
    '/dashboard': [
      () => import('../../components/dashboard/Dashboard'),
      () => import('../../components/dashboard/TaskManager'),
    ],
    '/questions': [
      () => import('../../components/questions/QuestionBank'),
      () => import('../../components/questions/FilterPanel'),
      () => import('../../components/questions/QuestionCard'),
    ],
    '/assessments': [
      () => import('../../components/assessments/MyAssessments'),
      () => import('../../components/assessments/CreateAssessmentModal'),
    ],
    '/editor': [
      () => import('../../components/editor/AssessmentEditor'),
      () => import('../../components/editor/AssessmentPreview'),
      () => import('../../components/editor/AssessmentSettings'),
    ],
    '/admin': [
      () => import('../../components/admin/UserManagement'),
      () => import('../../components/admin/AdminAnalytics'),
      () => import('../../components/admin/AdminLayout'),
    ]
  }
  
  const componentsToPreload = preloadMap[route] || []
  
  // Preload com delay para não impactar a performance inicial
  setTimeout(() => {
    componentsToPreload.forEach(importFn => {
      importFn().catch(error => {
        console.warn('Failed to preload component for route:', route, error)
      })
    })
  }, 500)
}

/**
 * Hook para preload inteligente baseado na navegação
 */
export const useIntelligentPreload = () => {
  const preloadForRoute = (route: string) => {
    preloadComponentsForRoute(route)
  }
  
  const preloadOnHover = (route: string) => {
    // Preload com delay menor para hover
    setTimeout(() => {
      preloadComponentsForRoute(route)
    }, 100)
  }
  
  return {
    preloadForRoute,
    preloadOnHover
  }
}

/**
 * Função para verificar se um componente já está carregado
 */
export const isComponentLoaded = (chunkName: string): boolean => {
  return componentCache.has(chunkName)
}

/**
 * Função para obter estatísticas de carregamento
 */
export const getLoadingStats = () => {
  return {
    loadedComponents: componentCache.size,
    loadedChunks: Array.from(componentCache.keys())
  }
}

/**
 * Configurações de chunk splitting para Vite
 */
export const chunkSplitConfig = {
  // Vendor chunks
  vendor: ['react', 'react-dom', 'react-router-dom'],
  ui: ['@headlessui/react', '@heroicons/react', 'lucide-react'],
  forms: ['react-hook-form', '@hookform/resolvers', 'zod'],
  query: ['@tanstack/react-query'],
  supabase: ['@supabase/supabase-js'],
  charts: ['recharts', 'chart.js'],
  pdf: ['jspdf', 'html2canvas'],

  // Feature chunks
  admin: [/src\/components\/admin/],
  editor: [/src\/components\/editor/, /src\/components\/ai/],
  public: [/src\/components\/public/],
  billing: [/src\/components\/billing/, /src\/services\/stripe/],
  analytics: [/src\/components\/analytics/, /src\/lib\/analytics/],
}

/**
 * Função para configurar code splitting no Vite
 */
export const configureCodeSplitting = () => {
  return {
    rollupOptions: {
      output: {
        manualChunks: (id: string) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (chunkSplitConfig.vendor.some(pkg => id.includes(pkg))) {
              return 'vendor'
            }
            if (chunkSplitConfig.ui.some(pkg => id.includes(pkg))) {
              return 'ui'
            }
            if (chunkSplitConfig.forms.some(pkg => id.includes(pkg))) {
              return 'forms'
            }
            if (chunkSplitConfig.query.some(pkg => id.includes(pkg))) {
              return 'query'
            }
            if (chunkSplitConfig.supabase.some(pkg => id.includes(pkg))) {
              return 'supabase'
            }
            if (chunkSplitConfig.charts.some(pkg => id.includes(pkg))) {
              return 'charts'
            }
            if (chunkSplitConfig.pdf.some(pkg => id.includes(pkg))) {
              return 'pdf'
            }
            return 'vendor-misc'
          }

          // Feature chunks
          if (chunkSplitConfig.admin.some(pattern => {
            if (typeof pattern === 'string') return id.includes(pattern)
            return pattern.test(id)
          })) {
            return 'admin'
          }

          if (chunkSplitConfig.editor.some(pattern => {
            if (typeof pattern === 'string') return id.includes(pattern)
            return pattern.test(id)
          })) {
            return 'editor'
          }

          if (chunkSplitConfig.public.some(pattern => {
            if (typeof pattern === 'string') return id.includes(pattern)
            return pattern.test(id)
          })) {
            return 'public'
          }

          if (chunkSplitConfig.billing.some(pattern => {
            if (typeof pattern === 'string') return id.includes(pattern)
            return pattern.test(id)
          })) {
            return 'billing'
          }

          if (chunkSplitConfig.analytics.some(pattern => {
            if (typeof pattern === 'string') return id.includes(pattern)
            return pattern.test(id)
          })) {
            return 'analytics'
          }
        }
      }
    }
  }
}
