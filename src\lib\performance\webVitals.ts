/**
 * Sistema de monitoramento de Core Web Vitals
 * Monitora métricas de performance em tempo real
 */

// Tipos para métricas
export interface WebVitalMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB' | 'INP'
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType: string
}

export interface PerformanceData {
  metrics: WebVitalMetric[]
  timestamp: number
  url: string
  userAgent: string
  connection?: {
    effectiveType: string
    downlink: number
    rtt: number
  }
  deviceInfo: {
    memory?: number
    cores?: number
    platform: string
  }
}

// Thresholds para classificação das métricas
const METRIC_THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 },
  INP: { good: 200, poor: 500 },
} as const

// Cache para métricas
const metricsCache = new Map<string, WebVitalMetric>()
const observers = new Set<(metric: WebVitalMetric) => void>()

/**
 * Classificar métrica baseada nos thresholds
 */
function getRating(name: WebVitalMetric['name'], value: number): WebVitalMetric['rating'] {
  const thresholds = METRIC_THRESHOLDS[name]
  if (value <= thresholds.good) return 'good'
  if (value <= thresholds.poor) return 'needs-improvement'
  return 'poor'
}

/**
 * Obter informações da conexão
 */
function getConnectionInfo() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    return {
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
    }
  }
  return undefined
}

/**
 * Obter informações do dispositivo
 */
function getDeviceInfo() {
  return {
    memory: (navigator as any).deviceMemory,
    cores: navigator.hardwareConcurrency,
    platform: navigator.platform,
  }
}

/**
 * Processar e armazenar métrica
 */
function processMetric(metric: any) {
  const webVitalMetric: WebVitalMetric = {
    name: metric.name,
    value: metric.value,
    rating: getRating(metric.name, metric.value),
    delta: metric.delta,
    id: metric.id,
    navigationType: metric.navigationType || 'navigate',
  }
  
  // Armazenar no cache
  metricsCache.set(metric.name, webVitalMetric)
  
  // Notificar observers
  observers.forEach(observer => observer(webVitalMetric))
  
  // Log em desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Web Vitals] ${metric.name}:`, {
      value: metric.value,
      rating: webVitalMetric.rating,
      delta: metric.delta,
    })
  }
}

/**
 * Inicializar monitoramento de Web Vitals
 */
export async function initWebVitals() {
  try {
    // Importar web-vitals dinamicamente
    const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals')
    
    // Configurar observers para cada métrica
    getCLS(processMetric)
    getFID(processMetric)
    getFCP(processMetric)
    getLCP(processMetric)
    getTTFB(processMetric)
    
    // INP (Interaction to Next Paint) - métrica mais recente
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'event') {
              const inp = (entry as any).processingStart - entry.startTime
              processMetric({
                name: 'INP',
                value: inp,
                delta: inp,
                id: `inp-${Date.now()}`,
                navigationType: 'navigate',
              })
            }
          }
        })
        
        observer.observe({ entryTypes: ['event'] })
      } catch (error) {
        console.warn('INP monitoring not supported:', error)
      }
    }
    
    console.log('[Web Vitals] Monitoring initialized')
  } catch (error) {
    console.warn('[Web Vitals] Failed to initialize:', error)
  }
}

/**
 * Adicionar observer para métricas
 */
export function addMetricObserver(callback: (metric: WebVitalMetric) => void) {
  observers.add(callback)
  
  // Enviar métricas já coletadas
  metricsCache.forEach(metric => callback(metric))
  
  return () => observers.delete(callback)
}

/**
 * Obter todas as métricas coletadas
 */
export function getAllMetrics(): WebVitalMetric[] {
  return Array.from(metricsCache.values())
}

/**
 * Obter dados completos de performance
 */
export function getPerformanceData(): PerformanceData {
  return {
    metrics: getAllMetrics(),
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    connection: getConnectionInfo(),
    deviceInfo: getDeviceInfo(),
  }
}

/**
 * Calcular score geral de performance
 */
export function calculatePerformanceScore(): {
  score: number
  grade: 'A' | 'B' | 'C' | 'D' | 'F'
  breakdown: Record<string, number>
} {
  const metrics = getAllMetrics()
  const breakdown: Record<string, number> = {}
  
  let totalScore = 0
  let metricCount = 0
  
  metrics.forEach(metric => {
    let score = 0
    
    switch (metric.rating) {
      case 'good':
        score = 100
        break
      case 'needs-improvement':
        score = 50
        break
      case 'poor':
        score = 0
        break
    }
    
    breakdown[metric.name] = score
    totalScore += score
    metricCount++
  })
  
  const averageScore = metricCount > 0 ? totalScore / metricCount : 0
  
  let grade: 'A' | 'B' | 'C' | 'D' | 'F'
  if (averageScore >= 90) grade = 'A'
  else if (averageScore >= 80) grade = 'B'
  else if (averageScore >= 70) grade = 'C'
  else if (averageScore >= 60) grade = 'D'
  else grade = 'F'
  
  return {
    score: Math.round(averageScore),
    grade,
    breakdown,
  }
}

/**
 * Enviar métricas para analytics
 */
export function sendMetricsToAnalytics(endpoint?: string) {
  const data = getPerformanceData()
  
  if (endpoint) {
    // Enviar para endpoint customizado
    fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }).catch(error => {
      console.warn('Failed to send metrics to analytics:', error)
    })
  } else {
    // Enviar para Google Analytics se disponível
    if (typeof gtag !== 'undefined') {
      data.metrics.forEach(metric => {
        gtag('event', 'web_vital', {
          event_category: 'Web Vitals',
          event_label: metric.name,
          value: Math.round(metric.value),
          custom_map: {
            metric_rating: metric.rating,
          },
        })
      })
    }
  }
}

/**
 * Hook para monitoramento de Web Vitals
 */
export function useWebVitals() {
  const [metrics, setMetrics] = useState<WebVitalMetric[]>([])
  const [performanceScore, setPerformanceScore] = useState<ReturnType<typeof calculatePerformanceScore>>({
    score: 0,
    grade: 'F',
    breakdown: {},
  })
  
  useEffect(() => {
    // Inicializar monitoramento
    initWebVitals()
    
    // Adicionar observer
    const unsubscribe = addMetricObserver((metric) => {
      setMetrics(prev => {
        const updated = prev.filter(m => m.name !== metric.name)
        return [...updated, metric]
      })
    })
    
    // Atualizar score periodicamente
    const interval = setInterval(() => {
      setPerformanceScore(calculatePerformanceScore())
    }, 5000)
    
    return () => {
      unsubscribe()
      clearInterval(interval)
    }
  }, [])
  
  return {
    metrics,
    performanceScore,
    sendToAnalytics: sendMetricsToAnalytics,
    getFullData: getPerformanceData,
  }
}

/**
 * Monitorar performance de recursos específicos
 */
export function monitorResourcePerformance() {
  if (!('PerformanceObserver' in window)) return
  
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming
        
        // Identificar recursos lentos
        if (resource.duration > 1000) {
          console.warn('[Performance] Slow resource:', {
            name: resource.name,
            duration: resource.duration,
            size: resource.transferSize,
            type: resource.initiatorType,
          })
        }
        
        // Identificar recursos grandes
        if (resource.transferSize > 1024 * 1024) { // > 1MB
          console.warn('[Performance] Large resource:', {
            name: resource.name,
            size: resource.transferSize,
            duration: resource.duration,
          })
        }
      }
    }
  })
  
  observer.observe({ entryTypes: ['resource'] })
  
  return () => observer.disconnect()
}

/**
 * Monitorar long tasks
 */
export function monitorLongTasks() {
  if (!('PerformanceObserver' in window)) return
  
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'longtask') {
        console.warn('[Performance] Long task detected:', {
          duration: entry.duration,
          startTime: entry.startTime,
        })
      }
    }
  })
  
  try {
    observer.observe({ entryTypes: ['longtask'] })
  } catch (error) {
    console.warn('Long task monitoring not supported:', error)
  }
  
  return () => observer.disconnect()
}

// Importar useState para o hook
import { useState, useEffect } from 'react'
