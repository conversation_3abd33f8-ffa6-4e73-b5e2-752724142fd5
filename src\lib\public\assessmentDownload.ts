import { supabase } from '../supabase'
import { generatePDF, downloadPDF } from '../../utils/pdfGenerator'
import { PublicAssessmentWithDetails } from '../../types/public'
import { WATERMARK_CONFIG } from '../../constants/usageLimits'

/**
 * Download a public assessment as PDF
 */
export const downloadPublicAssessment = async (
  assessmentId: string,
  userId?: string
): Promise<void> => {
  try {
    // Fetch assessment with questions
    const { data: assessment, error: assessmentError } = await supabase
      .from('assessments')
      .select(`
        *,
        profiles!assessments_autor_id_fkey(nome, escola)
      `)
      .eq('id', assessmentId)
      .eq('is_public', true)
      .single()

    if (assessmentError || !assessment) {
      throw new Error('Avaliação não encontrada ou não é pública')
    }

    // Fetch questions
    let questions: any[] = []
    if (assessment.questoes_ids && assessment.questoes_ids.length > 0) {
      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .select('*')
        .in('id', assessment.questoes_ids)
        .eq('is_public', true)

      if (questionsError) {
        throw new Error('Erro ao carregar questões')
      }

      questions = questionsData || []
    }

    // Get text blocks from configuration
    const textBlocks = assessment.configuracao?.textBlocks || []

    // Combine questions and text blocks into items array
    const items = [
      ...questions.map(q => ({
        ...q,
        tipo: q.tipo,
        enunciado: q.enunciado,
        alternativas: q.alternativas,
        resposta_correta: q.resposta_correta
      })),
      ...textBlocks
    ]

    // Determine if user is paid (no watermark) or free (with watermark)
    let isPaidUser = false
    if (userId) {
      const { data: subscription } = await supabase
        .from('subscriptions')
        .select('plano, status')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single()

      isPaidUser = subscription && ['premium', 'escolar'].includes(subscription.plano)
    }

    // Configure PDF options
    const pdfOptions = {
      paperSize: 'A4' as const,
      orientation: 'portrait' as const,
      fontSize: 'medium' as const,
      lineSpacing: 'normal' as const,
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      includeAnswerSheet: true,
      generateVersions: 1,
      qrCode: false,
      headerConfig: {
        nomeEscola: assessment.profiles?.escola || '',
        nomeProva: assessment.titulo,
        serie: assessment.serie,
        data: new Date().toLocaleDateString('pt-BR'),
        instrucoes: 'Leia atentamente cada questão antes de responder.'
      },
      showFooter: true,
      watermark: isPaidUser ? undefined : WATERMARK_CONFIG.FREE_PLAN_TEXT
    }

    // Generate PDF
    const blob = await generatePDF(items, pdfOptions)

    // Download PDF
    const fileName = `${assessment.titulo.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`
    downloadPDF(blob, fileName)

    // Track download
    if (userId) {
      await supabase.rpc('track_conversion', {
        p_assessment_id: assessmentId,
        p_user_id: userId,
        p_conversion_type: 'download',
        p_source_page: window.location.href,
        p_user_agent: navigator.userAgent,
        p_ip_address: null,
        p_utm_source: null,
        p_utm_medium: null,
        p_utm_campaign: null,
        p_utm_content: null,
        p_utm_term: null,
        p_metadata: {
          assessment_title: assessment.titulo,
          assessment_slug: assessment.slug,
          download_timestamp: new Date().toISOString()
        }
      })
    }

    // Increment download count
    await supabase
      .from('assessments')
      .update({ 
        download_count: (assessment.download_count || 0) + 1 
      })
      .eq('id', assessmentId)

  } catch (error) {
    console.error('Error downloading assessment:', error)
    throw error
  }
}

/**
 * Check if user can download assessment (for usage limits)
 */
export const canDownloadAssessment = async (
  assessmentId: string,
  userId?: string
): Promise<{ canDownload: boolean; reason?: string }> => {
  try {
    if (!userId) {
      return { canDownload: false, reason: 'Login necessário' }
    }

    // Check if user has active subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('plano, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()

    const isPaidUser = subscription && ['premium', 'escolar'].includes(subscription.plano)

    if (isPaidUser) {
      return { canDownload: true }
    }

    // For free users, check monthly download limit
    const { data: usageStats } = await supabase
      .from('usage_stats')
      .select('*')
      .eq('user_id', userId)
      .eq('action_type', 'pdf_downloaded')
      .gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString())

    const downloadsThisMonth = usageStats?.length || 0
    const monthlyLimit = 10 // Free plan limit

    if (downloadsThisMonth >= monthlyLimit) {
      return { 
        canDownload: false, 
        reason: `Limite de ${monthlyLimit} downloads por mês atingido` 
      }
    }

    return { canDownload: true }
  } catch (error) {
    console.error('Error checking download permission:', error)
    return { canDownload: false, reason: 'Erro ao verificar permissões' }
  }
}

/**
 * Get assessment preview data for public display
 */
export const getPublicAssessmentPreview = async (
  slug: string
): Promise<PublicAssessmentWithDetails | null> => {
  try {
    const { data: assessment, error } = await supabase
      .from('assessments')
      .select(`
        *,
        profiles!assessments_autor_id_fkey(nome, escola)
      `)
      .eq('slug', slug)
      .eq('is_public', true)
      .single()

    if (error || !assessment) {
      return null
    }

    // Fetch questions for preview
    if (assessment.questoes_ids && assessment.questoes_ids.length > 0) {
      const { data: questions } = await supabase
        .from('questions')
        .select('id, enunciado, tipo, alternativas, disciplina, serie, dificuldade, tags')
        .in('id', assessment.questoes_ids)
        .eq('is_public', true)

      assessment.questions = questions || []
    }

    return assessment as PublicAssessmentWithDetails
  } catch (error) {
    console.error('Error fetching assessment preview:', error)
    return null
  }
}

/**
 * Search public assessments with filters
 */
export const searchPublicAssessments = async (filters: {
  search?: string
  category?: string
  disciplina?: string
  serie?: string
  difficulty?: string
  limit?: number
  offset?: number
}) => {
  try {
    let query = supabase
      .from('assessments')
      .select(`
        *,
        profiles!assessments_autor_id_fkey(nome, escola)
      `, { count: 'exact' })
      .eq('is_public', true)

    // Apply filters
    if (filters.category) {
      query = query.eq('public_category', filters.category)
    }

    if (filters.disciplina) {
      query = query.eq('disciplina', filters.disciplina)
    }

    if (filters.serie) {
      query = query.eq('serie', filters.serie)
    }

    if (filters.difficulty) {
      query = query.eq('difficulty_level', filters.difficulty)
    }

    if (filters.search) {
      query = query.or(`titulo.ilike.%${filters.search}%,seo_description.ilike.%${filters.search}%`)
    }

    // Apply pagination
    const limit = filters.limit || 12
    const offset = filters.offset || 0
    query = query.range(offset, offset + limit - 1)

    // Order by view count (most popular first)
    query = query.order('view_count', { ascending: false })

    const { data: assessments, error, count } = await query

    if (error) throw error

    return {
      assessments: assessments || [],
      total: count || 0,
      hasMore: (assessments?.length || 0) === limit
    }
  } catch (error) {
    console.error('Error searching assessments:', error)
    throw error
  }
}
