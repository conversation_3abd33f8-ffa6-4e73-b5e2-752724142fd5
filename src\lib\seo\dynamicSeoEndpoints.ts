// Dynamic SEO endpoints handler for React SPA
// This handles sitemap.xml and robots.txt requests client-side

import { SitemapGenerator, generateRobotsTxt } from './sitemapGenerator'

/**
 * Handle sitemap.xml requests
 */
export const handleSitemapRequest = async (): Promise<string> => {
  try {
    const generator = new SitemapGenerator({
      baseUrl: 'https://atvpronta.com.br'
    })
    
    return await generator.generateSitemap()
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Fallback sitemap
    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://atvpronta.com.br/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://atvpronta.com.br/avaliacoes</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`
  }
}

/**
 * Handle robots.txt requests
 */
export const handleRobotsRequest = (): string => {
  try {
    return generateRobotsTxt('https://atvpronta.com.br')
  } catch (error) {
    console.error('Error generating robots.txt:', error)
    
    // Fallback robots.txt
    return `User-agent: *
Allow: /
Allow: /avaliacoes
Allow: /avaliacoes/*

# Disallow admin and app routes
Disallow: /app/
Disallow: /admin/
Disallow: /api/

# Disallow auth pages
Disallow: /login
Disallow: /register

# Allow sitemap
Sitemap: https://atvpronta.com.br/sitemap.xml

# Crawl delay
Crawl-delay: 1`
  }
}

/**
 * Initialize SEO endpoint handlers
 * This should be called when the app starts
 */
export const initializeSeoEndpoints = () => {
  // Check if we're in the browser and handle special routes
  if (typeof window !== 'undefined') {
    const path = window.location.pathname
    
    // Handle sitemap.xml requests
    if (path === '/sitemap.xml') {
      handleSitemapRequest().then(sitemap => {
        // Create a blob and download it
        const blob = new Blob([sitemap], { type: 'application/xml' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'sitemap.xml'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      })
      return
    }
    
    // Handle robots.txt requests
    if (path === '/robots.txt') {
      const robots = handleRobotsRequest()
      // Create a blob and download it
      const blob = new Blob([robots], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'robots.txt'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      return
    }
  }
}

/**
 * Generate and cache sitemap for better performance
 */
export const generateAndCacheSitemap = async (): Promise<void> => {
  try {
    const sitemap = await handleSitemapRequest()
    
    // Store in localStorage for quick access
    localStorage.setItem('cached_sitemap', sitemap)
    localStorage.setItem('sitemap_generated_at', new Date().toISOString())
    
    console.log('Sitemap generated and cached successfully')
  } catch (error) {
    console.error('Error generating and caching sitemap:', error)
  }
}

/**
 * Get cached sitemap or generate new one
 */
export const getCachedSitemap = async (): Promise<string> => {
  const cached = localStorage.getItem('cached_sitemap')
  const generatedAt = localStorage.getItem('sitemap_generated_at')
  
  // Check if cache is still valid (1 hour)
  if (cached && generatedAt) {
    const cacheAge = Date.now() - new Date(generatedAt).getTime()
    if (cacheAge < 3600000) { // 1 hour in milliseconds
      return cached
    }
  }
  
  // Generate new sitemap
  const sitemap = await handleSitemapRequest()
  localStorage.setItem('cached_sitemap', sitemap)
  localStorage.setItem('sitemap_generated_at', new Date().toISOString())
  
  return sitemap
}
