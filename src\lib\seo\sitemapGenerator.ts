import { supabase } from '../supabase'
import { SitemapUrl, SitemapConfig, DEFAULT_SITEMAP_CONFIG } from '../../types/public'

/**
 * Generate sitemap XML for public assessments
 */
export class SitemapGenerator {
  private config: SitemapConfig

  constructor(config: Partial<SitemapConfig> = {}) {
    this.config = { ...DEFAULT_SITEMAP_CONFIG, ...config }
  }

  /**
   * Generate complete sitemap XML
   */
  async generateSitemap(): Promise<string> {
    const urls = await this.getAllUrls()
    return this.generateXML(urls)
  }

  /**
   * Get all URLs for sitemap
   */
  private async getAllUrls(): Promise<SitemapUrl[]> {
    const urls: SitemapUrl[] = []

    // Static pages
    urls.push(
      {
        url: `${this.config.baseUrl}/`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly',
        priority: 1.0
      },
      {
        url: `${this.config.baseUrl}/avaliacoes`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily',
        priority: 0.9
      }
    )

    // Public assessment URLs
    const assessmentUrls = await this.getAssessmentUrls()
    urls.push(...assessmentUrls)

    // Category URLs
    const categoryUrls = await this.getCategoryUrls()
    urls.push(...categoryUrls)

    return urls.slice(0, this.config.maxUrls)
  }

  /**
   * Get assessment URLs
   */
  private async getAssessmentUrls(): Promise<SitemapUrl[]> {
    try {
      const { data: assessments, error } = await supabase
        .from('assessments')
        .select('slug, updated_at, view_count, is_featured')
        .eq('is_public', true)
        .not('slug', 'is', null)
        .order('view_count', { ascending: false })
        .limit(1000)

      if (error) throw error

      return (assessments || []).map(assessment => ({
        url: `${this.config.baseUrl}/avaliacoes/${assessment.slug}`,
        lastModified: assessment.updated_at,
        changeFrequency: 'weekly' as const,
        priority: this.calculateAssessmentPriority(assessment)
      }))
    } catch (error) {
      console.error('Error fetching assessment URLs:', error)
      return []
    }
  }

  /**
   * Get category URLs
   */
  private async getCategoryUrls(): Promise<SitemapUrl[]> {
    try {
      const { data: categories, error } = await supabase
        .from('public_categories')
        .select('slug, updated_at')
        .eq('is_active', true)

      if (error) throw error

      return (categories || []).map(category => ({
        url: `${this.config.baseUrl}/avaliacoes/categoria/${category.slug}`,
        lastModified: category.updated_at,
        changeFrequency: 'weekly' as const,
        priority: 0.7
      }))
    } catch (error) {
      console.error('Error fetching category URLs:', error)
      return []
    }
  }

  /**
   * Calculate priority for assessment based on metrics
   */
  private calculateAssessmentPriority(assessment: any): number {
    let priority = 0.6 // Base priority

    // Boost for featured assessments
    if (assessment.is_featured) {
      priority += 0.2
    }

    // Boost for popular assessments
    const viewCount = assessment.view_count || 0
    if (viewCount > 1000) {
      priority += 0.1
    } else if (viewCount > 100) {
      priority += 0.05
    }

    return Math.min(priority, 1.0)
  }

  /**
   * Generate XML from URLs
   */
  private generateXML(urls: SitemapUrl[]): string {
    const urlElements = urls.map(url => `
  <url>
    <loc>${this.escapeXml(url.url)}</loc>
    <lastmod>${url.lastModified.split('T')[0]}</lastmod>
    <changefreq>${url.changeFrequency}</changefreq>
    <priority>${url.priority.toFixed(1)}</priority>
  </url>`).join('')

    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${urlElements}
</urlset>`
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }

  /**
   * Update sitemap cache in database
   */
  async updateSitemapCache(): Promise<void> {
    try {
      const urls = await this.getAllUrls()

      // Clear existing cache
      await supabase
        .from('sitemap_cache')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all

      // Insert new URLs
      const cacheEntries = urls.map(url => ({
        url: url.url,
        last_modified: url.lastModified,
        change_frequency: url.changeFrequency,
        priority: url.priority,
        is_active: true
      }))

      if (cacheEntries.length > 0) {
        await supabase
          .from('sitemap_cache')
          .insert(cacheEntries)
      }
    } catch (error) {
      console.error('Error updating sitemap cache:', error)
      throw error
    }
  }

  /**
   * Get sitemap from cache
   */
  async getSitemapFromCache(): Promise<string | null> {
    try {
      const { data: urls, error } = await supabase
        .from('sitemap_cache')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: false })

      if (error) throw error

      if (!urls || urls.length === 0) {
        return null
      }

      const sitemapUrls: SitemapUrl[] = urls.map(url => ({
        url: url.url,
        lastModified: url.last_modified,
        changeFrequency: url.change_frequency as any,
        priority: url.priority
      }))

      return this.generateXML(sitemapUrls)
    } catch (error) {
      console.error('Error getting sitemap from cache:', error)
      return null
    }
  }
}

/**
 * Generate robots.txt content
 */
export const generateRobotsTxt = (baseUrl: string): string => {
  return `User-agent: *
Allow: /
Allow: /avaliacoes
Allow: /avaliacoes/*

# Disallow admin and app routes
Disallow: /app/
Disallow: /admin/
Disallow: /api/

# Disallow auth pages
Disallow: /login
Disallow: /register

# Allow sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay
Crawl-delay: 1`
}

/**
 * Generate structured data for assessment
 */
export const generateAssessmentStructuredData = (assessment: any) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'EducationalResource',
    name: assessment.titulo,
    description: assessment.seo_description || `Avaliação de ${assessment.disciplina} para ${assessment.serie}`,
    educationalLevel: assessment.serie,
    subject: assessment.disciplina,
    learningResourceType: 'Assessment',
    author: {
      '@type': 'Person',
      name: assessment.profiles?.nome || 'Atividade Pronta',
      affiliation: assessment.profiles?.escola
    },
    publisher: {
      '@type': 'Organization',
      name: 'Atividade Pronta',
      url: 'https://atvpronta.com.br',
      logo: {
        '@type': 'ImageObject',
        url: 'https://atvpronta.com.br/logo.png'
      }
    },
    datePublished: assessment.created_at,
    dateModified: assessment.updated_at,
    inLanguage: 'pt-BR',
    isAccessibleForFree: true,
    url: `https://atvpronta.com.br/avaliacoes/${assessment.slug}`,
    image: assessment.featured_image_url || 'https://atvpronta.com.br/og-assessment.png',
    keywords: assessment.seo_keywords?.join(', ') || `${assessment.disciplina}, ${assessment.serie}, avaliação, educação`,
    educationalUse: 'assessment',
    interactivityType: 'mixed',
    typicalAgeRange: getAgeRangeFromSerie(assessment.serie),
    difficulty: assessment.difficulty_level,
    timeRequired: assessment.estimated_duration ? `PT${assessment.estimated_duration}M` : undefined,
    aggregateRating: assessment.view_count > 0 ? {
      '@type': 'AggregateRating',
      ratingValue: Math.min(5, Math.max(3, Math.round((assessment.view_count / 100) * 10) / 10)),
      reviewCount: Math.max(1, Math.floor(assessment.view_count / 10)),
      bestRating: 5,
      worstRating: 1
    } : undefined
  }
}

/**
 * Generate structured data for category page
 */
export const generateCategoryStructuredData = (category: any, assessmentCount: number) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `Avaliações de ${category.name}`,
    description: category.description || `Encontre as melhores avaliações de ${category.name} para todos os níveis de ensino.`,
    url: `https://atvpronta.com.br/avaliacoes/categoria/${category.slug}`,
    mainEntity: {
      '@type': 'ItemList',
      name: `Avaliações de ${category.name}`,
      numberOfItems: assessmentCount,
      itemListElement: [] // Would be populated with actual assessments
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Início',
          item: 'https://atvpronta.com.br'
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Avaliações',
          item: 'https://atvpronta.com.br/avaliacoes'
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: category.name,
          item: `https://atvpronta.com.br/avaliacoes/categoria/${category.slug}`
        }
      ]
    }
  }
}

// Utility function to get age range from serie
function getAgeRangeFromSerie(serie: string): string {
  const serieMap: Record<string, string> = {
    '1º Ano': '6-7',
    '2º Ano': '7-8',
    '3º Ano': '8-9',
    '4º Ano': '9-10',
    '5º Ano': '10-11',
    '6º Ano': '11-12',
    '7º Ano': '12-13',
    '8º Ano': '13-14',
    '9º Ano': '14-15',
    '1ª Série': '15-16',
    '2ª Série': '16-17',
    '3ª Série': '17-18'
  }
  
  return serieMap[serie] || '6-18'
}
