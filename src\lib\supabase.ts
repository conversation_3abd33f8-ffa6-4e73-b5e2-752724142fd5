import { createClient } from '@supabase/supabase-js'
import { Database } from '../types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    // Adicionar headers para evitar problemas de CORS
    headers: {
      'X-Client-Info': 'eduassess-platform'
    }
  },
  db: {
    // Desabilitar cache para evitar problemas com RLS
    schema: 'public'
  }
})

// Adicionar função RPC para buscar perfil sem problemas de RLS
export const getProfileById = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()
    
    if (error) throw error
    return data
  } catch (error) {
    console.error('Error in getProfileById:', error)
    return null
  }
}