import { supabase } from '../lib/supabase'

export interface NotificationData {
  user_id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  action_url?: string
  action_label?: string
  metadata?: Record<string, any>
}

export const createNotification = async (data: NotificationData) => {
  try {
    const { data: result, error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: data.user_id,
        type: data.type,
        title: data.title,
        message: data.message,
        action_url: data.action_url,
        action_label: data.action_label,
        metadata: data.metadata || {},
        read: false
      })
      .select()
      .single()

    if (error) throw error
    return result
  } catch (error) {
    console.error('Error creating notification:', error)
    throw error
  }
}

export const createNotificationForAllUsers = async (data: Omit<NotificationData, 'user_id'>) => {
  try {
    // Get all user IDs
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')

    if (usersError) throw usersError

    // Create a notification for each user
    const promises = users?.map(user => 
      createNotification({
        ...data,
        user_id: user.id
      })
    ) || []

    await Promise.all(promises)
    return { success: true, count: users?.length || 0 }
  } catch (error) {
    console.error('Error creating notifications for all users:', error)
    throw error
  }
}

export const createNotificationForAdmins = async (data: Omit<NotificationData, 'user_id'>) => {
  try {
    // Get all admin user IDs
    const { data: admins, error: adminsError } = await supabase
      .from('profiles')
      .select('id')
      .eq('is_admin', true)

    if (adminsError) throw adminsError

    // Create a notification for each admin
    const promises = admins?.map(admin => 
      createNotification({
        ...data,
        user_id: admin.id
      })
    ) || []

    await Promise.all(promises)
    return { success: true, count: admins?.length || 0 }
  } catch (error) {
    console.error('Error creating notifications for admins:', error)
    throw error
  }
}