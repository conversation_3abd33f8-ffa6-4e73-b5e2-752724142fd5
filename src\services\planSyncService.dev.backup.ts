import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Plan = Database['public']['Tables']['plans']['Row']

export interface SyncResult {
  success: boolean
  stripe_product_id?: string
  stripe_price_id?: string
  message?: string
  error?: string
}

/**
 * VERSÃO DE DESENVOLVIMENTO - Simula sincronização com Stripe
 * Esta versão funciona sem a Edge Function para testes locais
 */

/**
 * Simula sincronização de um plano com o Stripe após criação
 */
export const syncPlanWithStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 [DEV] Simulando sincronização com Stripe:', plan.name)
    
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Gerar IDs fictícios do Stripe para desenvolvimento
    const mockStripeProductId = `prod_dev_${Date.now()}`
    const mockStripePriceId = `price_dev_${Date.now()}`
    
    // Atualizar o plano no Supabase com os IDs simulados
    const { error: updateError } = await supabase
      .from('plans')
      .update({
        stripe_product_id: mockStripeProductId,
        stripe_price_id: mockStripePriceId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', plan.id)

    if (updateError) {
      console.error('Erro ao atualizar plano:', updateError)
      throw updateError
    }

    console.log('✅ [DEV] Plano sincronizado com sucesso (simulado)')
    toast.success(`Plano ${plan.name} sincronizado com Stripe! (modo dev)`)
    
    return {
      success: true,
      stripe_product_id: mockStripeProductId,
      stripe_price_id: mockStripePriceId,
      message: 'Plan created and synced with Stripe successfully (development mode)'
    }
  } catch (error: any) {
    console.error('❌ [DEV] Erro ao sincronizar plano:', error)
    toast.error(`Erro ao sincronizar ${plan.name}: ${error.message}`)
    
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Simula atualização de um plano no Stripe após edição
 */
export const updatePlanInStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 [DEV] Simulando atualização no Stripe:', plan.name)
    
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Se o preço mudou, simular criação de novo price ID
    let newPriceId = plan.stripe_price_id
    if (Math.random() > 0.5) { // 50% chance de simular mudança de preço
      newPriceId = `price_dev_updated_${Date.now()}`
      
      // Atualizar no Supabase
      const { error: updateError } = await supabase
        .from('plans')
        .update({
          stripe_price_id: newPriceId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', plan.id)

      if (updateError) {
        console.error('Erro ao atualizar plano:', updateError)
        throw updateError
      }
    }

    console.log('✅ [DEV] Plano atualizado com sucesso (simulado)')
    toast.success(`Plano ${plan.name} atualizado no Stripe! (modo dev)`)
    
    return {
      success: true,
      stripe_price_id: newPriceId,
      message: newPriceId !== plan.stripe_price_id 
        ? 'Plan updated and new price created in Stripe (development mode)'
        : 'Plan updated in Stripe successfully (development mode)'
    }
  } catch (error: any) {
    console.error('❌ [DEV] Erro ao atualizar plano:', error)
    toast.error(`Erro ao atualizar ${plan.name}: ${error.message}`)
    
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Simula remoção/arquivamento de um plano no Stripe
 */
export const deletePlanFromStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 [DEV] Simulando arquivamento no Stripe:', plan.name)
    
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('✅ [DEV] Plano arquivado com sucesso (simulado)')
    toast.success(`Plano ${plan.name} arquivado no Stripe! (modo dev)`)
    
    return {
      success: true,
      message: 'Plan archived in Stripe successfully (development mode)'
    }
  } catch (error: any) {
    console.error('❌ [DEV] Erro ao arquivar plano:', error)
    toast.error(`Erro ao arquivar ${plan.name}: ${error.message}`)
    
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Valida se um plano pode ser sincronizado com Stripe
 */
export const validatePlanForSync = (plan: Partial<Plan>): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!plan.name || plan.name.trim() === '') {
    errors.push('Nome do plano é obrigatório')
  }

  if (plan.price === undefined || plan.price < 0) {
    errors.push('Preço deve ser maior ou igual a zero')
  }

  if (!plan.currency || !['BRL', 'USD', 'EUR'].includes(plan.currency)) {
    errors.push('Moeda deve ser BRL, USD ou EUR')
  }

  if (!plan.duration_months || ![1, 12].includes(plan.duration_months)) {
    errors.push('Duração deve ser 1 mês ou 12 meses')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Obtém status de sincronização de um plano
 */
export const getPlanSyncStatus = (plan: Plan): {
  isSynced: boolean
  hasProduct: boolean
  hasPrice: boolean
  canSync: boolean
} => {
  const hasProduct = !!plan.stripe_product_id
  const hasPrice = !!plan.stripe_price_id
  const isSynced = hasProduct && hasPrice
  const canSync = plan.name !== 'Gratuito' && plan.is_active

  return {
    isSynced,
    hasProduct,
    hasPrice,
    canSync
  }
}

/**
 * Formata preço para exibição
 */
export const formatPrice = (price: number, currency: string = 'BRL'): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency,
  }).format(price)
}

/**
 * Converte duração em meses para texto legível
 */
export const formatDuration = (months: number): string => {
  if (months === 1) return 'Mensal'
  if (months === 12) return 'Anual'
  return `${months} meses`
}

/**
 * Função para testar se a Edge Function está disponível
 */
export const testEdgeFunctionAvailability = async (): Promise<boolean> => {
  try {
    const response = await fetch(
      'https://wihmaklmjrylsqurtgwz.supabase.co/functions/v1/sync-plan-with-stripe',
      {
        method: 'OPTIONS',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
    
    return response.ok
  } catch (error) {
    console.log('Edge Function não disponível, usando modo de desenvolvimento')
    return false
  }
}

// Mostrar aviso sobre modo de desenvolvimento
console.log('🚧 Usando planSyncService em modo de DESENVOLVIMENTO')
console.log('   - Sincronização com Stripe é simulada')
console.log('   - IDs do Stripe são fictícios')
console.log('   - Para produção, use a Edge Function real')
