import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Plan = Database['public']['Tables']['plans']['Row']

export interface SyncResult {
  success: boolean
  stripe_product_id?: string
  stripe_price_id?: string
  message?: string
  error?: string
}

/**
 * Sincroniza um plano com o Stripe após criação
 */
export const syncPlanWithStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 Sincronizando plano com Stripe:', plan.name)
    
    const { data, error } = await supabase.functions.invoke('sync-plan-with-stripe', {
      body: {
        action: 'create',
        planData: plan
      }
    })

    if (error) {
      console.error('Erro na sincronização:', error)
      throw error
    }

    console.log('✅ Plano sincronizado com sucesso:', data)
    toast.success(`Plano ${plan.name} sincronizado com Stripe!`)
    
    return {
      success: true,
      stripe_product_id: data.stripe_product_id,
      stripe_price_id: data.stripe_price_id,
      message: data.message
    }
  } catch (error: any) {
    console.error('❌ Erro ao sincronizar plano:', error)

    // Melhor tratamento de erros baseado no tipo
    let errorMessage = 'Erro desconhecido';

    if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.error?.message) {
      errorMessage = error.error.message;
    }

    // Mensagens mais específicas baseadas no conteúdo do erro
    if (errorMessage.includes('Failed to send a request to the Edge Function')) {
      errorMessage = 'Falha na conexão com o servidor. Verifique sua conexão de internet.';
    } else if (errorMessage.includes('authorization')) {
      errorMessage = 'Erro de autorização. Faça login novamente.';
    } else if (errorMessage.includes('CORS')) {
      errorMessage = 'Erro de configuração do servidor. Contate o suporte.';
    }

    console.error('❌ Mensagem de erro processada:', errorMessage)
    toast.error(`Erro ao sincronizar ${plan.name} com Stripe: ${errorMessage}`)

    return {
      success: false,
      error: errorMessage
    }
  }
}

/**
 * Atualiza um plano no Stripe após edição
 */
export const updatePlanInStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 Atualizando plano no Stripe:', plan.name)

    const { data, error } = await supabase.functions.invoke('sync-plan-with-stripe', {
      body: {
        action: 'update',
        planData: plan
      }
    })

    if (error) {
      console.error('❌ Erro na atualização:', error)

      // Tentar extrair detalhes do erro da Edge Function
      if (error.context) {
        try {
          const errorBody = await error.context.text()
          const errorJson = JSON.parse(errorBody)
          if (errorJson.error) {
            throw new Error(errorJson.error)
          }
        } catch (parseError) {
          // Se não conseguir parsear, usar mensagem padrão
        }
      }

      throw error
    }

    console.log('✅ Plano atualizado com sucesso:', data)
    toast.success(`Plano ${plan.name} atualizado no Stripe!`)

    return {
      success: true,
      stripe_price_id: data.stripe_price_id,
      message: data.message
    }
  } catch (error: any) {
    console.error('❌ Erro ao atualizar plano:', error)

    // Melhor tratamento de erros baseado no tipo
    let errorMessage = 'Erro desconhecido';

    if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.error?.message) {
      errorMessage = error.error.message;
    }

    // Mensagens mais específicas baseadas no conteúdo do erro
    if (errorMessage.includes('Failed to send a request to the Edge Function')) {
      errorMessage = 'Falha na conexão com o servidor. Verifique sua conexão de internet.';
    } else if (errorMessage.includes('authorization')) {
      errorMessage = 'Erro de autorização. Faça login novamente.';
    } else if (errorMessage.includes('CORS')) {
      errorMessage = 'Erro de configuração do servidor. Contate o suporte.';
    }

    console.error('❌ Mensagem de erro processada:', errorMessage)
    toast.error(`Erro ao atualizar ${plan.name} no Stripe: ${errorMessage}`)

    return {
      success: false,
      error: errorMessage
    }
  }
}

/**
 * Remove/arquiva um plano no Stripe
 */
export const deletePlanFromStripe = async (plan: Plan): Promise<SyncResult> => {
  try {
    console.log('🔄 Arquivando plano no Stripe:', plan.name)
    
    const { data, error } = await supabase.functions.invoke('sync-plan-with-stripe', {
      body: {
        action: 'delete',
        planData: plan
      }
    })

    if (error) {
      console.error('Erro ao arquivar:', error)
      throw error
    }

    console.log('✅ Plano arquivado com sucesso:', data)
    toast.success(`Plano ${plan.name} arquivado no Stripe!`)
    
    return {
      success: true,
      message: data.message
    }
  } catch (error: any) {
    console.error('❌ Erro ao arquivar plano:', error)
    toast.error(`Erro ao arquivar ${plan.name} no Stripe: ${error.message}`)
    
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Valida se um plano pode ser sincronizado com Stripe
 */
export const validatePlanForSync = (plan: Partial<Plan>): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!plan.name || plan.name.trim() === '') {
    errors.push('Nome do plano é obrigatório')
  }

  if (plan.price === undefined || plan.price < 0) {
    errors.push('Preço deve ser maior ou igual a zero')
  }

  if (!plan.currency || !['BRL', 'USD', 'EUR'].includes(plan.currency)) {
    errors.push('Moeda deve ser BRL, USD ou EUR')
  }

  if (!plan.duration_months || ![1, 12].includes(plan.duration_months)) {
    errors.push('Duração deve ser 1 mês ou 12 meses')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Obtém status de sincronização de um plano
 */
export const getPlanSyncStatus = (plan: Plan): {
  isSynced: boolean
  hasProduct: boolean
  hasPrice: boolean
  canSync: boolean
} => {
  const hasProduct = !!plan.stripe_product_id
  const hasPrice = !!plan.stripe_price_id
  const isSynced = hasProduct && hasPrice
  const canSync = plan.name !== 'Gratuito' && plan.is_active

  return {
    isSynced,
    hasProduct,
    hasPrice,
    canSync
  }
}

/**
 * Formata preço para exibição
 */
export const formatPrice = (price: number, currency: string = 'BRL'): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency,
  }).format(price)
}

/**
 * Converte duração em meses para texto legível
 */
export const formatDuration = (months: number): string => {
  if (months === 1) return 'Mensal'
  if (months === 12) return 'Anual'
  return `${months} meses`
}
