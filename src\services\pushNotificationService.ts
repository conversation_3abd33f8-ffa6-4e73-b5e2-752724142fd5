export interface PushNotificationOptions {
  title: string
  body: string
  icon?: string
  badge?: string
  tag?: string
  requireInteraction?: boolean
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

class PushNotificationService {
  private permission: NotificationPermission = 'default'

  constructor() {
    this.permission = this.getPermission()
  }

  // Obter permissão atual
  getPermission(): NotificationPermission {
    if (!('Notification' in window)) {
      console.warn('Este navegador não suporta notificações push')
      return 'denied'
    }
    return Notification.permission
  }

  // Solicitar permissão
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied'
    }

    if (this.permission === 'granted') {
      return 'granted'
    }

    const permission = await Notification.requestPermission()
    this.permission = permission
    
    // Salvar preferência do usuário
    localStorage.setItem('push_notifications_permission', permission)
    
    return permission
  }

  // Verificar se notificações estão habilitadas
  isEnabled(): boolean {
    return this.permission === 'granted' && 
           localStorage.getItem('push_notifications_enabled') !== 'false'
  }

  // Enviar notificação
  async sendNotification(options: PushNotificationOptions): Promise<void> {
    if (!this.isEnabled()) {
      console.log('Notificações push não habilitadas')
      return
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/favicon.ico',
        badge: options.badge || '/favicon.ico',
        tag: options.tag || 'default',
        requireInteraction: options.requireInteraction || false,
        actions: options.actions || [],
        vibrate: [200, 100, 200], // Padrão de vibração
        silent: false
      })

      // Auto-fechar após 5 segundos se não for interativa
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close()
        }, 5000)
      }

      // Eventos da notificação
      notification.onclick = () => {
        window.focus()
        notification.close()
      }

      notification.onerror = (error) => {
        console.error('Erro na notificação:', error)
      }

    } catch (error) {
      console.error('Erro ao enviar notificação:', error)
    }
  }

  // Configurar preferências
  setEnabled(enabled: boolean): void {
    localStorage.setItem('push_notifications_enabled', enabled.toString())
  }

  // Verificar se áudio está habilitado
  isAudioEnabled(): boolean {
    return localStorage.getItem('notifications_audio') !== 'false'
  }

  // Configurar áudio
  setAudioEnabled(enabled: boolean): void {
    localStorage.setItem('notifications_audio', enabled.toString())
  }

  // Notificação para novos tipos específicos
  async notifyNewAssessment(title: string): Promise<void> {
    await this.sendNotification({
      title: '📝 Nova Avaliação',
      body: title,
      icon: '/favicon.ico',
      tag: 'assessment',
      actions: [
        { action: 'view', title: 'Ver Avaliação', icon: '/icons/view.png' },
        { action: 'dismiss', title: 'Dispensar' }
      ]
    })
  }

  async notifyNewQuestion(count: number): Promise<void> {
    await this.sendNotification({
      title: '❓ Novas Questões',
      body: `${count} nova${count > 1 ? 's' : ''} questão${count > 1 ? 'ões' : ''} disponível${count > 1 ? 'eis' : ''}`,
      icon: '/favicon.ico',
      tag: 'questions'
    })
  }

  async notifySystemUpdate(message: string): Promise<void> {
    await this.sendNotification({
      title: '🔄 Atualização do Sistema',
      body: message,
      icon: '/favicon.ico',
      tag: 'system',
      requireInteraction: true
    })
  }

  async notifySubscription(message: string): Promise<void> {
    await this.sendNotification({
      title: '💳 Assinatura',
      body: message,
      icon: '/favicon.ico',
      tag: 'subscription',
      requireInteraction: true
    })
  }
}

// Instância única
export const pushNotificationService = new PushNotificationService()
export default pushNotificationService 