import { loadStripe } from '@stripe/stripe-js'
import { supabase } from '../lib/supabase'

const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

// Allow development without Stripe key, but warn about it
if (!stripePublishableKey) {
  console.warn('⚠️ VITE_STRIPE_PUBLISHABLE_KEY is not set. Stripe functionality will be disabled.')
  console.warn('To enable Stripe, add your publishable key to your .env file.')
}

export const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null

export interface PricingPlan {
  id: string
  name: string
  price: number
  interval: 'month' | 'year'
  stripePriceId: string
  features: string[]
  limits: {
    questionsPerMonth: number
    assessmentsPerMonth: number
    templatesAccess: boolean
    aiGeneration: boolean
    collaboration: boolean
    analytics: boolean
  }
  popular?: boolean
  yearlyDiscount?: number // Percentage discount for yearly plans
  monthlyPrice?: number // Original monthly price for yearly plans
}

export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'gratuito',
    name: '<PERSON><PERSON>uit<PERSON>',
    price: 0,
    interval: 'month',
    stripePriceId: '',
    features: [
      '50 questões por mês',
      '5 avaliações por mês',
      'Templates básicos',
      'Suporte por email'
    ],
    limits: {
      questionsPerMonth: 50,
      assessmentsPerMonth: 5,
      templatesAccess: false,
      aiGeneration: false,
      collaboration: false,
      analytics: false
    }
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 29.90,
    interval: 'month',
    stripePriceId: 'price_1RgBHQE40rGVpnravhLnFEcK',
    features: [
      'Questões ilimitadas',
      'Avaliações ilimitadas',
      'Templates premium',
      'Geração por IA',
      'Colaboração',
      'Analytics avançados',
      'Suporte prioritário'
    ],
    limits: {
      questionsPerMonth: -1,
      assessmentsPerMonth: -1,
      templatesAccess: true,
      aiGeneration: true,
      collaboration: true,
      analytics: true
    },
    popular: true
  },
  {
    id: 'escolar',
    name: 'Escolar',
    price: 199.90,
    interval: 'month',
    stripePriceId: 'price_1RgCLfE40rGVpnrafFfvawtj', // Preço único - precisa criar um recorrente no Dashboard
    features: [
      'Tudo do Premium',
      'Até 50 professores',
      'Gestão centralizada',
      'Relatórios institucionais',
      'Treinamento incluído',
      'Suporte dedicado'
    ],
    limits: {
      questionsPerMonth: -1,
      assessmentsPerMonth: -1,
      templatesAccess: true,
      aiGeneration: true,
      collaboration: true,
      analytics: true
    }
  }
]

export const createCheckoutSession = async (priceId: string) => {
  if (!stripePublishableKey) {
    throw new Error('Stripe is not configured. Please add your Stripe publishable key to enable billing functionality.')
  }

  const { data, error } = await supabase.functions.invoke('create-checkout-session', {
    body: { priceId }
  })

  if (error) throw error
  return data
}

export const createPortalSession = async () => {
  if (!stripePublishableKey) {
    throw new Error('Stripe is not configured. Please add your Stripe publishable key to enable billing functionality.')
  }

  const { data, error } = await supabase.functions.invoke('create-portal-session')

  if (error) throw error
  return data
}

export const redirectToCheckout = async (priceId: string) => {
  try {
    if (!stripePublishableKey) {
      throw new Error('Stripe is not configured. Please add your Stripe publishable key to enable billing functionality.')
    }

    console.log('🚀 Iniciando checkout com Stripe:', { priceId, isTestMode: stripePublishableKey.startsWith('pk_test_') })
    
    const { sessionId } = await createCheckoutSession(priceId)
    const stripe = await stripePromise

    if (!stripe) {
      throw new Error('Stripe not loaded')
    }

    const { error } = await stripe.redirectToCheckout({ sessionId })

    if (error) {
      throw error
    }
  } catch (error) {
    console.error('Error redirecting to checkout:', error)
    throw error
  }
}

export const openBillingPortal = async () => {
  try {
    if (!stripePublishableKey) {
      throw new Error('Stripe is not configured. Please add your Stripe publishable key to enable billing functionality.')
    }

    console.log('🚀 Abrindo portal de cobrança:', { isTestMode: stripePublishableKey.startsWith('pk_test_') })
    
    const { url } = await createPortalSession()
    window.location.href = url
  } catch (error) {
    console.error('Error opening billing portal:', error)
    throw error
  }
}

export const isStripeConfigured = () => {
  return !!stripePublishableKey
}

export const isTestMode = () => {
  return stripePublishableKey?.startsWith('pk_test_') || false
}

