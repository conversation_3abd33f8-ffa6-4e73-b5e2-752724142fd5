export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          nome: string
          email: string
          escola: string | null
          disciplinas: string[]
          plano: 'gratuito' | 'premium' | 'escolar'
          avatar_url: string | null
          is_admin: boolean
          configuracoes: Record<string, any>
          estatisticas: Record<string, any>
          onboarding_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          nome: string
          email: string
          escola?: string | null
          disciplinas?: string[]
          plano?: 'gratuito' | 'premium' | 'escolar'
          avatar_url?: string | null
          is_admin?: boolean
          configuracoes?: Record<string, any>
          estatisticas?: Record<string, any>
          onboarding_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          nome?: string
          email?: string
          escola?: string | null
          disciplinas?: string[]
          plano?: 'gratuito' | 'premium' | 'escolar'
          avatar_url?: string | null
          is_admin?: boolean
          configuracoes?: Record<string, any>
          estatisticas?: Record<string, any>
          onboarding_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      questions: {
        Row: {
          id: string
          disciplina: string
          serie: string
          topico: string
          subtopico: string | null
          dificuldade: 'Fácil' | 'Médio' | 'Difícil'
          tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
          competencia_bncc: string | null
          enunciado: string
          alternativas: string[] | null
          resposta_correta: string
          explicacao: string
          imagem_url: string | null
          tags: string[]
          autor_id: string
          uso_count: number
          rating: number
          rating_count: number
          is_public: boolean
          is_verified: boolean
          metadata: Record<string, any>
          created_at: string
          updated_at: string
          status: 'pending' | 'approved' | 'rejected'
          is_shared_with_school: boolean
          school_id: string | null
          last_modified_at: string
        }
        Insert: {
          id?: string
          disciplina: string
          serie: string
          topico: string
          subtopico?: string | null
          dificuldade: 'Fácil' | 'Médio' | 'Difícil'
          tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
          competencia_bncc?: string | null
          enunciado: string
          alternativas?: string[] | null
          resposta_correta: string
          explicacao: string
          imagem_url?: string | null
          tags?: string[]
          autor_id: string
          uso_count?: number
          rating?: number
          rating_count?: number
          is_public?: boolean
          is_verified?: boolean
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
          status?: 'pending' | 'approved' | 'rejected'
          is_shared_with_school?: boolean
          school_id?: string | null
          last_modified_at?: string
        }
        Update: {
          id?: string
          disciplina?: string
          serie?: string
          topico?: string
          subtopico?: string | null
          dificuldade?: 'Fácil' | 'Médio' | 'Difícil'
          tipo?: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
          competencia_bncc?: string | null
          enunciado?: string
          alternativas?: string[] | null
          resposta_correta?: string
          explicacao?: string
          imagem_url?: string | null
          tags?: string[]
          autor_id?: string
          uso_count?: number
          rating?: number
          rating_count?: number
          is_public?: boolean
          is_verified?: boolean
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
          status?: 'pending' | 'approved' | 'rejected'
          is_shared_with_school?: boolean
          school_id?: string | null
          last_modified_at?: string
        }
      }
      assessments: {
        Row: {
          id: string
          titulo: string
          disciplina: string
          serie: string
          questoes_ids: string[]
          configuracao: Record<string, any>
          template_id: string | null
          autor_id: string
          is_public: boolean
          versoes: number
          estatisticas: Record<string, any>
          metadata: Record<string, any>
          created_at: string
          updated_at: string
          // SEO fields
          slug: string | null
          seo_title: string | null
          seo_description: string | null
          seo_keywords: string[]
          featured_image_url: string | null
          is_featured: boolean
          public_category: string | null
          difficulty_level: 'Fácil' | 'Médio' | 'Difícil' | null
          estimated_duration: number | null
          view_count: number
          download_count: number
          conversion_count: number
        }
        Insert: {
          id?: string
          titulo: string
          disciplina: string
          serie: string
          questoes_ids?: string[]
          configuracao: Record<string, any>
          template_id?: string | null
          autor_id: string
          is_public?: boolean
          versoes?: number
          estatisticas?: Record<string, any>
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
          // SEO fields
          slug?: string | null
          seo_title?: string | null
          seo_description?: string | null
          seo_keywords?: string[]
          featured_image_url?: string | null
          is_featured?: boolean
          public_category?: string | null
          difficulty_level?: 'Fácil' | 'Médio' | 'Difícil' | null
          estimated_duration?: number | null
          view_count?: number
          download_count?: number
          conversion_count?: number
        }
        Update: {
          id?: string
          titulo?: string
          disciplina?: string
          serie?: string
          questoes_ids?: string[]
          configuracao?: Record<string, any>
          template_id?: string | null
          autor_id?: string
          is_public?: boolean
          versoes?: number
          estatisticas?: Record<string, any>
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      templates: {
        Row: {
          id: string
          nome: string
          categoria: string
          descricao: string | null
          content: Record<string, any>
          metadata: Record<string, any>
          layout_config: Record<string, any>
          preview_image: string | null
          is_premium: boolean
          is_system: boolean
          autor_id: string | null
          rating: number
          rating_count: number
          downloads: number
          tags: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          nome: string
          categoria: string
          descricao?: string | null
          content?: Record<string, any>
          metadata?: Record<string, any>
          layout_config?: Record<string, any>
          preview_image?: string | null
          is_premium?: boolean
          is_system?: boolean
          autor_id?: string | null
          rating?: number
          rating_count?: number
          downloads?: number
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          nome?: string
          categoria?: string
          descricao?: string | null
          content?: Record<string, any>
          metadata?: Record<string, any>
          layout_config?: Record<string, any>
          preview_image?: string | null
          is_premium?: boolean
          is_system?: boolean
          autor_id?: string | null
          rating?: number
          rating_count?: number
          downloads?: number
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      favorites: {
        Row: {
          id: string
          user_id: string
          question_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          question_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          question_id?: string
          created_at?: string
        }
      }
      question_feedback: {
        Row: {
          id: string
          question_id: string
          user_id: string
          rating: number
          comment: string | null
          helpful: boolean | null
          suggestions: string[]
          feedback_type: 'rating' | 'improvement' | 'error' | 'general'
          is_approved: boolean
          is_reviewed: boolean
          admin_response: string | null
          reviewed_by: string | null
          reviewed_at: string | null
          ip_address: string | null
          user_agent: string | null
          metadata: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          question_id: string
          user_id: string
          rating: number
          comment?: string | null
          helpful?: boolean | null
          suggestions?: string[]
          feedback_type?: 'rating' | 'improvement' | 'error' | 'general'
          is_approved?: boolean
          is_reviewed?: boolean
          admin_response?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          question_id?: string
          user_id?: string
          rating?: number
          comment?: string | null
          helpful?: boolean | null
          suggestions?: string[]
          feedback_type?: 'rating' | 'improvement' | 'error' | 'general'
          is_approved?: boolean
          is_reviewed?: boolean
          admin_response?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      feedback_actions: {
        Row: {
          id: string
          feedback_id: string
          admin_id: string
          action_type: 'approved' | 'rejected' | 'flagged' | 'responded'
          reason: string | null
          previous_status: Record<string, any> | null
          new_status: Record<string, any> | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          feedback_id: string
          admin_id: string
          action_type: 'approved' | 'rejected' | 'flagged' | 'responded'
          reason?: string | null
          previous_status?: Record<string, any> | null
          new_status?: Record<string, any> | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          feedback_id?: string
          admin_id?: string
          action_type?: 'approved' | 'rejected' | 'flagged' | 'responded'
          reason?: string | null
          previous_status?: Record<string, any> | null
          new_status?: Record<string, any> | null
          notes?: string | null
          created_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          plano: string
          status: string
          current_period_start: string | null
          current_period_end: string | null
          cancel_at_period_end: boolean
          metadata: Record<string, any>
          created_at: string
          updated_at: string
          plan_id: string | null
          trial_start: string | null
          trial_end: string | null
          trial_status: string | null
        }
        Insert: {
          id?: string
          user_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          plano: string
          status: string
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
          plan_id?: string | null
          trial_start?: string | null
          trial_end?: string | null
          trial_status?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          plano?: string
          status?: string
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
          plan_id?: string | null
        }
      }
      usage_stats: {
        Row: {
          id: string
          user_id: string
          action_type: string
          resource_id: string | null
          resource_type: string | null
          metadata: Record<string, any>
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action_type: string
          resource_id?: string | null
          resource_type?: string | null
          metadata?: Record<string, any>
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action_type?: string
          resource_id?: string | null
          resource_type?: string | null
          metadata?: Record<string, any>
          created_at?: string
        }
      }
      admin_audit_log: {
        Row: {
          id: string
          admin_user_id: string | null
          action_type: string
          target_table: string | null
          target_id: string | null
          old_values: Record<string, any> | null
          new_values: Record<string, any> | null
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          admin_user_id?: string | null
          action_type: string
          target_table?: string | null
          target_id?: string | null
          old_values?: Record<string, any> | null
          new_values?: Record<string, any> | null
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          admin_user_id?: string | null
          action_type?: string
          target_table?: string | null
          target_id?: string | null
          old_values?: Record<string, any> | null
          new_values?: Record<string, any> | null
          description?: string | null
          created_at?: string
        }
      }
      system_settings: {
        Row: {
          id: string
          key: string
          value: Record<string, any>
          description: string | null
          updated_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          key: string
          value: Record<string, any>
          description?: string | null
          updated_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          key?: string
          value?: Record<string, any>
          description?: string | null
          updated_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      plans: {
        Row: {
          id: string
          name: string
          description: string | null
          price: number
          currency: string
          duration_months: number
          features: string[] | null
          is_active: boolean
          stripe_product_id: string | null
          stripe_price_id: string | null
          created_at: string
          updated_at: string
          max_teachers: number | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          price: number
          currency?: string
          duration_months: number
          features?: string[] | null
          is_active?: boolean
          stripe_product_id?: string | null
          stripe_price_id?: string | null
          created_at?: string
          updated_at?: string
          max_teachers?: number | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price?: number
          currency?: string
          duration_months?: number
          features?: string[] | null
          is_active?: boolean
          stripe_product_id?: string | null
          stripe_price_id?: string | null
          created_at?: string
          updated_at?: string
          max_teachers?: number | null
        }
      }
      schools: {
        Row: {
          id: string
          name: string
          cnpj: string | null
          contact_email: string
          contact_phone: string | null
          address: string | null
          plan_id: string | null
          max_teachers: number | null
          current_teachers_count: number | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          cnpj?: string | null
          contact_email: string
          contact_phone?: string | null
          address?: string | null
          plan_id?: string | null
          max_teachers?: number | null
          current_teachers_count?: number | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          cnpj?: string | null
          contact_email?: string
          contact_phone?: string | null
          address?: string | null
          plan_id?: string | null
          max_teachers?: number | null
          current_teachers_count?: number | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      public_assessment_conversions: {
        Row: {
          id: string
          assessment_id: string
          user_id: string | null
          conversion_type: 'signup' | 'upgrade' | 'download'
          source_page: string
          user_agent: string | null
          ip_address: string | null
          utm_source: string | null
          utm_medium: string | null
          utm_campaign: string | null
          utm_content: string | null
          utm_term: string | null
          metadata: Record<string, any>
          created_at: string
        }
        Insert: {
          id?: string
          assessment_id: string
          user_id?: string | null
          conversion_type: 'signup' | 'upgrade' | 'download'
          source_page: string
          user_agent?: string | null
          ip_address?: string | null
          utm_source?: string | null
          utm_medium?: string | null
          utm_campaign?: string | null
          utm_content?: string | null
          utm_term?: string | null
          metadata?: Record<string, any>
          created_at?: string
        }
        Update: {
          id?: string
          assessment_id?: string
          user_id?: string | null
          conversion_type?: 'signup' | 'upgrade' | 'download'
          source_page?: string
          user_agent?: string | null
          ip_address?: string | null
          utm_source?: string | null
          utm_medium?: string | null
          utm_campaign?: string | null
          utm_content?: string | null
          utm_term?: string | null
          metadata?: Record<string, any>
          created_at?: string
        }
      }
      public_categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          icon: string | null
          color: string | null
          sort_order: number
          is_active: boolean
          seo_title: string | null
          seo_description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          icon?: string | null
          color?: string | null
          sort_order?: number
          is_active?: boolean
          seo_title?: string | null
          seo_description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          icon?: string | null
          color?: string | null
          sort_order?: number
          is_active?: boolean
          seo_title?: string | null
          seo_description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      sitemap_cache: {
        Row: {
          id: string
          url: string
          last_modified: string
          change_frequency: string
          priority: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          url: string
          last_modified?: string
          change_frequency?: string
          priority?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          url?: string
          last_modified?: string
          change_frequency?: string
          priority?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}