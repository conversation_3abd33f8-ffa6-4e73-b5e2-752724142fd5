export interface User {
  id: string;
  nome: string;
  email: string;
  escola: string;
  disciplinas: string[];
  plano: 'gratuito' | 'premium';
  dataCadastro: string;
  configuracoes: UserSettings;
  estatisticas: UserStats;
}

export interface UserSettings {
  tema: 'claro' | 'escuro';
  layoutPreferido: string;
  notificacoes: boolean;
}

export interface UserStats {
  questoesCriadas: number;
  provasGeradas: number;
  ultimoAcesso: string;
}

export interface Question {
  id: string;
  disciplina: string;
  serie: string;
  topico: string;
  subtopico: string;
  dificuldade: 'Fácil' | 'Médio' | 'Difícil';
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso';
  competenciaBncc: string;
  enunciado: string;
  alternativas?: string[];
  respostaCorreta: string;
  explicacao: string;
  imagem?: string;
  tags: string[];
  autor: string;
  dataCriacao: string;
  usoCount: number;
  favorita?: boolean;
}

export interface Assessment {
  id: string;
  titulo: string;
  disciplina: string;
  serie: string;
  questoes: Question[];
  configuracaoLayout: LayoutConfig;
  dataCriacao: string;
  versoes: string[];
  estatisticasUso: AssessmentStats;
}

export interface LayoutConfig {
  cabecalho: {
    logoEscola?: string;
    nomeEscola: string;
    nomeProva: string;
    serie: string;
    data: string;
    instrucoes: string;
  };
  espacamento: 'compacto' | 'normal' | 'expandido';
  numeracao: 'automatica' | 'manual';
  quebrasPagina: boolean;
  folhaRespostas: boolean;
}

export interface AssessmentStats {
  vezesGerada: number;
  ultimaGeracao: string;
  avaliacaoMedia: number;
}

export interface Template {
  id: string;
  nome: string;
  categoria: string;
  layoutConfig: LayoutConfig;
  previewImage: string;
  premium: boolean;
  rating: number;
  downloads: number;
}

export interface FilterOptions {
  disciplina: string;
  serie: string;
  topico: string;
  dificuldade: string;
  tipo: string;
  competenciaBncc: string;
  tags: string[];
}