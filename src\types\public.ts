/**
 * Types for Public Assessments SEO System
 */

import { Database } from './database'

// Database table types
export type PublicAssessment = Database['public']['Tables']['assessments']['Row']
export type PublicAssessmentInsert = Database['public']['Tables']['assessments']['Insert']
export type PublicAssessmentUpdate = Database['public']['Tables']['assessments']['Update']

export type PublicCategory = Database['public']['Tables']['public_categories']['Row']
export type PublicCategoryInsert = Database['public']['Tables']['public_categories']['Insert']
export type PublicCategoryUpdate = Database['public']['Tables']['public_categories']['Update']

export type ConversionRecord = Database['public']['Tables']['public_assessment_conversions']['Row']
export type ConversionInsert = Database['public']['Tables']['public_assessment_conversions']['Insert']

export type SitemapEntry = Database['public']['Tables']['sitemap_cache']['Row']
export type SitemapInsert = Database['public']['Tables']['sitemap_cache']['Insert']

// Extended types for public assessments
export interface PublicAssessmentWithDetails extends PublicAssessment {
  questions?: PublicQuestion[]
  category?: PublicCategory
  author?: {
    nome: string
    escola?: string
  }
}

export interface PublicQuestion {
  id: string
  enunciado: string
  tipo: 'multipla_escolha' | 'verdadeiro_falso' | 'dissertativa'
  alternativas?: string[]
  disciplina: string
  serie: string
  dificuldade: 'Fácil' | 'Médio' | 'Difícil'
  tags?: string[]
}

// SEO and metadata types
export interface SEOMetadata {
  title: string
  description: string
  keywords: string[]
  canonicalUrl: string
  ogImage?: string
  ogType?: string
  twitterCard?: string
  schemaMarkup?: Record<string, any>
}

export interface BreadcrumbItem {
  label: string
  href: string
  current?: boolean
}

// Conversion tracking types
export type ConversionType = 'signup' | 'upgrade' | 'download'

export interface ConversionTrackingData {
  assessmentId: string
  userId?: string
  conversionType: ConversionType
  sourcePage: string
  userAgent?: string
  ipAddress?: string
  utmParams?: {
    source?: string
    medium?: string
    campaign?: string
    content?: string
    term?: string
  }
  metadata?: Record<string, any>
}

// Public assessment filters and search
export interface PublicAssessmentFilters {
  category?: string
  disciplina?: string
  serie?: string
  difficulty?: 'Fácil' | 'Médio' | 'Difícil'
  search?: string
  featured?: boolean
  sortBy?: 'recent' | 'popular' | 'title' | 'views'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface PublicAssessmentSearchResult {
  assessments: PublicAssessmentWithDetails[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  categories: PublicCategory[]
}

// Analytics and metrics
export interface PublicAssessmentMetrics {
  totalViews: number
  totalDownloads: number
  totalConversions: number
  conversionRate: number
  topCategories: Array<{
    category: string
    count: number
    conversionRate: number
  }>
  topAssessments: Array<{
    id: string
    title: string
    views: number
    conversions: number
    conversionRate: number
  }>
  trafficSources: Array<{
    source: string
    visits: number
    conversions: number
  }>
}

// URL generation and routing
export interface PublicRoutes {
  home: string
  assessmentList: string
  assessmentDetail: (slug: string) => string
  categoryPage: (categorySlug: string) => string
  searchResults: (query: string) => string
}

// Cache configuration
export interface CacheConfig {
  assessmentList: number // TTL in seconds
  assessmentDetail: number
  categories: number
  sitemap: number
}

// Sitemap generation
export interface SitemapUrl {
  url: string
  lastModified: string
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

export interface SitemapConfig {
  baseUrl: string
  defaultChangeFreq: SitemapUrl['changeFrequency']
  defaultPriority: number
  maxUrls: number
}

// Performance and optimization
export interface PerformanceMetrics {
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
  fcp: number // First Contentful Paint
}

export interface OptimizationConfig {
  enableLazyLoading: boolean
  enableImageOptimization: boolean
  enableCaching: boolean
  enableCompression: boolean
  maxImageSize: number
  cacheHeaders: Record<string, string>
}

// Error handling
export interface PublicError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
}

// API response types
export interface PublicAPIResponse<T = any> {
  data: T
  success: boolean
  error?: PublicError
  metadata?: {
    total?: number
    page?: number
    limit?: number
    hasMore?: boolean
  }
}

// Component props types
export interface PublicAssessmentCardProps {
  assessment: PublicAssessmentWithDetails
  showCategory?: boolean
  showAuthor?: boolean
  showStats?: boolean
  onView?: (assessment: PublicAssessmentWithDetails) => void
  onDownload?: (assessment: PublicAssessmentWithDetails) => void
}

export interface PublicCategoryCardProps {
  category: PublicCategory
  assessmentCount?: number
  onClick?: (category: PublicCategory) => void
}

export interface ConversionModalProps {
  isOpen: boolean
  onClose: () => void
  assessment: PublicAssessmentWithDetails
  conversionType: ConversionType
  onConvert: (data: ConversionTrackingData) => Promise<void>
}

// Hook return types
export interface UsePublicAssessmentsReturn {
  assessments: PublicAssessmentWithDetails[]
  loading: boolean
  error: PublicError | null
  total: number
  hasMore: boolean
  loadMore: () => void
  refetch: () => void
}

export interface UseConversionTrackingReturn {
  trackConversion: (data: ConversionTrackingData) => Promise<void>
  trackView: (assessmentId: string) => Promise<void>
  isTracking: boolean
  error: PublicError | null
}

export interface UseSEOMetadataReturn {
  metadata: SEOMetadata
  updateMetadata: (updates: Partial<SEOMetadata>) => void
  generateSchemaMarkup: (type: string, data: Record<string, any>) => Record<string, any>
}

// Constants
export const PUBLIC_ROUTES: PublicRoutes = {
  home: '/',
  assessmentList: '/avaliacoes',
  assessmentDetail: (slug: string) => `/avaliacoes/${slug}`,
  categoryPage: (categorySlug: string) => `/avaliacoes/categoria/${categorySlug}`,
  searchResults: (query: string) => `/avaliacoes?search=${encodeURIComponent(query)}`
}

export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  assessmentList: 300, // 5 minutes
  assessmentDetail: 3600, // 1 hour
  categories: 86400, // 1 day
  sitemap: 21600 // 6 hours
}

export const DEFAULT_SITEMAP_CONFIG: SitemapConfig = {
  baseUrl: 'https://atvpronta.com.br',
  defaultChangeFreq: 'weekly',
  defaultPriority: 0.5,
  maxUrls: 50000
}

export const PERFORMANCE_THRESHOLDS = {
  lcp: 2500, // Good: < 2.5s
  fid: 100,  // Good: < 100ms
  cls: 0.1,  // Good: < 0.1
  ttfb: 800, // Good: < 800ms
  fcp: 1800  // Good: < 1.8s
} as const
