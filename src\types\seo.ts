/**
 * Tipos TypeScript para o sistema de gerenciamento SEO
 */

// Configurações globais de SEO
export interface SEOGlobalSettings {
  id?: string
  site_title: string
  site_description: string
  global_keywords: string[]
  og_image_url: string
  robots_txt: string
  twitter_card_type: 'summary' | 'summary_large_image' | 'app' | 'player'
  facebook_app_id?: string
  google_site_verification?: string
  bing_site_verification?: string
  created_at?: string
  updated_at?: string
}

// Configurações de SEO por página
export interface SEOPageSettings {
  id?: string
  page_path: string
  page_name: string
  title?: string
  description?: string
  keywords?: string[]
  og_image_url?: string
  canonical_url?: string
  meta_robots: string
  priority: number
  change_frequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  is_active: boolean
  created_at?: string
  updated_at?: string
}

// Métricas de performance de página
export interface SEOPageMetrics {
  id?: string
  page_url: string
  lcp_score?: number
  fid_score?: number
  cls_score?: number
  fcp_score?: number
  ttfb_score?: number
  performance_score?: number
  seo_score?: number
  accessibility_score?: number
  best_practices_score?: number
  mobile_score?: number
  desktop_score?: number
  last_checked?: string
  created_at?: string
}

// Recomendações de SEO
export interface SEORecommendation {
  id?: string
  page_url: string
  recommendation_type: 'performance' | 'seo' | 'accessibility' | 'best_practices'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  impact_score?: number
  is_resolved: boolean
  resolved_at?: string
  created_at?: string
}

// Tracking de palavras-chave
export interface SEOKeywordTracking {
  id?: string
  keyword: string
  page_url: string
  search_volume?: number
  difficulty_score?: number
  current_position?: number
  previous_position?: number
  best_position?: number
  clicks: number
  impressions: number
  ctr?: number
  last_updated?: string
  created_at?: string
}

// Core Web Vitals
export interface WebVitalsMetrics {
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  fcp: number // First Contentful Paint
  ttfb: number // Time to First Byte
}

// Status de performance
export type PerformanceStatus = 'good' | 'needs-improvement' | 'poor'

// Dados de análise de página
export interface PageAnalysis {
  url: string
  title: string
  description: string
  keywords: string[]
  webVitals: WebVitalsMetrics
  performanceScore: number
  seoScore: number
  accessibilityScore: number
  bestPracticesScore: number
  recommendations: SEORecommendation[]
  lastAnalyzed: string
}

// Configurações de validação SEO
export interface SEOValidationRules {
  title: {
    minLength: number
    maxLength: number
    required: boolean
  }
  description: {
    minLength: number
    maxLength: number
    required: boolean
  }
  keywords: {
    minCount: number
    maxCount: number
    maxLength: number
  }
  ogImage: {
    required: boolean
    minWidth: number
    minHeight: number
    maxFileSize: number
  }
}

// Resultado de validação
export interface SEOValidationResult {
  field: string
  isValid: boolean
  message: string
  severity: 'error' | 'warning' | 'info'
}

// Dados para relatório SEO
export interface SEOReportData {
  period: {
    start: string
    end: string
  }
  overview: {
    totalPages: number
    averagePerformanceScore: number
    averageSEOScore: number
    totalRecommendations: number
    resolvedRecommendations: number
  }
  performance: {
    webVitals: WebVitalsMetrics
    scores: {
      performance: number
      seo: number
      accessibility: number
      bestPractices: number
    }
    trends: {
      date: string
      performance: number
      seo: number
    }[]
  }
  pages: PageAnalysis[]
  recommendations: SEORecommendation[]
  keywords: SEOKeywordTracking[]
}

// Configurações de exportação
export interface SEOExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  includeCharts: boolean
  includeTrends: boolean
  includeRecommendations: boolean
  dateRange: {
    start: string
    end: string
  }
  pages?: string[]
}

// Dados do dashboard SEO
export interface SEODashboardData {
  overview: {
    totalScore: number
    totalPages: number
    indexedPages: number
    crawlErrors: number
    averagePerformance: number
    trend: number
  }
  webVitals: {
    lcp: { value: number; status: PerformanceStatus }
    fid: { value: number; status: PerformanceStatus }
    cls: { value: number; status: PerformanceStatus }
  }
  topPages: {
    url: string
    title: string
    performanceScore: number
    seoScore: number
    traffic: number
  }[]
  recentRecommendations: SEORecommendation[]
  keywordPerformance: {
    keyword: string
    position: number
    change: number
    clicks: number
    impressions: number
  }[]
}

// Configurações de alerta SEO
export interface SEOAlertConfig {
  id?: string
  name: string
  type: 'performance' | 'seo' | 'accessibility' | 'crawl_error'
  threshold: number
  comparison: 'less_than' | 'greater_than' | 'equals'
  pages: string[]
  email_recipients: string[]
  is_active: boolean
  last_triggered?: string
  created_at?: string
}

// Dados de integração com APIs externas
export interface ExternalSEOData {
  googlePageSpeed?: {
    mobile: any
    desktop: any
  }
  googleSearchConsole?: {
    clicks: number
    impressions: number
    ctr: number
    position: number
    queries: {
      query: string
      clicks: number
      impressions: number
      ctr: number
      position: number
    }[]
  }
  lighthouse?: {
    performance: number
    accessibility: number
    bestPractices: number
    seo: number
    pwa: number
  }
}

// Configurações de API
export interface SEOAPIConfig {
  googlePageSpeedApiKey?: string
  googleSearchConsoleCredentials?: string
  enableRealTimeMonitoring: boolean
  monitoringInterval: number // em minutos
  alertThresholds: {
    performance: number
    seo: number
    accessibility: number
  }
}

// Constantes de validação
export const SEO_VALIDATION_RULES: SEOValidationRules = {
  title: {
    minLength: 30,
    maxLength: 60,
    required: true
  },
  description: {
    minLength: 120,
    maxLength: 160,
    required: true
  },
  keywords: {
    minCount: 3,
    maxCount: 10,
    maxLength: 50
  },
  ogImage: {
    required: true,
    minWidth: 1200,
    minHeight: 630,
    maxFileSize: 5 * 1024 * 1024 // 5MB
  }
}

// Thresholds para Core Web Vitals
export const WEB_VITALS_THRESHOLDS = {
  lcp: { good: 2.5, poor: 4.0 },
  fid: { good: 100, poor: 300 },
  cls: { good: 0.1, poor: 0.25 },
  fcp: { good: 1.8, poor: 3.0 },
  ttfb: { good: 800, poor: 1800 }
}

// Utilitários de tipo
export type SEOMetricType = keyof WebVitalsMetrics
export type SEOScoreType = 'performance' | 'seo' | 'accessibility' | 'best_practices'
export type SEORecommendationType = SEORecommendation['recommendation_type']
export type SEOPriorityType = SEORecommendation['priority']
