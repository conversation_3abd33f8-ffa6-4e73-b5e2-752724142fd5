// Template Management Types - Enhanced Structure

export interface HeaderConfig {
  nomeEscola: string
  nomeProva: string
  serie: string
  data: string
  instrucoes: string
  customHeaderImage?: string
  schoolLogo?: string
}

export interface FooterConfig {
  showPageNumbers: boolean
  customText?: string
  showDate: boolean
}

export interface TemplateStructure {
  includeHeader: boolean
  includeInstructions: boolean
  includeAnswerSheet: boolean
  pageBreaks: boolean
}

export interface TemplateQuestions {
  includeQuestions: boolean
  questionIds: string[]
  preserveOrder: boolean
  allowModification: boolean
}

export interface TemplateLayout {
  fontSize: 'small' | 'medium' | 'large'
  fontFamily?: 'helvetica' | 'times' | 'courier'
  lineSpacing: 'compact' | 'normal' | 'expanded'
  paperSize: 'A4' | 'Letter'
  orientation: 'portrait' | 'landscape'
}

export interface TemplateCustomization {
  headerConfig: HeaderConfig
  footerConfig?: FooterConfig
  watermark?: string
  customStyles?: Record<string, any>
}

export interface TemplateLegacy {
  migratedFrom?: string
  originalData?: Record<string, any>
  version?: string
}

export interface TemplateContent {
  structure: TemplateStructure
  questions: TemplateQuestions
  layout: TemplateLayout
  customization: TemplateCustomization
  legacy?: TemplateLegacy
}

export interface TemplateMetadata {
  version: string
  migrated?: boolean
  migratedAt?: string
  compatibility?: 'legacy' | 'current'
  createdWith?: 'manual' | 'assessment' | 'wizard'
  lastUsed?: string
  usageCount?: number
  tags?: string[]
}

// Template Creation Options
export interface TemplateCreationOptions {
  includeStructure: boolean
  includeQuestions: boolean
  includeLayout: boolean
  includeCustomization: boolean
  selectedQuestionIds?: string[]
  customName?: string
  customDescription?: string
  category?: string
}

// Template Application Options
export interface TemplateApplicationOptions {
  applyStructure: boolean
  applyQuestions: boolean
  applyLayout: boolean
  applyCustomization: boolean
  replaceExisting: boolean
  mergeWithCurrent: boolean
}

// Enhanced Template Types for Database
export interface EnhancedTemplate {
  id: string
  nome: string
  categoria: string
  descricao: string | null
  content: TemplateContent
  metadata: TemplateMetadata
  layout_config: Record<string, any> // Kept for backward compatibility
  preview_image: string | null
  is_premium: boolean
  is_system: boolean
  autor_id: string | null
  rating: number
  rating_count: number
  downloads: number
  tags: string[]
  created_at: string
  updated_at: string
}

// Template with Profile Information
export interface TemplateWithProfile extends EnhancedTemplate {
  profiles?: {
    nome: string
    email: string
  }
}

// Template Creation/Update DTOs
export interface CreateTemplateDTO {
  nome: string
  categoria: string
  descricao?: string
  content: TemplateContent
  metadata?: Partial<TemplateMetadata>
  is_premium?: boolean
  is_system?: boolean
  tags?: string[]
}

export interface UpdateTemplateDTO {
  nome?: string
  categoria?: string
  descricao?: string
  content?: TemplateContent
  metadata?: Partial<TemplateMetadata>
  preview_image?: string | null
  is_premium?: boolean
  is_system?: boolean
  tags?: string[]
}

// Template Validation
export interface TemplateValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Template Statistics
export interface TemplateStats {
  totalTemplates: number
  userTemplates: number
  systemTemplates: number
  premiumTemplates: number
  mostUsed: EnhancedTemplate[]
  recentlyCreated: EnhancedTemplate[]
}

// Export utility types
export type TemplateContentKeys = keyof TemplateContent
export type TemplateMetadataKeys = keyof TemplateMetadata
export type TemplateCreationStep = 'basic' | 'structure' | 'questions' | 'layout' | 'customization' | 'review'
