import { supabase } from '../lib/supabase'

export enum AnalyticsEvent {
  // Authentication
  USER_SIGNED_UP = 'user_signed_up',
  USER_SIGNED_IN = 'user_signed_in',
  USER_SIGNED_OUT = 'user_signed_out',
  
  // Questions
  QUESTION_CREATED = 'question_created',
  QUESTION_VIEWED = 'question_viewed',
  QUESTION_FAVORITED = 'question_favorited',
  QUESTION_ADDED_TO_ASSESSMENT = 'question_added_to_assessment',
  
  // Assessments
  ASSESSMENT_CREATED = 'assessment_created',
  ASSESSMENT_EDITED = 'assessment_edited',
  ASSESSMENT_PREVIEWED = 'assessment_previewed',
  PDF_GENERATED = 'pdf_generated',
  PDF_DOWNLOADED = 'pdf_downloaded',
  
  // Subscriptions
  SUBSCRIPTION_STARTED = 'subscription_started',
  SUBSCRIPTION_CANCELED = 'subscription_canceled',
  SUBSCRIPTION_RENEWED = 'subscription_renewed',
  
  // Search and Filters
  SEARCH_PERFORMED = 'search_performed',
  FILTER_APPLIED = 'filter_applied',
  
  // AI
  AI_QUESTION_GENERATED = 'ai_question_generated',
  AI_QUESTION_ACCEPTED = 'ai_question_accepted',
  AI_QUESTION_REJECTED = 'ai_question_rejected'
}

interface AnalyticsProperties {
  [key: string]: string | number | boolean | null | undefined
}

export const trackEvent = async (
  event: AnalyticsEvent,
  properties?: AnalyticsProperties
) => {
  try {
    // Google Analytics 4 (if available)
    if (typeof window !== 'undefined' && 'gtag' in window) {
      // @ts-ignore
      window.gtag('event', event, properties)
    }

    // Supabase Analytics (custom)
    const { data: { user } } = await supabase.auth.getUser()
    
    if (user) {
      await supabase.from('usage_stats').insert({
        user_id: user.id,
        action_type: event,
        resource_type: properties?.resource_type || null,
        resource_id: properties?.resource_id || null,
        metadata: properties || {}
      })
    }
  } catch (error) {
    console.error('Analytics error:', error)
  }
}

export const trackPageView = (page: string) => {
  trackEvent(AnalyticsEvent.SEARCH_PERFORMED, { page })
}

export const trackQuestionInteraction = (questionId: string, action: string) => {
  trackEvent(AnalyticsEvent.QUESTION_VIEWED, {
    resource_type: 'question',
    resource_id: questionId,
    action
  })
}

export const trackAssessmentAction = (assessmentId: string, action: string) => {
  const eventMap: Record<string, AnalyticsEvent> = {
    'created': AnalyticsEvent.ASSESSMENT_CREATED,
    'edited': AnalyticsEvent.ASSESSMENT_EDITED,
    'previewed': AnalyticsEvent.ASSESSMENT_PREVIEWED,
    'pdf_generated': AnalyticsEvent.PDF_GENERATED,
    'pdf_downloaded': AnalyticsEvent.PDF_DOWNLOADED
  }

  const event = eventMap[action] || AnalyticsEvent.ASSESSMENT_EDITED

  trackEvent(event, {
    resource_type: 'assessment',
    resource_id: assessmentId,
    action
  })
}