/**
 * Utilitários para processamento de imagens
 */

export interface ImageValidationResult {
  isValid: boolean
  error?: string
  dimensions?: { width: number; height: number }
}

export interface ImageProcessingOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
}

/**
 * Valida se um arquivo é uma imagem válida
 */
export const validateImageFile = (file: File): Promise<ImageValidationResult> => {
  return new Promise((resolve) => {
    // Verificar tipo MIME
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      resolve({
        isValid: false,
        error: 'Formato não suportado. Use PNG, JPG ou JPEG.'
      })
      return
    }

    // Verificar tamanho do arquivo (5MB máximo)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      resolve({
        isValid: false,
        error: 'Arquivo muito grande. Tamanho máximo: 5MB.'
      })
      return
    }

    // Verificar dimensões da imagem
    const img = new Image()
    const url = URL.createObjectURL(file)

    img.onload = () => {
      URL.revokeObjectURL(url)
      
      const { width, height } = img
      
      // Verificar dimensões mínimas
      if (width < 200 || height < 100) {
        resolve({
          isValid: false,
          error: 'Imagem muito pequena. Mínimo: 200x100 pixels.'
        })
        return
      }

      // Verificar dimensões máximas
      if (width > 3000 || height > 2000) {
        resolve({
          isValid: false,
          error: 'Imagem muito grande. Máximo: 3000x2000 pixels.'
        })
        return
      }

      resolve({
        isValid: true,
        dimensions: { width, height }
      })
    }

    img.onerror = () => {
      URL.revokeObjectURL(url)
      resolve({
        isValid: false,
        error: 'Arquivo de imagem corrompido ou inválido.'
      })
    }

    img.src = url
  })
}

/**
 * Redimensiona uma imagem mantendo a proporção
 */
export const resizeImage = (
  file: File, 
  options: ImageProcessingOptions = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1200,
      maxHeight = 800,
      quality = 0.8,
      format = 'jpeg'
    } = options

    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Não foi possível criar contexto do canvas'))
      return
    }

    img.onload = () => {
      const { width, height } = img

      // Calcular novas dimensões mantendo proporção
      let newWidth = width
      let newHeight = height

      if (width > maxWidth) {
        newWidth = maxWidth
        newHeight = (height * maxWidth) / width
      }

      if (newHeight > maxHeight) {
        newHeight = maxHeight
        newWidth = (width * maxHeight) / height
      }

      // Configurar canvas
      canvas.width = newWidth
      canvas.height = newHeight

      // Desenhar imagem redimensionada
      ctx.drawImage(img, 0, 0, newWidth, newHeight)

      // Converter para blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Erro ao processar imagem'))
          }
        },
        `image/${format}`,
        quality
      )
    }

    img.onerror = () => {
      reject(new Error('Erro ao carregar imagem'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Converte imagem para base64 para uso no PDF
 */
export const imageToBase64 = (file: File | Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new Error('Erro ao converter imagem para base64'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Erro ao ler arquivo'))
    }
    
    reader.readAsDataURL(file)
  })
}

/**
 * Calcula dimensões proporcionais para uma área específica
 */
export const calculateProportionalDimensions = (
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } => {
  const aspectRatio = originalWidth / originalHeight
  
  let width = maxWidth
  let height = maxWidth / aspectRatio
  
  if (height > maxHeight) {
    height = maxHeight
    width = maxHeight * aspectRatio
  }
  
  return { width, height }
}

/**
 * Gera nome único para arquivo
 */
export const generateUniqueFileName = (originalName: string): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()
  return `${timestamp}_${random}.${extension}`
}

/**
 * Formata tamanho de arquivo para exibição
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
