import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { Database } from '../types/database'

type Question = Database['public']['Tables']['questions']['Row']

interface TextBlock {
  id: string;
  type: 'text';
  content: string;
  style?: 'normal' | 'heading' | 'subheading' | 'instruction';
  textAlign?: 'left' | 'center' | 'right' | 'justify';
}

type AssessmentItem = Question | TextBlock; // Definir AssessmentItem aqui também para consistência

export interface PDFOptions {
  template?: string; // Tornar opcional, pois nem sempre é usado
  paperSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
  fontSize: 'small' | 'medium' | 'large';
  fontFamily?: 'helvetica' | 'times' | 'courier' | 'arial' | 'verdana' | 'georgia';
  lineSpacing: 'compact' | 'normal' | 'expanded';
  margins: { top: number; right: number; bottom: number; left: number };
  includeAnswerSheet: boolean;
  generateVersions: number;
  watermark?: string;
  qrCode?: boolean; // Tornar opcional
  addBorder?: boolean;
  headerConfig: {
    nomeEscola: string;
    nomeProva: string;
    serie: string;
    data: string;
    instrucoes: string;
    customization?: {
      customHeader?: {
        enabled: boolean;
        imageUrl?: string;
      };
      schoolLogo?: {
        enabled: boolean;
        imageUrl?: string;
      };
    };
  };
  showFooter?: boolean; // novo campo
}

export class PDFGenerator {
  private pdf: jsPDF;
  private options: PDFOptions;
  private pageHeight: number;
  private pageWidth: number;
  private currentY: number;
  private items: AssessmentItem[]; // Agora o gerador recebe a lista combinada e ordenada
  private fontSizes: {
    title: number;
    subtitle: number;
    normal: number;
    small: number;
  };
  private lineHeights: {
    compact: number;
    normal: number;
    expanded: number;
  };
  private fontFamily: string;

  constructor(items: AssessmentItem[], options: PDFOptions) {
    this.items = items; // Recebe a lista combinada
    this.options = options;

    this.pdf = new jsPDF({
      orientation: options.orientation,
      unit: 'mm',
      format: options.paperSize.toLowerCase() as 'a4' | 'letter'
    });

    this.pageHeight = this.pdf.internal.pageSize.height;
    this.pageWidth = this.pdf.internal.pageSize.width;
    this.currentY = options.margins.top;

    // Set font family - mapear para nomes corretos do jsPDF
    const fontMapping: Record<string, string> = {
      'helvetica': 'helvetica',
      'times': 'times',
      'courier': 'courier',
      'arial': 'helvetica', // Arial é similar ao Helvetica no jsPDF
      'verdana': 'helvetica', // Verdana é similar ao Helvetica no jsPDF
      'georgia': 'times' // Georgia é similar ao Times no jsPDF
    };

    this.fontFamily = fontMapping[options.fontFamily || 'times'] || 'times';

    // Set font sizes based on user preference
    this.fontSizes = {
      title: options.fontSize === 'small' ? 14 : options.fontSize === 'large' ? 18 : 16,
      subtitle: options.fontSize === 'small' ? 12 : options.fontSize === 'large' ? 16 : 14,
      normal: options.fontSize === 'small' ? 11 : options.fontSize === 'large' ? 14 : 12, // Questões: 11pt/12pt/14pt
      small: options.fontSize === 'small' ? 9 : options.fontSize === 'large' ? 11 : 10
    };

    // Set line heights based on user preference
    this.lineHeights = {
      compact: 1.1,
      normal: 1.3,
      expanded: 1.5
    };

    // Add watermark if specified
    if (options.watermark) {
      this.addWatermark(options.watermark);
    }
  }

  async generateAssessment(): Promise<Blob> {
    // Add border if enabled
    if (this.options.addBorder) {
      this.addBorder();
    }

    // Add header
    await this.addHeader();

    let questionNumber = 1; // Para numerar as questões

    // Iterate over the combined items list
    for (let i = 0; i < this.items.length; i++) {
      const item = this.items[i];

      if ('tipo' in item) { // É uma questão (Duck typing para Question)
        await this.addQuestion(item as Question, questionNumber);
        questionNumber++;
      } else if ('type' in item && item.type === 'text') { // É um bloco de texto
        this.addTextBlock(item as TextBlock);
      }
    }

    // Add answer sheet if requested
    if (this.options.includeAnswerSheet) {
      this.addAnswerSheet();
    }

    // Adicionar rodapé se showFooter for true (ou undefined, para manter compatibilidade)
    if (this.options.showFooter !== false) {
      this.addFooter();
    }

    // Generate multiple versions if requested
    if (this.options.generateVersions > 1) {
      return this.generateMultipleVersions();
    }

    return this.pdf.output('blob');
  }

  private addBorder() {
    const { margins } = this.options;

    // Definir espessura da borda (mais sutil)
    this.pdf.setLineWidth(0.2);
    this.pdf.setDrawColor(0, 0, 0); // Cor preta

    // Desenhar retângulo da borda
    this.pdf.rect(
      margins.left - 5, // x (com margem extra)
      margins.top - 5,  // y (com margem extra)
      this.pageWidth - margins.left - margins.right + 10, // width
      this.pageHeight - margins.top - margins.bottom + 10  // height
    );
  }

  private addWatermark(text: string) {
    const watermarkFontSize = 60;
    this.pdf.setTextColor(220, 220, 220); // Light gray
    this.pdf.setFontSize(watermarkFontSize);
    this.pdf.setFont(this.fontFamily, 'bold');

    // Save the current state
    this.pdf.saveGraphicsState();

    // Rotate and position the watermark
    const angle = -45;
    const x = this.pageWidth / 2;
    const y = this.pageHeight / 2;

    this.pdf.setGState(this.pdf.GState({ opacity: 0.2 }));
    this.pdf.text(text, x, y, {
      align: 'center',
      angle,
      renderingMode: 'fillThenStroke'
    });

    // Restore the state
    this.pdf.restoreGraphicsState();

    // Reset text color
    this.pdf.setTextColor(0, 0, 0);
  }

  private async addHeader() {
    const { margins, headerConfig } = this.options;
    const customization = headerConfig.customization;

    // Aumentar espaçamento superior para evitar proximidade com a borda
    this.currentY += 5; // Adicionar 5mm de espaço extra no topo

    // Se cabeçalho personalizado estiver ativo
    if (customization?.customHeader?.enabled && customization.customHeader.imageUrl) {
      await this.addCustomHeaderImage(customization.customHeader.imageUrl);
      return;
    }

    // Novo layout compacto: Logo à esquerda, informações à direita
    const hasLogo = customization?.schoolLogo?.enabled && customization.schoolLogo.imageUrl;
    const logoSize = 25; // Tamanho do logo (25mm x 25mm)
    const initialY = this.currentY;

    // Definir posições para layout de duas colunas
    const rightColumnX = hasLogo ? margins.left + logoSize + 10 : margins.left + 40; // 10mm de espaço após logo
    const lineHeight = 6; // Espaçamento compacto entre linhas

    // Adicionar logo da escola se habilitado (lado esquerdo)
    if (hasLogo) {
      await this.addSchoolLogo(customization.schoolLogo.imageUrl);
    }

    // Informações do lado direito (ou centralizadas se não há logo)
    this.currentY = initialY; // Resetar para alinhar com o logo
    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'bold');

    // 1. Nome da escola (lado direito, alinhado à esquerda)
    this.pdf.text(headerConfig.nomeEscola, rightColumnX, this.currentY);
    this.currentY += lineHeight;

    // 2. Nome do aluno (com linha)
    this.pdf.setFont(this.fontFamily, 'normal');
    this.pdf.text('Nome:', rightColumnX, this.currentY);
    // Desenhar linha para o nome
    const nameLineY = this.currentY + 1;
    this.pdf.setDrawColor(0, 0, 0);
    this.pdf.setLineWidth(0.3);
    this.pdf.line(rightColumnX + 20, nameLineY, this.pageWidth - margins.right, nameLineY);
    this.pdf.setTextColor(0, 0, 0);
    this.currentY += lineHeight;

    // 3. Série
    this.pdf.text(`Série: ${headerConfig.serie || '_______'}`, rightColumnX, this.currentY);
    this.currentY += lineHeight;

    // 4. Data
    this.pdf.text(`Data: ${headerConfig.data || '___/___/______'}`, rightColumnX, this.currentY);
    this.currentY += lineHeight;

    // Ajustar currentY para após o logo se necessário
    if (hasLogo) {
      this.currentY = Math.max(this.currentY, initialY + logoSize + 5);
    } else {
      this.currentY += 5; // Espaçamento adicional se não há logo
    }

    // 5. Instruções da prova (se existirem) - largura total
    if (headerConfig.instrucoes) {
      this.pdf.setFontSize(this.fontSizes.normal);
      this.pdf.setFont(this.fontFamily, 'italic');
      const lines = this.pdf.splitTextToSize(
        headerConfig.instrucoes,
        this.pageWidth - margins.left - margins.right
      );
      this.pdf.text(lines, margins.left, this.currentY);
      this.currentY += lines.length * 4 + 6;
    }

    // 6. Nome da avaliação (centralizado)
    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'bold');
    this.pdf.text(headerConfig.nomeProva, this.pageWidth / 2, this.currentY, { align: 'center' });
    this.currentY += 10;
  }

  private async addCustomHeaderImage(imageUrl: string) {
    try {
      const { margins } = this.options;

      // Carregar imagem
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const base64 = await this.blobToBase64(blob);

      // Calcular dimensões (largura total da página, altura proporcional)
      const maxWidth = this.pageWidth - margins.left - margins.right;
      const maxHeight = 60; // Altura máxima para cabeçalho personalizado

      // Detectar formato da imagem
      const format = blob.type.includes('png') ? 'PNG' : 'JPEG';

      // Adicionar imagem
      this.pdf.addImage(
        base64,
        format,
        margins.left,
        this.currentY,
        maxWidth,
        maxHeight,
        undefined,
        'FAST'
      );

      this.currentY += maxHeight + 10;

    } catch (error) {
      console.error('Erro ao adicionar cabeçalho personalizado:', error);
      // Fallback para cabeçalho padrão em caso de erro
      this.addDefaultHeader();
    }
  }

  private async addSchoolLogo(imageUrl: string): Promise<number> {
    try {
      const { margins } = this.options;

      // Carregar imagem
      const response = await fetch(imageUrl, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'image/*',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const base64 = await this.blobToBase64(blob);

      // Dimensões do logo (canto superior esquerdo)
      const logoSize = 25; // 25mm x 25mm

      // Detectar formato da imagem
      let format = 'JPEG'; // Default
      if (blob.type.includes('png')) {
        format = 'PNG';
      } else if (blob.type.includes('jpeg') || blob.type.includes('jpg')) {
        format = 'JPEG';
      }

      // Adicionar logo no canto superior esquerdo
      this.pdf.addImage(
        base64,
        format,
        margins.left,
        this.currentY,
        logoSize,
        logoSize,
        undefined,
        'FAST'
      );

      // Retornar altura do logo para ajuste de layout
      return logoSize + 5; // 5mm de margem

    } catch (error) {
      console.error('Erro ao adicionar logo da escola:', error);
      // Retornar 0 para continuar sem logo
      return 0;
    }
  }

  private addDefaultHeader() {
    const { margins, headerConfig } = this.options;

    // Aumentar espaçamento superior para consistência
    this.currentY += 5; // Adicionar 5mm de espaço extra no topo

    // Layout compacto sem logo - informações alinhadas à direita
    const rightColumnX = margins.left + 40; // Posição fixa para alinhamento
    const lineHeight = 6; // Espaçamento compacto entre linhas
    const initialY = this.currentY;

    // Informações do lado direito
    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'bold');

    // 1. Nome da escola (lado direito, alinhado à esquerda)
    this.pdf.text(headerConfig.nomeEscola, rightColumnX, this.currentY);
    this.currentY += lineHeight;

    // 2. Nome do aluno (com linha)
    this.pdf.setFont(this.fontFamily, 'normal');
    this.pdf.text('Nome:', rightColumnX, this.currentY);
    // Desenhar linha para o nome
    const nameLineY = this.currentY + 1;
    this.pdf.setDrawColor(0, 0, 0);
    this.pdf.setLineWidth(0.3);
    this.pdf.line(rightColumnX + 20, nameLineY, this.pageWidth - margins.right, nameLineY);
    this.pdf.setTextColor(0, 0, 0);
    this.currentY += lineHeight;

    // 3. Série
    this.pdf.text(`Série: ${headerConfig.serie || '_______'}`, rightColumnX, this.currentY);
    this.currentY += lineHeight;

    // 4. Data
    this.pdf.text(`Data: ${headerConfig.data || '___/___/______'}`, rightColumnX, this.currentY);
    this.currentY += lineHeight + 5; // Espaçamento adicional

    // 5. Instruções da prova (se existirem) - largura total
    if (headerConfig.instrucoes) {
      this.pdf.setFontSize(this.fontSizes.normal);
      this.pdf.setFont(this.fontFamily, 'italic');
      const lines = this.pdf.splitTextToSize(
        headerConfig.instrucoes,
        this.pageWidth - margins.left - margins.right
      );
      this.pdf.text(lines, margins.left, this.currentY);
      this.currentY += lines.length * 4 + 6;
    }

    // 6. Nome da avaliação (centralizado)
    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'bold');
    this.pdf.text(headerConfig.nomeProva, this.pageWidth / 2, this.currentY, { align: 'center' });
    this.currentY += 10;
  }

  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Resultado da leitura não é uma string'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Erro ao ler arquivo de imagem'));
      };

      reader.readAsDataURL(blob);
    });
  }

  private async addQuestion(question: Question, number: number) {
    const { margins } = this.options;
    const availableWidth = this.pageWidth - margins.left - margins.right;
    const lineHeight = this.lineHeights[this.options.lineSpacing];

    // Check if we need a new page
    if (this.currentY > this.pageHeight - 50) {
      this.pdf.addPage();
      this.currentY = margins.top;

      // Add watermark to new page if specified
      if (this.options.watermark) {
        this.addWatermark(this.options.watermark);
      }
    }

    // Add question number and content
    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'normal'); // Alterado de 'bold' para 'normal'
    const text = `Questão ${number}. ` + question.enunciado;
    const lines = this.pdf.splitTextToSize(text, availableWidth);
    this.pdf.text(lines, margins.left, this.currentY);
    
    // Corrigir cálculo da altura das linhas da questão
    const questionHeight = lines.length * this.fontSizes.normal * lineHeight / 3.5; // Ajustar divisor
    this.currentY += questionHeight;

    // Add alternatives if multiple choice
    if (question.tipo === 'multipla_escolha' && question.alternativas && question.alternativas.length > 0) {
      this.pdf.setFontSize(this.fontSizes.normal);
      this.pdf.setFont(this.fontFamily, 'normal');
      this.currentY += 3; // Spacing before alternatives (reduzido)
      
      for (let i = 0; i < question.alternativas.length; i++) {
        const altText = `${String.fromCharCode(97 + i)}) ${question.alternativas[i]}`;
        const altLines = this.pdf.splitTextToSize(altText, availableWidth - 10);

        // Calcular altura necessária para esta alternativa
        const altHeight = altLines.length * this.fontSizes.normal * lineHeight / 3.5;

        // Verificar se precisa de nova página
        if (this.currentY + altHeight > this.pageHeight - margins.bottom) {
          this.pdf.addPage();
          this.currentY = margins.top;
          if (this.options.watermark) {
            this.addWatermark(this.options.watermark);
          }
        }
        
        this.pdf.text(altLines, margins.left + 5, this.currentY);
        this.currentY += altHeight + 1; // Adicionar pequeno espaçamento entre alternativas (reduzido)
      }
      this.currentY += 3; // Spacing after alternatives (reduzido)
    } else if (question.tipo === 'verdadeiro_falso') {
      // Render V/F options
      const vfOptions = ['(  ) Verdadeiro', '(  ) Falso'];
      this.pdf.setFont(this.fontFamily, 'normal');
      this.pdf.setFontSize(this.fontSizes.normal);
      this.currentY += 3;

      vfOptions.forEach(opt => {
        const optHeight = this.fontSizes.normal * lineHeight / 3.5;
        if (this.currentY + optHeight > this.pageHeight - margins.bottom) {
          this.pdf.addPage();
          this.currentY = margins.top;
          if (this.options.watermark) this.addWatermark(this.options.watermark);
        }
        this.pdf.text(opt, margins.left + 5, this.currentY);
        this.currentY += optHeight + 1;
      });
      this.currentY += 3;
    } else if (question.tipo === 'dissertativa') {
      // Adicionar linhas para questões dissertativas
      this.pdf.setFont(this.fontFamily, 'normal');
      this.pdf.setFontSize(this.fontSizes.normal);
      this.currentY += 4; // Espaçamento reduzido antes das linhas (de 8 para 4)
      
      const lineSpacing = 8; // Espaçamento entre linhas
      const numberOfLines = 5; // Número de linhas para resposta
      const lineWidth = availableWidth; // Largura total disponível
      
      for (let i = 0; i < numberOfLines; i++) {
        // Verificar se precisa de nova página
        if (this.currentY + lineSpacing > this.pageHeight - margins.bottom) {
          this.pdf.addPage();
          this.currentY = margins.top;
          if (this.options.watermark) {
            this.addWatermark(this.options.watermark);
          }
        }
        
        // Desenhar linha ocupando toda a largura disponível
        this.pdf.setDrawColor(150, 150, 150); // Cor cinza clara
        this.pdf.line(
          margins.left,
          this.currentY + 2,
          margins.left + lineWidth,
          this.currentY + 2
        );
        
        this.currentY += lineSpacing;
      }
      
      this.currentY += 3; // Espaçamento reduzido após as linhas (de 5 para 3)
    } else {
      this.currentY += 10; // Spacing for other types
    }
    
    // Adicionar espaçamento entre questões (reduzido pela metade)
    this.currentY += 4;
  }

  private addTextBlock(textBlock: TextBlock) {
    const { margins } = this.options;
    const availableWidth = this.pageWidth - margins.left - margins.right;

    let fontSize;
    let fontStyle = 'normal';
    const lineHeightMultiplier = this.lineHeights[this.options.lineSpacing];
    let textColor = [0, 0, 0];

    switch (textBlock.style) {
      case 'heading':
        fontSize = this.fontSizes.title;
        fontStyle = 'bold';
        break;
      case 'subheading':
        fontSize = this.fontSizes.subtitle;
        fontStyle = 'bold';
        break;
      case 'instruction':
        fontSize = this.fontSizes.small;
        fontStyle = 'italic';
        textColor = [100, 100, 100];
        break;
      default: // normal
        fontSize = this.fontSizes.normal;
        break;
    }

    this.pdf.setFontSize(fontSize);
    this.pdf.setFont(this.fontFamily, fontStyle);
    this.pdf.setTextColor(textColor[0], textColor[1], textColor[2]);

    const lines = this.pdf.splitTextToSize(textBlock.content, availableWidth);
    const textHeight = lines.length * fontSize * lineHeightMultiplier;

    if (this.currentY + textHeight > this.pageHeight - margins.bottom) {
      this.pdf.addPage();
      this.currentY = margins.top;
      if (this.options.watermark) {
        this.addWatermark(this.options.watermark);
      }
    }

    let xPosition = margins.left;
    let align: 'left' | 'center' | 'right' | 'justify' = 'left';

    // Apply alignment based on textBlock.textAlign
    if (textBlock.textAlign) {
      align = textBlock.textAlign;
      switch (textBlock.textAlign) {
        case 'center':
          xPosition = this.pageWidth / 2;
          break;
        case 'right':
          xPosition = this.pageWidth - margins.right;
          break;
        case 'justify':
          xPosition = margins.left; // jsPDF's justify starts from left
          break;
        default: // left
          xPosition = margins.left;
          break;
      }
    }

    this.pdf.text(lines, xPosition, this.currentY, { align: align });
    this.currentY += textHeight + 5; // Add spacing after the block
  }

  private addAnswerSheet() {
    this.pdf.addPage();
    this.currentY = this.options.margins.top;
    if (this.options.watermark) {
      this.addWatermark(this.options.watermark);
    }

    this.pdf.setFontSize(this.fontSizes.title);
    this.pdf.setFont(this.fontFamily, 'bold');
    this.pdf.text('Folha de Respostas', this.pageWidth / 2, this.currentY, { align: 'center' });
    this.currentY += 15;

    this.pdf.setFontSize(this.fontSizes.normal);
    this.pdf.setFont(this.fontFamily, 'normal');
    this.pdf.text(`Nome: ${'_'.repeat(50)}`, this.options.margins.left, this.currentY);
    this.currentY += 10;

    const questionsOnly = this.items.filter(item => 'tipo' in item) as Question[];

    const columns = 5;
    const colWidth = (this.pageWidth - this.options.margins.left - this.options.margins.right) / columns;
    const startX = this.options.margins.left;
    let currentX = startX;

    for (let i = 0; i < questionsOnly.length; i++) {
      const question = questionsOnly[i];
      const row = Math.floor(i / columns);
      const col = i % columns;

      currentX = startX + (col * colWidth);
      let yPos = this.currentY + (row * 10); // 10mm per row

      if (yPos > this.pageHeight - this.options.margins.bottom - 10) { // Check for new page
        this.pdf.addPage();
        this.currentY = this.options.margins.top;
        if (this.options.watermark) {
          this.addWatermark(this.options.watermark);
        }
        yPos = this.currentY + (row * 10);
      }

      this.pdf.text(`${i + 1}. ( ) ( ) ( ) ( )`, currentX, yPos); // A B C D placeholders
    }
  }

  private addFooter() {
    // Adiciona o rodapé na última página
    const footerText = 'Gerado pela plataforma EduAssess';
    const y = this.pageHeight - 10;
    this.pdf.setFontSize(this.fontSizes.small);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setTextColor(150, 150, 150);
    this.pdf.text(footerText, this.pageWidth / 2, y, { align: 'center' });
    this.pdf.setTextColor(0, 0, 0);
  }

  // This method is for generating multiple versions of the PDF (e.g., shuffling questions/alternatives)
  // For now, it will just return the current PDF blob, as the shuffling logic is not implemented.
  private async generateMultipleVersions(): Promise<Blob> {
    // In a real scenario, this would involve cloning the PDF,
    // re-arranging questions/alternatives, and re-rendering.
    // For simplicity, we just return the single version for now.
    console.warn("Multiple versions generation is not fully implemented. Returning single version.");
    return this.pdf.output('blob');
  }
}

export const generatePDF = async (
  items: AssessmentItem[], // Agora aceita AssessmentItem[]
  options: Partial<PDFOptions> = {}
): Promise<Blob> => {
  const defaultOptions: PDFOptions = {
    paperSize: 'A4',
    orientation: 'portrait',
    fontSize: 'medium',
    lineSpacing: 'normal',
    margins: { top: 20, right: 20, bottom: 20, left: 20 },
    includeAnswerSheet: true,
    generateVersions: 1,
    qrCode: false,
    headerConfig: {
      nomeEscola: '',
      nomeProva: '',
      serie: '',
      data: '',
      instrucoes: ''
    },
    showFooter: true
  };

  const mergedOptions: PDFOptions = { ...defaultOptions, ...options };

  // Passar a lista combinada e ordenada diretamente para o construtor do PDFGenerator
  const generator = new PDFGenerator(items, mergedOptions);

  return generator.generateAssessment();
};

export const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};