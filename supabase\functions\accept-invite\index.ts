import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
// Configura os cabeçalhos CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
Deno.serve(async (req)=>{
  // Trata requisições OPTIONS (preflight CORS)
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  // Altere esta linha para usar SUPABASE_SERVICE_ROLE_KEY
  const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''); 

  try {
    const { token } = await req.json();
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Missing invite token'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    // 1. Validar o token e buscar o convite
    // Esta consulta agora ignorará as políticas RLS
    const { data: invite, error: inviteError } = await supabaseClient.from('school_invites').select('*').eq('invite_token', token).single();
    
    if (inviteError || !invite) {
      console.error('Error fetching invite or invite not found:', inviteError);
      return new Response(JSON.stringify({
        error: 'Invalid or expired invite token.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    // Verificar se o convite já foi aceito ou expirou
    if (invite.is_accepted || (invite.expires_at && new Date(invite.expires_at) < new Date())) { // Adicionado check para invite.expires_at ser não nulo
      return new Response(JSON.stringify({
        error: 'Invite already accepted or expired.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    // 2. Obter o usuário autenticado que está tentando aceitar o convite
    // Mantenha esta parte para obter o usuário logado para quem o perfil será atualizado
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
        req.headers.get('Authorization')?.replace('Bearer ', '') // Passa o token do cabeçalho da requisição
    );
    if (authError || !user) {
      console.error('Error getting authenticated user:', authError);
      return new Response(JSON.stringify({
        error: 'User not authenticated. Please log in first.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }
    // Verificar se o email do usuário autenticado corresponde ao email do convite
    if (user.email !== invite.email) {
      return new Response(JSON.stringify({
        error: 'Authenticated user email does not match invite email.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 403
      });
    }
    // 3. Atualizar o perfil do usuário com o school_id E o plano 'escolar'
    const { error: profileUpdateError } = await supabaseClient.from('profiles').update({
      school_id: invite.school_id,
      plano: 'escolar'
    }).eq('id', user.id);
    if (profileUpdateError) {
      console.error('Error updating user profile with school_id and plano:', profileUpdateError);
      return new Response(JSON.stringify({
        error: 'Failed to update user profile.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    // Marcar o convite como aceito
    const { error: inviteAcceptError } = await supabaseClient.from('school_invites').update({
      is_accepted: true,
      accepted_at: new Date().toISOString(),
      invited_by_user_id: user.id // Adicionado para registrar quem aceitou o convite
    }).eq('id', invite.id);
    if (inviteAcceptError) {
      console.error('Error marking invite as accepted:', inviteAcceptError);
    // Decida se você quer lançar um erro aqui ou apenas logar. O perfil já foi atualizado.
    }
    return new Response(JSON.stringify({
      message: 'Invite accepted successfully!'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('Error in accept-invite function:', error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
}); 