import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// LLM Provider Configuration
interface LLMProvider {
  name: string
  endpoint: string
  model: string
  apiKeyEnv: string
  maxTokens?: number
  temperature: number
  costPerToken?: number
}

// Dynamic provider loading from database
async function loadProviderSettings(supabase: any): Promise<LLMProvider[]> {
  try {
    const { data: providers, error } = await supabase
      .from('ai_provider_settings')
      .select('*')
      .eq('is_enabled', true)
      .order('admin_override_priority', { ascending: true, nullsFirst: false })
      .order('priority', { ascending: true })

    if (error) throw error

    return providers.map((p: any) => ({
      name: p.provider_name,
      endpoint: getProviderEndpoint(p.provider_name),
      model: p.model_name,
      apiKeyEnv: getProviderApiKeyEnv(p.provider_name),
      maxTokens: p.settings?.max_tokens || 4000,
      temperature: p.settings?.temperature || 0.7,
      costPerToken: p.cost_per_token,
      isDefault: p.is_default
    }))
  } catch (error) {
    console.error('Error loading provider settings:', error)
    // Fallback to static configuration
    return [
      {
        name: 'anthropic',
        endpoint: 'https://api.anthropic.com/v1/messages',
        model: 'claude-3-5-sonnet-20241022',
        apiKeyEnv: 'ANTHROPIC_API_KEY',
        maxTokens: 4000,
        temperature: 0.7,
        costPerToken: 0.000003
      },
      {
        name: 'openai',
        endpoint: 'https://api.openai.com/v1/chat/completions',
        model: 'gpt-4o-2024-11-20',
        apiKeyEnv: 'OPENAI_API_KEY',
        temperature: 0.7,
        costPerToken: 0.0000025
      },
      {
        name: 'google',
        endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent',
        model: 'gemini-1.5-pro',
        apiKeyEnv: 'GOOGLE_API_KEY',
        temperature: 0.7,
        costPerToken: 0.00000125
      }
    ]
  }
}

function getProviderEndpoint(providerName: string): string {
  switch (providerName.toLowerCase()) {
    case 'openai':
    case 'openai-gpt4o-mini':
      return 'https://api.openai.com/v1/chat/completions'
    case 'anthropic':
    case 'claude-3-5-haiku':
    case 'claude-sonnet-4':
      return 'https://api.anthropic.com/v1/messages'
    case 'google':
    case 'gemini-2-0-flash':
      return 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent'
    case 'gemini-2-5-pro':
      return 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent'
    case 'gemini-2-5-flash':
      return 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent'
    case 'cohere-command-r-plus':
      return 'https://api.cohere.ai/v1/chat'
    default:
      return 'https://api.openai.com/v1/chat/completions'
  }
}

function getProviderApiKeyEnv(providerName: string): string {
  switch (providerName.toLowerCase()) {
    case 'openai':
    case 'openai-gpt4o-mini':
      return 'OPENAI_API_KEY'
    case 'anthropic':
    case 'claude-3-5-haiku':
    case 'claude-sonnet-4':
      return 'ANTHROPIC_API_KEY'
    case 'google':
    case 'gemini-2-0-flash':
    case 'gemini-2-5-pro':
    case 'gemini-2-5-flash':
      return 'GOOGLE_API_KEY'
    case 'cohere-command-r-plus':
      return 'COHERE_API_KEY'
    default:
      return 'OPENAI_API_KEY'
  }
}

interface GenerationResult {
  success: boolean
  questions?: any[]
  provider?: string
  error?: string
  tokensUsed?: number
  cost?: number
}

interface RateLimitInfo {
  requests: number
  windowStart: number
  isBlocked: boolean
}

// Enhanced prompt engineering for better question quality
const buildPrompt = (params: any): string => {
  const questionTypeInstructions = {
    'multipla_escolha': `
      - Create exactly 4 alternatives (a, b, c, d)
      - Only ONE alternative should be correct
      - Make distractors plausible but clearly incorrect
      - Avoid "all of the above" or "none of the above" options`,
    'verdadeiro_falso': `
      - Create clear, unambiguous statements
      - Avoid double negatives or complex conditional statements
      - Ensure the statement can be definitively true or false`,
    'dissertativa': `
      - Create questions that require analytical thinking
      - Include specific criteria for evaluation
      - Provide detailed rubric in the explanation`
  }

  return `You are an expert Brazilian educator creating high-quality educational questions.

CONTEXT:
- Subject: ${params.disciplina}
- Grade Level: ${params.serie}
- Topic: ${params.topico}
- Difficulty: ${params.dificuldade}
- Question Type: ${params.tipo}
${params.subtopico ? `- Subtopic: ${params.subtopico}` : ''}
${params.competencia_bncc ? `- BNCC Competency: ${params.competencia_bncc}` : ''}
${params.contexto ? `- Additional Context: ${params.contexto}` : ''}

REQUIREMENTS:
- Generate exactly ${params.quantidade} questions
- Follow Brazilian educational standards (BNCC)
- Use appropriate language for ${params.serie} students
- Ensure questions are pedagogically sound
${questionTypeInstructions[params.tipo] || ''}

QUALITY STANDARDS:
- Questions must be clear and unambiguous
- Avoid cultural bias or assumptions
- Include detailed explanations that help learning
- Align with specified difficulty level
- Reference relevant BNCC competencies when applicable

OUTPUT FORMAT:
Return a JSON array with exactly ${params.quantidade} question objects. Each object must have this exact structure:
{
  "id": "unique-identifier",
  "enunciado": "clear question text",
  "alternativas": ["option a", "option b", "option c", "option d"], // only for multiple choice
  "resposta_correta": "correct answer (letter for multiple choice, true/false for boolean, full answer for essay)",
  "explicacao": "detailed pedagogical explanation of the correct answer and why other options are incorrect",
  "competencia_bncc": "relevant BNCC competency code",
  "confidence": 0.95 // confidence score between 0.8 and 1.0
}

Generate the questions now:`
}

// Provider-specific API call functions
async function callOpenAI(prompt: string, provider: LLMProvider): Promise<GenerationResult> {
  const apiKey = Deno.env.get(provider.apiKeyEnv)
  if (!apiKey) {
    return { success: false, error: 'OpenAI API key not configured' }
  }

  try {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: provider.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: provider.temperature,
        max_tokens: provider.maxTokens || 4000,
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return {
        success: false,
        error: `OpenAI API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`
      }
    }

    const data = await response.json()
    const content = data.choices[0].message.content
    const tokensUsed = data.usage?.total_tokens || 0

    return {
      success: true,
      questions: parseQuestionResponse(content),
      provider: provider.name,
      tokensUsed,
      cost: tokensUsed * (provider.costPerToken || 0)
    }
  } catch (error) {
    return { success: false, error: `OpenAI request failed: ${error.message}` }
  }
}

async function callAnthropic(prompt: string, provider: LLMProvider): Promise<GenerationResult> {
  const apiKey = Deno.env.get(provider.apiKeyEnv)
  if (!apiKey) {
    return { success: false, error: 'Anthropic API key not configured' }
  }

  try {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: provider.model,
        max_tokens: provider.maxTokens || 4000,
        temperature: provider.temperature,
        messages: [{ role: 'user', content: prompt }]
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return {
        success: false,
        error: `Anthropic API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`
      }
    }

    const data = await response.json()
    const content = data.content[0].text
    const tokensUsed = data.usage?.input_tokens + data.usage?.output_tokens || 0

    return {
      success: true,
      questions: parseQuestionResponse(content),
      provider: provider.name,
      tokensUsed,
      cost: tokensUsed * (provider.costPerToken || 0)
    }
  } catch (error) {
    return { success: false, error: `Anthropic request failed: ${error.message}` }
  }
}

async function callGoogleGemini(prompt: string, provider: LLMProvider): Promise<GenerationResult> {
  const apiKey = Deno.env.get(provider.apiKeyEnv)
  if (!apiKey) {
    return { success: false, error: 'Google API key not configured' }
  }

  try {
    // Enhanced configuration for 2025 Gemini models
    const generationConfig: any = {
      temperature: provider.temperature,
      maxOutputTokens: provider.maxTokens || 4000,
    }

    // Add thinking mode for Gemini 2.5 models
    if (provider.name.toLowerCase().includes('gemini-2-5')) {
      generationConfig.responseLogprobs = true
      generationConfig.logprobs = 5
    }

    const response = await fetch(`${provider.endpoint}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig,
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return {
        success: false,
        error: `Google API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`
      }
    }

    const data = await response.json()
    const content = data.candidates[0].content.parts[0].text
    const tokensUsed = data.usageMetadata?.totalTokenCount || 0

    return {
      success: true,
      questions: parseQuestionResponse(content),
      provider: provider.name,
      tokensUsed,
      cost: tokensUsed * (provider.costPerToken || 0)
    }
  } catch (error) {
    return { success: false, error: `Google request failed: ${error.message}` }
  }
}

async function callCohere(prompt: string, provider: LLMProvider): Promise<GenerationResult> {
  const apiKey = Deno.env.get(provider.apiKeyEnv)
  if (!apiKey) {
    return { success: false, error: 'Cohere API key not configured' }
  }

  try {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: provider.model,
        message: prompt,
        temperature: provider.temperature,
        max_tokens: provider.maxTokens || 4000,
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return {
        success: false,
        error: `Cohere API error: ${response.status} ${response.statusText} - ${errorData.message || 'Unknown error'}`
      }
    }

    const data = await response.json()
    const content = data.text
    const tokensUsed = data.meta?.tokens?.input_tokens + data.meta?.tokens?.output_tokens || 0

    return {
      success: true,
      questions: parseQuestionResponse(content),
      provider: provider.name,
      tokensUsed,
      cost: tokensUsed * (provider.costPerToken || 0)
    }
  } catch (error) {
    return { success: false, error: `Cohere request failed: ${error.message}` }
  }
}

// Parse question response from any provider
function parseQuestionResponse(content: string): any[] {
  try {
    // Try to extract JSON from markdown code blocks or direct JSON
    const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) ||
                     content.match(/```\n([\s\S]*?)\n```/) ||
                     content.match(/\[[\s\S]*\]/)

    const jsonContent = jsonMatch ? jsonMatch[1] || jsonMatch[0] : content
    const parsed = JSON.parse(jsonContent.trim())

    // Ensure it's an array
    return Array.isArray(parsed) ? parsed : [parsed]
  } catch (error) {
    console.error('Error parsing question response:', error)
    throw new Error('Failed to parse AI-generated questions. Response format was invalid.')
  }
}

// Server-side rate limiting
async function checkRateLimit(supabase: any, userId: string): Promise<{ allowed: boolean, error?: string }> {
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = 10 // 10 requests per minute
  const windowStart = new Date(Date.now() - windowMs)

  try {
    const { data: requests, error } = await supabase
      .from('ai_generation_logs')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', windowStart.toISOString())

    if (error) throw error

    const requestCount = requests?.length || 0

    if (requestCount >= maxRequests) {
      return {
        allowed: false,
        error: `Rate limit exceeded. Maximum ${maxRequests} requests per minute. Try again in ${Math.ceil(windowMs / 1000)} seconds.`
      }
    }

    return { allowed: true }
  } catch (error) {
    console.error('Rate limit check failed:', error)
    return { allowed: true } // Allow on error to avoid blocking users
  }
}

// Intelligent caching system
async function checkCache(supabase: any, cacheKey: string): Promise<any[] | null> {
  const cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours
  const expiryTime = new Date(Date.now() - cacheExpiryMs)

  try {
    const { data: cached, error } = await supabase
      .from('question_cache')
      .select('questions')
      .eq('cache_key', cacheKey)
      .gte('created_at', expiryTime.toISOString())
      .single()

    if (error || !cached) return null

    return cached.questions
  } catch (error) {
    console.error('Cache check failed:', error)
    return null
  }
}

async function saveToCache(supabase: any, cacheKey: string, questions: any[]): Promise<void> {
  try {
    await supabase
      .from('question_cache')
      .upsert({
        cache_key: cacheKey,
        questions: questions,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Cache save failed:', error)
    // Don't throw - caching is optional
  }
}

// Generate cache key from parameters
function generateCacheKey(params: any): string {
  const keyData = {
    disciplina: params.disciplina,
    serie: params.serie,
    topico: params.topico,
    subtopico: params.subtopico,
    dificuldade: params.dificuldade,
    tipo: params.tipo,
    quantidade: params.quantidade,
    competencia_bncc: params.competencia_bncc,
    contexto: params.contexto
  }

  return btoa(JSON.stringify(keyData)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50)
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()
  let generationResult: GenerationResult | null = null

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get admin user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    // Verify admin status
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (adminError || !adminProfile?.is_admin) {
      throw new Error('Unauthorized: Admin access required')
    }

    const params = await req.json()

    // Validate required parameters
    if (!params.disciplina || !params.serie || !params.topico || !params.tipo || !params.dificuldade) {
      throw new Error('Missing required parameters: disciplina, serie, topico, tipo, dificuldade')
    }

    // Validate quantidade
    if (!params.quantidade || params.quantidade < 1 || params.quantidade > 20) {
      throw new Error('Invalid quantidade: must be between 1 and 20')
    }

    // Check server-side rate limiting
    const rateLimitCheck = await checkRateLimit(supabase, user.id)
    if (!rateLimitCheck.allowed) {
      throw new Error(rateLimitCheck.error)
    }

    // Check cache first
    const cacheKey = generateCacheKey(params)
    const cachedQuestions = await checkCache(supabase, cacheKey)

    if (cachedQuestions) {
      console.log('Returning cached questions for key:', cacheKey)

      // Log cache hit
      await supabase.from('ai_generation_logs').insert({
        user_id: user.id,
        provider: 'cache',
        success: true,
        questions_generated: cachedQuestions.length,
        tokens_used: 0,
        cost: 0,
        duration_ms: Date.now() - startTime,
        cache_hit: true,
        parameters: params
      })

      return new Response(
        JSON.stringify({
          questions: cachedQuestions,
          provider: 'cache',
          cached: true,
          generated_at: new Date().toISOString()
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Load dynamic provider settings from database
    const providers = await loadProviderSettings(supabase)

    // Build enhanced prompt
    const prompt = buildPrompt(params)

    // Create provider calls based on database configuration
    const providerCalls = providers.map(provider => {
      switch (provider.name.toLowerCase()) {
        case 'openai':
        case 'openai-gpt4o-mini':
          return () => callOpenAI(prompt, provider)
        case 'anthropic':
        case 'claude-3-5-haiku':
        case 'claude-sonnet-4':
          return () => callAnthropic(prompt, provider)
        case 'google':
        case 'gemini-2-0-flash':
        case 'gemini-2-5-pro':
        case 'gemini-2-5-flash':
          return () => callGoogleGemini(prompt, provider)
        case 'cohere-command-r-plus':
          return () => callCohere(prompt, provider)
        default:
          return () => callOpenAI(prompt, provider)
      }
    })

    let lastError = ''

    for (let i = 0; i < providerCalls.length; i++) {
      const provider = providers[i]
      console.log(`Attempting generation with ${provider.name}...`)

      try {
        generationResult = await providerCalls[i]()

        if (generationResult.success && generationResult.questions) {
          console.log(`Successfully generated ${generationResult.questions.length} questions with ${provider.name}`)

          // Add missing fields from params to each question
          const enrichedQuestions = generationResult.questions.map(q => ({
            ...q,
            disciplina: params.disciplina,
            serie: params.serie,
            topico: params.topico,
            subtopico: params.subtopico,
            dificuldade: params.dificuldade,
            tipo: params.tipo
          }))

          // Save to cache for future requests
          await saveToCache(supabase, cacheKey, enrichedQuestions)

          // Log successful generation
          await supabase.from('ai_generation_logs').insert({
            user_id: user.id,
            provider: provider.name.toLowerCase(),
            success: true,
            questions_generated: enrichedQuestions.length,
            tokens_used: generationResult.tokensUsed || 0,
            cost: generationResult.cost || 0,
            duration_ms: Date.now() - startTime,
            cache_hit: false,
            parameters: params
          })

          // Log admin action
          await supabase.from('admin_audit_log').insert({
            admin_user_id: user.id,
            action_type: 'generate_questions',
            description: `Generated ${enrichedQuestions.length} questions using ${provider.name} for ${params.disciplina} - ${params.serie} - ${params.topico}`,
            metadata: {
              provider: provider.name,
              tokens_used: generationResult.tokensUsed,
              cost: generationResult.cost
            }
          })

          return new Response(
            JSON.stringify({
              questions: enrichedQuestions,
              provider: provider.name,
              tokens_used: generationResult.tokensUsed,
              cost: generationResult.cost,
              cached: false,
              generated_at: new Date().toISOString()
            }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 200,
            }
          )
        } else {
          lastError = generationResult.error || `${provider.name} generation failed`
          console.error(`${provider.name} failed:`, lastError)
        }
      } catch (error) {
        lastError = `${provider.name} request failed: ${error.message}`
        console.error(lastError)
      }
    }

    // If all providers failed, return detailed error
    const errorMessage = `All AI providers failed to generate questions. Last error: ${lastError}. Please check your API keys and try again later.`
    console.error('All providers failed:', errorMessage)

    // Log failed generation attempt
    await supabase.from('ai_generation_logs').insert({
      user_id: user.id,
      provider: 'all_failed',
      success: false,
      questions_generated: 0,
      tokens_used: 0,
      cost: 0,
      duration_ms: Date.now() - startTime,
      cache_hit: false,
      parameters: params,
      error_message: errorMessage
    })

    throw new Error(errorMessage)
  } catch (error) {
    console.error('Admin question generation error:', error)

    // Determine appropriate error status and message
    let status = 500
    let errorMessage = error.message

    if (error.message.includes('Rate limit exceeded')) {
      status = 429
    } else if (error.message.includes('Unauthorized') || error.message.includes('Invalid user')) {
      status = 401
    } else if (error.message.includes('Missing required parameters')) {
      status = 400
    } else if (error.message.includes('API key not configured')) {
      status = 503
      errorMessage = 'AI service temporarily unavailable. Please try again later.'
    }

    return new Response(
      JSON.stringify({
        error: errorMessage,
        timestamp: new Date().toISOString(),
        request_id: crypto.randomUUID()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status,
      }
    )
  }
})