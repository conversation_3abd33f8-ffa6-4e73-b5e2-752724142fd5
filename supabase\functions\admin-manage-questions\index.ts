import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get admin user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    // Verify admin status
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (adminError || !adminProfile?.is_admin) {
      throw new Error('Unauthorized: Admin access required')
    }

    const { action, questionId, updates } = await req.json()

    let result

    switch (action) {
      case 'verify_question':
        result = await supabase
          .from('questions')
          .update({ is_verified: updates.is_verified })
          .eq('id', questionId)
          .select()
          .single()

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'verify_question',
          target_table: 'questions',
          target_id: questionId,
          new_values: { is_verified: updates.is_verified },
          description: `Question ${updates.is_verified ? 'verified' : 'unverified'}: ${questionId}`
        })
        break

      case 'toggle_public':
        result = await supabase
          .from('questions')
          .update({ is_public: updates.is_public })
          .eq('id', questionId)
          .select()
          .single()

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'toggle_question_public',
          target_table: 'questions',
          target_id: questionId,
          new_values: { is_public: updates.is_public },
          description: `Question made ${updates.is_public ? 'public' : 'private'}: ${questionId}`
        })
        break

      case 'update_question':
        result = await supabase
          .from('questions')
          .update(updates)
          .eq('id', questionId)
          .select()
          .single()

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'update_question',
          target_table: 'questions',
          target_id: questionId,
          new_values: updates,
          description: `Question updated: ${questionId}`
        })
        break

      case 'delete_question':
        result = await supabase
          .from('questions')
          .delete()
          .eq('id', questionId)

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'delete_question',
          target_table: 'questions',
          target_id: questionId,
          description: `Question deleted: ${questionId}`
        })
        break

      default:
        throw new Error('Invalid action')
    }

    if (result?.error) {
      throw result.error
    }

    return new Response(
      JSON.stringify({ success: true, data: result?.data }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Admin question management error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})