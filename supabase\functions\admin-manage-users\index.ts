import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get admin user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    // Verify admin status
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (adminError || !adminProfile?.is_admin) {
      throw new Error('Unauthorized: Admin access required')
    }

    const { action, userId, updates } = await req.json()

    let result

    switch (action) {
      case 'update_admin_status':
        result = await supabase
          .from('profiles')
          .update({ is_admin: updates.is_admin })
          .eq('id', userId)
          .select()
          .single()

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'update_admin_status',
          target_table: 'profiles',
          target_id: userId,
          new_values: { is_admin: updates.is_admin },
          description: `Admin status ${updates.is_admin ? 'granted to' : 'revoked from'} user ${userId}`
        })
        break

      case 'update_profile':
        result = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', userId)
          .select()
          .single()

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'update_profile',
          target_table: 'profiles',
          target_id: userId,
          new_values: updates,
          description: `Profile updated for user ${userId}`
        })
        break

      case 'delete_user':
        // First delete the profile (this will cascade to related data)
        result = await supabase
          .from('profiles')
          .delete()
          .eq('id', userId)

        // Then delete the auth user
        await supabase.auth.admin.deleteUser(userId)

        // Log admin action
        await supabase.from('admin_audit_log').insert({
          admin_user_id: user.id,
          action_type: 'delete_user',
          target_table: 'profiles',
          target_id: userId,
          description: `User ${userId} deleted`
        })
        break

      default:
        throw new Error('Invalid action')
    }

    if (result?.error) {
      throw result.error
    }

    return new Response(
      JSON.stringify({ success: true, data: result?.data }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Admin user management error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})