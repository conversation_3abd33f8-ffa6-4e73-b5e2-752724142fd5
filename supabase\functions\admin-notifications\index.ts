import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get admin user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    // Verify admin status
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (adminError || !adminProfile?.is_admin) {
      throw new Error('Unauthorized: Admin access required')
    }

    const { action, notification } = await req.json()

    let result

    switch (action) {
      case 'create_for_user':
        // Create notification for a specific user
        result = await supabase
          .from('user_notifications')
          .insert({
            user_id: notification.user_id,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            action_url: notification.action_url,
            action_label: notification.action_label,
            metadata: notification.metadata || {}
          })
          .select()
          .single()
        break

      case 'create_for_all_users':
        // Get all users
        const { data: users, error: usersError } = await supabase
          .from('profiles')
          .select('id')

        if (usersError) throw usersError

        // Create notification for each user
        const notifications = users?.map(u => ({
          user_id: u.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          action_url: notification.action_url,
          action_label: notification.action_label,
          metadata: notification.metadata || {}
        })) || []

        result = await supabase
          .from('user_notifications')
          .insert(notifications)
        break

      case 'create_for_admins':
        // Get all admin users
        const { data: admins, error: adminsError } = await supabase
          .from('profiles')
          .select('id')
          .eq('is_admin', true)

        if (adminsError) throw adminsError

        // Create notification for each admin
        const adminNotifications = admins?.map(a => ({
          user_id: a.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          action_url: notification.action_url,
          action_label: notification.action_label,
          metadata: notification.metadata || {}
        })) || []

        result = await supabase
          .from('user_notifications')
          .insert(adminNotifications)
        break

      case 'delete_notification':
        result = await supabase
          .from('user_notifications')
          .delete()
          .eq('id', notification.id)
        break

      default:
        throw new Error('Invalid action')
    }

    if (result?.error) {
      throw result.error
    }

    // Log admin action
    await supabase.from('admin_audit_log').insert({
      admin_user_id: user.id,
      action_type: `notification_${action}`,
      description: `Admin ${action} notification: ${notification.title}`
    })

    return new Response(
      JSON.stringify({ success: true, data: result?.data }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Admin notification error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})