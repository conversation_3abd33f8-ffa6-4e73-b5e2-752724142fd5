// Supabase Edge Function for dynamic robots.txt generation
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const baseUrl = 'https://atvpronta.com.br'
    
    const robotsTxt = `User-agent: *
Allow: /
Allow: /avaliacoes
Allow: /avaliacoes/*

# Disallow admin and app routes
Disallow: /app/
Disallow: /admin/
Disallow: /api/

# Disallow auth pages
Disallow: /login
Disallow: /register

# Allow sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay
Crawl-delay: 1`

    return new Response(robotsTxt, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400'
      }
    })

  } catch (error) {
    console.error('Error generating robots.txt:', error)
    
    return new Response('User-agent: *\nDisallow: /', {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain; charset=utf-8'
      }
    })
  }
})
