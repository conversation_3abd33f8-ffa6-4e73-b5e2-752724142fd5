// Supabase Edge Function for dynamic sitemap.xml generation
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SitemapUrl {
  url: string
  lastModified: string
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    const baseUrl = 'https://atvpronta.com.br'
    const urls: SitemapUrl[] = []

    // Add static pages
    urls.push(
      {
        url: `${baseUrl}/`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly',
        priority: 1.0
      },
      {
        url: `${baseUrl}/avaliacoes`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily',
        priority: 0.9
      }
    )

    // Add public assessment URLs
    const { data: assessments, error } = await supabase
      .from('assessments')
      .select('slug, updated_at, view_count, is_featured')
      .eq('is_public', true)
      .not('slug', 'is', null)
      .order('view_count', { ascending: false })
      .limit(1000)

    if (!error && assessments) {
      for (const assessment of assessments) {
        const priority = assessment.is_featured ? 0.8 : 
                        assessment.view_count > 100 ? 0.7 : 
                        assessment.view_count > 50 ? 0.6 : 0.5

        urls.push({
          url: `${baseUrl}/avaliacoes/${assessment.slug}`,
          lastModified: assessment.updated_at,
          changeFrequency: 'weekly',
          priority
        })
      }
    }

    // Add category URLs
    const { data: categories } = await supabase
      .from('public_categories')
      .select('slug, updated_at')
      .eq('is_active', true)

    if (categories) {
      for (const category of categories) {
        urls.push({
          url: `${baseUrl}/avaliacoes/categoria/${category.slug}`,
          lastModified: category.updated_at,
          changeFrequency: 'weekly',
          priority: 0.6
        })
      }
    }

    // Generate XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map(url => `  <url>
    <loc>${url.url}</loc>
    <lastmod>${url.lastModified.split('T')[0]}</lastmod>
    <changefreq>${url.changeFrequency}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`

    return new Response(xml, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })

  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Fallback sitemap
    const fallbackXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://atvpronta.com.br/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://atvpronta.com.br/avaliacoes</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`

    return new Response(fallbackXml, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/xml; charset=utf-8'
      }
    })
  }
})
