import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@13.6.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature!,
      Deno.env.get('STRIPE_WEBHOOK_SECRET')!
    )

    console.log('Webhook event type:', event.type)

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        // Get user ID from customer metadata
        const customer = await stripe.customers.retrieve(subscription.customer as string)
        const userId = (customer as Stripe.Customer).metadata?.supabase_user_id

        if (!userId) {
          console.error('No user ID found in customer metadata')
          break
        }

        // Determine plan from price by looking up in database
        const priceId = subscription.items.data[0].price.id
        let plano = 'gratuito'

        // Look up plan in database by stripe_price_id
        const { data: planData } = await supabase
          .from('plans')
          .select('name')
          .eq('stripe_price_id', priceId)
          .single()

        if (planData) {
          // Map plan names to subscription plan types
          const planName = planData.name.toLowerCase()
          if (planName.includes('premium')) {
            plano = 'premium'
          } else if (planName.includes('escolar')) {
            plano = 'escolar'
          }
        }

        // Prepare subscription data with trial information
        const subscriptionData: any = {
          user_id: userId,
          stripe_customer_id: subscription.customer as string,
          stripe_subscription_id: subscription.id,
          status: subscription.status,
          plano: plano,
          current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
          cancel_at_period_end: subscription.cancel_at_period_end,
        }

        // Add trial information if subscription is in trial
        if (subscription.status === 'trialing' && subscription.trial_start && subscription.trial_end) {
          subscriptionData.trial_start = new Date(subscription.trial_start * 1000).toISOString()
          subscriptionData.trial_end = new Date(subscription.trial_end * 1000).toISOString()
          subscriptionData.trial_status = 'active'
        } else if (subscription.status === 'active' && subscription.trial_end) {
          // Trial has ended and converted to active
          subscriptionData.trial_status = 'converted'
        }

        // Upsert subscription
        const { error: subError } = await supabase
          .from('subscriptions')
          .upsert(subscriptionData)

        if (subError) {
          console.error('Error upserting subscription:', subError)
        }

        // Update user profile plan
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ plano: plano })
          .eq('id', userId)

        if (profileError) {
          console.error('Error updating profile plan:', profileError)
        }

        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        // Update subscription status
        const { error: subError } = await supabase
          .from('subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', subscription.id)

        if (subError) {
          console.error('Error updating subscription status:', subError)
        }

        // Get user ID and update profile to free plan
        const { data: subData } = await supabase
          .from('subscriptions')
          .select('user_id')
          .eq('stripe_subscription_id', subscription.id)
          .single()

        if (subData?.user_id) {
          const { error: profileError } = await supabase
            .from('profiles')
            .update({ plano: 'gratuito' })
            .eq('id', subData.user_id)

          if (profileError) {
            console.error('Error updating profile to free plan:', profileError)
          }
        }

        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        // Log successful payment
        console.log('Payment succeeded for subscription:', invoice.subscription)
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice

        // Handle payment failure during trial or active subscription
        console.log('Payment failed for subscription:', invoice.subscription)

        if (invoice.subscription) {
          // Update subscription status if needed
          const { error } = await supabase
            .from('subscriptions')
            .update({
              status: 'past_due',
              metadata: { last_payment_failure: new Date().toISOString() }
            })
            .eq('stripe_subscription_id', invoice.subscription as string)

          if (error) {
            console.error('Error updating subscription after payment failure:', error)
          }
        }
        break
      }

      case 'customer.subscription.trial_will_end': {
        const subscription = event.data.object as Stripe.Subscription

        console.log('Trial ending soon for subscription:', subscription.id)

        // Update trial status to 'ending'
        const { error } = await supabase
          .from('subscriptions')
          .update({
            trial_status: 'ending',
            metadata: { trial_ending_notification_sent: new Date().toISOString() }
          })
          .eq('stripe_subscription_id', subscription.id)

        if (error) {
          console.error('Error updating trial status:', error)
        }

        // Here you could also send notification to user
        // await sendTrialEndingNotification(userId)
        break
      }

      case 'invoice.upcoming': {
        const invoice = event.data.object as Stripe.Invoice

        // Handle upcoming invoice (trial ending, renewal, etc.)
        console.log('Upcoming invoice for subscription:', invoice.subscription)

        if (invoice.subscription) {
          // Get subscription details to check if it's trial-related
          const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription as string)

          if (stripeSubscription.status === 'trialing') {
            console.log('Trial-to-paid conversion upcoming')

            // Update trial status
            const { error } = await supabase
              .from('subscriptions')
              .update({
                trial_status: 'ending',
                metadata: { trial_conversion_upcoming: new Date().toISOString() }
              })
              .eq('stripe_subscription_id', stripeSubscription.id)

            if (error) {
              console.error('Error updating trial conversion status:', error)
            }
          }
        }
        break
      }

      default:
        console.log('Unhandled event type:', event.type)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (err) {
    console.error('Webhook error:', err.message)
    return new Response(`Webhook error: ${err.message}`, {
      status: 400,
    })
  }
})