import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@13.6.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Enhanced type definitions
interface PlanData {
  id: string;
  name: string;
  description?: string;
  price: number | string;
  currency: string;
  duration_months: number;
  stripe_product_id?: string;
  stripe_price_id?: string;
}

interface RequestBody {
  action: 'create' | 'update' | 'delete';
  planData: PlanData;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

// Centralized error handling function
function createErrorResponse(message: string, status: number = 400) {
  return new Response(
    JSON.stringify({ 
      success: false, 
      error: message 
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: status,
    }
  )
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  console.log(`🚀 Sync request received: ${req.method} ${req.url}`)

  try {
    // Validate request method
    if (req.method !== 'POST') {
      return createErrorResponse('Only POST method is allowed', 405)
    }

    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body safely
    let requestBody: RequestBody;
    try {
      requestBody = await req.json()
    } catch (error) {
      console.error('❌ Error parsing request body:', error)
      return createErrorResponse('Invalid request body')
    }

    // Validate request body structure
    if (!requestBody.action || !requestBody.planData) {
      return createErrorResponse('Missing action or plan data')
    }

    const { action, planData } = requestBody
    console.log(`📋 Action: ${action}, Plan: ${planData?.name}`)

    // Get user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('❌ No authorization header provided')
      return createErrorResponse('No authorization header')
    }

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      console.error('❌ Invalid user:', userError?.message)
      return createErrorResponse('Invalid user authentication')
    }

    console.log(`👤 User authenticated: ${user.email}`)

    // Verify user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.is_admin) {
      console.error('❌ Unauthorized access attempt by:', user.email)
      return createErrorResponse('Unauthorized: Admin access required')
    }

    console.log('✅ Admin access confirmed for:', user.email)

    // Process the request based on action
    let result;
    switch (action) {
      case 'create':
        result = await createStripeProduct(stripe, supabase, planData)
        break
      case 'update':
        result = await updateStripeProduct(stripe, supabase, planData)
        break
      case 'delete':
        result = await deleteStripeProduct(stripe, supabase, planData)
        break
      default:
        return createErrorResponse('Invalid action')
    }

    return new Response(
      JSON.stringify({ success: true, ...result }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Unexpected error syncing plan with Stripe:', error)
    return createErrorResponse('Unexpected error occurred')
  }
})

// Helper function to check if a price is the default for a product
async function isDefaultPrice(stripe: Stripe, productId: string, priceId: string): Promise<boolean> {
  try {
    const product = await stripe.products.retrieve(productId)
    return product.default_price === priceId
  } catch (error) {
    console.error('Error checking default price:', error)
    return false
  }
}

async function createStripeProduct(stripe: Stripe, supabase: any, planData: PlanData) {
  // Validate input
  if (!planData.name || !planData.price || !planData.currency) {
    throw new Error('Missing required fields for creating Stripe product')
  }

  // Ensure price is a number
  const price = typeof planData.price === 'string' 
    ? parseFloat(planData.price) 
    : planData.price

  try {
    // Create product in Stripe
    const product = await stripe.products.create({
      name: planData.name,
      description: planData.description || `Plano ${planData.name}`,
      metadata: {
        supabase_plan_id: planData.id,
      },
    })

    // Create price in Stripe
    const stripePrice = await stripe.prices.create({
      product: product.id,
      unit_amount: Math.round(price * 100), // Convert to cents
      currency: planData.currency.toLowerCase(),
      recurring: {
        interval: planData.duration_months === 12 ? 'year' : 'month',
      },
      metadata: {
        supabase_plan_id: planData.id,
      },
    })

    // Update plan in Supabase with Stripe IDs
    const { error: updateError } = await supabase
      .from('plans')
      .update({
        stripe_product_id: product.id,
        stripe_price_id: stripePrice.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', planData.id)

    if (updateError) {
      // Attempt to deactivate Stripe product and price if Supabase update fails
      await Promise.allSettled([
        stripe.products.update(product.id, { active: false }),
        stripe.prices.update(stripePrice.id, { active: false })
      ])
      throw updateError
    }

    return {
      stripe_product_id: product.id,
      stripe_price_id: stripePrice.id,
      message: 'Plan created and synced with Stripe successfully'
    }
  } catch (error) {
    console.error('Error in createStripeProduct:', error)
    throw error
  }
}

async function updateStripeProduct(stripe: Stripe, supabase: any, planData: PlanData) {
  // Validate input
  if (!planData.stripe_product_id) {
    throw new Error('Plan does not have a Stripe product ID')
  }

  // Ensure price is a number
  const price = typeof planData.price === 'string' 
    ? parseFloat(planData.price) 
    : planData.price

  try {
    // Update product in Stripe
    await stripe.products.update(planData.stripe_product_id, {
      name: planData.name,
      description: planData.description || `Plano ${planData.name}`,
    })

    // Check and update price if needed
    if (planData.stripe_price_id) {
      const currentPrice = await stripe.prices.retrieve(planData.stripe_price_id)
      const newPriceAmount = Math.round(price * 100)

      // Use a small threshold to account for floating-point imprecision
      const priceDifference = Math.abs(currentPrice.unit_amount - newPriceAmount)
      const PRICE_CHANGE_THRESHOLD = 1 // 1 cent difference

      if (priceDifference > PRICE_CHANGE_THRESHOLD) {
        // Create new price
        const newPrice = await stripe.prices.create({
          product: planData.stripe_product_id,
          unit_amount: newPriceAmount,
          currency: planData.currency.toLowerCase(),
          recurring: {
            interval: planData.duration_months === 12 ? 'year' : 'month',
          },
          metadata: {
            supabase_plan_id: planData.id,
          },
        })

        // Set new price as default for the product before archiving old one
        console.log('🔄 Setting new price as default for product...')
        await stripe.products.update(planData.stripe_product_id, {
          default_price: newPrice.id,
        })
        console.log('✅ New price set as default')

        // Now safely archive old price (no longer default)
        console.log('🗄️ Archiving old price...')

        // Double-check if old price is still default (safety check)
        const isStillDefault = await isDefaultPrice(stripe, planData.stripe_product_id, planData.stripe_price_id)
        if (isStillDefault) {
          console.log('⚠️ Old price is still default - skipping archive to avoid error')
        } else {
          try {
            await stripe.prices.update(planData.stripe_price_id, {
              active: false,
            })
            console.log('✅ Old price archived successfully')
          } catch (archiveError: any) {
            console.error('⚠️ Warning: Could not archive old price:', archiveError.message)
            // Don't throw error - the new price is already created and set as default
            // This is not critical for the operation to succeed
          }
        }

        // Update plan in Supabase with new price ID
        const { error: updateError } = await supabase
          .from('plans')
          .update({
            stripe_price_id: newPrice.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', planData.id)

        if (updateError) {
          throw updateError
        }

        return {
          stripe_price_id: newPrice.id,
          message: 'Plan updated and new price created in Stripe'
        }
      }
    }

    return {
      message: 'Plan updated in Stripe successfully'
    }
  } catch (error) {
    console.error('Error in updateStripeProduct:', error)
    throw error
  }
}

async function deleteStripeProduct(stripe: Stripe, supabase: any, planData: PlanData) {
  // Validate input
  if (!planData.stripe_product_id) {
    throw new Error('Plan does not have a Stripe product ID')
  }

  try {
    // Archive product in Stripe (cannot delete products with prices)
    await stripe.products.update(planData.stripe_product_id, {
      active: false,
    })

    // Archive price if exists
    if (planData.stripe_price_id) {
      try {
        await stripe.prices.update(planData.stripe_price_id, {
          active: false,
        })
        console.log('✅ Price archived successfully')
      } catch (priceError: any) {
        console.error('⚠️ Warning: Could not archive price:', priceError.message)
        // Check if it's the default price error
        if (priceError.message?.includes('default price')) {
          console.log('💡 Price is default for product - this is expected when deleting')
          // When deleting a product, Stripe automatically handles default prices
        } else {
          // Re-throw other errors as they might be more serious
          throw priceError
        }
      }
    }

    return {
      message: 'Plan archived in Stripe successfully'
    }
  } catch (error) {
    console.error('Error in deleteStripeProduct:', error)
    throw error
  }
}