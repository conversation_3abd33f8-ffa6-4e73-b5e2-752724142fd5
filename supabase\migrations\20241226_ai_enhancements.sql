-- Migration for AI system enhancements
-- Adds support for multi-provider LLM, caching, and comprehensive logging

-- AI Generation Logs table for tracking all generation attempts
CREATE TABLE IF NOT EXISTS ai_generation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL, -- 'openai', 'anthropic', 'google', 'cache', 'all_failed'
  success BOOLEAN NOT NULL DEFAULT false,
  questions_generated INTEGER DEFAULT 0,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10,6) DEFAULT 0, -- Cost in USD
  duration_ms INTEGER DEFAULT 0,
  cache_hit BOOLEAN DEFAULT false,
  parameters JSONB, -- Store generation parameters
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question Cache table for intelligent caching
CREATE TABLE IF NOT EXISTS question_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key TEXT UNIQUE NOT NULL,
  questions JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accessed_count INTEGER DEFAULT 0,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Provider Settings table for admin configuration
CREATE TABLE IF NOT EXISTS ai_provider_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  provider_name TEXT UNIQUE NOT NULL,
  is_enabled BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 0, -- Lower number = higher priority
  api_key_configured BOOLEAN DEFAULT false,
  max_requests_per_minute INTEGER DEFAULT 10,
  cost_per_token DECIMAL(10,8) DEFAULT 0,
  model_name TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default provider settings
INSERT INTO ai_provider_settings (provider_name, priority, cost_per_token, model_name, settings) VALUES
('openai', 1, 0.00003, 'gpt-4-turbo-preview', '{"temperature": 0.7, "max_tokens": 4000}'),
('anthropic', 2, 0.000015, 'claude-3-5-sonnet-20241022', '{"temperature": 0.7, "max_tokens": 4000}'),
('google', 3, 0.0000125, 'gemini-1.5-pro', '{"temperature": 0.7, "max_tokens": 4000}')
ON CONFLICT (provider_name) DO NOTHING;

-- Usage Statistics Enhancement for AI tracking
ALTER TABLE usage_stats ADD COLUMN IF NOT EXISTS ai_provider TEXT;
ALTER TABLE usage_stats ADD COLUMN IF NOT EXISTS tokens_used INTEGER DEFAULT 0;
ALTER TABLE usage_stats ADD COLUMN IF NOT EXISTS ai_cost DECIMAL(10,6) DEFAULT 0;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_generation_logs_user_id ON ai_generation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_generation_logs_created_at ON ai_generation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_generation_logs_provider ON ai_generation_logs(provider);
CREATE INDEX IF NOT EXISTS idx_question_cache_key ON question_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_question_cache_created_at ON question_cache(created_at);

-- RLS Policies
ALTER TABLE ai_generation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_provider_settings ENABLE ROW LEVEL SECURITY;

-- AI Generation Logs policies
CREATE POLICY "Users can view their own AI generation logs" ON ai_generation_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all AI generation logs" ON ai_generation_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

CREATE POLICY "System can insert AI generation logs" ON ai_generation_logs
  FOR INSERT WITH CHECK (true);

-- Question Cache policies (admin only for management)
CREATE POLICY "Admins can manage question cache" ON question_cache
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- Provider Settings policies (admin only)
CREATE POLICY "Admins can manage provider settings" ON ai_provider_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- Function to clean old cache entries (run daily)
CREATE OR REPLACE FUNCTION clean_old_cache_entries()
RETURNS void AS $$
BEGIN
  DELETE FROM question_cache 
  WHERE created_at < NOW() - INTERVAL '7 days'
  AND accessed_count < 5; -- Keep frequently accessed items longer
END;
$$ LANGUAGE plpgsql;

-- Function to update cache access statistics
CREATE OR REPLACE FUNCTION update_cache_access(cache_key_param TEXT)
RETURNS void AS $$
BEGIN
  UPDATE question_cache 
  SET accessed_count = accessed_count + 1,
      last_accessed = NOW()
  WHERE cache_key = cache_key_param;
END;
$$ LANGUAGE plpgsql;

-- Function to get provider statistics
CREATE OR REPLACE FUNCTION get_provider_statistics(days_back INTEGER DEFAULT 30)
RETURNS TABLE (
  provider TEXT,
  total_requests BIGINT,
  successful_requests BIGINT,
  failed_requests BIGINT,
  total_questions BIGINT,
  total_tokens BIGINT,
  total_cost DECIMAL,
  avg_duration_ms DECIMAL,
  success_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    agl.provider,
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE agl.success = true) as successful_requests,
    COUNT(*) FILTER (WHERE agl.success = false) as failed_requests,
    COALESCE(SUM(agl.questions_generated), 0) as total_questions,
    COALESCE(SUM(agl.tokens_used), 0) as total_tokens,
    COALESCE(SUM(agl.cost), 0) as total_cost,
    COALESCE(AVG(agl.duration_ms), 0) as avg_duration_ms,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        ROUND((COUNT(*) FILTER (WHERE agl.success = true)::DECIMAL / COUNT(*)) * 100, 2)
      ELSE 0 
    END as success_rate
  FROM ai_generation_logs agl
  WHERE agl.created_at >= NOW() - (days_back || ' days')::INTERVAL
  GROUP BY agl.provider
  ORDER BY total_requests DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON TABLE ai_generation_logs IS 'Comprehensive logging of all AI generation attempts';
COMMENT ON TABLE question_cache IS 'Intelligent caching system for generated questions';
COMMENT ON TABLE ai_provider_settings IS 'Configuration and settings for AI providers';
