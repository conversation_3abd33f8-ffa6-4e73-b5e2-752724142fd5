-- Migration: Optimize usage_stats table for usage limits queries
-- Created: 2025-01-26
-- Description: Add indexes and functions to efficiently query monthly usage limits

-- Add composite index for monthly usage queries
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_action_date 
ON usage_stats(user_id, action_type, created_at DESC);

-- Add index for monthly period queries
CREATE INDEX IF NOT EXISTS idx_usage_stats_monthly 
ON usage_stats(user_id, action_type, date_trunc('month', created_at));

-- Create function to get monthly usage counts
CREATE OR REPLACE FUNCTION get_monthly_usage_counts(
  p_user_id UUID,
  p_year INTEGER DEFAULT EXTRACT(YEAR FROM NOW()),
  p_month INTEGER DEFAULT EXTRACT(MONTH FROM NOW())
)
RETURNS TABLE (
  action_type TEXT,
  count BIGINT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  period_start TIMESTAMP WITH TIME ZONE;
  period_end TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Calculate period boundaries
  period_start := DATE_TRUNC('month', MAKE_DATE(p_year, p_month, 1));
  period_end := period_start + INTERVAL '1 month' - INTERVAL '1 microsecond';
  
  RETURN QUERY
  SELECT 
    us.action_type,
    COUNT(*) as count
  FROM usage_stats us
  WHERE us.user_id = p_user_id
    AND us.created_at >= period_start
    AND us.created_at <= period_end
    AND us.action_type IN ('assessment_created', 'pdf_downloaded', 'question_created')
  GROUP BY us.action_type;
END;
$$;

-- Create function to check if user can perform action
CREATE OR REPLACE FUNCTION can_user_perform_action(
  p_user_id UUID,
  p_action_type TEXT,
  p_limit INTEGER DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
  user_subscription RECORD;
  monthly_limit INTEGER;
BEGIN
  -- Get user subscription info
  SELECT s.plano, s.status INTO user_subscription
  FROM subscriptions s
  WHERE s.user_id = p_user_id AND s.status = 'active'
  LIMIT 1;
  
  -- If user has active premium/escolar subscription, allow unlimited
  IF user_subscription.plano IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Set limits for free users
  monthly_limit := CASE 
    WHEN p_action_type = 'assessment_created' THEN 5
    WHEN p_action_type = 'pdf_downloaded' THEN 10
    WHEN p_action_type = 'question_created' THEN 50
    ELSE COALESCE(p_limit, 999999)
  END;
  
  -- Count current month usage
  SELECT COUNT(*) INTO current_count
  FROM usage_stats
  WHERE user_id = p_user_id
    AND action_type = p_action_type
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- Create function to get usage summary for dashboard
CREATE OR REPLACE FUNCTION get_user_usage_summary(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  period_start TIMESTAMP WITH TIME ZONE;
  period_end TIMESTAMP WITH TIME ZONE;
  user_subscription RECORD;
BEGIN
  -- Calculate current month boundaries
  period_start := DATE_TRUNC('month', NOW());
  period_end := period_start + INTERVAL '1 month' - INTERVAL '1 microsecond';
  
  -- Get user subscription
  SELECT s.plano, s.status INTO user_subscription
  FROM subscriptions s
  WHERE s.user_id = p_user_id AND s.status = 'active'
  LIMIT 1;
  
  -- Build usage summary
  WITH usage_counts AS (
    SELECT 
      action_type,
      COUNT(*) as count
    FROM usage_stats
    WHERE user_id = p_user_id
      AND created_at >= period_start
      AND created_at <= period_end
      AND action_type IN ('assessment_created', 'pdf_downloaded', 'question_created')
    GROUP BY action_type
  ),
  limits AS (
    SELECT 
      CASE 
        WHEN COALESCE(user_subscription.plano, 'gratuito') IN ('premium', 'escolar') THEN -1
        ELSE 5
      END as assessment_limit,
      CASE 
        WHEN COALESCE(user_subscription.plano, 'gratuito') IN ('premium', 'escolar') THEN -1
        ELSE 10
      END as pdf_limit,
      CASE 
        WHEN COALESCE(user_subscription.plano, 'gratuito') IN ('premium', 'escolar') THEN -1
        ELSE 50
      END as question_limit
  )
  SELECT json_build_object(
    'period_start', period_start,
    'period_end', period_end,
    'plan', COALESCE(user_subscription.plano, 'gratuito'),
    'assessments_created', COALESCE((SELECT count FROM usage_counts WHERE action_type = 'assessment_created'), 0),
    'pdf_downloads', COALESCE((SELECT count FROM usage_counts WHERE action_type = 'pdf_downloaded'), 0),
    'questions_created', COALESCE((SELECT count FROM usage_counts WHERE action_type = 'question_created'), 0),
    'limits', json_build_object(
      'assessments', (SELECT assessment_limit FROM limits),
      'pdf_downloads', (SELECT pdf_limit FROM limits),
      'questions', (SELECT question_limit FROM limits)
    ),
    'can_create_assessment', (
      CASE 
        WHEN COALESCE(user_subscription.plano, 'gratuito') IN ('premium', 'escolar') THEN true
        ELSE COALESCE((SELECT count FROM usage_counts WHERE action_type = 'assessment_created'), 0) < 5
      END
    ),
    'can_download_pdf', (
      CASE 
        WHEN COALESCE(user_subscription.plano, 'gratuito') IN ('premium', 'escolar') THEN true
        ELSE COALESCE((SELECT count FROM usage_counts WHERE action_type = 'pdf_downloaded'), 0) < 10
      END
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_monthly_usage_counts(UUID, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_perform_action(UUID, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_usage_summary(UUID) TO authenticated;

-- Add RLS policies for the functions (they already use SECURITY DEFINER)
-- The functions will only return data for the requesting user

-- Add comment for documentation
COMMENT ON FUNCTION get_monthly_usage_counts IS 'Returns monthly usage counts for a specific user and period';
COMMENT ON FUNCTION can_user_perform_action IS 'Checks if a user can perform a specific action based on their plan limits';
COMMENT ON FUNCTION get_user_usage_summary IS 'Returns comprehensive usage summary for dashboard display';
