-- Migration: Server-Side Usage Limits Enforcement
-- Created: 2025-01-26
-- Description: Add RLS policies and triggers to enforce usage limits at database level
-- This prevents client-side bypasses and ensures limits are enforced atomically

-- First, ensure we have the necessary functions from the previous migration
-- (This migration depends on 20250126000000_usage_limits_optimization.sql)

-- Create a function to get user's current plan
CREATE OR REPLACE FUNCTION get_user_plan(p_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
BEGIN
  -- Get active subscription plan
  SELECT s.plano INTO user_plan
  FROM subscriptions s
  WHERE s.user_id = p_user_id 
    AND s.status = 'active'
  ORDER BY s.created_at DESC
  LIMIT 1;
  
  -- Default to free plan if no active subscription
  RETURN COALESCE(user_plan, 'gratuito');
END;
$$;

-- Create a function to check if user can create assessment
CREATE OR REPLACE FUNCTION can_create_assessment(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 5; -- Free plan limit
BEGIN
  -- Get user's plan
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count assessments created this month for free users
  SELECT COUNT(*) INTO current_count
  FROM assessments
  WHERE autor_id = p_user_id
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- Create a function to check if user can download PDF
CREATE OR REPLACE FUNCTION can_download_pdf(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 10; -- Free plan limit
BEGIN
  -- Get user's plan
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count PDF downloads this month for free users
  SELECT COUNT(*) INTO current_count
  FROM usage_stats
  WHERE user_id = p_user_id
    AND action_type = 'pdf_downloaded'
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- Add RLS policy to assessments table to enforce creation limits
-- First, enable RLS on assessments if not already enabled
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can create assessments within limits" ON assessments;
DROP POLICY IF EXISTS "Users can view own assessments" ON assessments;
DROP POLICY IF EXISTS "Users can update own assessments" ON assessments;
DROP POLICY IF EXISTS "Users can delete own assessments" ON assessments;

-- Create comprehensive RLS policies for assessments
CREATE POLICY "Users can view own assessments" ON assessments
  FOR SELECT TO authenticated
  USING (autor_id = auth.uid());

CREATE POLICY "Users can create assessments within limits" ON assessments
  FOR INSERT TO authenticated
  WITH CHECK (
    autor_id = auth.uid() 
    AND can_create_assessment(auth.uid())
  );

CREATE POLICY "Users can update own assessments" ON assessments
  FOR UPDATE TO authenticated
  USING (autor_id = auth.uid())
  WITH CHECK (autor_id = auth.uid());

CREATE POLICY "Users can delete own assessments" ON assessments
  FOR DELETE TO authenticated
  USING (autor_id = auth.uid());

-- Add RLS policy to usage_stats table to enforce PDF download limits
-- Enable RLS on usage_stats if not already enabled
ALTER TABLE usage_stats ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can view own usage stats" ON usage_stats;
DROP POLICY IF EXISTS "Users can insert usage stats within limits" ON usage_stats;

-- Create RLS policies for usage_stats
CREATE POLICY "Users can view own usage stats" ON usage_stats
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert usage stats within limits" ON usage_stats
  FOR INSERT TO authenticated
  WITH CHECK (
    user_id = auth.uid()
    AND (
      -- Allow all actions for premium users
      get_user_plan(auth.uid()) IN ('premium', 'escolar')
      OR
      -- For free users, check specific limits
      (
        CASE 
          WHEN NEW.action_type = 'pdf_downloaded' THEN
            can_download_pdf(auth.uid())
          ELSE
            TRUE -- Allow other action types (they're controlled elsewhere)
        END
      )
    )
  );

-- Create a trigger function to automatically track assessment creation
CREATE OR REPLACE FUNCTION track_assessment_creation()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert usage tracking record for assessment creation
  INSERT INTO usage_stats (
    user_id,
    action_type,
    resource_type,
    metadata
  ) VALUES (
    NEW.autor_id,
    'assessment_created',
    'assessment',
    jsonb_build_object(
      'assessment_id', NEW.id,
      'titulo', NEW.titulo,
      'disciplina', NEW.disciplina,
      'serie', NEW.serie,
      'auto_tracked', true
    )
  );
  
  RETURN NEW;
END;
$$;

-- Create trigger to automatically track assessment creation
DROP TRIGGER IF EXISTS trigger_track_assessment_creation ON assessments;
CREATE TRIGGER trigger_track_assessment_creation
  AFTER INSERT ON assessments
  FOR EACH ROW
  EXECUTE FUNCTION track_assessment_creation();

-- Create a function to validate usage limits before insertion
CREATE OR REPLACE FUNCTION validate_usage_limits()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER;
BEGIN
  -- Get user's plan
  user_plan := get_user_plan(NEW.user_id);
  
  -- Skip validation for premium users
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN NEW;
  END IF;
  
  -- Validate based on action type
  CASE NEW.action_type
    WHEN 'pdf_downloaded' THEN
      monthly_limit := 10;
      SELECT COUNT(*) INTO current_count
      FROM usage_stats
      WHERE user_id = NEW.user_id
        AND action_type = 'pdf_downloaded'
        AND created_at >= DATE_TRUNC('month', NOW())
        AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
        
      IF current_count >= monthly_limit THEN
        RAISE EXCEPTION 'PDF download limit exceeded for free plan (% per month)', monthly_limit;
      END IF;
      
    WHEN 'question_created' THEN
      monthly_limit := 50;
      SELECT COUNT(*) INTO current_count
      FROM usage_stats
      WHERE user_id = NEW.user_id
        AND action_type = 'question_created'
        AND created_at >= DATE_TRUNC('month', NOW())
        AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
        
      IF current_count >= monthly_limit THEN
        RAISE EXCEPTION 'Question creation limit exceeded for free plan (% per month)', monthly_limit;
      END IF;
  END CASE;
  
  RETURN NEW;
END;
$$;

-- Create trigger to validate usage limits
DROP TRIGGER IF EXISTS trigger_validate_usage_limits ON usage_stats;
CREATE TRIGGER trigger_validate_usage_limits
  BEFORE INSERT ON usage_stats
  FOR EACH ROW
  EXECUTE FUNCTION validate_usage_limits();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_plan(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION can_create_assessment(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION can_download_pdf(UUID) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessments_autor_created_month 
ON assessments(autor_id, date_trunc('month', created_at));

CREATE INDEX IF NOT EXISTS idx_usage_stats_user_action_month 
ON usage_stats(user_id, action_type, date_trunc('month', created_at));

-- Add comments for documentation
COMMENT ON FUNCTION get_user_plan IS 'Returns the current active plan for a user (gratuito, premium, escolar)';
COMMENT ON FUNCTION can_create_assessment IS 'Checks if user can create an assessment based on their plan and current usage';
COMMENT ON FUNCTION can_download_pdf IS 'Checks if user can download a PDF based on their plan and current usage';
COMMENT ON TRIGGER trigger_track_assessment_creation ON assessments IS 'Automatically tracks assessment creation in usage_stats';
COMMENT ON TRIGGER trigger_validate_usage_limits ON usage_stats IS 'Validates usage limits before inserting usage records';

-- Test the functions (these will be removed in production)
-- Uncomment for testing:
-- SELECT get_user_plan('00000000-0000-0000-0000-000000000000'::UUID);
-- SELECT can_create_assessment('00000000-0000-0000-0000-000000000000'::UUID);
-- SELECT can_download_pdf('00000000-0000-0000-0000-000000000000'::UUID);
