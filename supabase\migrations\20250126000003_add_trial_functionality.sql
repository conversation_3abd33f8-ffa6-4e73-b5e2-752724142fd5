-- Add trial functionality to subscriptions table
-- This migration adds trial-specific fields and updates related functions

-- 1. Add trial fields to subscriptions table
ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS trial_start TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS trial_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS trial_status TEXT CHECK (trial_status IN ('active', 'ending', 'expired', 'converted', NULL));

-- 2. Create index for trial queries
CREATE INDEX IF NOT EXISTS idx_subscriptions_trial_status ON subscriptions(trial_status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_trial_end ON subscriptions(trial_end);

-- 3. Update get_user_plan function to include trialing status
CREATE OR REPLACE FUNCTION get_user_plan(p_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
BEGIN
  -- Get active or trialing subscription plan
  SELECT s.plano INTO user_plan
  FROM subscriptions s
  WHERE s.user_id = p_user_id 
    AND s.status IN ('active', 'trialing')  -- Include trialing status
  ORDER BY s.created_at DESC
  LIMIT 1;
  
  -- Default to free plan if no active/trialing subscription
  RETURN COALESCE(user_plan, 'gratuito');
END;
$$;

-- 4. Create function to check if user is in trial
CREATE OR REPLACE FUNCTION is_user_in_trial(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  trial_active BOOLEAN := FALSE;
BEGIN
  -- Check if user has active trial
  SELECT EXISTS(
    SELECT 1 FROM subscriptions s
    WHERE s.user_id = p_user_id 
      AND s.status = 'trialing'
      AND (s.trial_end IS NULL OR s.trial_end > NOW())
  ) INTO trial_active;
  
  RETURN trial_active;
END;
$$;

-- 5. Create function to get trial end date
CREATE OR REPLACE FUNCTION get_trial_end_date(p_user_id UUID)
RETURNS TIMESTAMP WITH TIME ZONE
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  trial_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  SELECT s.trial_end INTO trial_end_date
  FROM subscriptions s
  WHERE s.user_id = p_user_id 
    AND s.status = 'trialing'
  ORDER BY s.created_at DESC
  LIMIT 1;
  
  RETURN trial_end_date;
END;
$$;

-- 6. Update usage limit functions to include trial users
CREATE OR REPLACE FUNCTION can_create_assessment(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 5; -- Free plan limit
BEGIN
  -- Get user's plan (includes trialing users)
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans (including trials) have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count assessments created this month for free users
  SELECT COUNT(*) INTO current_count
  FROM assessments
  WHERE autor_id = p_user_id
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- 7. Update PDF download function to include trial users
CREATE OR REPLACE FUNCTION can_download_pdf(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 10; -- Free plan limit
BEGIN
  -- Get user's plan (includes trialing users)
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans (including trials) have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count PDF downloads this month for free users
  SELECT COUNT(*) INTO current_count
  FROM usage_stats
  WHERE user_id = p_user_id
    AND action_type = 'pdf_downloaded'
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- 8. Grant permissions for new functions
GRANT EXECUTE ON FUNCTION is_user_in_trial(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_trial_end_date(UUID) TO authenticated;

-- 9. Add comments for documentation
COMMENT ON COLUMN subscriptions.trial_start IS 'When the trial period started';
COMMENT ON COLUMN subscriptions.trial_end IS 'When the trial period ends';
COMMENT ON COLUMN subscriptions.trial_status IS 'Current trial status: active, ending, expired, converted';
COMMENT ON FUNCTION is_user_in_trial IS 'Checks if user currently has an active trial';
COMMENT ON FUNCTION get_trial_end_date IS 'Returns the trial end date for a user';

-- 10. Create trigger to automatically set trial_end when trial_start is set
CREATE OR REPLACE FUNCTION set_trial_end_date()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- If trial_start is being set and trial_end is not provided, calculate it
  IF NEW.trial_start IS NOT NULL AND NEW.trial_end IS NULL THEN
    NEW.trial_end := NEW.trial_start + INTERVAL '7 days';
  END IF;
  
  -- Set trial_status to 'active' if status is 'trialing' and trial_status is null
  IF NEW.status = 'trialing' AND NEW.trial_status IS NULL THEN
    NEW.trial_status := 'active';
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_set_trial_end_date ON subscriptions;
CREATE TRIGGER trigger_set_trial_end_date
  BEFORE INSERT OR UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION set_trial_end_date();
