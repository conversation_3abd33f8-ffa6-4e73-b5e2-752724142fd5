-- Trial Abuse Prevention System
-- This migration creates tables and functions to prevent trial abuse

-- 1. Create trial history table
CREATE TABLE IF NOT EXISTS trial_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  stripe_customer_id TEXT,
  plan_type TEXT NOT NULL CHECK (plan_type IN ('premium', 'escolar')),
  trial_start TIMESTAMP WITH TIME ZONE NOT NULL,
  trial_end TIMESTAMP WITH TIME ZONE NOT NULL,
  trial_status TEXT NOT NULL CHECK (trial_status IN ('active', 'ended', 'cancelled', 'converted')),
  ip_address INET,
  user_agent TEXT,
  payment_method_fingerprint TEXT, -- Stripe payment method fingerprint
  device_fingerprint TEXT, -- Custom device fingerprint
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_trial_history_email ON trial_history(email);
CREATE INDEX IF NOT EXISTS idx_trial_history_user_id ON trial_history(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_history_payment_method ON trial_history(payment_method_fingerprint);
CREATE INDEX IF NOT EXISTS idx_trial_history_device ON trial_history(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_trial_history_ip ON trial_history(ip_address);
CREATE INDEX IF NOT EXISTS idx_trial_history_created_at ON trial_history(created_at);

-- 3. Create function to check if user can start trial
CREATE OR REPLACE FUNCTION can_user_start_trial(
  p_user_id UUID,
  p_email TEXT,
  p_plan_type TEXT,
  p_ip_address INET DEFAULT NULL,
  p_payment_method_fingerprint TEXT DEFAULT NULL,
  p_device_fingerprint TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  email_trial_count INTEGER := 0;
  user_trial_count INTEGER := 0;
  ip_trial_count INTEGER := 0;
  payment_method_trial_count INTEGER := 0;
  device_trial_count INTEGER := 0;
  cooldown_period INTERVAL := INTERVAL '90 days';
  max_trials_per_email INTEGER := 1;
  max_trials_per_ip INTEGER := 3;
  max_trials_per_payment_method INTEGER := 1;
  max_trials_per_device INTEGER := 2;
  result JSONB;
BEGIN
  -- Check email-based trials (most restrictive)
  SELECT COUNT(*) INTO email_trial_count
  FROM trial_history
  WHERE LOWER(email) = LOWER(p_email)
    AND created_at > NOW() - cooldown_period;
  
  IF email_trial_count >= max_trials_per_email THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'email_limit_exceeded',
      'message', 'Este email já foi usado para um teste gratuito. Cada email pode ter apenas um teste.',
      'cooldown_ends', (
        SELECT created_at + cooldown_period
        FROM trial_history
        WHERE LOWER(email) = LOWER(p_email)
        ORDER BY created_at DESC
        LIMIT 1
      )
    );
  END IF;
  
  -- Check user-based trials
  SELECT COUNT(*) INTO user_trial_count
  FROM trial_history
  WHERE user_id = p_user_id
    AND created_at > NOW() - cooldown_period;
  
  IF user_trial_count >= max_trials_per_email THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'user_limit_exceeded',
      'message', 'Você já utilizou seu teste gratuito.',
      'cooldown_ends', (
        SELECT created_at + cooldown_period
        FROM trial_history
        WHERE user_id = p_user_id
        ORDER BY created_at DESC
        LIMIT 1
      )
    );
  END IF;
  
  -- Check IP-based trials (less restrictive, for shared networks)
  IF p_ip_address IS NOT NULL THEN
    SELECT COUNT(*) INTO ip_trial_count
    FROM trial_history
    WHERE ip_address = p_ip_address
      AND created_at > NOW() - INTERVAL '30 days'; -- Shorter period for IP
    
    IF ip_trial_count >= max_trials_per_ip THEN
      RETURN jsonb_build_object(
        'allowed', false,
        'reason', 'ip_limit_exceeded',
        'message', 'Muitos testes foram iniciados deste endereço IP. Tente novamente mais tarde.',
        'cooldown_ends', NOW() + INTERVAL '7 days'
      );
    END IF;
  END IF;
  
  -- Check payment method fingerprint
  IF p_payment_method_fingerprint IS NOT NULL THEN
    SELECT COUNT(*) INTO payment_method_trial_count
    FROM trial_history
    WHERE payment_method_fingerprint = p_payment_method_fingerprint
      AND created_at > NOW() - cooldown_period;
    
    IF payment_method_trial_count >= max_trials_per_payment_method THEN
      RETURN jsonb_build_object(
        'allowed', false,
        'reason', 'payment_method_limit_exceeded',
        'message', 'Este método de pagamento já foi usado para um teste gratuito.',
        'cooldown_ends', (
          SELECT created_at + cooldown_period
          FROM trial_history
          WHERE payment_method_fingerprint = p_payment_method_fingerprint
          ORDER BY created_at DESC
          LIMIT 1
        )
      );
    END IF;
  END IF;
  
  -- Check device fingerprint
  IF p_device_fingerprint IS NOT NULL THEN
    SELECT COUNT(*) INTO device_trial_count
    FROM trial_history
    WHERE device_fingerprint = p_device_fingerprint
      AND created_at > NOW() - cooldown_period;
    
    IF device_trial_count >= max_trials_per_device THEN
      RETURN jsonb_build_object(
        'allowed', false,
        'reason', 'device_limit_exceeded',
        'message', 'Este dispositivo já foi usado para testes gratuitos. Limite: ' || max_trials_per_device || ' testes por dispositivo.',
        'cooldown_ends', (
          SELECT created_at + cooldown_period
          FROM trial_history
          WHERE device_fingerprint = p_device_fingerprint
          ORDER BY created_at DESC
          LIMIT 1
        )
      );
    END IF;
  END IF;
  
  -- All checks passed
  RETURN jsonb_build_object(
    'allowed', true,
    'reason', 'eligible',
    'message', 'Usuário elegível para teste gratuito.'
  );
END;
$$;

-- 4. Create function to record trial start
CREATE OR REPLACE FUNCTION record_trial_start(
  p_user_id UUID,
  p_email TEXT,
  p_plan_type TEXT,
  p_trial_start TIMESTAMP WITH TIME ZONE,
  p_trial_end TIMESTAMP WITH TIME ZONE,
  p_stripe_customer_id TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_payment_method_fingerprint TEXT DEFAULT NULL,
  p_device_fingerprint TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  trial_history_id UUID;
BEGIN
  INSERT INTO trial_history (
    user_id,
    email,
    stripe_customer_id,
    plan_type,
    trial_start,
    trial_end,
    trial_status,
    ip_address,
    user_agent,
    payment_method_fingerprint,
    device_fingerprint
  ) VALUES (
    p_user_id,
    p_email,
    p_stripe_customer_id,
    p_plan_type,
    p_trial_start,
    p_trial_end,
    'active',
    p_ip_address,
    p_user_agent,
    p_payment_method_fingerprint,
    p_device_fingerprint
  ) RETURNING id INTO trial_history_id;
  
  RETURN trial_history_id;
END;
$$;

-- 5. Create function to update trial status
CREATE OR REPLACE FUNCTION update_trial_status(
  p_stripe_customer_id TEXT,
  p_new_status TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE trial_history
  SET 
    trial_status = p_new_status,
    updated_at = NOW()
  WHERE stripe_customer_id = p_stripe_customer_id
    AND trial_status = 'active';
  
  RETURN FOUND;
END;
$$;

-- 6. Create trigger to automatically update trial_history when subscriptions change
CREATE OR REPLACE FUNCTION sync_trial_history()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update trial history when subscription status changes
  IF NEW.status != OLD.status AND NEW.stripe_customer_id IS NOT NULL THEN
    CASE NEW.status
      WHEN 'active' THEN
        PERFORM update_trial_status(NEW.stripe_customer_id, 'converted');
      WHEN 'canceled' THEN
        PERFORM update_trial_status(NEW.stripe_customer_id, 'cancelled');
      ELSE
        -- Keep existing status
    END CASE;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_sync_trial_history ON subscriptions;
CREATE TRIGGER trigger_sync_trial_history
  AFTER UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION sync_trial_history();

-- 7. Grant permissions
GRANT EXECUTE ON FUNCTION can_user_start_trial(UUID, TEXT, TEXT, INET, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION record_trial_start(UUID, TEXT, TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, INET, TEXT, TEXT, TEXT) TO authenticated;

-- 8. Create RLS policies for trial_history
ALTER TABLE trial_history ENABLE ROW LEVEL SECURITY;

-- Users can only see their own trial history
CREATE POLICY "Users can view own trial history" ON trial_history
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- Only system can insert trial history (via functions)
CREATE POLICY "System can insert trial history" ON trial_history
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

-- 9. Add comments for documentation
COMMENT ON TABLE trial_history IS 'Tracks all trial attempts to prevent abuse';
COMMENT ON FUNCTION can_user_start_trial IS 'Checks if user is eligible for a trial based on multiple factors';
COMMENT ON FUNCTION record_trial_start IS 'Records the start of a new trial period';
COMMENT ON FUNCTION update_trial_status IS 'Updates trial status when subscription changes';

-- 10. Create view for admin monitoring
CREATE OR REPLACE VIEW trial_abuse_monitoring AS
SELECT 
  email,
  COUNT(*) as trial_count,
  array_agg(DISTINCT plan_type) as plans_tried,
  array_agg(DISTINCT ip_address::text) as ip_addresses,
  MIN(created_at) as first_trial,
  MAX(created_at) as last_trial,
  array_agg(DISTINCT trial_status) as statuses
FROM trial_history
GROUP BY email
HAVING COUNT(*) > 1
ORDER BY trial_count DESC, last_trial DESC;

COMMENT ON VIEW trial_abuse_monitoring IS 'Admin view to monitor potential trial abuse patterns';
