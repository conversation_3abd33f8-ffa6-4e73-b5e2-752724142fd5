-- Migration: Question Feedback System
-- Creates comprehensive feedback system for questions with moderation capabilities

-- Create question_feedback table
CREATE TABLE IF NOT EXISTS question_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
  comment TEXT,
  helpful BOOLEAN,
  suggestions TEXT[] DEFAULT '{}',
  feedback_type TEXT CHECK (feedback_type IN ('rating', 'improvement', 'error', 'general')) DEFAULT 'general',
  is_approved BOOLEAN DEFAULT false,
  is_reviewed BOOLEAN DEFAULT false,
  admin_response TEXT,
  reviewed_by UUID REFERENCES profiles(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate feedback from same user on same question
  UNIQUE(user_id, question_id)
);

-- Create feedback_actions table for admin audit trail
CREATE TABLE IF NOT EXISTS feedback_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  feedback_id UUID REFERENCES question_feedback(id) ON DELETE CASCADE NOT NULL,
  admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  action_type TEXT CHECK (action_type IN ('approved', 'rejected', 'flagged', 'responded')) NOT NULL,
  reason TEXT,
  previous_status JSONB,
  new_status JSONB,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_question_feedback_question ON question_feedback(question_id);
CREATE INDEX IF NOT EXISTS idx_question_feedback_user ON question_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_question_feedback_approved ON question_feedback(is_approved);
CREATE INDEX IF NOT EXISTS idx_question_feedback_reviewed ON question_feedback(is_reviewed);
CREATE INDEX IF NOT EXISTS idx_question_feedback_rating ON question_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_question_feedback_type ON question_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_question_feedback_created ON question_feedback(created_at);

CREATE INDEX IF NOT EXISTS idx_feedback_actions_feedback ON feedback_actions(feedback_id);
CREATE INDEX IF NOT EXISTS idx_feedback_actions_admin ON feedback_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_feedback_actions_type ON feedback_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_feedback_actions_created ON feedback_actions(created_at);

-- Enable RLS
ALTER TABLE question_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE feedback_actions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for question_feedback
-- Users can view their own feedback and approved feedback from others
CREATE POLICY "Users can view own feedback" ON question_feedback
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view approved feedback" ON question_feedback
  FOR SELECT USING (is_approved = true);

-- Users can insert their own feedback
CREATE POLICY "Users can create feedback" ON question_feedback
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own unreviewed feedback
CREATE POLICY "Users can update own unreviewed feedback" ON question_feedback
  FOR UPDATE USING (auth.uid() = user_id AND is_reviewed = false);

-- Admins can view all feedback
CREATE POLICY "Admins can view all feedback" ON question_feedback
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- Admins can update any feedback
CREATE POLICY "Admins can update feedback" ON question_feedback
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- RLS Policies for feedback_actions
-- Only admins can view and create feedback actions
CREATE POLICY "Admins can view feedback actions" ON feedback_actions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

CREATE POLICY "Admins can create feedback actions" ON feedback_actions
  FOR INSERT WITH CHECK (
    auth.uid() = admin_id AND
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- Function to update question rating when feedback is approved
CREATE OR REPLACE FUNCTION update_question_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update when feedback is approved
  IF NEW.is_approved = true AND (OLD.is_approved = false OR OLD.is_approved IS NULL) THEN
    UPDATE questions 
    SET 
      rating = (
        SELECT COALESCE(AVG(rating::DECIMAL), 0)
        FROM question_feedback 
        WHERE question_id = NEW.question_id 
        AND is_approved = true
      ),
      rating_count = (
        SELECT COUNT(*)
        FROM question_feedback 
        WHERE question_id = NEW.question_id 
        AND is_approved = true
      ),
      updated_at = NOW()
    WHERE id = NEW.question_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log feedback actions
CREATE OR REPLACE FUNCTION log_feedback_action()
RETURNS TRIGGER AS $$
BEGIN
  -- Log when feedback status changes
  IF OLD.is_approved != NEW.is_approved OR OLD.is_reviewed != NEW.is_reviewed THEN
    INSERT INTO feedback_actions (
      feedback_id,
      admin_id,
      action_type,
      previous_status,
      new_status,
      notes
    ) VALUES (
      NEW.id,
      NEW.reviewed_by,
      CASE 
        WHEN NEW.is_approved = true THEN 'approved'
        WHEN NEW.is_reviewed = true AND NEW.is_approved = false THEN 'rejected'
        ELSE 'reviewed'
      END,
      jsonb_build_object(
        'is_approved', OLD.is_approved,
        'is_reviewed', OLD.is_reviewed
      ),
      jsonb_build_object(
        'is_approved', NEW.is_approved,
        'is_reviewed', NEW.is_reviewed
      ),
      NEW.admin_response
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
CREATE TRIGGER trigger_update_question_rating
  AFTER UPDATE OF is_approved ON question_feedback
  FOR EACH ROW
  EXECUTE FUNCTION update_question_rating();

CREATE TRIGGER trigger_log_feedback_action
  AFTER UPDATE ON question_feedback
  FOR EACH ROW
  WHEN (OLD.is_approved IS DISTINCT FROM NEW.is_approved OR OLD.is_reviewed IS DISTINCT FROM NEW.is_reviewed)
  EXECUTE FUNCTION log_feedback_action();

-- Function to get feedback statistics
CREATE OR REPLACE FUNCTION get_feedback_stats(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  total_feedback BIGINT,
  approved_feedback BIGINT,
  pending_feedback BIGINT,
  rejected_feedback BIGINT,
  avg_rating DECIMAL,
  feedback_by_type JSONB,
  top_issues JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_feedback,
    COUNT(*) FILTER (WHERE qf.is_approved = true) as approved_feedback,
    COUNT(*) FILTER (WHERE qf.is_reviewed = false) as pending_feedback,
    COUNT(*) FILTER (WHERE qf.is_reviewed = true AND qf.is_approved = false) as rejected_feedback,
    COALESCE(AVG(qf.rating), 0) as avg_rating,
    jsonb_object_agg(
      qf.feedback_type, 
      COUNT(*) FILTER (WHERE qf.feedback_type IS NOT NULL)
    ) as feedback_by_type,
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'suggestion', suggestion,
          'count', suggestion_count
        )
      )
      FROM (
        SELECT 
          unnest(suggestions) as suggestion,
          COUNT(*) as suggestion_count
        FROM question_feedback qf2
        WHERE (start_date IS NULL OR qf2.created_at >= start_date)
          AND (end_date IS NULL OR qf2.created_at <= end_date)
          AND array_length(suggestions, 1) > 0
        GROUP BY unnest(suggestions)
        ORDER BY suggestion_count DESC
        LIMIT 10
      ) top_suggestions
    ) as top_issues
  FROM question_feedback qf
  WHERE (start_date IS NULL OR qf.created_at >= start_date)
    AND (end_date IS NULL OR qf.created_at <= end_date);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_feedback_stats TO authenticated;
GRANT EXECUTE ON FUNCTION update_question_rating TO authenticated;
GRANT EXECUTE ON FUNCTION log_feedback_action TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE question_feedback IS 'Stores user feedback and ratings for questions with moderation capabilities';
COMMENT ON TABLE feedback_actions IS 'Audit trail for administrative actions on feedback';
COMMENT ON FUNCTION get_feedback_stats IS 'Returns comprehensive feedback statistics for admin dashboard';
COMMENT ON FUNCTION update_question_rating IS 'Automatically updates question rating when feedback is approved';
COMMENT ON FUNCTION log_feedback_action IS 'Logs administrative actions on feedback for audit trail';
