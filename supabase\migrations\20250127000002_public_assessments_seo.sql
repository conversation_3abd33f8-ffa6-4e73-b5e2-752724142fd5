-- Migration: Public Assessments SEO System
-- Adds support for public assessments with SEO optimization, conversion tracking, and categorization

-- 1. Add SEO fields to existing assessments table
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS slug TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_title TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_description TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS seo_keywords TEXT[] DEFAULT '{}';
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS featured_image_url TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS public_category TEXT;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS difficulty_level TEXT CHECK (difficulty_level IN ('Fácil', 'Médio', 'Difícil'));
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS estimated_duration INTEGER; -- em minutos
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS download_count INTEGER DEFAULT 0;
ALTER TABLE assessments ADD COLUMN IF NOT EXISTS conversion_count INTEGER DEFAULT 0;

-- 2. Create unique constraint for slug (only for non-null values)
CREATE UNIQUE INDEX IF NOT EXISTS idx_assessments_slug_unique ON assessments(slug) WHERE slug IS NOT NULL;

-- 3. Create table for conversion tracking
CREATE TABLE IF NOT EXISTS public_assessment_conversions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  conversion_type TEXT NOT NULL CHECK (conversion_type IN ('signup', 'upgrade', 'download')),
  source_page TEXT NOT NULL, -- URL da página de origem
  user_agent TEXT,
  ip_address INET,
  utm_source TEXT,
  utm_medium TEXT,
  utm_campaign TEXT,
  utm_content TEXT,
  utm_term TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create table for public categories
CREATE TABLE IF NOT EXISTS public_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  color TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  seo_title TEXT,
  seo_description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create table for sitemap cache
CREATE TABLE IF NOT EXISTS sitemap_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL UNIQUE,
  last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  change_frequency TEXT DEFAULT 'weekly',
  priority DECIMAL(2,1) DEFAULT 0.5,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create performance indexes
CREATE INDEX IF NOT EXISTS idx_assessments_slug ON assessments(slug);
CREATE INDEX IF NOT EXISTS idx_assessments_public_category ON assessments(public_category);
CREATE INDEX IF NOT EXISTS idx_assessments_is_public_featured ON assessments(is_public, is_featured);
CREATE INDEX IF NOT EXISTS idx_assessments_view_count ON assessments(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_assessments_public_seo ON assessments(is_public, created_at DESC) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_public_conversions_assessment ON public_assessment_conversions(assessment_id);
CREATE INDEX IF NOT EXISTS idx_public_conversions_created ON public_assessment_conversions(created_at);
CREATE INDEX IF NOT EXISTS idx_public_conversions_type ON public_assessment_conversions(conversion_type);
CREATE INDEX IF NOT EXISTS idx_public_categories_slug ON public_categories(slug);
CREATE INDEX IF NOT EXISTS idx_public_categories_active ON public_categories(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_sitemap_cache_url ON sitemap_cache(url);
CREATE INDEX IF NOT EXISTS idx_sitemap_cache_active ON sitemap_cache(is_active, last_modified);

-- 7. Function to generate unique slug
CREATE OR REPLACE FUNCTION generate_unique_slug(base_title TEXT)
RETURNS TEXT AS $$
DECLARE
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER := 0;
BEGIN
    -- Convert title to slug
    base_slug := lower(trim(regexp_replace(base_title, '[^a-zA-Z0-9\s\-àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]', '', 'g')));
    base_slug := regexp_replace(base_slug, '[àáâãäå]', 'a', 'g');
    base_slug := regexp_replace(base_slug, '[èéêë]', 'e', 'g');
    base_slug := regexp_replace(base_slug, '[ìíîï]', 'i', 'g');
    base_slug := regexp_replace(base_slug, '[òóôõöø]', 'o', 'g');
    base_slug := regexp_replace(base_slug, '[ùúûü]', 'u', 'g');
    base_slug := regexp_replace(base_slug, '[ç]', 'c', 'g');
    base_slug := regexp_replace(base_slug, '[ñ]', 'n', 'g');
    base_slug := regexp_replace(base_slug, '\s+', '-', 'g');
    base_slug := regexp_replace(base_slug, '-+', '-', 'g');
    base_slug := trim(base_slug, '-');
    
    -- Limit size
    IF length(base_slug) > 50 THEN
        base_slug := substring(base_slug, 1, 50);
        base_slug := trim(base_slug, '-');
    END IF;
    
    final_slug := base_slug;
    
    -- Check if slug exists and add counter if necessary
    WHILE EXISTS (SELECT 1 FROM assessments WHERE slug = final_slug) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- 8. Trigger to auto-generate slug
CREATE OR REPLACE FUNCTION auto_generate_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate slug for public assessments or when explicitly requested
    IF (NEW.is_public = true OR NEW.slug IS NOT NULL) AND (NEW.slug IS NULL OR NEW.slug = '') THEN
        NEW.slug := generate_unique_slug(NEW.titulo);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_generate_assessment_slug
    BEFORE INSERT OR UPDATE ON assessments
    FOR EACH ROW EXECUTE FUNCTION auto_generate_slug();

-- 9. Function to increment view count
CREATE OR REPLACE FUNCTION increment_view_count(assessment_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE assessments 
    SET view_count = COALESCE(view_count, 0) + 1 
    WHERE id = assessment_id;
END;
$$ LANGUAGE plpgsql;

-- 10. Function to track conversion
CREATE OR REPLACE FUNCTION track_conversion(
    p_assessment_id UUID,
    p_user_id UUID,
    p_conversion_type TEXT,
    p_source_page TEXT,
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_utm_source TEXT DEFAULT NULL,
    p_utm_medium TEXT DEFAULT NULL,
    p_utm_campaign TEXT DEFAULT NULL,
    p_utm_content TEXT DEFAULT NULL,
    p_utm_term TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    conversion_id UUID;
BEGIN
    INSERT INTO public_assessment_conversions (
        assessment_id, user_id, conversion_type, source_page,
        user_agent, ip_address, utm_source, utm_medium,
        utm_campaign, utm_content, utm_term, metadata
    ) VALUES (
        p_assessment_id, p_user_id, p_conversion_type, p_source_page,
        p_user_agent, p_ip_address, p_utm_source, p_utm_medium,
        p_utm_campaign, p_utm_content, p_utm_term, p_metadata
    ) RETURNING id INTO conversion_id;
    
    -- Update conversion count on assessment
    UPDATE assessments 
    SET conversion_count = COALESCE(conversion_count, 0) + 1 
    WHERE id = p_assessment_id;
    
    RETURN conversion_id;
END;
$$ LANGUAGE plpgsql;

-- 11. Enable RLS on new tables
ALTER TABLE public_assessment_conversions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE sitemap_cache ENABLE ROW LEVEL SECURITY;

-- 12. RLS Policies for public_assessment_conversions
CREATE POLICY "Admins can view all conversions" ON public_assessment_conversions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

CREATE POLICY "System can insert conversions" ON public_assessment_conversions
  FOR INSERT WITH CHECK (true); -- Allow system to track conversions

-- 13. RLS Policies for public_categories
CREATE POLICY "Public categories are viewable by everyone" ON public_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage categories" ON public_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- 14. RLS Policies for sitemap_cache
CREATE POLICY "Sitemap is viewable by everyone" ON sitemap_cache
  FOR SELECT USING (is_active = true);

CREATE POLICY "System can manage sitemap" ON sitemap_cache
  FOR ALL WITH CHECK (true); -- Allow system to manage sitemap

-- 15. Grant permissions for functions
GRANT EXECUTE ON FUNCTION generate_unique_slug(TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION increment_view_count(UUID) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION track_conversion(UUID, UUID, TEXT, TEXT, TEXT, INET, TEXT, TEXT, TEXT, TEXT, TEXT, JSONB) TO authenticated, anon;

-- 16. Insert default public categories
INSERT INTO public_categories (name, slug, description, icon, color, sort_order) VALUES
('Matemática', 'matematica', 'Avaliações de matemática para todos os níveis', '📊', '#3B82F6', 1),
('Português', 'portugues', 'Avaliações de língua portuguesa e literatura', '📚', '#10B981', 2),
('Ciências', 'ciencias', 'Avaliações de ciências naturais e biologia', '🔬', '#8B5CF6', 3),
('História', 'historia', 'Avaliações de história e estudos sociais', '📜', '#F59E0B', 4),
('Geografia', 'geografia', 'Avaliações de geografia e meio ambiente', '🌍', '#06B6D4', 5),
('Inglês', 'ingles', 'Avaliações de língua inglesa', '🇺🇸', '#EF4444', 6),
('Educação Física', 'educacao-fisica', 'Avaliações teóricas de educação física', '⚽', '#84CC16', 7),
('Artes', 'artes', 'Avaliações de artes visuais e música', '🎨', '#EC4899', 8)
ON CONFLICT (slug) DO NOTHING;
