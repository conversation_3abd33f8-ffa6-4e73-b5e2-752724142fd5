/*
  # Template Management Refactor - Phase 1
  
  1. Schema Updates
    - Add missing fields to templates table: descricao, content, metadata
    - Migrate existing layout_config data to new content structure
    - Update indexes for better performance
  
  2. Data Migration
    - Convert existing templates to new structure
    - Preserve backward compatibility
  
  3. Security
    - Update RLS policies if needed
    - Add validation constraints
*/

-- Add missing fields to templates table
ALTER TABLE templates 
ADD COLUMN IF NOT EXISTS descricao TEXT,
ADD COLUMN IF NOT EXISTS content JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Create indexes for new fields
CREATE INDEX IF NOT EXISTS idx_templates_content ON templates USING GIN (content);
CREATE INDEX IF NOT EXISTS idx_templates_metadata ON templates USING GIN (metadata);

-- Migrate existing data from layout_config to content
UPDATE templates 
SET 
  content = CASE 
    WHEN layout_config IS NOT NULL THEN 
      jsonb_build_object(
        'structure', jsonb_build_object(
          'includeHeader', true,
          'includeInstructions', true,
          'includeAnswerSheet', COALESCE((layout_config->>'folhaRespostas')::boolean, false),
          'pageBreaks', COALESCE((layout_config->>'quebrasPagina')::boolean, true)
        ),
        'questions', jsonb_build_object(
          'includeQuestions', false,
          'questionIds', '[]'::jsonb,
          'preserveOrder', true
        ),
        'layout', jsonb_build_object(
          'fontSize', COALESCE(layout_config->>'fontSize', 'medium'),
          'lineSpacing', COALESCE(layout_config->>'espacamento', 'normal'),
          'paperSize', 'A4',
          'orientation', 'portrait'
        ),
        'customization', jsonb_build_object(
          'headerConfig', COALESCE(layout_config->'cabecalho', '{}'::jsonb),
          'footerConfig', '{}'::jsonb
        ),
        'legacy', jsonb_build_object(
          'migratedFrom', 'layout_config',
          'originalData', layout_config
        )
      )
    ELSE '{}'::jsonb
  END,
  descricao = CASE 
    WHEN descricao IS NULL THEN 'Template migrado automaticamente do sistema anterior'
    ELSE descricao
  END,
  metadata = CASE 
    WHEN metadata = '{}'::jsonb THEN 
      jsonb_build_object(
        'version', '2.0',
        'migrated', true,
        'migratedAt', NOW()::text,
        'compatibility', 'legacy'
      )
    ELSE metadata
  END
WHERE content = '{}'::jsonb OR content IS NULL;

-- Add validation constraints
ALTER TABLE templates 
ADD CONSTRAINT templates_content_not_empty 
CHECK (content IS NOT NULL AND content != '{}'::jsonb);

-- Update existing RLS policies to include new fields
-- (Policies should already cover all columns, but let's be explicit)

-- Create function to validate template content structure
CREATE OR REPLACE FUNCTION validate_template_content(content_data JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if content has required structure
  IF content_data IS NULL OR content_data = '{}'::jsonb THEN
    RETURN FALSE;
  END IF;
  
  -- Validate required sections exist
  IF NOT (content_data ? 'structure' AND content_data ? 'layout') THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to validate content on insert/update
CREATE OR REPLACE FUNCTION validate_template_content_trigger()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT validate_template_content(NEW.content) THEN
    RAISE EXCEPTION 'Invalid template content structure';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS validate_template_content_trigger ON templates;
CREATE TRIGGER validate_template_content_trigger
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION validate_template_content_trigger();

-- Update updated_at trigger to include new fields
CREATE OR REPLACE FUNCTION update_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_templates_updated_at ON templates;
CREATE TRIGGER update_templates_updated_at
  BEFORE UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION update_templates_updated_at();

-- Add comments for documentation
COMMENT ON COLUMN templates.descricao IS 'Descrição detalhada do template para usuários';
COMMENT ON COLUMN templates.content IS 'Estrutura JSONB contendo configurações, questões e layout do template';
COMMENT ON COLUMN templates.metadata IS 'Metadados adicionais como versão, compatibilidade e informações de migração';

-- Create view for easier template querying with structured content
CREATE OR REPLACE VIEW templates_with_structure AS
SELECT 
  t.*,
  (t.content->'structure'->>'includeHeader')::boolean as include_header,
  (t.content->'structure'->>'includeQuestions')::boolean as include_questions,
  (t.content->'questions'->>'questionIds')::jsonb as question_ids,
  (t.content->'layout'->>'fontSize') as font_size,
  (t.content->'layout'->>'lineSpacing') as line_spacing,
  (t.metadata->>'version') as content_version
FROM templates t;

-- Grant appropriate permissions
GRANT SELECT ON templates_with_structure TO authenticated;
