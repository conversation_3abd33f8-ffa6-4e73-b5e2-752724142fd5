-- Migration: SEO Management System
-- Description: Create tables for SEO global settings and page-specific configurations
-- Date: 2025-01-28

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table for global SEO settings
CREATE TABLE IF NOT EXISTS seo_global_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  site_title TEXT NOT NULL DEFAULT 'Atividade Pronta | Crie avaliações em minutos',
  site_description TEXT DEFAULT 'A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados.',
  global_keywords TEXT[] DEFAULT ARRAY['avaliação', 'educação', 'professores', 'questões', 'ensino', 'atividades'],
  og_image_url TEXT DEFAULT 'https://atvpronta.com.br/og-image.png',
  robots_txt TEXT DEFAULT 'User-agent: *
Allow: /
Allow: /avaliacoes
Allow: /avaliacoes/*

# Disallow admin and app routes
Disallow: /app/
Disallow: /admin/
Disallow: /api/

# Disallow auth pages
Disallow: /login
Disallow: /register

# Allow sitemap
Sitemap: https://atvpronta.com.br/sitemap.xml

# Crawl delay
Crawl-delay: 1',
  twitter_card_type TEXT DEFAULT 'summary_large_image',
  facebook_app_id TEXT,
  google_site_verification TEXT,
  bing_site_verification TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for page-specific SEO settings
CREATE TABLE IF NOT EXISTS seo_page_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  page_path TEXT UNIQUE NOT NULL,
  page_name TEXT NOT NULL,
  title TEXT,
  description TEXT,
  keywords TEXT[],
  og_image_url TEXT,
  canonical_url TEXT,
  meta_robots TEXT DEFAULT 'index,follow',
  priority DECIMAL(2,1) DEFAULT 0.8 CHECK (priority >= 0.0 AND priority <= 1.0),
  change_frequency TEXT DEFAULT 'weekly' CHECK (change_frequency IN ('always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for SEO performance metrics
CREATE TABLE IF NOT EXISTS seo_page_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  page_url TEXT NOT NULL,
  lcp_score DECIMAL(8,2),
  fid_score DECIMAL(8,2),
  cls_score DECIMAL(8,3),
  fcp_score DECIMAL(8,2),
  ttfb_score DECIMAL(8,2),
  performance_score INTEGER CHECK (performance_score >= 0 AND performance_score <= 100),
  seo_score INTEGER CHECK (seo_score >= 0 AND seo_score <= 100),
  accessibility_score INTEGER CHECK (accessibility_score >= 0 AND accessibility_score <= 100),
  best_practices_score INTEGER CHECK (best_practices_score >= 0 AND best_practices_score <= 100),
  mobile_score INTEGER CHECK (mobile_score >= 0 AND mobile_score <= 100),
  desktop_score INTEGER CHECK (desktop_score >= 0 AND desktop_score <= 100),
  last_checked TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for SEO recommendations and alerts
CREATE TABLE IF NOT EXISTS seo_recommendations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  page_url TEXT NOT NULL,
  recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('performance', 'seo', 'accessibility', 'best_practices')),
  priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  impact_score INTEGER CHECK (impact_score >= 0 AND impact_score <= 100),
  is_resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for keyword tracking
CREATE TABLE IF NOT EXISTS seo_keyword_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  keyword TEXT NOT NULL,
  page_url TEXT NOT NULL,
  search_volume INTEGER,
  difficulty_score INTEGER CHECK (difficulty_score >= 0 AND difficulty_score <= 100),
  current_position INTEGER,
  previous_position INTEGER,
  best_position INTEGER,
  clicks INTEGER DEFAULT 0,
  impressions INTEGER DEFAULT 0,
  ctr DECIMAL(5,4),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(keyword, page_url)
);

-- Insert default global settings
INSERT INTO seo_global_settings (id) VALUES (uuid_generate_v4())
ON CONFLICT DO NOTHING;

-- Insert default page settings for main pages
INSERT INTO seo_page_settings (page_path, page_name, title, description, keywords, canonical_url) VALUES
('/', 'Página Inicial', 'Atividade Pronta | Crie avaliações em minutos', 'A plataforma completa para professores criarem avaliações profissionais, com banco de questões inteligente, templates e geração de PDFs otimizados.', ARRAY['avaliação', 'educação', 'professores', 'questões', 'ensino'], 'https://atvpronta.com.br/'),
('/avaliacoes', 'Avaliações Públicas', 'Avaliações Educacionais Gratuitas | Atividade Pronta', 'Acesse milhares de avaliações educacionais gratuitas para todos os níveis de ensino. Material didático de qualidade para professores.', ARRAY['avaliações educacionais', 'atividades escolares', 'ensino fundamental', 'ensino médio', 'material didático'], 'https://atvpronta.com.br/avaliacoes'),
('/avaliacoes/matematica', 'Avaliações de Matemática', 'Avaliações de Matemática | Atividade Pronta', 'Avaliações de matemática para ensino fundamental e médio. Exercícios, problemas e questões organizadas por série e conteúdo.', ARRAY['matemática', 'avaliações matemática', 'exercícios matemática', 'ensino fundamental', 'ensino médio'], 'https://atvpronta.com.br/avaliacoes/matematica'),
('/avaliacoes/portugues', 'Avaliações de Português', 'Avaliações de Português | Atividade Pronta', 'Avaliações de língua portuguesa com foco em interpretação, gramática e produção textual para todos os níveis.', ARRAY['português', 'língua portuguesa', 'interpretação de texto', 'gramática', 'redação'], 'https://atvpronta.com.br/avaliacoes/portugues')
ON CONFLICT (page_path) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_seo_page_settings_path ON seo_page_settings(page_path);
CREATE INDEX IF NOT EXISTS idx_seo_page_settings_active ON seo_page_settings(is_active);
CREATE INDEX IF NOT EXISTS idx_seo_page_metrics_url ON seo_page_metrics(page_url);
CREATE INDEX IF NOT EXISTS idx_seo_page_metrics_checked ON seo_page_metrics(last_checked);
CREATE INDEX IF NOT EXISTS idx_seo_recommendations_url ON seo_recommendations(page_url);
CREATE INDEX IF NOT EXISTS idx_seo_recommendations_priority ON seo_recommendations(priority);
CREATE INDEX IF NOT EXISTS idx_seo_recommendations_resolved ON seo_recommendations(is_resolved);
CREATE INDEX IF NOT EXISTS idx_seo_keyword_tracking_keyword ON seo_keyword_tracking(keyword);
CREATE INDEX IF NOT EXISTS idx_seo_keyword_tracking_url ON seo_keyword_tracking(page_url);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_seo_global_settings_updated_at BEFORE UPDATE ON seo_global_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_seo_page_settings_updated_at BEFORE UPDATE ON seo_page_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE seo_global_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_page_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_page_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_keyword_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for admin access
CREATE POLICY "Admin can manage SEO global settings" ON seo_global_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage SEO page settings" ON seo_page_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage SEO metrics" ON seo_page_metrics
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage SEO recommendations" ON seo_recommendations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage keyword tracking" ON seo_keyword_tracking
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Grant permissions
GRANT ALL ON seo_global_settings TO authenticated;
GRANT ALL ON seo_page_settings TO authenticated;
GRANT ALL ON seo_page_metrics TO authenticated;
GRANT ALL ON seo_recommendations TO authenticated;
GRANT ALL ON seo_keyword_tracking TO authenticated;
