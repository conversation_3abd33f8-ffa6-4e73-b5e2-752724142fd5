-- EduAssess Database Schema
-- This migration creates the initial database structure

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  nome TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  escola TEXT,
  disciplinas TEXT[] DEFAULT '{}',
  plano TEXT DEFAULT 'gratuito' CHECK (plano IN ('gratuito', 'premium', 'escolar')),
  avatar_url TEXT,
  configuracoes JSONB DEFAULT '{}',
  estatisticas JSONB DEFAULT '{}',
  onboarding_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create questions table
CREATE TABLE IF NOT EXISTS questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  disciplina TEXT NOT NULL,
  serie TEXT NOT NULL,
  topico TEXT NOT NULL,
  subtopico TEXT,
  dificuldade TEXT NOT NULL CHECK (dificuldade IN ('Fácil', 'Médio', 'Difícil')),
  tipo TEXT NOT NULL CHECK (tipo IN ('multipla_escolha', 'dissertativa', 'verdadeiro_falso')),
  competencia_bncc TEXT,
  enunciado TEXT NOT NULL,
  alternativas JSONB,
  resposta_correta TEXT NOT NULL,
  explicacao TEXT NOT NULL,
  imagem_url TEXT,
  tags TEXT[] DEFAULT '{}',
  autor_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  uso_count INTEGER DEFAULT 0,
  rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  is_public BOOLEAN DEFAULT true,
  is_verified BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assessments table
CREATE TABLE IF NOT EXISTS assessments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  titulo TEXT NOT NULL,
  disciplina TEXT NOT NULL,
  serie TEXT NOT NULL,
  questoes_ids UUID[] DEFAULT '{}',
  configuracao JSONB NOT NULL DEFAULT '{}',
  template_id UUID,
  autor_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT false,
  versoes INTEGER DEFAULT 1,
  estatisticas JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create templates table
CREATE TABLE IF NOT EXISTS templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome TEXT NOT NULL,
  categoria TEXT NOT NULL,
  layout_config JSONB NOT NULL,
  preview_image TEXT,
  is_premium BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false,
  autor_id UUID REFERENCES profiles(id),
  rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  downloads INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create favorites table
CREATE TABLE IF NOT EXISTS favorites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, question_id)
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT UNIQUE,
  plano TEXT NOT NULL,
  status TEXT NOT NULL,
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_stats table
CREATE TABLE IF NOT EXISTS usage_stats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  resource_id UUID,
  resource_type TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_questions_disciplina ON questions(disciplina);
CREATE INDEX IF NOT EXISTS idx_questions_serie ON questions(serie);
CREATE INDEX IF NOT EXISTS idx_questions_topico ON questions(topico);
CREATE INDEX IF NOT EXISTS idx_questions_dificuldade ON questions(dificuldade);
CREATE INDEX IF NOT EXISTS idx_questions_tipo ON questions(tipo);
CREATE INDEX IF NOT EXISTS idx_questions_autor ON questions(autor_id);
CREATE INDEX IF NOT EXISTS idx_questions_public ON questions(is_public);
CREATE INDEX IF NOT EXISTS idx_questions_tags ON questions USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_assessments_autor ON assessments(autor_id);
CREATE INDEX IF NOT EXISTS idx_assessments_disciplina ON assessments(disciplina);
CREATE INDEX IF NOT EXISTS idx_assessments_serie ON assessments(serie);

CREATE INDEX IF NOT EXISTS idx_favorites_user ON favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_question ON favorites(question_id);

CREATE INDEX IF NOT EXISTS idx_subscriptions_user ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

CREATE INDEX IF NOT EXISTS idx_usage_stats_user ON usage_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_stats_action ON usage_stats(action_type);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at 
    BEFORE UPDATE ON questions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessments_updated_at 
    BEFORE UPDATE ON assessments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at 
    BEFORE UPDATE ON templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_stats ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles 
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Questions policies
CREATE POLICY "Public questions are viewable by everyone" ON questions 
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view own questions" ON questions 
  FOR SELECT USING (auth.uid() = autor_id);

CREATE POLICY "Users can create questions" ON questions 
  FOR INSERT WITH CHECK (auth.uid() = autor_id);

CREATE POLICY "Users can update own questions" ON questions 
  FOR UPDATE USING (auth.uid() = autor_id);

CREATE POLICY "Users can delete own questions" ON questions 
  FOR DELETE USING (auth.uid() = autor_id);

-- Assessments policies
CREATE POLICY "Users can view own assessments" ON assessments 
  FOR SELECT USING (auth.uid() = autor_id);

CREATE POLICY "Public assessments are viewable by everyone" ON assessments 
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can create assessments" ON assessments 
  FOR INSERT WITH CHECK (auth.uid() = autor_id);

CREATE POLICY "Users can update own assessments" ON assessments 
  FOR UPDATE USING (auth.uid() = autor_id);

CREATE POLICY "Users can delete own assessments" ON assessments 
  FOR DELETE USING (auth.uid() = autor_id);

-- Templates policies
CREATE POLICY "Templates are viewable by everyone" ON templates 
  FOR SELECT USING (true);

CREATE POLICY "Users can create templates" ON templates 
  FOR INSERT WITH CHECK (auth.uid() = autor_id);

CREATE POLICY "Users can update own templates" ON templates 
  FOR UPDATE USING (auth.uid() = autor_id);

-- Favorites policies
CREATE POLICY "Users can manage own favorites" ON favorites 
  FOR ALL USING (auth.uid() = user_id);

-- Subscriptions policies
CREATE POLICY "Users can view own subscription" ON subscriptions 
  FOR SELECT USING (auth.uid() = user_id);

-- Usage stats policies
CREATE POLICY "Users can view own usage stats" ON usage_stats 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage stats" ON usage_stats 
  FOR INSERT WITH CHECK (auth.uid() = user_id);