-- Seed data for EduAssess platform

-- Insert default templates
INSERT INTO templates (nome, categoria, layout_config, is_premium, is_system, rating, downloads) VALUES
(
  'Clássico Escolar',
  'Padrão',
  '{
    "cabecalho": {
      "nomeEscola": "Nome da Escola",
      "nomeProva": "Avaliação",
      "serie": "",
      "data": "",
      "instrucoes": "Leia atentamente cada questão antes de responder."
    },
    "espacamento": "normal",
    "numeracao": "automatica",
    "quebrasPagina": true,
    "folhaRespostas": false
  }',
  false,
  true,
  4.5,
  1250
),
(
  'Moderno Premium',
  'Premium',
  '{
    "cabecalho": {
      "nomeEscola": "Nome da Escola",
      "nomeProva": "Avaliação Premium",
      "serie": "",
      "data": "",
      "instrucoes": "Responda com atenção e boa sorte!"
    },
    "espacamento": "expandido",
    "numeracao": "automatica",
    "quebrasPagina": true,
    "folhaRespostas": true
  }',
  true,
  true,
  4.8,
  890
),
(
  'Minimalista',
  'Design',
  '{
    "cabecalho": {
      "nomeEscola": "Nome da Escola",
      "nomeProva": "Avaliação",
      "serie": "",
      "data": "",
      "instrucoes": "Leia com atenção."
    },
    "espacamento": "compacto",
    "numeracao": "automatica",
    "quebrasPagina": false,
    "folhaRespostas": false
  }',
  true,
  true,
  4.6,
  650
);

-- Insert sample questions (only if no questions exist)
INSERT INTO questions (disciplina, serie, topico, subtopico, dificuldade, tipo, competencia_bncc, enunciado, alternativas, resposta_correta, explicacao, tags, is_public, is_verified)
SELECT * FROM (VALUES
  (
    'Matemática',
    '6º Ano',
    'Frações',
    'Operações com frações',
    'Médio',
    'multipla_escolha',
    'EF06MA07',
    'Qual é o resultado de 2/3 + 1/4?',
    '["5/12", "11/12", "3/7", "2/12"]'::jsonb,
    'b',
    'Para somar frações com denominadores diferentes, devemos encontrar o MMC dos denominadores (3 e 4 = 12), depois convertemos as frações: 2/3 = 8/12 e 1/4 = 3/12. Somando: 8/12 + 3/12 = 11/12.',
    ARRAY['soma', 'frações', 'denominadores diferentes'],
    true,
    true
  ),
  (
    'Matemática',
    '7º Ano',
    'Números Inteiros',
    'Operações com números inteiros',
    'Fácil',
    'multipla_escolha',
    'EF07MA03',
    'Calcule: (-5) + (+3) - (-2) =',
    '["-10", "0", "6", "-4"]'::jsonb,
    'b',
    'Seguindo a regra dos sinais: (-5) + (+3) - (-2) = -5 + 3 + 2 = 0',
    ARRAY['números inteiros', 'operações', 'regra dos sinais'],
    true,
    true
  ),
  (
    'Português',
    '8º Ano',
    'Interpretação de Texto',
    'Inferência',
    'Médio',
    'dissertativa',
    'EF08LP04',
    'Leia o texto abaixo e explique qual é a crítica social presente na narrativa: "João acordou cedo para trabalhar na fábrica. Seus filhos foram para a escola sem café da manhã, pois não havia dinheiro para comprar comida. Enquanto isso, o dono da fábrica comprava seu terceiro carro do ano."',
    null,
    'O texto critica a desigualdade social e econômica, contrastando a situação de pobreza do trabalhador com a riqueza excessiva do patrão.',
    'A crítica social evidencia o contraste entre as classes sociais, mostrando como o trabalhador vive em condições precárias enquanto o empregador ostenta riqueza.',
    ARRAY['interpretação', 'crítica social', 'desigualdade'],
    true,
    true
  ),
  (
    'Ciências',
    '7º Ano',
    'Ecologia',
    'Cadeia alimentar',
    'Fácil',
    'multipla_escolha',
    'EF07CI07',
    'Em uma cadeia alimentar, os organismos que produzem seu próprio alimento são chamados de:',
    '["Consumidores primários", "Produtores", "Consumidores secundários", "Decompositores"]'::jsonb,
    'b',
    'Os produtores são organismos autótrofos, principalmente plantas, que produzem seu próprio alimento através da fotossíntese.',
    ARRAY['ecologia', 'cadeia alimentar', 'produtores'],
    true,
    true
  ),
  (
    'Matemática',
    '9º Ano',
    'Equações do 2º Grau',
    'Resolução por fórmula de Bhaskara',
    'Difícil',
    'dissertativa',
    'EF09MA09',
    'Resolva a equação x² - 5x + 6 = 0 usando a fórmula de Bhaskara e verifique as raízes encontradas.',
    null,
    'x₁ = 2 e x₂ = 3',
    'Aplicando a fórmula de Bhaskara: Δ = b² - 4ac = 25 - 24 = 1; x = (5 ± 1)/2, resultando em x₁ = 2 e x₂ = 3. Verificação: 2² - 5(2) + 6 = 0 ✓ e 3² - 5(3) + 6 = 0 ✓',
    ARRAY['equação segundo grau', 'bhaskara', 'raízes'],
    true,
    true
  )
) AS sample_data
WHERE NOT EXISTS (SELECT 1 FROM questions LIMIT 1);