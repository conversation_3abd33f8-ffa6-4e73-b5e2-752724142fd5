/*
  # Add trigger to handle new user profile creation

  1. New Functions
    - `handle_new_user()` - Creates a profile automatically when a new user signs up
  
  2. New Triggers
    - `on_auth_user_created` - Triggers profile creation on user signup
  
  3. Security
    - Function runs with SECURITY DEFINER to bypass <PERSON><PERSON> for profile creation
    - Ensures profiles are created reliably for all new users
*/

-- <PERSON>reate function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    nome, 
    email, 
    escola,
    disciplinas,
    plano,
    configuracoes,
    estatisticas,
    onboarding_completed
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'nome', ''),
    NEW.email,
    NEW.raw_user_meta_data->>'escola',
    '{}',
    'gratuito',
    '{}',
    jsonb_build_object(
      'questoesCriadas', 0,
      'provasGeradas', 0,
      'ultimoAcesso', NOW()
    ),
    false
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();