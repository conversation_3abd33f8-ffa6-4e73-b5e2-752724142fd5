-- Fix profile creation trigger
-- This migration ensures profiles are created automatically when users sign up

-- First, let's make sure the function exists and is correct
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the trigger execution for debugging
  RAISE LOG 'Creating profile for user: %', NEW.id;
  
  INSERT INTO public.profiles (
    id, 
    nome, 
    email, 
    escola,
    disciplinas,
    plano,
    configuracoes,
    estatisticas,
    onboarding_completed,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'nome', NEW.raw_user_meta_data->>'name', 'Usu<PERSON>rio'),
    NEW.email,
    NEW.raw_user_meta_data->>'escola',
    ARRAY[]::TEXT[],
    'gratuito',
    '{}'::JSONB,
    jsonb_build_object(
      'questoesCriadas', 0,
      'provasGeradas', 0,
      'ultimoAcesso', NOW()::TEXT
    ),
    false,
    NOW(),
    NOW()
  );
  
  RAISE LOG 'Profile created successfully for user: %', NEW.id;
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW; -- Don't fail the auth process if profile creation fails
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON public.profiles TO postgres, anon, authenticated, service_role;

-- Ensure the function can be executed by the trigger
ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

-- Test the trigger by creating a test function (optional, for debugging)
CREATE OR REPLACE FUNCTION public.test_profile_creation()
RETURNS TEXT AS $$
DECLARE
  test_result TEXT;
BEGIN
  -- Check if trigger exists
  SELECT 'Trigger exists: ' || CASE WHEN EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'on_auth_user_created'
  ) THEN 'YES' ELSE 'NO' END INTO test_result;
  
  RETURN test_result;
END;
$$ LANGUAGE plpgsql;