/*
  # Fix RLS infinite recursion in profiles table

  1. Security Updates
    - Update profiles RLS policies to prevent infinite recursion
    - Allow service_role to bypass RLS checks for internal operations
    - Add admin policies that don't cause recursion loops

  2. Changes Made
    - Modified "Users can view own profile" policy to include service_role bypass
    - Updated admin policies to use direct role checks instead of profile lookups
    - Added service_role bypass for critical operations
*/

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all questions" ON questions;
DROP POLICY IF EXISTS "Ad<PERSON> can update all questions" ON questions;
DROP POLICY IF EXISTS "Ad<PERSON> can delete all questions" ON questions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all assessments" ON assessments;
DROP POLICY IF EXISTS "Ad<PERSON> can update all assessments" ON assessments;
DROP POLICY IF EXISTS "Ad<PERSON> can delete all assessments" ON assessments;

-- Create new non-recursive policies for profiles
CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT USING (
    auth.uid() = id OR 
    auth.jwt() ->> 'role' = 'service_role'
  );

CREATE POLICY "Service role can manage profiles" ON profiles 
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create admin policies that don't cause recursion
CREATE POLICY "Admins can view all profiles" ON profiles 
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can update all profiles" ON profiles 
  FOR UPDATE USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

-- Update questions policies to avoid recursion
CREATE POLICY "Admins can view all questions" ON questions 
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can update all questions" ON questions 
  FOR UPDATE USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can delete all questions" ON questions 
  FOR DELETE USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

-- Update assessments policies to avoid recursion
CREATE POLICY "Admins can view all assessments" ON assessments 
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can update all assessments" ON assessments 
  FOR UPDATE USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can delete all assessments" ON assessments 
  FOR DELETE USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );