/*
  # Fix infinite recursion in profiles RLS policy

  1. Problem
    - Current RLS policy on profiles table is too restrictive
    - Causes infinite recursion when other tables try to fetch profile data
    - Queries from questions/assessments tables fail when trying to get author information

  2. Solution
    - Drop the existing restrictive SELECT policy
    - Create new policy allowing authenticated users to view all profiles
    - Keep existing UPDATE and INSERT policies for security

  3. Security
    - Maintains data security by requiring authentication
    - Allows necessary profile lookups for application functionality
    - Users can still only modify their own profiles
*/

-- Drop the existing restrictive SELECT policy that causes recursion
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;

-- Create new SELECT policy that allows authenticated users to view all profiles
-- This prevents recursion while maintaining security
CREATE POLICY "Allow authenticated users to view all profiles" ON profiles
  FOR SELECT 
  TO authenticated
  USING (true);

-- Ensure other policies remain secure (these should already exist)
-- Users can only update their own profile
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE 
  TO authenticated
  USING (auth.uid() = id);

-- Users can only insert their own profile during signup
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile" ON profiles 
  FOR INSERT 
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Service role can manage all profiles (for admin functions)
DROP POLICY IF EXISTS "Service role can manage profiles" ON profiles;
CREATE POLICY "Service role can manage profiles" ON profiles 
  FOR ALL 
  USING (auth.jwt() ->> 'role' = 'service_role');

-- Admin users can view all profiles
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
CREATE POLICY "Admins can view all profiles" ON profiles 
  FOR SELECT 
  TO authenticated
  USING (
    (auth.jwt() ->> 'role' = 'service_role') OR 
    (auth.uid() IN (
      SELECT profiles.id 
      FROM profiles 
      WHERE profiles.is_admin = true AND profiles.id = auth.uid()
    ))
  );

-- Admin users can update all profiles
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
CREATE POLICY "Admins can update all profiles" ON profiles 
  FOR UPDATE 
  TO authenticated
  USING (
    (auth.jwt() ->> 'role' = 'service_role') OR 
    (auth.uid() IN (
      SELECT profiles.id 
      FROM profiles 
      WHERE profiles.is_admin = true AND profiles.id = auth.uid()
    ))
  );