/*
  # Add notifications table and related functionality

  1. New Tables
    - `user_notifications` - Stores user notifications
  
  2. Security
    - RLS policies to ensure users can only access their own notifications
    - <PERSON><PERSON> can view all notifications
*/

-- Create user notifications table
CREATE TABLE IF NOT EXISTS user_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  action_label TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_notifications_user ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(read);
CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON user_notifications(created_at);

-- Enable RLS
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own notifications" ON user_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON user_notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notifications" ON user_notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Admin policies
CREATE POLICY "Admins can view all notifications" ON user_notifications
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

CREATE POLICY "Admins can manage all notifications" ON user_notifications
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

-- Function to create a notification
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_type TEXT,
  p_title TEXT,
  p_message TEXT,
  p_action_url TEXT DEFAULT NULL,
  p_action_label TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO user_notifications (
    user_id,
    type,
    title,
    message,
    action_url,
    action_label,
    metadata
  ) VALUES (
    p_user_id,
    p_type,
    p_title,
    p_message,
    p_action_url,
    p_action_label,
    p_metadata
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_read(
  p_user_id UUID
)
RETURNS VOID AS $$
BEGIN
  UPDATE user_notifications
  SET read = true
  WHERE user_id = p_user_id AND read = false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create notification when a user's question is verified by admin
CREATE OR REPLACE FUNCTION notify_question_verified()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_verified = true AND OLD.is_verified = false THEN
    PERFORM create_notification(
      NEW.autor_id,
      'success',
      'Questão verificada',
      'Sua questão foi verificada e aprovada por um administrador.',
      NULL,
      NULL,
      jsonb_build_object(
        'question_id', NEW.id,
        'enunciado', substring(NEW.enunciado from 1 for 100)
      )
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_question_verified
  AFTER UPDATE OF is_verified ON questions
  FOR EACH ROW
  WHEN (NEW.is_verified = true AND OLD.is_verified = false)
  EXECUTE FUNCTION notify_question_verified();

-- Insert some sample notifications for testing
INSERT INTO user_notifications (user_id, type, title, message, created_at)
SELECT 
  id as user_id,
  'info' as type,
  'Bem-vindo ao EduAssess' as title,
  'Obrigado por se juntar à plataforma. Comece explorando o banco de questões e criando sua primeira avaliação.' as message,
  NOW() as created_at
FROM profiles
WHERE NOT EXISTS (
  SELECT 1 FROM user_notifications WHERE title = 'Bem-vindo ao EduAssess'
)
LIMIT 5;