/*
  # Add FAQ table for help center

  1. New Tables
    - `faq_items` - Stores frequently asked questions
  
  2. Security
    - RLS policies to allow public read access
    - Admin-only write access
*/

-- Create FAQ items table
CREATE TABLE IF NOT EXISTS faq_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category TEXT NOT NULL,
  order_index INTEGER DEFAULT 0,
  is_published BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for ordering
CREATE INDEX IF NOT EXISTS idx_faq_items_category ON faq_items(category);
CREATE INDEX IF NOT EXISTS idx_faq_items_order ON faq_items(order_index);
CREATE INDEX IF NOT EXISTS idx_faq_items_published ON faq_items(is_published);

-- Enable RLS
ALTER TABLE faq_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "FAQ items are viewable by everyone" ON faq_items
  FOR SELECT USING (is_published = true);

CREATE POLICY "Admins can manage FAQ items" ON faq_items
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

-- Create trigger for updated_at
CREATE TRIGGER update_faq_items_updated_at
  BEFORE UPDATE ON faq_items
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert sample FAQ items
INSERT INTO faq_items (question, answer, category, order_index) VALUES
  (
    'Como criar minha primeira avaliação?',
    'Para criar uma avaliação, vá até o Editor, selecione questões do banco e configure o layout. Em seguida, gere o PDF para impressão.',
    'Primeiros Passos',
    1
  ),
  (
    'Como importar questões de outros sistemas?',
    'Você pode importar questões através da funcionalidade de Import/Export no Banco de Questões, suportando formatos JSON e CSV.',
    'Questões',
    1
  ),
  (
    'Qual a diferença entre os planos?',
    'O plano gratuito oferece 50 questões/mês. O Premium oferece questões ilimitadas, IA e templates premium. O Escolar inclui gestão para até 50 professores.',
    'Planos',
    1
  ),
  (
    'Como personalizar o cabeçalho das avaliações?',
    'No Editor de Avaliações, clique em "Configurar" para acessar as opções de cabeçalho, onde você pode definir nome da escola, título da prova, instruções e outros detalhes.',
    'Avaliações',
    1
  ),
  (
    'Como funciona a geração de questões por IA?',
    'A geração por IA está disponível para usuários Premium. Acesse o Banco de Questões, clique no botão "IA" e preencha os parâmetros desejados para gerar questões automaticamente.',
    'Questões',
    2
  ),
  (
    'Posso compartilhar minhas avaliações com outros professores?',
    'Sim, usuários Premium podem compartilhar avaliações. Vá até "Minhas Avaliações", selecione a avaliação desejada e clique em "Compartilhar" para gerar um link ou convidar colegas diretamente.',
    'Avaliações',
    2
  ),
  (
    'Como recuperar minha senha?',
    'Na tela de login, clique em "Esqueceu a senha?" e siga as instruções enviadas para seu email para criar uma nova senha.',
    'Primeiros Passos',
    2
  ),
  (
    'O sistema funciona offline?',
    'Não, o EduAssess requer conexão com a internet para funcionar corretamente. No entanto, PDFs gerados podem ser salvos localmente para uso offline.',
    'Técnico',
    1
  )
ON CONFLICT DO NOTHING;