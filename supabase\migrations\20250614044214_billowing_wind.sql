-- Create teacher tasks table
CREATE TABLE IF NOT EXISTS teacher_tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  due_date DATE NOT NULL,
  priority TEXT NOT NULL CHECK (priority IN ('high', 'medium', 'low')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed')),
  subject TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_user ON teacher_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_status ON teacher_tasks(status);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_due_date ON teacher_tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_priority ON teacher_tasks(priority);

-- Enable RLS
ALTER TABLE teacher_tasks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own tasks" ON teacher_tasks
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tasks" ON teacher_tasks
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks" ON teacher_tasks
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks" ON teacher_tasks
  FOR DELETE USING (auth.uid() = user_id);

-- Admin policies
CREATE POLICY "Admins can view all tasks" ON teacher_tasks
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    (auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true AND id = auth.uid()
    ))
  );

-- Create trigger for updated_at
CREATE TRIGGER update_teacher_tasks_updated_at
  BEFORE UPDATE ON teacher_tasks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert sample tasks for testing
INSERT INTO teacher_tasks (user_id, title, description, due_date, priority, status, subject)
SELECT 
  id as user_id,
  'Criar prova de recuperação' as title,
  'Preparar avaliação de recuperação para alunos com notas baixas' as description,
  (CURRENT_DATE + INTERVAL '7 days') as due_date,
  'high' as priority,
  'pending' as status,
  'Matemática' as subject
FROM profiles
WHERE NOT EXISTS (
  SELECT 1 FROM teacher_tasks LIMIT 1
)
LIMIT 3;

INSERT INTO teacher_tasks (user_id, title, description, due_date, priority, status, subject)
SELECT 
  id as user_id,
  'Revisar questões do 8º ano' as title,
  'Revisar banco de questões para o próximo bimestre' as description,
  (CURRENT_DATE + INTERVAL '14 days') as due_date,
  'medium' as priority,
  'pending' as status,
  'Português' as subject
FROM profiles
WHERE NOT EXISTS (
  SELECT 1 FROM teacher_tasks LIMIT 1
)
LIMIT 3;

INSERT INTO teacher_tasks (user_id, title, description, due_date, priority, status, subject)
SELECT 
  id as user_id,
  'Preparar simulado ENEM' as title,
  'Montar simulado completo para preparação do ENEM' as description,
  (CURRENT_DATE + INTERVAL '30 days') as due_date,
  'low' as priority,
  'pending' as status,
  'Ciências' as subject
FROM profiles
WHERE NOT EXISTS (
  SELECT 1 FROM teacher_tasks LIMIT 1
)
LIMIT 3;