/*
  # Corrigir recursão infinita nas políticas RLS

  1. Problema
    - <PERSON><PERSON>: "infinite recursion detected in policy for relation profiles"
    - As políticas RLS atuais estão causando recursão infinita
    - Isso impede que os perfis sejam carregados corretamente

  2. Sol<PERSON>ção
    - <PERSON><PERSON><PERSON> as políticas RLS para evitar recursão
    - Simplificar as verificações de administrador
    - Garantir que o service_role possa acessar todos os dados
*/

-- Remover todas as políticas existentes que podem estar causando recursão
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow authenticated users to view all profiles" ON profiles;

-- Políticas simplificadas para profiles
CREATE POLICY "Service role can manage profiles" ON profiles 
  FOR ALL 
  TO public
  USING (auth.jwt() ->> 'role' = 'service_role')
  WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT 
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE 
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles 
  FOR INSERT 
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Permitir que todos os usuários autenticados vejam todos os perfis
-- Isso é necessário para exibir informações de autor em questões, avaliações, etc.
CREATE POLICY "Allow users to view all profiles" ON profiles 
  FOR SELECT 
  TO authenticated
  USING (true);

-- Atualizar políticas para questions
DROP POLICY IF EXISTS "Admins can view all questions" ON questions;
DROP POLICY IF EXISTS "Admins can update all questions" ON questions;
DROP POLICY IF EXISTS "Admins can delete all questions" ON questions;

CREATE POLICY "Admins can manage questions" ON questions 
  FOR ALL 
  TO authenticated
  USING (
    auth.jwt() ->> 'role' = 'service_role' OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Atualizar políticas para assessments
DROP POLICY IF EXISTS "Admins can view all assessments" ON assessments;
DROP POLICY IF EXISTS "Admins can update all assessments" ON assessments;
DROP POLICY IF EXISTS "Admins can delete all assessments" ON assessments;

CREATE POLICY "Admins can manage assessments" ON assessments 
  FOR ALL 
  TO authenticated
  USING (
    auth.jwt() ->> 'role' = 'service_role' OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Atualizar políticas para outras tabelas
DROP POLICY IF EXISTS "Admins can view audit log" ON admin_audit_log;
CREATE POLICY "Admins can view audit log" ON admin_audit_log 
  FOR SELECT 
  TO public
  USING (
    auth.jwt() ->> 'role' = 'service_role' OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

DROP POLICY IF EXISTS "Admins can manage system settings" ON system_settings;
CREATE POLICY "Admins can manage system settings" ON system_settings 
  FOR ALL 
  TO public
  USING (
    auth.jwt() ->> 'role' = 'service_role' OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  );