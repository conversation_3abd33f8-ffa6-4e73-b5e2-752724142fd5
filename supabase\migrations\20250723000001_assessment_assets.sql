-- Create assessment_assets table for storing header customization images
CREATE TABLE IF NOT EXISTS assessment_assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  mime_type TEXT NOT NULL,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('custom_header', 'school_logo')),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessment_assets_user_id ON assessment_assets(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_assets_type ON assessment_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assessment_assets_created ON assessment_assets(created_at);

-- Enable RLS
ALTER TABLE assessment_assets ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own assets" ON assessment_assets 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own assets" ON assessment_assets 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own assets" ON assessment_assets 
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own assets" ON assessment_assets 
  FOR DELETE USING (auth.uid() = user_id);

-- Create storage bucket for assessment assets
INSERT INTO storage.buckets (id, name, public) 
VALUES ('assessment-assets', 'assessment-assets', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policies
CREATE POLICY "Users can view own assessment assets" ON storage.objects 
  FOR SELECT USING (bucket_id = 'assessment-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can upload assessment assets" ON storage.objects 
  FOR INSERT WITH CHECK (bucket_id = 'assessment-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update own assessment assets" ON storage.objects 
  FOR UPDATE USING (bucket_id = 'assessment-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own assessment assets" ON storage.objects 
  FOR DELETE USING (bucket_id = 'assessment-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_assessment_assets_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_assessment_assets_updated_at
  BEFORE UPDATE ON assessment_assets
  FOR EACH ROW
  EXECUTE FUNCTION update_assessment_assets_updated_at();
