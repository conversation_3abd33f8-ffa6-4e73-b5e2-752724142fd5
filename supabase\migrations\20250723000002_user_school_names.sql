-- <PERSON><PERSON><PERSON> tabela para armazenar nomes de escola dos usuários
CREATE TABLE IF NOT EXISTS user_school_names (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_user_school_names_user_id ON user_school_names(user_id);
CREATE INDEX IF NOT EXISTS idx_user_school_names_created_at ON user_school_names(created_at);

-- Criar índice único para evitar nomes duplicados por usuário
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_school_names_user_name 
ON user_school_names(user_id, <PERSON>OWER(name));

-- Ha<PERSON><PERSON><PERSON> (Row Level Security)
ALTER TABLE user_school_names ENABLE ROW LEVEL SECURITY;

-- Política para usuários autenticados poderem ver apenas seus próprios nomes
CREATE POLICY "Users can view their own school names" ON user_school_names
  FOR SELECT USING (auth.uid() = user_id);

-- Política para usuários autenticados poderem inserir seus próprios nomes
CREATE POLICY "Users can insert their own school names" ON user_school_names
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Política para usuários autenticados poderem atualizar seus próprios nomes
CREATE POLICY "Users can update their own school names" ON user_school_names
  FOR UPDATE USING (auth.uid() = user_id);

-- Política para usuários autenticados poderem deletar seus próprios nomes
CREATE POLICY "Users can delete their own school names" ON user_school_names
  FOR DELETE USING (auth.uid() = user_id);

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para atualizar updated_at
CREATE TRIGGER update_user_school_names_updated_at
  BEFORE UPDATE ON user_school_names
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
