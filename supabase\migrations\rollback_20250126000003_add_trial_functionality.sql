-- Rollback script for trial functionality migration
-- Use this script if Phase 1 needs to be rolled back

-- 1. Drop trigger and function
DROP TRIGGER IF EXISTS trigger_set_trial_end_date ON subscriptions;
DROP FUNCTION IF EXISTS set_trial_end_date();

-- 2. Drop new functions
DROP FUNCTION IF EXISTS is_user_in_trial(UUID);
DROP FUNCTION IF EXISTS get_trial_end_date(UUID);

-- 3. Restore original get_user_plan function
CREATE OR REPLACE FUNCTION get_user_plan(p_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
BEGIN
  -- Get active subscription plan (original version)
  SELECT s.plano INTO user_plan
  FROM subscriptions s
  WHERE s.user_id = p_user_id 
    AND s.status = 'active'  -- Only active status
  ORDER BY s.created_at DESC
  LIMIT 1;
  
  -- Default to free plan if no active subscription
  RETURN COALESCE(user_plan, 'gratuito');
END;
$$;

-- 4. Restore original can_create_assessment function
CREATE OR REPLACE FUNCTION can_create_assessment(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 5; -- Free plan limit
BEGIN
  -- Get user's plan (original version)
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count assessments created this month for free users
  SELECT COUNT(*) INTO current_count
  FROM assessments
  WHERE autor_id = p_user_id
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- 5. Restore original can_download_pdf function
CREATE OR REPLACE FUNCTION can_download_pdf(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_plan TEXT;
  current_count INTEGER;
  monthly_limit INTEGER := 10; -- Free plan limit
BEGIN
  -- Get user's plan (original version)
  user_plan := get_user_plan(p_user_id);
  
  -- Premium and escolar plans have unlimited access
  IF user_plan IN ('premium', 'escolar') THEN
    RETURN TRUE;
  END IF;
  
  -- Count PDF downloads this month for free users
  SELECT COUNT(*) INTO current_count
  FROM usage_stats
  WHERE user_id = p_user_id
    AND action_type = 'pdf_downloaded'
    AND created_at >= DATE_TRUNC('month', NOW())
    AND created_at < DATE_TRUNC('month', NOW()) + INTERVAL '1 month';
  
  RETURN current_count < monthly_limit;
END;
$$;

-- 6. Drop indexes
DROP INDEX IF EXISTS idx_subscriptions_trial_status;
DROP INDEX IF EXISTS idx_subscriptions_trial_end;

-- 7. Remove trial columns (WARNING: This will delete data)
-- Uncomment only if you're sure you want to remove the columns
-- ALTER TABLE subscriptions DROP COLUMN IF EXISTS trial_start;
-- ALTER TABLE subscriptions DROP COLUMN IF EXISTS trial_end;
-- ALTER TABLE subscriptions DROP COLUMN IF EXISTS trial_status;

-- Note: Column removal is commented out to prevent accidental data loss
-- If you need to remove the columns, uncomment the lines above

RAISE NOTICE 'Phase 1 rollback completed. Trial columns preserved but functions restored to original versions.';
