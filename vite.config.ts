import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { configureCodeSplitting } from './src/lib/performance/lazyComponents';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@supabase/supabase-js'
    ],
  },
  build: {
    // Configurações de otimização
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    // Code splitting configurado
    ...configureCodeSplitting(),
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Sourcemaps apenas em desenvolvimento
    sourcemap: process.env.NODE_ENV === 'development',
  },
  // Configurações de servidor para desenvolvimento
  server: {
    // Preload de módulos
    warmup: {
      clientFiles: [
        './src/main.tsx',
        './src/App.tsx',
        './src/contexts/AuthContext.tsx',
        './src/lib/supabase.ts',
      ],
    },
  },
  // Configurações de preview
  preview: {
    port: 4173,
    strictPort: true,
  },
});
